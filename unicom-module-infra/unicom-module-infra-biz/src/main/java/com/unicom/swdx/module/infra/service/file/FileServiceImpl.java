package com.unicom.swdx.module.infra.service.file;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.io.FileUtils;
import com.unicom.swdx.framework.file.core.client.FileClient;
import com.unicom.swdx.framework.file.core.utils.FileTypeUtils;
import com.unicom.swdx.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import com.unicom.swdx.module.infra.dal.dataobject.file.FileDO;
import com.unicom.swdx.module.infra.dal.mysql.file.FileMapper;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Set;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.infra.enums.ErrorCodeConstants.FILE_NOT_EXISTS;
import static com.unicom.swdx.module.infra.enums.ErrorCodeConstants.FILE_TYPE_NOT_ALLOWED;

/**
 * 文件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class FileServiceImpl implements FileService {

//    @Value("#{'${unicom.file.allow-types}'.split(',')}")
//    private Set<String> allowFileTypes;


    private static final List<String> allowFileTypes = ListUtil.of(
            // 图像文件
            "jpg", "png", "jpeg", "bmp", "svg", "gif",
            // 文档文件
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
            // 文本文件
            "txt",

            "text/plain", "text/csv" , "image/jpeg" ,"image/png","application/pdf" ,"application/msword" ,"application/vnd.openxmlformats-officedocument.wordprocessingml.document",

            "application/vnd.ms-excel" ,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint" ,"application/vnd.openxmlformats-officedocument.presentationml.presentation",

            "application/rtf",
            "application/vnd.ms-word2006ml",

            "application/zip" ,"application/vnd.rar","application/x-7z-compressed" ,"application/gzip" , "application/x-tar"
    );

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileMapper fileMapper;

    @Override
    public PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO) {
        return fileMapper.selectPage(pageReqVO);
    }

    @Override
    @SneakyThrows
    public String createFile(String name, String path, byte[] content) {
        return this.createFile(name,path,content,null);
    }

    @SneakyThrows
    @Override
    public String createFile(String name, String path, byte[] content, String module) {
        String type = FileTypeUtils.getMineType(content, name);
        // 校验文件的后缀名是否允许
        if(name!=null) {
            validFileMineName(type);
        }
        // 计算默认的 path 名
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name, module);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            String[] split = path.split("/");
            name = split[split.length - 1];
        }

        // 上传到文件存储器
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.upload(content, path);

        // 保存到数据库
        FileDO file = new FileDO();
        file.setConfigId(client.getId());
        file.setName(name);
        file.setPath(path);
        file.setUrl(url);
        file.setType(type);
        file.setSize(content.length);
        fileMapper.insert(file);
        return url;
    }

    /**
     * 校验文件后缀名
     * @param name
     */
    private void validFileExtName(String name) {
        String extName = FileNameUtil.extName(name);
        if (!allowFileTypes.contains(extName.toLowerCase())) {
            throw exception(FILE_TYPE_NOT_ALLOWED, extName);
        }
    }


    private void validFileMineName(String name) {

        if (!allowFileTypes.contains(name.toLowerCase())) {
            throw exception(FILE_TYPE_NOT_ALLOWED, name);
        }
    }

    @SneakyThrows
    @Override
    public FileDO createMutilFile(String name, String path, byte[] content, String module) {

        // 校验文件的后缀名是否允许
        if(name!=null) {
            validFileExtName(name);
        }

        String type = FileTypeUtils.getMineType(content, name);
        // 计算默认的 path 名
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name, module);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            String[] split = path.split("/");
            name = split[split.length - 1];
        }

        // 上传到文件存储器
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.upload(content, path);

        // 保存到数据库
        FileDO file = new FileDO();
        file.setConfigId(client.getId());
        file.setName(name);
        file.setPath(path);
        file.setUrl(url);
        file.setType(type);
        file.setSize(content.length);
        fileMapper.insert(file);
        return file;
    }

    @SneakyThrows
    @Override
    public Boolean updateFile(String name, String path, byte[] content, String module) {
        List<FileDO> fileDOS = fileMapper.selectListByPath(path);
        if (CollUtil.isEmpty(fileDOS)) {
            this.createFile(name,path,content,module);
            return true;
        }
        FileDO fileDO =  fileDOS.get(0);
        FileClient client = fileConfigService.getFileClient(fileDO.getConfigId());
        Assert.notNull(client, "客户端({}) 不能为空", fileDO.getConfigId());
        client.delete(fileDO.getPath());
        client.upload(content, path);
        return true;
    }

    @Override
    public void deleteFile(Long id) throws Exception {
        // 校验存在
        FileDO file = this.validateFileExists(id);

        // 从文件存储器中删除
        FileClient client = fileConfigService.getFileClient(file.getConfigId());
        Assert.notNull(client, "客户端({}) 不能为空", file.getConfigId());
        client.delete(file.getPath());

        // 删除记录
        fileMapper.deleteById(id);
    }

    private FileDO validateFileExists(Long id) {
        FileDO fileDO = fileMapper.selectById(id);
        if (fileDO == null) {
            throw exception(FILE_NOT_EXISTS);
        }
        return fileDO;
    }

    @Override
    public byte[] getFileContent(Long configId, String path) throws Exception {
        FileClient client = fileConfigService.getFileClient(configId);
        Assert.notNull(client, "客户端({}) 不能为空", configId);
        return client.getContent(path);
    }

    @Override
    public String getFileType(String path) {
        List<FileDO> fileDOS = fileMapper.selectListByPath(path);
        if (CollUtil.isNotEmpty(fileDOS)) {
            return fileDOS.get(0).getType();
        }
        return null;
    }

    @Override
    public String getFileName(String path) {
        List<FileDO> fileDOS = fileMapper.selectListByPath(path);
        if (CollUtil.isNotEmpty(fileDOS)) {
            return fileDOS.get(0).getName();
        }
        return null;
    }

    @Override
    public Long getFileConfigId(String path) {
        List<FileDO> fileDOS = fileMapper.selectListByPath(path);
        if (CollUtil.isNotEmpty(fileDOS)) {
            return fileDOS.get(0).getConfigId();
        }
        return null;
    }

    @Override
    public String getFileUrl(String path){
        List<FileDO> fileDOS = fileMapper.selectListByPath(path);
        if (CollUtil.isNotEmpty(fileDOS)){
            return fileDOS.get(0).getUrl();
        }
        return null;
    }

    @Override
    public String getFilesByPath(String path) {
        List<FileDO> fileDOS = fileMapper.selectListByPath(path);
        if (CollUtil.isNotEmpty(fileDOS)){
            return JSONObject.toJSONString(fileDOS);
        }
        return null;
    }

    @Override
    public InputStream getFileByteContent(Long configId, String sourcePath) {
        String path = StrUtil.subAfter(sourcePath, "/get/", false);
        if (StrUtil.isEmpty(path)) {
            throw new IllegalArgumentException("结尾的 path 路径必须传递");
        }
        // 读取内容
        byte[] content = new byte[0];
        try {
            content = this.getFileContent(configId, path);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ByteArrayInputStream(content);
    }

    @Override
    public String getFileOriginalName(Long configId, String sourcePath) {
        String path = StrUtil.subAfter(sourcePath, "/get/", false);
        if (StrUtil.isEmpty(path)) {
            throw new IllegalArgumentException("结尾的 path 路径必须传递");
        }
        return getFileName(path);
    }

    @Override
    public FileDO getFileByPath(String path) {
        List<FileDO> fileDOS = fileMapper.selectListByPath(path);
        if (CollUtil.isNotEmpty(fileDOS)){
            return fileDOS.get(0);
        }
        return null;
    }

    @Override
    public void updateFile(FileDO fileDO) {
        fileMapper.updateById(fileDO);
    }
}
