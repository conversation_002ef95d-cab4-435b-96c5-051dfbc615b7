package com.unicom.swdx.module.infra.controller.admin.file;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.UrlResource;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.servlet.ServletUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.infra.controller.admin.file.vo.file.*;
import com.unicom.swdx.module.infra.convert.file.FileConvert;
import com.unicom.swdx.module.infra.dal.dataobject.file.FileDO;
import com.unicom.swdx.module.infra.service.file.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriUtils;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.imageio.ImageIO;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.unicom.swdx.framework.common.pojo.CommonResult.error;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 文件存储")
@RestController
@RequestMapping("/infra/file")
@Validated
@Slf4j
public class FileController {

    @Resource
    private FileService fileService;
    private static final String[] ALLOWED_EXTENSIONS = {
            // 图像文件
            "jpg", "png", "jpeg", "bmp", "svg", "gif",
            // 文档文件
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
            // 文本文件
            "txt"
    };

    @PostMapping("/upload")
    @ApiOperation("上传文件")
    @OperateLog(logArgs = false) // 上传文件，没有记录操作日志的必要
    public CommonResult<String> uploadFile(FileUploadReqVO uploadReqVO) throws Exception {
        MultipartFile file = uploadReqVO.getFile();
        //测试环境的service的文件类型校验未生效，加的逻辑。
        String fileName = file.getOriginalFilename();
        int dotIndex = fileName.lastIndexOf('.');
        String fileExtension = fileName.substring(dotIndex + 1).toLowerCase();
        if (!isAllowedExtension(fileExtension)) {
            return error(400,"文件类型不合法");
        }
        String path = uploadReqVO.getPath();
        String module = uploadReqVO.getModule();
        return success(fileService.createFile(file.getOriginalFilename(), path, IoUtil.readBytes(file.getInputStream()),module));

    }

    @PostMapping("/upload_watermark")
    @ApiOperation("上传图片带水印")
    @OperateLog(logArgs = false)
    public CommonResult<String> uploadFileWithWatermark(FileUploadReqVO uploadReqVO) throws Exception {
        MultipartFile file = uploadReqVO.getFile();
//        file= addWorkMarkToMutipartFile(file,"仅供湖南省党校系统,一体化数智平台使用");
        String fileName = file.getOriginalFilename();
        int dotIndex = fileName.lastIndexOf('.');
        String fileExtension = fileName.substring(dotIndex + 1).toLowerCase();
        if (!isAllowedExtension(fileExtension)) {
            return error(400, "文件类型不合法");
        }

        // 获取文件扩展名
        String extension = FilenameUtils.getExtension(fileName);
// 读取文件内容为字节数组
        byte[] fileBytes = IoUtil.readBytes(file.getInputStream());

// 将字节数组转换为 BufferedImage
        ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);
        BufferedImage originalImage = ImageIO.read(inputStream);

//// 添加水印
        BufferedImage watermarkedImage = addWatermark(originalImage, extension);

// 将水印图片转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(watermarkedImage, extension, outputStream);
        byte[] imageBytes = outputStream.toByteArray();
        String path = uploadReqVO.getPath();
        String module = uploadReqVO.getModule();
        // 将水印图片字节数组写入到本地文件
//        String watermarkFilePath = "D:/watermarked_image." + extension; // 替换为你希望保存的文件路径
//        try (FileOutputStream fos = new FileOutputStream(watermarkFilePath)) {
//            fos.write(imageBytes);
//        } catch (IOException e) {
//            e.printStackTrace();
//            // 处理文件写入异常
//        }
        return success(fileService.createFile(fileName, path, imageBytes, module));
//        } else {
        // 对于非图片文件，直接上传
//            String path = uploadReqVO.getPath();
//            String module = uploadReqVO.getModule();
//            return success(fileService.createFile(fileName, path, IoUtil.readBytes(file.getInputStream()), module));
//        }
    }

    /**
     * 直接给multipartFile加上文字水印再进行保存图片的操作方便省事
     *
     * @param multipartFile
     *            文件上传的对象
     * @param  word
     *            水印文件的路径 如果是相对路径请使用相对路径new Image的方法,此处用的是url
     * @return
     * @throws IOException
     */
    public static MultipartFile addWorkMarkToMutipartFile(MultipartFile multipartFile,
                                                          String word) throws IOException {
        // 获取图片文件名 xxx.png xxx
        String originFileName = multipartFile.getOriginalFilename();
        // 获取原图片后缀 png
        int lastSplit = originFileName.lastIndexOf(".");
        String suffix = originFileName.substring(lastSplit + 1);
        // 获取图片原始信息
        String dOriginFileName = multipartFile.getOriginalFilename();
        String dContentType = multipartFile.getContentType();
        // 是图片且不是gif才加水印
        if (!suffix.equalsIgnoreCase("gif") && dContentType.contains("image")) {
            // 获取水印图片
            InputStream inputImg = multipartFile.getInputStream();
            Image img = ImageIO.read(inputImg);
            // 加图片水印
            int imgWidth = img.getWidth(null);
            int imgHeight = img.getHeight(null);


            BufferedImage bufImg = new BufferedImage(imgWidth, imgHeight,
                    BufferedImage.TYPE_INT_RGB);

            int strsize =20;

            if((imgHeight>1000&&imgHeight<=3000) || (imgWidth>1000&&imgWidth<=3000)){
                strsize =27;
            }else if(imgHeight>3000&&imgHeight<=5000||imgWidth>3000&&imgWidth<=5000 ){
                strsize =64;
            }else if(imgHeight>5000 || imgWidth>5000){
                strsize =74;
            }

            //设置字体
            Font font = new Font(null, Font.PLAIN, strsize);

            String keyword ="网络异常，暂无地址信息";


            String[] wordsplit = word.split("/");

            List temp = Stream.of(wordsplit).collect(Collectors.toList());



            WaterMakeUtils.markWord(bufImg, img,  temp , font , Color.white, strsize);

            ByteArrayOutputStream bs = new ByteArrayOutputStream();
            ImageOutputStream imOut = ImageIO.createImageOutputStream(bs);
            ImageIO.write(bufImg, suffix, imOut);
            InputStream is = new ByteArrayInputStream(bs.toByteArray());

            // 加水印后的文件上传
            multipartFile = new MockMultipartFile(dOriginFileName, dOriginFileName, dContentType,
                    is);
        }
        //返回加了水印的上传对象
        return multipartFile;
    }
    // 添加水印方法
    private BufferedImage addWatermark(BufferedImage originalImage,String type) {
        BufferedImage watermarkedImage = new BufferedImage(
                originalImage.getWidth(), originalImage.getHeight(), BufferedImage.TYPE_INT_ARGB);

        Graphics2D g2d = watermarkedImage.createGraphics();
        g2d.drawImage(originalImage, 0, 0, null);

// 添加透明背景矩形
        g2d.setColor(new Color(255, 255, 255, 0)); // 透明白色
        g2d.fillRect(0, originalImage.getWidth() - 50, originalImage.getHeight(), 50);

// 添加水印文本
        String watermarkText = "仅供湖南省党校系统";
        String watermarkText1 = "一体化信息平台使用";
        g2d.setColor(new Color(255, 0, 0, 100)); // 设置透明度为100（取值范围：0-255）
        Font font = new Font("微软雅黑", Font.BOLD, originalImage.getWidth()/8);
        g2d.setFont(font);
        //旋转originalImage.getHeight()/2.5改成originalImage.getHeight()/4水印往左了
        //0到-originalImage.getWidth()/10变下了
        g2d.rotate(Math.PI/2,originalImage.getWidth()/4, originalImage.getHeight()/3.5);
        g2d.drawString(watermarkText, (int) (originalImage.getWidth()/4), (int) (originalImage.getHeight()*0.2));
        // 获取第一行文本的宽度和高度
        FontMetrics fontMetrics = g2d.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(watermarkText);
        int textHeight = fontMetrics.getHeight();

// 计算第二行文本的位置（位于第一行文本正下方）
        int text1X = originalImage.getWidth()/4 ; // 使第二行文本与第一行文本水平居中
        int text1Y = (int) (originalImage.getHeight()*0.2) + textHeight; // 使第二行文本位于第一行文本正下方

// 绘制第二行文本
        g2d.drawString(watermarkText1, text1X, text1Y);
        g2d.dispose();


//        // 保存水印图像到本地目录
//        File outputImageFile = new File("D:/watermarked_image.jpg");
//        try {
//            ImageIO.write(watermarkedImage, type, outputImageFile);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
        return watermarkedImage;
    }

    @PostMapping("/delete")
    @ApiOperation("删除文件")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('infra:file:delete')")
    public CommonResult<Boolean> deleteFile(@RequestParam("id") Long id) throws Exception {
        fileService.deleteFile(id);
        return success(true);
    }

    @GetMapping("/{configId}/get/**")
    @PermitAll
    @ApiOperation("下载文件")
    @ApiImplicitParam(name = "configId", value = "配置编号",  required = true, dataTypeClass = Long.class)
    public void getFileContent(HttpServletRequest request,
                               HttpServletResponse response,
                               @PathVariable("configId") Long configId) throws Exception {
        // 获取请求的路径
        String path = StrUtil.subAfter(request.getRequestURI(), "/get/", false);
        if (StrUtil.isEmpty(path)) {
            throw new IllegalArgumentException("结尾的 path 路径必须传递");
        }
        String type = fileService.getFileType(path);
        if (StrUtil.isEmpty(type)) {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            return;
        }

        // 读取内容
        byte[] content = fileService.getFileContent(configId, path);
        if (content == null) {
            log.warn("[getFileContent][configId({}) path({}) 文件不存在]", configId, path);
            response.setStatus(HttpStatus.NOT_FOUND.value());
            return;
        }
        String fileName = fileService.getFileName(path);
        if (StrUtil.isBlank(fileName)) {
            fileName = path;
        }
        if (!StringUtils.isEmpty(fileName)) {
            fileName = fileName.replaceAll(" ", "");
        }

        if (StringUtils.startsWithIgnoreCase(type,"image/")) {
            ServletUtils.writeImage(response, fileName, content);
        } else {
            ServletUtils.writeAttachment(response, fileName, content);
        }


    }

    //下载操作手册

    @GetMapping("/download_manual")
    public ResponseEntity<FileSystemResource> downloadFile(
            @RequestParam(value = "name",required = false,defaultValue = "/操作手册.doc") String name,
            @RequestParam(value = "fileName",required = false,defaultValue = "操作手册.doc") String fileName) {
        // 文件的路径，这里应该是你服务器上实际文件的路径
        String filePath = "/opt/upload" + name;
        // 尝试获取文件作为资源
        try {
            FileSystemResource resource = new FileSystemResource(filePath);
            if (resource.exists() && resource.isReadable()) {
                // 设置文件名，这里是你想让用户下载时看到的文件名

                // 设置Content-Disposition头信息
                HttpHeaders headers = new HttpHeaders();
                headers.add("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

                // 构建响应实体并返回
                return ResponseEntity.ok()
                        .headers(headers)
                        .contentType(MediaType.APPLICATION_OCTET_STREAM)
                        .body(resource);
            } else {
                // 如果文件不可读或不存在，返回404或其他错误响应
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            // 处理异常，例如文件读取错误等
            return ResponseEntity.internalServerError().build();
        }
    }



    @PostMapping("/batch-upload")
    @ApiOperation("批量上传文件")
    @PermitAll
    @OperateLog(logArgs = false) // 上传文件，没有记录操作日志的必要
    public CommonResult<List<Map<String, Object>>> batchUploadFile(FileBatchUploadReqVO uploadReqVO) throws Exception {
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (MultipartFile file : uploadReqVO.getFile()) {
            String fileName = file.getOriginalFilename();
            int dotIndex = fileName.lastIndexOf('.');
            String fileExtension = fileName.substring(dotIndex + 1).toLowerCase();
            if (!isAllowedExtension(fileExtension)) {
                return error(400,"文件类型不合法");
            }
            Map<String, Object> map = new HashMap<>();
            String path = uploadReqVO.getPath();
            String module = uploadReqVO.getModule();
            FileDO fileDO = fileService.createMutilFile(file.getOriginalFilename(), path, IoUtil.readBytes(file.getInputStream()),module);
            map.put("path", fileDO.getUrl());
            map.put("fileName", fileDO.getName());
            map.put("fileType", fileDO.getName().substring(fileDO.getName().lastIndexOf(".")+1));
            mapList.add(map);
        }
        return success(mapList);
    }

    @PostMapping("/batch-download")
    @PermitAll
    @ApiOperation("批量下载文件")
    public void batchDownloadFile(HttpServletResponse response,
                               @RequestBody FileDownloadReqVO fileDownloadReqVO)  {
        //设置返回响应头
        response.reset();

        //判断文件id是否为空
        if (CollectionUtils.isEmpty(fileDownloadReqVO.getPath())) {
            throw new IllegalArgumentException("结尾的 path 路径必须传递");
        }

        //调用单个文件的下载接口  拿到所有文件的流数组
        InputStream[] inputStreams = fileDownloadReqVO.getPath().stream().map(info -> fileService.getFileByteContent(fileDownloadReqVO.getConfigId(), info)).toArray(InputStream[]::new);

        //拿到所有文件名字的数组  因为名字可能重复我拼接了一个时间戳
        String[] strings = fileDownloadReqVO.getPath().stream().map(info ->fileService.getFileOriginalName(fileDownloadReqVO.getConfigId(),info)).toArray(String[]::new);

        //调用Hutool 工具的压缩方法
        try {
            response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("批量下载.zip", "UTF-8"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            ZipUtil.zip(response.getOutputStream(), strings, inputStreams);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @GetMapping("/page")
    @ApiOperation("获得文件分页")
    @PreAuthorize("@ss.hasPermission('infra:file:query')")
    public CommonResult<PageResult<FileRespVO>> getFilePage(@Valid FilePageReqVO pageVO) {
        PageResult<FileDO> pageResult = fileService.getFilePage(pageVO);
        return success(FileConvert.INSTANCE.convertPage(pageResult));
    }

    private boolean isAllowedExtension(String fileExtension) {
        for (String allowedExtension : ALLOWED_EXTENSIONS) {
            if (allowedExtension.equals(fileExtension)) {
                return true;
            }
        }
        return false;
    }
}
