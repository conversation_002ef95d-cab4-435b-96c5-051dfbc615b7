package com.unicom.swdx.module.system.api.tenant;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.tenant.dto.TenantInfoRespDTO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class TenantApiImpl implements TenantApi {

    @Resource
    private TenantService tenantService;

    @Override
    public CommonResult<List<Long>> getTenantIds() {
        return success(tenantService.getTenantIds());
    }

    @Override
    public CommonResult<Boolean> validTenant(Long id) {
        tenantService.validTenant(id);
        return success(true);
    }

    @Override
    public CommonResult<String> getTenantCodeById(Long id) {
        return success(tenantService.getTenantFromCache(id).getTenantCode());
    }

    @Override
    public CommonResult<String> getTenantCodeByUserId(Long userId) {
        TenantDO tenant = tenantService.getTenantByUserId(userId);
        if(Objects.nonNull(tenant)){
            return success(tenant.getTenantCode());
        }
        return success(null);
    }

    /**
     * 根据租户管理员用户ID获取租户信息
     * @param tenantAdminUserIds 租户管理员用户ID集合
     * @return 租户信息集合
     */
    @Override
    public CommonResult<List<TenantInfoRespDTO>> getTenantByContactUserIds(Collection<Long> tenantAdminUserIds) {
        return success(tenantService.getTenantByContactUserIds(tenantAdminUserIds));
    }

    @Override
    public CommonResult<Boolean> tenantAdmin(Long userId) {
        return success(tenantService.isTenantAdmin(userId));
    }

    @Override
    public CommonResult<Boolean> getTenantCheckRuleById(Long id) {
        TenantDO tenantFromCache = tenantService.getTenantFromCache(id);
        boolean result = tenantFromCache != null && Boolean.TRUE.equals(tenantFromCache.getCheckByPeriodRule());
        return success(result);
    }
}
