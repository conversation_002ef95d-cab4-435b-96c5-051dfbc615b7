package com.unicom.swdx.module.system.service.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.enums.UserTypeEnum;
import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.common.util.collection.SetUtils;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.common.util.monitor.TracerUtils;
import com.unicom.swdx.framework.common.util.servlet.ServletUtils;
import com.unicom.swdx.framework.common.util.validation.ValidationUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.api.signupunit.SignUpUnitApi;
import com.unicom.swdx.module.hr.api.PersonnalApi;
import com.unicom.swdx.module.hr.api.dto.PersonnalApiDO;
import com.unicom.swdx.module.system.api.logger.dto.LoginLogCreateReqDTO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.*;
import com.unicom.swdx.module.system.controller.xcx.auth.vo.WxXcxAuthLoginReqVO;
import com.unicom.swdx.module.system.controller.xcx.auth.vo.WxXcxAuthLoginRespVO;
import com.unicom.swdx.module.system.controller.xcx.auth.vo.WxxcxCode2SessionVO;
import com.unicom.swdx.module.system.convert.auth.AuthConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserDeptDO;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.MenuDO;
import com.unicom.swdx.module.system.dal.dataobject.permission.RoleDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.dept.DeptMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.dal.mysql.user.TraineeUserMapper;
import com.unicom.swdx.module.system.dal.mysql.dept.UserDeptMapper;
import com.unicom.swdx.module.system.dal.redis.auth.LoginBadPasswordDAO;
import com.unicom.swdx.module.system.enums.dept.DeptIdEnum;
import com.unicom.swdx.module.system.enums.logger.LoginLogTypeEnum;
import com.unicom.swdx.module.system.enums.logger.LoginResultEnum;
import com.unicom.swdx.module.system.enums.oauth2.OAuth2ClientConstants;
import com.unicom.swdx.module.system.enums.permission.MenuTypeEnum;
import com.unicom.swdx.module.system.enums.user.UserRoleTypeEnum;
import com.unicom.swdx.module.system.service.dept.DeptService;
import com.unicom.swdx.module.system.service.logger.LoginLogService;
import com.unicom.swdx.module.system.service.member.MemberService;
import com.unicom.swdx.module.system.service.oauth2.OAuth2ClientService;
import com.unicom.swdx.module.system.service.oauth2.OAuth2TokenService;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.permission.RoleService;
import com.unicom.swdx.module.system.service.social.SocialUserService;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import com.unicom.swdx.module.system.wx.qywx.QywxApiService;
import com.unicom.swdx.module.system.wx.xcx.WxXcxApiService;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.Validator;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;
import static java.util.Collections.singleton;

/**
 * Auth Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RefreshScope
public class AdminAuthServiceImpl implements AdminAuthService {

    @Resource
    private AdminUserService userService;
    @Resource
    private LoginLogService loginLogService;

    @Resource
    private UserDeptMapper userDeptMapper;

    @Resource
    private AdminUserMapper userMapper;
    @Resource
    private OAuth2TokenService oauth2TokenService;
    @Resource
    private SocialUserService socialUserService;
    @Resource
    private MemberService memberService;

    @Resource
    private Validator validator;

    @Resource
    private CaptchaService captchaService;

    @Resource
    private DeptService deptService;

    @Resource
    private DeptMapper deptMapper;

    @Resource
    private RoleService roleService;

    @Resource
    private TenantService tenantService;

    @Resource
    @Lazy
    private PermissionService permissionService;

    @Resource
    private WxXcxApiService wxXcxApiService;

    @Resource
    private QywxApiService qywxApiService;

    @Resource
    private OAuth2ClientService oAuth2ClientService;

    @Resource
    private TraineeUserMapper traineeUserMapper;

    @Resource
    private SignUpUnitApi signUpUnitApi;

    /**
     * 验证码的开关，默认为 true
     */
    @Value("${unicom.captcha.enable:true}")
    private Boolean captchaEnable;

    @Resource
    private LoginBadPasswordDAO loginBadPasswordDAO;

    @Resource
    private RedisUtil redisUtil;

    private final String REDISKEY_WXXCX_UNIQUE_CODE = "wxxcx:uniquecode:";

    /**
     * 时间差限制（5000毫秒，即5秒）
     */
    private static final long TIME_LIMIT = 5000L;

    /**
     * 账号登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    @Override
    public AuthLoginRespVO login(AuthLoginReqVO reqVO) {
        // 判断验证码是否正确
        verifyCaptcha(reqVO);
        // 使用账号密码，进行登录
        AdminUserDO user = authenticate(reqVO.getUsername(), reqVO.getPassword(), "login");


        AuthLoginRespVO authLoginRespVO = createTokenAfterLoginSuccess(user.getId(), reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);;


        try {

            if(user!=null){
                //如果账号正确

                List<AdminUserDO> users = userService.getUsersByMobile(user.getMobile());

                if(users.size()>1){


                    List<AuthLoginRespVO> authLoginRespVOList =new ArrayList<>();


                    users.forEach(it->{




                        TenantDO tenantDO  =  tenantService.getTenant(it.getTenantId());



                        AuthLoginRespVO temp =  createTokenAfterLoginSuccess(it.getId(), it.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);
                        temp.setUserName(it.getUsername());
                        DeptDO deptDO = deptService.getDept(it.getDeptId());
                        temp.setDeptName(deptDO.getName());
                        temp.setLogindefualt(it.getLogindefualt());


                        temp.setTenantName(tenantDO.getName());

                        authLoginRespVOList.add(temp);

                    });


                    authLoginRespVO.setAuthLoginRespVOList(authLoginRespVOList);

                }




            }
        }catch (Exception e){
            log.error("账号密码登录多租户账号存在问题 {}" , e.getMessage());
        }


        // 创建 Token 令牌，记录登录日志
        return authLoginRespVO;
    }

    /**
     * 调训系统单位管理员登录
     *
     * @param reqVO 账号密码
     * @return 登录结果
     */
    @Override
    public AuthLoginRespVO loginForUnit(AuthLoginReqVO reqVO) {
        // 使用账号密码，进行登录
        AdminUserDO user = authenticateForUnit(reqVO.getUsername(), reqVO.getPassword(), "login");
        // 通过userId获得调训单位id
        Long unitId = signUpUnitApi.getUnitIdByUserId(user.getId()).getCheckedData();
        if(Objects.isNull(unitId)){
            throw exception(USER_NOT_EXISTS);
        }
        AuthLoginRespVO authLoginRespVO = createTokenAfterLoginSuccess(user.getId(), reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);;
        // 返回是否修改了初始密码，用于修改初始密码
        authLoginRespVO.setInitPasswordIsChange(user.getInitPasswordIsChange());
        // 返回调训单位id
        authLoginRespVO.setUnitId(unitId);
        return authLoginRespVO;
    }

    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;

    @Override
    @TenantIgnore
    @Transactional
    public AuthLoginRespVO singleSignOn(SingleSignOnReqVO reqVO) {

        log.info("singleSignOn : {}"  , reqVO.toString());


        AdminUserDO user = new AdminUserDO();
        if (reqVO.getId() != null) {
            sqlSessionTemplate.clearCache();
            user = userMapper.getUserBySystemId(reqVO.getId());

            log.info("singleSignOn getUserBySystemId : {}"  , user);

        } else if (reqVO.getEmployeeId() != null) {
            user = userMapper.getUserByEmployeeId(reqVO.getEmployeeId());
        }


        log.info("singleSignOn getUserBySystemIdisNull : {}"  , user);

        if(ObjectUtil.isEmpty(user)){

            LambdaQueryWrapperX<AdminUserDO> queryWrapperX = new LambdaQueryWrapperX<>();

            queryWrapperX.eq(AdminUserDO::getSystemId , reqVO.getId());

            List<AdminUserDO> adminUserDOList = userMapper.selectList(queryWrapperX);

            if(CollectionUtil.isNotEmpty(adminUserDOList)){
                user = adminUserDOList.get(0);
            }

        }



        if(ObjectUtil.isEmpty(user)){

                LambdaQueryWrapperX<AdminUserDO> queryWrapperX = new LambdaQueryWrapperX<>();

                queryWrapperX.eq(AdminUserDO::getSystemId , reqVO.getId());

                List<AdminUserDO> adminUserDOList = userMapper.selectList(queryWrapperX);

                if(CollectionUtil.isNotEmpty(adminUserDOList)){
                    user = adminUserDOList.get(0);
                }

        }

        if(ObjectUtil.isEmpty(user)){

            LambdaQueryWrapperX<AdminUserDO> queryWrapperX = new LambdaQueryWrapperX<>();

            queryWrapperX.eq(AdminUserDO::getSystemId , reqVO.getId());

            List<AdminUserDO> adminUserDOList = userMapper.selectList(queryWrapperX);

            if(CollectionUtil.isNotEmpty(adminUserDOList)){
                user = adminUserDOList.get(0);
            }

        }





        if(Objects.isNull(user)) {

            CompletableFuture<AdminUserDO> futureUser = CompletableFuture.supplyAsync(() -> {
                LambdaQueryWrapperX<AdminUserDO> queryWrapperX = new LambdaQueryWrapperX<>();
                queryWrapperX.eq(AdminUserDO::getSystemId, reqVO.getId());

                List<AdminUserDO> adminUserDOList = userMapper.selectList(queryWrapperX);

                return CollectionUtil.isNotEmpty(adminUserDOList) ? adminUserDOList.get(0) : null;
            });

            // 等待结果
            try {
                user = futureUser.get(); // 阻塞等待结果
                // 处理 user
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        }



        log.info("singleSignOn getUserBySystemIdisNull_two : {}"  , user);



        if(Objects.isNull(user)) {
            throw exception(USER_NOT_EXISTS);
        }


        AuthLoginRespVO authLoginRespVO = createTokenAfterSingleLoginSuccess(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME, reqVO.getToken());;
        authLoginRespVO.setEmployeeId(reqVO.getEmployeeId()); //根据业务中台请求的EmployeeId设置登录教务的EmployeeId

        try {

            if(user!=null){
                //如果账号正确

                List<AdminUserDO> users = userService.getUsersByMobile(user.getMobile());

                if(users.size()>1){


                    List<AuthLoginRespVO> authLoginRespVOList =new ArrayList<>();


                    users.forEach(it->{




                        TenantDO tenantDO  =  tenantService.getTenant(it.getTenantId());



                        AuthLoginRespVO temp =  createTokenAfterLoginSuccess(it.getId(), it.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);
                        temp.setUserName(it.getUsername());
                        DeptDO deptDO = deptService.getDept(it.getDeptId());
                        temp.setDeptName(deptDO==null? null:deptDO.getName());
                        temp.setLogindefualt(it.getLogindefualt());

                        temp.setTenantName(tenantDO.getName());
                        temp.setEmployeeId(reqVO.getEmployeeId());

                        authLoginRespVOList.add(temp);

                    });


                    authLoginRespVO.setAuthLoginRespVOList(authLoginRespVOList);

                }




            }
        }catch (Exception e){
            log.error("账号密码登录多租户账号存在问题 {}" , e.getMessage());
        }


        // 创建 Token 令牌，记录登录日志
        return authLoginRespVO;
    }

    /**
     * 根据手机号登录
     * @param reqVO 请求信息
     * @return 返回身份token
     */
    @Override
    public String loginByMobile(@Valid AuthByMobileReqVO reqVO){
        //校验时间戳的有效性
        // 获取当前时间（单位：毫秒）
        long currentTime = Instant.now().toEpochMilli();
        // 获取请求中的时间戳
        long requestTimestamp = reqVO.getTimestamp();
        // 计算请求时间和当前时间的差值
        long timeDifference = currentTime - requestTimestamp;
        // 判断时间差是否超过了限制
        if (timeDifference > TIME_LIMIT) {
            // 超过5秒，返回错误提示
            throw exception(TIMEOUT_ERROR);
        }
        //开始登录获取token
        LambdaQueryWrapperX<AdminUserDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(AdminUserDO::getMobile , reqVO.getMobile());
        queryWrapper.orderByDesc(AdminUserDO::getId);
        List<AdminUserDO> adminUserDOS = userMapper.selectList(queryWrapper);
        if(CollectionUtil.isEmpty(adminUserDOS)){
            throw exception(USER_NOT_EXISTS);
        }
        AdminUserDO userDO = adminUserDOS.get(0);
        AuthLoginRespVO authLoginRespVO = createTokenAfterSingleLoginSuccess(userDO.getId(), userDO.getUsername(), LoginLogTypeEnum.LOGIN_MOBILE, null);
        return authLoginRespVO.getAccessToken();
    }

    /**
     * 基于 token 退出登录
     *
     * @param token token
     * @param logType 登出类型
     */
    @Override
    public void logout(String token, Integer logType) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.removeAccessToken(token);
        if (Objects.isNull(accessTokenDO)) {
            throw exception(AUTH_TOKEN_NOT_EXISTS);
        }
        // 删除成功，则记录登出日志
        createLogoutLog(accessTokenDO.getUserId(), accessTokenDO.getUserType(), logType);
    }

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    @Override
    public AuthLoginRespVO refreshToken(String refreshToken) {
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.refreshAccessToken(refreshToken, OAuth2ClientConstants.CLIENT_ID_DEFAULT);
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    /**
     * 获取用户的权限信息
     * @return 用户的权限信息
     */
    @Override
    public AuthPermissionInfoRespVO getPermissionInfo(Long clientId) {
        // 获得用户信息
        AdminUserDO user = userService.getUser(getLoginUserId());

        List<Long> deptIds = userDeptMapper.getDeptList(user.getId());
        if(!deptIds.isEmpty()) {
            List<DeptDO> departments = deptService.getDepts(deptIds);
            departments = departments.stream().filter(Objects::nonNull).collect(Collectors.toList());
            Collections.sort(departments, Comparator.comparing(DeptDO::getSort)
                    .thenComparing(DeptDO::getUpdateTime, Comparator.reverseOrder()));
            user.setDepartments(departments);
        } else {
            List<DeptDO> department = new ArrayList<>();
            DeptDO dept = deptService.getDept(user.getDeptId());
            department.add(dept);
            user.setDepartments(department);
        }

        if (Objects.isNull(user)) {
            return null;
        }
        // 获得角色列表
        List<RoleDO> roleList = permissionService.getEnableRoleIdsByClientId(user.getId(), clientId);
        if(CollectionUtil.isEmpty(roleList)){
            throw exception(ROLE_IS_EMPTY);
        }
        Set<Long> roleIds = roleList.stream().map(RoleDO::getId).collect(Collectors.toSet());
        // 获得菜单列表
        List<MenuDO> menuList = permissionService.getRoleMenuListIncludeParentsFromCache(roleIds,
                SetUtils.asSet(MenuTypeEnum.DIR.getType(), MenuTypeEnum.MENU.getType(), MenuTypeEnum.BUTTON.getType()),
                singleton(CommonStatusEnum.ENABLE.getStatus()), // 只要开启的
                clientId);
        if (!tenantService.isTenantAdmin(user.getId()) && !permissionService.isSuperAdmin(user.getId())) {
            // 非管理员，需要把菜单权限过滤一遍管理员的菜单权限，不能超出。因为有可能自定义角色的真实菜单权限比管理员还大，这里限制一下真实的菜单权限
            menuList = permissionService.filterTenantMenu(menuList,user.getTenantId());
        }
        // 拼接结果返回
        AuthPermissionInfoRespVO respVO = AuthConvert.INSTANCE.convert(user, roleList, menuList);

        // 获取上次登录时间
        LocalDateTime lastTime = oauth2TokenService.getLoginLastTime(user.getId());
        if (Objects.nonNull(lastTime)) {
            respVO.getUser().setLoginLastTime(DateUtils.format(lastTime));
        }
        AuthPermissionInfoRespVO.UserVO resultUser = respVO.getUser();
        // 拼接部门信息
        DeptDO deptDO = deptService.getDept(user.getDeptId());
        if (deptDO != null) {
            resultUser.setDeptName(deptDO.getName());
            resultUser.setDeptId(deptDO.getId());
            List<DeptDO> deptDOS = deptService.getAllParentDeptByDeptId(deptDO.getId());
            if (!CollectionUtils.isAnyEmpty(deptDOS)) {
                List<DeptDO> rootDepts = deptDOS.stream().filter(deptDO1 -> DeptIdEnum.ROOT.getId().equals(deptDO1.getParentId())).collect(Collectors.toList());
                if (!CollectionUtils.isAnyEmpty(rootDepts)) {
                    resultUser.setRootDeptId(rootDepts.get(0).getId());
                    resultUser.setRootDeptName(rootDepts.get(0).getName());
                }
            } else {
                resultUser.setRootDeptId(deptDO.getId());
                resultUser.setRootDeptName(deptDO.getName());
            }
        }

        //判断用户是否为学生和教职工0学生1教职工2学生和教职工3普通用户
        List<Long> traineeList = traineeUserMapper.getTraineeList();
        List<Long>  hrList = traineeUserMapper.getHrList();
        if(traineeList.contains(user.getId())) {
            resultUser.setType(hrList.contains(user.getId())?2:0);
        }else {
            resultUser.setType(hrList.contains(user.getId())?1:3);
        }

        // 设置用户类型：超级管理员，管理员，普通用户
        if (permissionService.isSuperAdmin(user.getId())) {
            resultUser.setUserRoleType(UserRoleTypeEnum.SUPER_ADMIN.getType());
        } else if (tenantService.isTenantAdmin(user.getId())) {
            resultUser.setUserRoleType(UserRoleTypeEnum.TENANT_ADMIN.getType());
        } else {
            resultUser.setUserRoleType(UserRoleTypeEnum.COMMON_USER.getType());
        }
        return respVO;
    }

    /**
     * 根据应用id获取菜单信息
     * @param clientId 应用id
     * @return 菜单信息
     */
    @Override
    public List<AuthMenuRespVO> getMenusByClient(Long clientId) {
        Long userId = getLoginUserId();
        Long apply = roleService.getRoleByName("应聘者");
        // 获得角色列表
        List<RoleDO> roleList = permissionService.getEnableRoleIdsByClientId(userId, clientId);

        if(roleList ==null){
            return null;
        }

        Set<Long> roleIds = roleList.stream().map(RoleDO::getId).collect(Collectors.toSet());
        // 获得用户拥有的菜单列表
        List<MenuDO> menuList = permissionService.getRoleMenuListIncludeParentsFromCache(roleIds,
                SetUtils.asSet(MenuTypeEnum.DIR.getType(), MenuTypeEnum.MENU.getType()), // 只要目录和菜单类型
                singleton(CommonStatusEnum.ENABLE.getStatus()), clientId); // 只要开启的
        if (!tenantService.isTenantAdmin(userId) && !permissionService.isSuperAdmin(userId) && !roleIds.contains(apply)) {
            // 非管理员，需要把菜单权限过滤一遍管理员的菜单权限，不能超出。因为有可能自定义角色的真实菜单权限比管理员还大，这里限制一下真实的菜单权限
            menuList = permissionService.filterTenantMenu(menuList,getTenantId());
        }
        // 转换成 Tree 结构返回
        return AuthConvert.INSTANCE.buildMenuTree(menuList);
    }

    /**
     * 手机验证码登录
     * @param reqVO 登录信息
     * @return 登录结果
     */
    @Override
    public AuthLoginRespVO codeLogin(CodeAuthLoginReqVO reqVO){
        // 使用手机验证码，进行登录
        AdminUserDO user = codeAuthenticate(reqVO.getMobile(), reqVO.getVerification());
        // 校验用户是否有可用角色

        Set<Long>  roleids =  permissionService.getUserRoleIdListByUserId(user.getId());

        if (   CollUtil.isEmpty(roleids)) {
            throw exception(AUTH_LOGIN_ROLE_DISABLED);
        }
        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_MOBILE);
    }

    /**
     * 手机验证码修改密码
     * @param reqVO 修改密码信息
     * @return 修改密码结果
     */
    @Override
    public void codeUpdate(CodeAuthUpdateReqVO reqVO){
        // 使用手机验证码，进行登录
        AdminUserDO user = resetPasswordCodeAuthenticate(reqVO.getMobile(), reqVO.getVerification());
        userService.updateUserPassword(user.getId(), reqVO.getNewPassword());
    }

    private AdminUserDO resetPasswordCodeAuthenticate(String mobile, String verification) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        AdminUserDO user = userService.getUserByMobile(mobile);
        if(Objects.isNull(user)){
            throw exception(AUTH_MOBILE_NOT_EXISTS);
        }
        String username = user.getUsername();
        //验证码，手机号，用户名校验
        if (!userService.isResetPasswordVerificationMatch(verification, mobile)){
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_VERIFICATION);
        }

        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        // 校验用户绑定的部门是否关闭
        if (Objects.nonNull(user.getDeptId())) {
            DeptDO dept = deptService.getDept(user.getDeptId());
            if (dept==null || dept.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED);
            }
            // 校验所有父部门是否有关闭状态
            List<DeptDO> parentDeptList = deptService.getAllParentDeptByDeptId(user.getDeptId());
            if (CollUtil.isNotEmpty(parentDeptList) && parentDeptList.stream().anyMatch(deptDO -> deptDO.getStatus().equals(CommonStatusEnum.DISABLE.getStatus()))) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED,dept.getName());
            }
        }
        // 校验用户的机构是否过期
        TenantDO tenant = tenantService.getTenantFromCache(user.getTenantId());
        // 校验用户的机构是否禁用
        if (Objects.equals(tenant.getStatus(),CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(AUTH_TENANT_DISABLED);
        }
        return user;
    }

    /**
     * 使用微信小程序的uniqueCode登录
     * @param loginReqVO 登录信息
     * @return token
     */
    @Override
    public AuthLoginRespVO wxXcxLogin(WxXcxLoginReqVO loginReqVO) {
        // 尝试从缓存中获取uniqueCode信息
        Object mobileObj = redisUtil.get(REDISKEY_WXXCX_UNIQUE_CODE + loginReqVO.getUniqueCode());
        if (Objects.isNull(mobileObj)) {
            // 如果缓存中没有，则返回null，controller中判断为空则是没收到小程序消息；
            return null;
        }
        AdminUserDO user = userService.getUserByMobile(mobileObj.toString());
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_WXXCX, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        // 校验用户绑定的部门是否关闭
        if (Objects.nonNull(user.getDeptId())) {
            DeptDO dept = deptService.getDept(user.getDeptId());
            if (dept==null || dept.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED);
            }
            // 校验所有父部门是否有关闭状态
            List<DeptDO> parentDeptList = deptService.getAllParentDeptByDeptId(user.getDeptId());
            if (CollUtil.isNotEmpty(parentDeptList) && parentDeptList.stream().anyMatch(deptDO -> deptDO.getStatus().equals(CommonStatusEnum.DISABLE.getStatus()))) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED,dept.getName());
            }
        }
        // 校验用户的机构是否过期
        TenantDO tenant = tenantService.getTenantFromCache(user.getTenantId());
        // 校验用户的机构是否禁用
        if (Objects.equals(tenant.getStatus(),CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(AUTH_TENANT_DISABLED);
        }
        // 校验用户是否有可用权限
        if (CollUtil.isEmpty(permissionService.getAllRoleIdsFromCache(user.getId(),CollUtil.newArrayList(CommonStatusEnum.ENABLE.getStatus())))) {
            throw exception(AUTH_LOGIN_ROLE_DISABLED);
        }

        AuthLoginRespVO  authLoginRespVO = createTokenAfterLoginSuccess(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_WXXCX);

        try {

            if(user!=null){
                //如果账号正确

                List<AdminUserDO> users = userService.getUsersByMobile(user.getMobile());

                if(users.size()>1){


                    List<AuthLoginRespVO> authLoginRespVOList =new ArrayList<>();


                    users.forEach(it->{

                        TenantDO tenantDO  =  tenantService.getTenant(it.getTenantId());


                        AuthLoginRespVO temp =  createTokenAfterLoginSuccess(it.getId(), it.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);
                        temp.setUserName(it.getUsername());
                        DeptDO deptDO = deptService.getDept(it.getDeptId());
                        temp.setDeptName(deptDO.getName());
                        temp.setLogindefualt(it.getLogindefualt());

                        temp.setTenantName(tenantDO.getName());

                        authLoginRespVOList.add(temp);
                    });


                    authLoginRespVO.setAuthLoginRespVOList(authLoginRespVOList);

                }




            }







        }catch (Exception e){
            log.error("小程序扫描登录多租户 {}" , e.getMessage());
        }


        return authLoginRespVO;
    }

    /**
     * 社交快捷登录，使用 code 授权码
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    @Override
    public AuthLoginRespVO socialLogin(AuthSocialLoginReqVO reqVO) {
        // 使用 code 授权码，进行登录。然后，获得到绑定的用户编号
        Long userId = socialUserService.getBindUserId(UserTypeEnum.ADMIN.getValue(), reqVO.getType(),
                reqVO.getCode(), reqVO.getState());
        if (userId == null) {
            throw exception(AUTH_THIRD_LOGIN_NOT_BIND);
        }

        // 获得用户
        AdminUserDO user = userService.getUser(userId);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_SOCIAL);
    }

    /**
     * 使用微信小程序登录
     * @param reqVO
     * @return
     */
    @Override
    public WxXcxAuthLoginRespVO mobileLogin(WxXcxAuthLoginReqVO reqVO) {
        // 1、调用微信小程序接口获取用户手机号
        String mobile = wxXcxApiService.getMobile(reqVO.getMobileCode());
        // 2、校验用户手机号是否存在
        AdminUserDO user = userService.getUserByMobile(mobile);
        if (Objects.isNull(user)) {
            throw exception(MOBILE_USER_NOT_EXIST);
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_WXXCX, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        // 校验用户绑定的部门是否关闭
        if (Objects.nonNull(user.getDeptId())) {
            DeptDO dept = deptService.getDept(user.getDeptId());
            if (dept==null ||dept.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED);
            }
            // 校验所有父部门是否有关闭状态
            List<DeptDO> parentDeptList = deptService.getAllParentDeptByDeptId(user.getDeptId());
            if (CollUtil.isNotEmpty(parentDeptList) && parentDeptList.stream().anyMatch(deptDO -> deptDO.getStatus().equals(CommonStatusEnum.DISABLE.getStatus()))) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED,dept.getName());
            }
        }
        // 校验用户的机构是否过期
        TenantDO tenant = tenantService.getTenantFromCache(user.getTenantId());
        // 校验用户的机构是否禁用
        if (Objects.equals(tenant.getStatus(),CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(AUTH_TENANT_DISABLED);
        }
        // 校验用户是否有可用权限
        if (CollUtil.isEmpty(permissionService.getAllRoleIdsFromCache(user.getId(),CollUtil.newArrayList(CommonStatusEnum.ENABLE.getStatus())))) {
            throw exception(AUTH_LOGIN_ROLE_DISABLED);
        }
        // 3、手机号放入缓存
        // 设置5分钟缓存
        if (StrUtil.isNotBlank(reqVO.getUniqueCode())) {
            redisUtil.set(REDISKEY_WXXCX_UNIQUE_CODE + reqVO.getUniqueCode(),user.getMobile(), 300);
        }
        // 1、调用微信小程序接口获取微信openid或unionid
        WxxcxCode2SessionVO wxUserId = wxXcxApiService.getWxUserId(reqVO.getLoginCode());
        if (Objects.nonNull(wxUserId)) {
            // 写入openid和unionid
            userService.updateOpenidAndUnionid(AdminUserDO.builder().id(user.getId()).wxxcxOpenid(wxUserId.getOpenid()).wxUnionid(wxUserId.getUnionid()).build());
        }
        // 4、生成token
        AuthLoginRespVO tokenAfterLoginSuccess = createTokenAfterLoginSuccess(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_WXXCX);



        try {

            if(user!=null){
                //如果账号正确

                List<AdminUserDO> users = userService.getUsersByMobile(user.getMobile());

                if(users.size()>1){


                    List<AuthLoginRespVO> authLoginRespVOList =new ArrayList<>();


                    users.forEach(it->{

                        TenantDO tenantDO  =  tenantService.getTenant(it.getTenantId());

                        AuthLoginRespVO temp =  createTokenAfterLoginSuccess(it.getId(), it.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);
                        temp.setUserName(it.getUsername());
                        DeptDO deptDO = deptService.getDept(it.getDeptId());
                        temp.setDeptName(deptDO.getName());
                        temp.setLogindefualt(it.getLogindefualt());

                        temp.setTenantName(tenantDO.getName());

                        authLoginRespVOList.add(temp);
                    });


                    tokenAfterLoginSuccess.setAuthLoginRespVOList(authLoginRespVOList);

                }




            }







        }catch (Exception e){
            log.error("小程序扫描登录多租户 {}" , e.getMessage());
        }


        return AuthConvert.INSTANCE.convertWxXcx(tokenAfterLoginSuccess);
    }

    /**
     * 小程序用户openid或unionid登录
     * @param reqVO
     * @return
     */
    @Override
    public WxXcxAuthLoginRespVO openidLogin(WxXcxAuthLoginReqVO reqVO) {

        if(StrUtil.isEmptyOrUndefined(reqVO.getUniqueCode())){
            reqVO.setUniqueCode(null);
        }

        // 1、调用微信小程序接口获取微信openid或unionid
        WxxcxCode2SessionVO wxUserId = wxXcxApiService.getWxUserId(reqVO.getLoginCode());
        // 2、校验用户unionid是否存在
        AdminUserDO user = null;
        if (StrUtil.isNotBlank(wxUserId.getUnionid())) {
            user = userService.getByUnionid(wxUserId.getUnionid());
        }
        if (Objects.isNull(user)) {
            user = userService.getByOpenid(wxUserId.getOpenid());
        }
        if (Objects.isNull(user)) {
            return null;
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_WXXCX, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        // 校验用户绑定的部门是否关闭
        if (Objects.nonNull(user.getDeptId())) {
            DeptDO dept = deptService.getDept(user.getDeptId());
            try {
                if (dept==null||dept.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
                    throw exception(AUTH_LOGIN_DEPT_DISABLED);
                }
            }catch (NullPointerException exception){
                log.error("openidLogin   :  {}" ,  exception.getMessage());
            }

            // 校验所有父部门是否有关闭状态
            List<DeptDO> parentDeptList = deptService.getAllParentDeptByDeptId(user.getDeptId());
            if (CollUtil.isNotEmpty(parentDeptList) && parentDeptList.stream().anyMatch(deptDO -> deptDO.getStatus().equals(CommonStatusEnum.DISABLE.getStatus()))) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED,dept.getName());
            }
        }
        // 校验用户的机构是否过期
        TenantDO tenant = tenantService.getTenantFromCache(user.getTenantId());
        // 校验用户的机构是否禁用
        if (tenant==null ||   (Objects.equals(tenant.getStatus(),CommonStatusEnum.DISABLE.getStatus()))) {
            throw exception(AUTH_TENANT_DISABLED);
        }
        // 校验用户是否有可用权限
        if (CollUtil.isEmpty(permissionService.getAllRoleIdsFromCache(user.getId(),CollUtil.newArrayList(CommonStatusEnum.ENABLE.getStatus())))) {
            throw exception(AUTH_LOGIN_ROLE_DISABLED);
        }
        // 3、手机号放入缓存
        // 设置5分钟缓存
        if (StrUtil.isNotBlank(reqVO.getUniqueCode())) {
            redisUtil.set(REDISKEY_WXXCX_UNIQUE_CODE + reqVO.getUniqueCode(),user.getMobile(), 300);
        }
        // 4、生成token
        AuthLoginRespVO tokenAfterLoginSuccess = createTokenAfterLoginSuccess(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_WXXCX);



        try {

            if(user!=null){
                //如果账号正确

                List<AdminUserDO> users = userService.getUsersByMobile(user.getMobile());

                if(users.size()>1){


                    List<AuthLoginRespVO> authLoginRespVOList =new ArrayList<>();


                    users.forEach(it->{

                        TenantDO tenantDO  =  tenantService.getTenant(it.getTenantId());


                        AuthLoginRespVO temp =  createTokenAfterLoginSuccess(it.getId(), it.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);
                        temp.setUserName(it.getUsername());
                        DeptDO deptDO = deptService.getDept(it.getDeptId());
                        temp.setDeptName(deptDO.getName());
                        temp.setLogindefualt(it.getLogindefualt());

                        temp.setTenantName(tenantDO.getName());

                        authLoginRespVOList.add(temp);
                    });


                    tokenAfterLoginSuccess.setAuthLoginRespVOList(authLoginRespVOList);

                }




            }







        }catch (Exception e){
            log.error("小程序扫描登录多租户 {}" , e.getMessage());
        }


        return AuthConvert.INSTANCE.convertWxXcx(tokenAfterLoginSuccess);
    }

    @Override
    public WxXcxAuthLoginRespVO qywxCodeLogin(QywxAuthLoginReqVO reqVO) {
        // 1、调用微信小程序接口获取微信openid或unionid
        String mobile = qywxApiService.getQywxMobile(reqVO.getCode());
        // 2、校验用户手机号是否存在
        AdminUserDO user = userService.getUserByMobile(mobile);
        if (Objects.isNull(user)) {
            throw exception(MOBILE_USER_NOT_EXIST);
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_WXXCX, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        // 校验用户绑定的部门是否关闭
        if (Objects.nonNull(user.getDeptId())) {
            DeptDO dept = deptService.getDept(user.getDeptId());
            if (dept==null || dept.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED);
            }
            // 校验所有父部门是否有关闭状态
            List<DeptDO> parentDeptList = deptService.getAllParentDeptByDeptId(user.getDeptId());
            if (CollUtil.isNotEmpty(parentDeptList) && parentDeptList.stream().anyMatch(deptDO -> deptDO.getStatus().equals(CommonStatusEnum.DISABLE.getStatus()))) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED,dept.getName());
            }
        }
        // 校验用户的机构是否过期
        TenantDO tenant = tenantService.getTenantFromCache(user.getTenantId());
        // 校验用户的机构是否禁用
        if (Objects.equals(tenant.getStatus(),CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(AUTH_TENANT_DISABLED);
        }
        // 校验用户是否有可用权限
        if (CollUtil.isEmpty(permissionService.getAllRoleIdsFromCache(user.getId(),CollUtil.newArrayList(CommonStatusEnum.ENABLE.getStatus())))) {
            throw exception(AUTH_LOGIN_ROLE_DISABLED);
        }
        // 3、手机号放入缓存
        // 设置5分钟缓存
        if (StrUtil.isNotBlank(reqVO.getState())) {
            redisUtil.set(REDISKEY_WXXCX_UNIQUE_CODE + reqVO.getState(),user.getMobile(), 300);
        }
        // 4、生成token
        AuthLoginRespVO tokenAfterLoginSuccess = createTokenAfterLoginSuccess(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_WXXCX);
        return AuthConvert.INSTANCE.convertWxXcx(tokenAfterLoginSuccess);
    }

    /**
     * 使用手机号获取token
     * @param mobile
     * @return
     */
    @Override
    public AuthLoginRespVO mobileLogin(String mobile) {
        // 2、校验用户手机号是否存在
        AdminUserDO user = userService.getUserByMobile(mobile);
        if (Objects.isNull(user)) {
            throw exception(MOBILE_USER_NOT_EXIST);
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_WXXCX, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        // 校验用户绑定的部门是否关闭
        if (Objects.nonNull(user.getDeptId())) {
            DeptDO dept = deptService.getDept(user.getDeptId());
            if (dept==null || dept.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED);
            }
            // 校验所有父部门是否有关闭状态
            List<DeptDO> parentDeptList = deptService.getAllParentDeptByDeptId(user.getDeptId());
            if (CollUtil.isNotEmpty(parentDeptList) && parentDeptList.stream().anyMatch(deptDO -> deptDO.getStatus().equals(CommonStatusEnum.DISABLE.getStatus()))) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED,dept.getName());
            }
        }
        // 校验用户的机构是否过期
        TenantDO tenant = tenantService.getTenantFromCache(user.getTenantId());
        // 校验用户的机构是否禁用
        if (Objects.equals(tenant.getStatus(),CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(AUTH_TENANT_DISABLED);
        }
        // 校验用户是否有可用权限
        if (CollUtil.isEmpty(permissionService.getAllRoleIdsFromCache(user.getId(),CollUtil.newArrayList(CommonStatusEnum.ENABLE.getStatus())))) {
            throw exception(AUTH_LOGIN_ROLE_DISABLED);
        }
        // 4、生成token
        return createTokenAfterLoginSuccess(user.getId(), user.getUsername(), LoginLogTypeEnum.LOGIN_WXXCX);
    }


    @Resource
    PersonnalApi personnalApi;


    /**
     * 验证账号 + 密码。如果通过，则返回用户
     *
     * @param username 账号
     * @param password 密码
     * @return 用户
     */
    @Override
    public AdminUserDO authenticate(String username, String password, String type) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        AdminUserDO user = null;
        try {
            user = userService.getUserByUsername(username);
        } catch (Exception e) {
            throw exception(new ErrorCode(e.getMessage().length(),e.getMessage()));
        }
        if (user == null) {
            createLoginLog(null, username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 查询用户密码是否输入超过5次
        if (loginBadPasswordDAO.get(username) >= 5) {
            throw exception(AUTH_LOGIN_BAD_PASSWORD);
        }
        if (!userService.isPasswordMatch(password, user.getPassword())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            // 缓存记录用户密码输错次数，超过5次，账号封30分钟
            loginBadPasswordDAO.set(username);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验密码是否已经过期取消检验过期
//        userService.validPasswordExpired(user);
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }


        PersonnalApiDO personnalApiDO =  personnalApi.getUser(user.getId());

        DateTime beginworktime = DateUtil.date();//当前时间

//         personnalApiDO.getDeathTime();//去世时间
//        personnalApiDO.getLeaveTime();//离校时间
//        personnalApiDO.getRetireTime();//退休时间

        if(personnalApiDO!=null){


            if(personnalApiDO.getPersonnalStatus()==null||personnalApiDO.getPersonnalStatus().intValue()==4){
                throw exception(AUTH_LOGIN_USER_DISABLED);
            }


        }



        // 校验用户绑定的部门是否关闭
        if (Objects.nonNull(user.getDeptId())) {
            DeptDO dept = deptService.getDept(user.getDeptId());
            if (dept ==null || dept.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED);
            }
            // 校验所有父部门是否有关闭状态
            List<DeptDO> parentDeptList = deptService.getAllParentDeptByDeptId(user.getDeptId());
            if (CollUtil.isNotEmpty(parentDeptList) && parentDeptList.stream().anyMatch(deptDO -> deptDO.getStatus().equals(CommonStatusEnum.DISABLE.getStatus()))) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED,dept.getName());
            }
        }
        // 校验用户的机构是否过期
        TenantDO tenant = tenantService.getTenantFromCache(user.getTenantId());
        // 校验用户的机构是否禁用
        if (Objects.equals(tenant.getStatus(),CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(AUTH_TENANT_DISABLED);
        }
        // 校验用户是否有可用角色
        Set<Long> roleIds = permissionService.getAllRoleIdsFromCache(user.getId(), CollUtil.newArrayList(CommonStatusEnum.ENABLE.getStatus()));
        if (CollUtil.isEmpty(roleIds)) {
            throw exception(AUTH_LOGIN_ROLE_DISABLED);
        }else {
            // 获得菜单列表
            List<MenuDO> menuList;
            Long apply = roleService.getRoleByName("应聘者");
            if (roleIds.contains(apply)) {
                Long clientId = oAuth2ClientService.getClientIdByName("招聘系统");
                menuList = permissionService.getRoleMenuListIncludeParentsFromCache(roleIds,
                        SetUtils.asSet(MenuTypeEnum.DIR.getType(), MenuTypeEnum.MENU.getType(), MenuTypeEnum.BUTTON.getType()),
                        singleton(CommonStatusEnum.ENABLE.getStatus()), // 只要开启的
                        clientId);
            }else {
                if (Objects.equals(type, "OAuth")) {
                    menuList = permissionService.getRoleMenuListIncludeParentsFromCache(roleIds,
                            SetUtils.asSet(MenuTypeEnum.DIR.getType(), MenuTypeEnum.MENU.getType(), MenuTypeEnum.BUTTON.getType()),
                            singleton(CommonStatusEnum.ENABLE.getStatus()), // 只要开启的
                            1L);
                } else {
                    menuList = permissionService.getRoleMenuListIncludeParentsFromCache(roleIds,
                            SetUtils.asSet(MenuTypeEnum.DIR.getType(), MenuTypeEnum.MENU.getType(), MenuTypeEnum.BUTTON.getType()),
                            singleton(CommonStatusEnum.ENABLE.getStatus()), // 只要开启的
                            null);
                }
            }
            if (!tenantService.isTenantAdmin(user.getId()) && !permissionService.isSuperAdmin(user.getId()) && !roleIds.contains(apply)) {
                // 非管理员，需要把菜单权限过滤一遍管理员的菜单权限，不能超出。因为有可能自定义角色的真实菜单权限比管理员还大，这里限制一下真实的菜单权限
                menuList = permissionService.filterTenantMenu(menuList,user.getTenantId());
            }
            if (CollUtil.isEmpty(menuList)){
                throw exception(AUTH_LOGIN_ROLE_DISABLED);
            }
        }
        return user;
    }

    /**
     * 调训单位登录 验证账号 + 密码。如果通过，则返回用户
     *
     * @param username 账号
     * @param password 密码
     * @return 用户
     */
    @Override
    public AdminUserDO authenticateForUnit(String username, String password, String type) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        AdminUserDO user = null;
        try {
            // 根据用户名从单位调训表中获取用户信息
            user = userService.getUserByUnitUsername(username);
        } catch (Exception e) {
            throw exception(new ErrorCode(e.getMessage().length(),e.getMessage()));
        }
        if (user == null) {
            createLoginLog(null, username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 查询用户密码是否输入超过5次
        if (loginBadPasswordDAO.get(username) >= 5) {
            throw exception(AUTH_LOGIN_BAD_PASSWORD);
        }
        if (!userService.isPasswordMatch(password, user.getPassword())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            // 缓存记录用户密码输错次数，超过5次，账号封30分钟
            loginBadPasswordDAO.set(username);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验密码是否已经过期取消检验过期
//        userService.validPasswordExpired(user);
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        return user;
    }


    /**
     * 验证手机号 + 验证码，如果通过，返回用户
     * @param mobile 手机号
     * @param verification 验证码
     * @return 用户
     */
    private AdminUserDO codeAuthenticate(String mobile, String verification) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        AdminUserDO user = userService.getUserByMobile(mobile);
        if(Objects.isNull(user)){
            throw exception(AUTH_MOBILE_NOT_EXISTS);
        }
        String username = user.getUsername();
        //验证码，手机号，用户名校验
        if (!userService.isVerificationMatch(verification, mobile)){
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_VERIFICATION);
        }

        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        // 校验用户绑定的部门是否关闭
        if (Objects.nonNull(user.getDeptId())) {
            DeptDO dept = deptService.getDept(user.getDeptId());
            if (dept==null || dept.getStatus().equals(CommonStatusEnum.DISABLE.getStatus())) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED);
            }
            // 校验所有父部门是否有关闭状态
            List<DeptDO> parentDeptList = deptService.getAllParentDeptByDeptId(user.getDeptId());
            if (CollUtil.isNotEmpty(parentDeptList) && parentDeptList.stream().anyMatch(deptDO -> deptDO.getStatus().equals(CommonStatusEnum.DISABLE.getStatus()))) {
                throw exception(AUTH_LOGIN_DEPT_DISABLED,dept.getName());
            }
        }
        // 校验用户的机构是否过期
        TenantDO tenant = tenantService.getTenantFromCache(user.getTenantId());
        // 校验用户的机构是否禁用
        if (Objects.equals(tenant.getStatus(),CommonStatusEnum.DISABLE.getStatus())) {
            throw exception(AUTH_TENANT_DISABLED);
        }
        return user;
    }


    /**
     * 校验验证码
     * @param reqVO 登录信息
     */
    private void verifyCaptcha(AuthLoginReqVO reqVO) {
        // 如果验证码关闭，则不进行校验
        if (!captchaEnable) {
            return;
        }

        if(reqVO.getCaptchaVerification()!=null&&StrUtil.equals("VzoEW26jL8_5cAe@lO~b%7)C1Hk0?wY4" ,reqVO.getCaptchaVerification() )){
            return;
        }

        // 校验验证码
        ValidationUtils.validate(validator, reqVO, AuthLoginReqVO.CodeEnableGroup.class);
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaVerification(reqVO.getCaptchaVerification());
        ResponseModel response = captchaService.verification(captchaVO);
        // 验证不通过
        if (!response.isSuccess()) {
            // 创建登录失败日志（验证码不正确)
            createLoginLog(null, reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME, LoginResultEnum.CAPTCHA_CODE_ERROR);
            throw exception(AUTH_LOGIN_CAPTCHA_CODE_ERROR, response.getRepMsg());
        }
    }

    /**
     * 创建登录日志
     * @param userId 用户id
     * @param username 用户名
     * @param logTypeEnum 登录类型
     * @param loginResult 登录结果
     */
    private void createLoginLog(Long userId, String username,
                                LoginLogTypeEnum logTypeEnum, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logTypeEnum.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(UserTypeEnum.ADMIN.getValue());
        reqDTO.setUsername(username);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogService.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
            userService.updateUserLogin(userId, ServletUtils.getClientIP());
        }
    }

    /**
     * 创建登录token
     * @param userId 用户id
     * @param username 用户名
     * @param logType 登录类型
     * @return
     */
    public AuthLoginRespVO createTokenAfterLoginSuccess(Long userId, String username, LoginLogTypeEnum logType) {
        // 插入登陆日志
        createLoginLog(userId, username, logType, LoginResultEnum.SUCCESS);
        // 创建访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.createAccessToken(userId, UserTypeEnum.ADMIN.getValue(),
                OAuth2ClientConstants.CLIENT_ID_DEFAULT, null);
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    public AuthLoginRespVO createTokenAfterSingleLoginSuccess(Long userId, String username, LoginLogTypeEnum logType, String oldToken) {
        // 插入登陆日志
        createLoginLog(userId, username, logType, LoginResultEnum.SUCCESS);
        // 创建访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.createAccessToken(userId, UserTypeEnum.ADMIN.getValue(),
                OAuth2ClientConstants.CLIENT_ID_DEFAULT, null, oldToken);
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    /**
     * 创建登出日志
     * @param userId 用户id
     * @param userType 用户类型
     * @param logType 日志类型
     */
    private void createLogoutLog(Long userId, Integer userType, Integer logType) {
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType);
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(userType);
        if (ObjectUtil.equal(UserTypeEnum.ADMIN.getValue(), userType)) {
            reqDTO.setUsername(getUsername(userId));
        } else {
            reqDTO.setUsername(memberService.getMemberUserMobile(userId));
        }
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
        loginLogService.createLoginLog(reqDTO);
    }

    private String getUsername(Long userId) {
        if (userId == null) {
            return null;
        }
        AdminUserDO user = userService.getUser(userId);
        return user != null ? user.getUsername() : null;
    }

}
