package com.unicom.swdx.module.system.service.businesscenter;


import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.Aes.AesEncryptUtil;
import com.unicom.swdx.module.system.api.businesscenter.dto.OAuth2UserInfosRespDTO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import com.unicom.swdx.module.system.controller.admin.businesscenter.vo.*;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptSimpleRespVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserRespVO;
import com.unicom.swdx.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.dal.redis.businesscenter.BusinessCenterRedisDAO;
import com.unicom.swdx.module.system.dal.redis.oauth2.OAuth2AccessTokenRedisDAO;
import com.unicom.swdx.module.system.service.yezhong.YezhongService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业中远程调用 Service 实现类
 */
@Service
@Validated
@Slf4j
public class BusinessCenterServiceImpl implements BusinessCenterService {

    @Resource
    private OAuth2AccessTokenRedisDAO oauth2AccessTokenRedisDAO;

    @Resource
    private BusinessCenterRedisDAO businessCenterRedisDAO;

    @Resource
    private YezhongService yezhongService;

    @Resource
    private AdminUserMapper adminUserMapper;

    @Value("${unicom.mid-base-uri}")
    private String MID_BASE_URI;

    @Value("${businesscenter.url_dept:/prod-api/system/dept/list-all-simple}")
    private String URL_DEPT;

    @Value("${businesscenter.url_user:/prod-api/system/user/list-all-simple}")
    private String URL_USER;

    @Value("${businesscenter.url_user:/prod-api/system/user/get}")
    private String URL_USER_INFO;

    @Value("${businesscenter.url_user:/prod-api/system/oauth2/user/get}")
    private String URL_USER_OAUTH2_INFO;

    @Value("${businesscenter.url_user:/prod-api/hr/personnal/getAllPersonInfoSimple}")
    private String URL_PERSONNAL_SIMPLE;

    @Value("${businesscenter.url_user:/prod-api/hr/personnal/getPersonInfoById}")
    private String URL_PERSONNAL_INFO;

    @Value("${businesscenter.url_user:/prod-api/hr/personnal/getEncryptPersonInfoList}")
    private String URL_PERSONNAL_INFO_LIST;

    private static final  String desensitizePublicKeyStr = "04d89d95d0129f8b22a27979fd2c5d1fad305e1ec9ebbd2f6ff26f5bfdf00e5493574e8b93a2a16d4f924efd5133b6e7474aa694ec6314e39c34ff89c22d7b3768";

    private static final String desensitizePrivateKeyStr = "5174bc2473f86d5cda2ddad6a2d9b705848dd65b6fa13ff0923b1d2b55f09d30";

    private static final SM2 sm2Desensitize = SmUtil.sm2(desensitizePrivateKeyStr,desensitizePublicKeyStr);

    public static String desensitizeEncrypt(String content) {
        return sm2Desensitize.encryptBase64(content, KeyType.PublicKey);
    }

    public static String desensitizeDecrypt(String content) {
        return sm2Desensitize.decryptStr(content, KeyType.PrivateKey);
    }


//    @Value("${prod-api.system.role.get-role-users}")
//    private String URL_ROLE_USERS_LIST;


    /**
     * 获取业中部门列表
     *
     * @param tenantId 租户编号
     * @param type     区分是否用户管理的部门列表查询
     * @param token    当前登录用户的token
     * @return 部门列表
     */
    @Override
    public List<DeptSimpleRespVO> getDept(Long tenantId, Integer type, String token) throws URISyntaxException {
        return getDept(tenantId, type, token, null);
    }


    /**
     * 获取业中部门列表
     *
     * @param tenantId            租户编号
     * @param type                区分是否用户管理的部门列表查询
     * @param token               当前登录用户的token
     * @param oAuth2AccessTokenDO 登录用户信息
     * @return 部门列表
     */


    private List<DeptSimpleRespVO> getDept(Long tenantId, Integer type, String token, OAuth2AccessTokenDO oAuth2AccessTokenDO) throws URISyntaxException {
        String key = "getDept_" + tenantId + "_" + type;
        String cacheDept = businessCenterRedisDAO.get(key);
        try {
            if (StringUtils.isNotBlank(cacheDept)) {
                log.info("获取params='{}'缓存的业中部门成功！{}", key, cacheDept);
                return JSON.parseArray(cacheDept, DeptSimpleRespVO.class);
            }
        } catch (Exception e) {
            log.info("获取params='{}'缓存的业中部门失败！原因{}", key, e.getMessage());
        }
        log.info("未获取params='{}'业中部门缓存，开始请求业务中心！", key);

        if (Objects.isNull(oAuth2AccessTokenDO)) {
            oAuth2AccessTokenDO = oauth2AccessTokenRedisDAO.get(token);
        }

        String oldToken = oAuth2AccessTokenDO.getOldToken();

        // 构建请求参数
        Map<String, Object> params = new HashMap<>();

        if (tenantId != null) {
            params.put("tenantId", tenantId.toString());
        }

        if (type != null) {
            params.put("type", type.toString());
        }

        // 构建请求
        HttpRequest request = HttpUtil.createGet(MID_BASE_URI + URL_DEPT).timeout(10000);
        request.form(params);

        if (token != null) {
            request.header("Authorization", "Bearer " + oldToken);
        }

        request.contentType("application/json");

        // 执行请求
        HttpResponse response = request.execute();


        List<DeptSimpleRespVO> deptSimpleRespVOS = new ArrayList<>();
        try {
            CommonResult<List<DeptSimpleRespVO>> deptListResp = JSON.parseObject(response.body(), new TypeReference<CommonResult<List<DeptSimpleRespVO>>>() {
            });
            deptSimpleRespVOS = deptListResp.getCheckedData();
            log.info("请求业中获取params='{}'部门成功！\n{}", key, deptSimpleRespVOS);
            businessCenterRedisDAO.set(key, JSON.toJSONString(deptSimpleRespVOS));
        } catch (Exception e) {
            log.error("请求业中获取params='{}'部门失败：{}", key, e.getMessage());
        }
        return deptSimpleRespVOS;
    }


    /**
     * 获取业中某个部门所有子子部门列表
     *
     * @param tenantId 租户编号
     * @param type     区分是否用户管理的部门列表查询
     * @param deptId   部门编号
     * @param token    当前业中登录用户的token
     * @return 部门列表
     */
    @Override
    public List<DeptSimpleRespVO> getDeptAllChildren(Long tenantId, Integer type,
                                                     Long deptId, String token) throws URISyntaxException {
        return getDeptAllChildren(tenantId, type, deptId, token, null);
    }

    /**
     * 获取业中某个部门所有子子部门列表
     *
     * @param tenantId            租户编号
     * @param type                区分是否用户管理的部门列表查询
     * @param deptId              部门编号
     * @param token               当前业中登录用户的token
     * @param oAuth2AccessTokenDO 登录用户信息
     * @return 部门列表
     */
    private List<DeptSimpleRespVO> getDeptAllChildren(Long tenantId, Integer type,
                                                      Long deptId, String token, OAuth2AccessTokenDO oAuth2AccessTokenDO) throws URISyntaxException {
        // 获取所有部门
        List<DeptSimpleRespVO> allDeptList = getDept(tenantId, type, token, oAuth2AccessTokenDO);
        return getAllChildrenDeptList(Collections.singletonList(deptId), allDeptList);
    }

    private static List<DeptSimpleRespVO> getAllChildrenDeptList(List<Long> deptIdList, List<DeptSimpleRespVO> allDeptList) {
        Set<Long> existsIds = new HashSet<>();
        List<DeptSimpleRespVO> childrenDeptList = new ArrayList<>();
        deptIdList.forEach(deptId -> {
            List<DeptSimpleRespVO> nextLevelDeptList = allDeptList.stream().filter(deptSimpleRespVO ->
                    deptId.equals(deptSimpleRespVO.getParentId())).collect(Collectors.toList());
            nextLevelDeptList.forEach(vo -> {
                if (!existsIds.contains(vo.getId())) {
                    childrenDeptList.add(vo);
                    existsIds.add(vo.getId());
                }
            });
            while (!nextLevelDeptList.isEmpty()) {
                List<DeptSimpleRespVO> tempNextLevelDeptList = new ArrayList<>();
                nextLevelDeptList.forEach(deptSimpleRespVO -> tempNextLevelDeptList.addAll(allDeptList.stream()
                        .filter(deptSimpleRespVO1 -> deptSimpleRespVO1.getParentId().equals(deptSimpleRespVO.getId()))
                        .collect(Collectors.toList())));
                nextLevelDeptList = tempNextLevelDeptList;
                nextLevelDeptList.forEach(vo -> {
                    if (!existsIds.contains(vo.getId())) {
                        childrenDeptList.add(vo);
                        existsIds.add(vo.getId());
                    }
                });
            }
        });


        log.info("'{}'所有子部门：{}", deptIdList, childrenDeptList);
        return childrenDeptList;
    }

    /**
     * 获取业中用户列表
     *
     * @param param 参数
     * @param token 当前业中登录用户的token
     * @return 用户列表
     */
    @Override
    public List<BusinessCenterUserSimpleRespVO> getUser(Map<String, String> param, String token) throws URISyntaxException {
        OAuth2AccessTokenDO oAuth2AccessTokenDO = oauth2AccessTokenRedisDAO.get(token);

        // 设置完整的url
        URIBuilder uriBuilder = new URIBuilder(MID_BASE_URI + URL_USER);
        // 添加参数
        if (Objects.nonNull(param)) {
            // 添加参数
            for (Map.Entry<String, String> entry : param.entrySet()) {
                uriBuilder.setParameter(entry.getKey(), entry.getValue());
            }
        }

        HttpRequest httpRequest = HttpRequest.get(uriBuilder.build().toString());
        if (token != null) {
            httpRequest.header("Authorization", "Bearer " + oAuth2AccessTokenDO.getOldToken());
        }
        httpRequest.contentType("application/json");
        HttpResponse response = httpRequest.execute();
        List<BusinessCenterUserSimpleRespVO> userSimpleRespVOS = new ArrayList<>();
        try {
            CommonResult<List<BusinessCenterUserSimpleRespVO>> deptListResp = JSON.parseObject(response.body(), new TypeReference<CommonResult<List<BusinessCenterUserSimpleRespVO>>>() {
            });
            userSimpleRespVOS = deptListResp.getCheckedData();
        } catch (Exception e) {
            log.error("获取业中部门失败：" + e.getMessage());
        }
        return userSimpleRespVOS;
    }

    /**
     * 获取业中用户详情
     *
     * @param id 用户id
     * @return 业中用户详情
     */
    @Override
    public UserRespVO getUserInfoByUserId(Long id) throws URISyntaxException {
        AuthLoginRespVO authLoginRespVO = yezhongService.loginUserAdmin(true);
        return getUserInfoByUserId(id, authLoginRespVO.getAccessToken());
    }

    /**
     * 获取业中用户详情
     *
     * @param id    用户id
     * @param token 当前业中登录用户的token
     * @return 业中用户详情
     */
    public UserRespVO getUserInfoByUserId(Long id, String token) throws URISyntaxException {
        String key = "getUserInfoByUserId_" + id;
        String userInfo = businessCenterRedisDAO.get(key);
        try {
            if (StringUtils.isNotBlank(userInfo)) {
                log.info("从缓存中获取用户userId='{}'信息：{}", id, userInfo);
                return JSON.parseObject(userInfo, UserRespVO.class);
            }
        } catch (Exception e) {
            log.error("从缓存中获取用户userId='{}'信息失败：{}", id, e.getMessage());
        }
        log.info("未成功从缓存中获取用户userId='{}'信息，开始请求业中获取", id);
        // 设置完整的url
        URIBuilder uriBuilder = new URIBuilder(MID_BASE_URI + URL_USER_INFO);
        // 添加参数
        uriBuilder.setParameter("id", String.valueOf(id));
        HttpRequest httpRequest = HttpRequest.get(uriBuilder.build().toString()).timeout(10000);
        httpRequest.header("Authorization", "Bearer " + token);
        httpRequest.contentType("application/json");
        HttpResponse response = httpRequest.execute();
        UserRespVO userRespVO = new UserRespVO();
        try {
            CommonResult<UserRespVO> userRespVOCommonResult = JSON.parseObject(response.body(), new TypeReference<CommonResult<UserRespVO>>() {
            });
            userRespVO = userRespVOCommonResult.getCheckedData();
            businessCenterRedisDAO.set(key, JSON.toJSONString(userRespVO));
        } catch (Exception e) {
            log.error("根据userId = '{}'请求业中获取用户详情失败：" + e.getMessage(), id);
        }
        log.info("根据userId = '{}'请求业中获取用户详情：{}", id, userRespVO);
        return userRespVO;
    }

    /**
     * 获取业中用户详情
     *
     * @param token 当前登录用户的token
     * @return 业中用户详情
     */
    @Override
    public OAuth2UserInfosRespDTO getUserInfoByToken(String token) throws URISyntaxException {
        return getUserInfoByToken(token, null);
    }

    /**
     * 获取业中用户详情
     *
     * @param token               当前登录用户的token
     * @param oAuth2AccessTokenDO 登录用户信息
     * @return 业中用户详情
     */
    public OAuth2UserInfosRespDTO getUserInfoByToken(String token, OAuth2AccessTokenDO oAuth2AccessTokenDO) throws URISyntaxException {
        if (Objects.isNull(oAuth2AccessTokenDO)) {
            oAuth2AccessTokenDO = oauth2AccessTokenRedisDAO.get(token);
        }

        // 设置完整的url
        URIBuilder uriBuilder = new URIBuilder(MID_BASE_URI + URL_USER_OAUTH2_INFO);
        HttpRequest httpRequest = HttpRequest.get(uriBuilder.build().toString()).timeout(10000);
        if (token != null) {
            httpRequest.header("Authorization", "Bearer " + oAuth2AccessTokenDO.getOldToken());
        }
        httpRequest.contentType("application/json");
        HttpResponse response = httpRequest.execute();
        OAuth2UserInfosRespDTO userRespVO = new OAuth2UserInfosRespDTO();
        try {
            CommonResult<OAuth2UserInfosRespDTO> userRespVOCommonResult = JSON.parseObject(response.body(), new TypeReference<CommonResult<OAuth2UserInfosRespDTO>>() {
            });
            userRespVO = userRespVOCommonResult.getCheckedData();
        } catch (Exception e) {
            log.error("根据token = '{}' 获取业中用户详情失败：" + e.getMessage(), token);
        }
        log.info("根据token = '{}'获取业中用户详情：{}", token, userRespVO);
        return userRespVO;
    }

    /**
     * 获取某个用户在业中的部门权限列表
     *
     * @param tenantId 租户编号
     * @param type     区分是否用户管理的部门列表查询
     * @param token    当前登录用户的token
     * @return 部门列表
     */
    @Override
    public List<Long> getUserDeptPermissionByToken(Long tenantId, Integer type, String token) throws URISyntaxException {
        OAuth2AccessTokenDO oAuth2AccessTokenDO = oauth2AccessTokenRedisDAO.get(token);
        OAuth2UserInfosRespDTO userInfo = getUserInfoByToken(token, oAuth2AccessTokenDO);
        List<Long> userDeptIds = Collections.singletonList(userInfo.getDeptId());
        List<DeptSimpleRespVO> allDeptList = getDept(tenantId, type, token, oAuth2AccessTokenDO);
        List<DeptSimpleRespVO> allChildrenDeptList = getAllChildrenDeptList(userDeptIds, allDeptList);
        log.info("用户token='{}' 所有子部门列表：{}", token, allChildrenDeptList);
        List<Long> allChildrenDeptIdList = allChildrenDeptList.stream().map(DeptSimpleRespVO::getId).collect(Collectors.toList());
        userDeptIds.forEach(deptId -> {
            if (!allChildrenDeptIdList.contains(deptId)) {
                allChildrenDeptIdList.add(deptId);
            }
        });
        return allChildrenDeptIdList;
    }


    /**
     * 获取某个用户在业中的部门权限列表
     *
     * @param tenantId 租户编号
     * @param type     区分是否用户管理的部门列表查询
     * @param userId   用户ID
     * @param token    当前登录用户的token
     * @return 部门列表
     */
    @Override
    public List<Long> getUserDeptPermissionByUserId(Long tenantId, Integer type, Long userId, String token) throws URISyntaxException {
        OAuth2AccessTokenDO oAuth2AccessTokenDO = oauth2AccessTokenRedisDAO.get(token);
        AdminUserDO adminUserDO = adminUserMapper.selectById(userId);
        UserRespVO userInfo = getUserInfoByUserId(Long.valueOf(adminUserDO.getSystemId()));
        List<Long> userDeptIds = userInfo.getDeptIds();
        log.info("用户'{}' 所属部门列表：{} 详情：{}", userId, userDeptIds, userInfo);
        List<DeptSimpleRespVO> allDeptList = getDept(tenantId, type, token, oAuth2AccessTokenDO);
        List<DeptSimpleRespVO> allChildrenDeptList = getAllChildrenDeptList(userDeptIds, allDeptList);
        List<Long> allChildrenDeptIdList = allChildrenDeptList.stream().map(DeptSimpleRespVO::getId).collect(Collectors.toList());
        userDeptIds.forEach(deptId -> {
            if (!allChildrenDeptIdList.contains(deptId)) {
                allChildrenDeptIdList.add(deptId);
            }
        });
        return allChildrenDeptIdList;
    }


    @Override
    public String getOldToken(String tokenOfEdu) {

        OAuth2AccessTokenDO oAuth2AccessTokenDO = oauth2AccessTokenRedisDAO.get(tokenOfEdu);
        log.info("业中的token：" + oAuth2AccessTokenDO.getOldToken());
        return oAuth2AccessTokenDO.getOldToken();
    }

    @Override
    public PageResult<BusinessCenterPersonnalSimpleRespVO> getPersonnalSimple(Long tenantId, Integer deptId, String username, String token, Integer pageNo, Integer pageSize) throws URISyntaxException {
        OAuth2AccessTokenDO oAuth2AccessTokenDO = oauth2AccessTokenRedisDAO.get(token);

        // 设置完整的url
        URIBuilder uriBuilder = new URIBuilder(MID_BASE_URI + URL_PERSONNAL_SIMPLE);
        // 添加参数
        if (tenantId != null) {
            uriBuilder.setParameter("tenantId", tenantId.toString());
        }
        if (deptId != null) {
            uriBuilder.setParameter("deptId", deptId.toString());
        }
        if (username != null) {
            uriBuilder.setParameter("username", username.toString());
        }
        if (pageNo != null) {
            uriBuilder.setParameter("pageNo", pageNo.toString());
        }
        if (pageSize != null) {
            uriBuilder.setParameter("pageSize", pageSize.toString());
        }

        HttpRequest httpRequest = HttpRequest.get(uriBuilder.build().toString());
        if (token != null) {
            httpRequest.header("Authorization", "Bearer " + oAuth2AccessTokenDO.getOldToken());
        }

        httpRequest.contentType("application/json");
        HttpResponse response = httpRequest.execute();
        PageResult<BusinessCenterPersonnalSimpleRespVO> personnalSimpleRespVOS = new PageResult<>();
        try {
            CommonResult<PageResult<BusinessCenterPersonnalSimpleRespVO>> personnalListResp = JSON.parseObject(response.body(), new TypeReference<CommonResult<PageResult<BusinessCenterPersonnalSimpleRespVO>>>() {
            });
            personnalSimpleRespVOS = personnalListResp.getCheckedData();
        } catch (Exception e) {
            log.error("获取业中人员失败：" + e.getMessage());
        }
        return personnalSimpleRespVOS;
    }

    @Override
    public List<BusinessCenterPersonnalRespVO> getPersonnalList(Long tenantId, String token) throws URISyntaxException {

        // 设置完整的url
        URIBuilder uriBuilder = new URIBuilder(MID_BASE_URI + URL_PERSONNAL_INFO_LIST);
        // 添加参数
        if (tenantId != null) {
            uriBuilder.setParameter("tenantId", tenantId.toString());
        }

        HttpRequest httpRequest = HttpRequest.post(uriBuilder.build().toString()).timeout(10000);


        if (token != null) {
            httpRequest.header("Authorization", "Bearer " + token);
        }

        httpRequest.contentType("application/json");
        HttpResponse response = httpRequest.execute();
        List<BusinessCenterPersonnalRespVO> personnalRespVOList = new ArrayList<>();
        try {
            CommonResult<String> personnalRespList = JSON.parseObject(response.body(), new TypeReference<CommonResult<String>>() {
            });
            String encryptedData = personnalRespList.getCheckedData();
            String decryptedData = AesEncryptUtil.aesDecrypt(encryptedData);
            personnalRespVOList = JSON.parseObject(decryptedData, new TypeReference<List<BusinessCenterPersonnalRespVO>>() {
            });
        } catch (Exception e) {
            log.error("获取业中人员失败：" + e.getMessage());
        }
        return personnalRespVOList;
    }

    @Override
    public BusinessCenterPersonnalRespVO getPersonnalById(Long id, String token) throws URISyntaxException {
        OAuth2AccessTokenDO oAuth2AccessTokenDO = oauth2AccessTokenRedisDAO.get(token);

        // 设置完整的url
        URIBuilder uriBuilder = new URIBuilder(MID_BASE_URI + URL_PERSONNAL_INFO);
        // 添加参数
        if (id != null) {
            uriBuilder.setParameter("id", id.toString());
        }

        HttpRequest httpRequest = HttpRequest.get(uriBuilder.build().toString());
        if (token != null) {
            httpRequest.header("Authorization", "Bearer " + oAuth2AccessTokenDO.getOldToken());
        }

        httpRequest.contentType("application/json");
        HttpResponse response = httpRequest.execute();
        BusinessCenterPersonnalRespVO personnalRespVO = new BusinessCenterPersonnalRespVO();
        try {
            CommonResult<BusinessCenterPersonnalRespVO> personnalResp = JSON.parseObject(response.body(), new TypeReference<CommonResult<BusinessCenterPersonnalRespVO>>() {
            });
            personnalRespVO = personnalResp.getCheckedData();
        } catch (Exception e) {
            log.error("获取业中人员失败：" + e.getMessage());
        }

        String contactInformation = personnalRespVO.getContactInformation();
        if (contactInformation != null && contactInformation != "") {
            String decryptedContactInformation = desensitizeEncrypt((contactInformation));
            personnalRespVO.setContactInformation(decryptedContactInformation);
        }
        String idNumber = personnalRespVO.getIdNumber();
        if (idNumber != null && idNumber != "") {
            String decryptedIdNumber = desensitizeEncrypt((idNumber));
            personnalRespVO.setIdNumber(decryptedIdNumber);
        }
        String professionalTitle = personnalRespVO.getProfessionalTitle();
        if (professionalTitle != null && professionalTitle != "") {
            String decryptedProfessionalTitle = desensitizeEncrypt((professionalTitle));
            personnalRespVO.setProfessionalTitle(decryptedProfessionalTitle);
        }

        return personnalRespVO;
    }

    @Override
    public List<Long> getAdminInfo(Long roleId,String token) throws URISyntaxException{
        OAuth2AccessTokenDO oAuth2AccessTokenDO = oauth2AccessTokenRedisDAO.get(token);

        // 设置完整的url
        URIBuilder uriBuilder = new URIBuilder(MID_BASE_URI + "/prod-api/system/role/get-role-users");
        // 添加参数
        if (roleId != null) {
            uriBuilder.setParameter("roleId", roleId.toString());
            uriBuilder.setParameter("pageNo", "1");
            uriBuilder.setParameter("pageSize", "999");
        }

        HttpRequest httpRequest = HttpRequest.get(uriBuilder.build().toString());
        if (token != null) {
            httpRequest.header("Authorization", "Bearer " + oAuth2AccessTokenDO.getOldToken());
        }

        httpRequest.contentType("application/json");
        HttpResponse response = httpRequest.execute();
        RoleUsersRespVO roleUsersRespVO = new RoleUsersRespVO();
        try {
            CommonResult<RoleUsersRespVO> roleUsersResp = JSON.parseObject(response.body(), new TypeReference<CommonResult<RoleUsersRespVO>>() {
            });
            roleUsersRespVO = roleUsersResp.getCheckedData();
        } catch (Exception e) {
            log.error("获取业中人员失败：" + e.getMessage());
        }
        return roleUsersRespVO.getUsers().getList().stream().map(UserSimpleRespVO::getId).collect(Collectors.toList());
    }
}
