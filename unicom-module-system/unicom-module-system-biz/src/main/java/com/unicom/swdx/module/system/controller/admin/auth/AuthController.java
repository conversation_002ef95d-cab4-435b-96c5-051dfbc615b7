package com.unicom.swdx.module.system.controller.admin.auth;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.security.config.SecurityProperties;
import com.unicom.swdx.module.hr.api.PersonnalApi;
import com.unicom.swdx.module.hr.api.dto.PersonnalApiDO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.*;
import com.unicom.swdx.module.system.controller.admin.message.vo.template.MessageAuthorityUpdateReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserUpdatePasswordRespVO;
import com.unicom.swdx.module.system.controller.xcx.auth.vo.WxXcxAuthLoginRespVO;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.dataobject.user.OldPersonDO;
import com.unicom.swdx.module.system.dal.dataobject.user.TraineeUserDO;
import com.unicom.swdx.module.system.dal.dataobject.user.UserIdentityVO;
import com.unicom.swdx.module.system.dal.dataobject.yjs.YjsAddDO;
import com.unicom.swdx.module.system.dal.mysql.dangjian.DangjianMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.UserRoleMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.dal.mysql.yjs.YjsAddMapper;
import com.unicom.swdx.module.system.enums.logger.LoginLogTypeEnum;
import com.unicom.swdx.module.system.enums.sms.SmsMessageEnum;
import com.unicom.swdx.module.system.mq.producer.dept.DeptProducer;
import com.unicom.swdx.module.system.service.auth.AdminAuthService;
import com.unicom.swdx.module.system.service.sms.SmsSendService;
import com.unicom.swdx.module.system.service.social.SocialUserService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import com.unicom.swdx.module.system.service.user.OldPersonService;
import com.unicom.swdx.module.system.service.user.TraineeService;
import com.unicom.swdx.module.system.wx.qywx.QywxApiService;
import com.unicom.swdx.module.system.wx.xcx.WxXcxApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.error;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.OTHER;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.UPDATE;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.*;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;

@Api(tags = "管理后台 - 认证")
@RestController
@RequestMapping("/system/auth")
@Validated
@Slf4j
public class AuthController {

    @Resource
    private AdminAuthService authService;
    @Resource
    private AdminUserService userService;

    @Resource
    private SecurityProperties securityProperties;
    @Resource
    private SmsSendService smsSendService;
    @Resource
    private SocialUserService socialUserService;

    @Resource
    private WxXcxApiService wxXcxApiService;

    @Resource
    private QywxApiService qywxApiService;


    @Resource
    private DeptProducer deptProducer;

    @Resource
    private DangjianMapper dangjianMapper;

    @Resource
    private UserRoleMapper userRoleMapper;

    private static final  String DESENSITIZE_PUBLIC_KEY_STR = "04d89d95d0129f8b22a27979fd2c5d1fad305e1ec9ebbd2f6ff26f5bfdf00e5493574e8b93a2a16d4f924efd5133b6e7474aa694ec6314e39c34ff89c22d7b3768";
    private static final String DESENSITIZE_PRIVATE_KEY_STR = "5174bc2473f86d5cda2ddad6a2d9b705848dd65b6fa13ff0923b1d2b55f09d30";
    private static final SM2 SM2_DESENSITIZE = SmUtil.sm2(DESENSITIZE_PRIVATE_KEY_STR,DESENSITIZE_PUBLIC_KEY_STR);
    private static String desensitizeEncrypt(String content) {
        return SM2_DESENSITIZE.encryptBase64(content, KeyType.PublicKey);
    }
    private static String desensitizeDecrypt(String content) {
        return SM2_DESENSITIZE.decryptStr(content, KeyType.PrivateKey);
    }

    @PostMapping("/kafkasend")
    @PermitAll
    @ApiOperation("kafka测试")
    public CommonResult<String> kafkasend() {

        deptProducer.sendRefreshMessage();

        return success("1");
    }


    @PostMapping("/login")
    @PermitAll
    @ApiOperation("使用账号密码登录")
    public CommonResult<AuthLoginRespVO> login(@RequestBody @Valid AuthLoginReqVO reqVO) {
        return success(authService.login(reqVO));
    }

    @PostMapping("/loginForUnit")
    @PermitAll
    @ApiOperation("调训系统单位管理员使用账号密码登录")
    public CommonResult<AuthLoginRespVO> loginForUnit(@RequestBody @Valid AuthLoginReqVO reqVO) {
        return success(authService.loginForUnit(reqVO));
    }

    @PostMapping("/singleSignOn")
    @PermitAll
    @ApiOperation("单点登录")
    public CommonResult<AuthLoginRespVO> singleSignOn(@RequestBody @Valid SingleSignOnReqVO reqVO) {
        return success(authService.singleSignOn(reqVO));
    }

    /**
     * 根据手机号登录，获取身份token
     * @param reqVO 请求参数包含手机号和时间戳
     * @return 身份token
     */
    @PostMapping("/loginByMobile")
    @PermitAll
    @ApiOperation("根据手机号登录，获取身份token")
    public CommonResult<String> loginByMobile(@RequestBody @Valid AuthByMobileReqVO reqVO) {
//        //有风险安全风险的接口暂时关闭，后续持续跟进衡阳数据接入需求
//        return null;
        reqVO.setMobile(desensitizeDecrypt(reqVO.getMobile()));
        return success(authService.loginByMobile(reqVO));
    }

    @GetMapping("/logindefualt")
    @ApiOperation("切换到默认账号并返回token")
    public CommonResult<AuthLoginRespVO> login( Long userid) {
        return success( userService.updateUserDefault(userid));
    }


    @PostMapping("/logout")
    @PermitAll
    @ApiOperation("登出系统")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = obtainAuthorization(request, securityProperties.getTokenHeader());
        if (StrUtil.isBlank(token)) {
            throw exception(AUTH_TOKEN_ERROR);
        }
        authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        return success(true);
    }

    @PostMapping("/refresh-token")
    @PermitAll
    @ApiOperation("刷新令牌")
    @ApiImplicitParam(name = "refreshToken", value = "刷新令牌", required = true, dataTypeClass = String.class)
    public CommonResult<AuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return success(authService.refreshToken(refreshToken));
    }

    @GetMapping("/get-permission-info")
    @ApiOperation("获取登录用户的权限信息")
    public CommonResult<AuthPermissionInfoRespVO> getPermissionInfo(@RequestParam(required = false) Long clientId) {
        return success(authService.getPermissionInfo(clientId));
    }

    @GetMapping("/list-menus-client")
    @ApiOperation("根据应用id获得登录用户的菜单列表")
    public CommonResult<List<AuthMenuRespVO>> getMenusByClient(@RequestParam("id") Long clientId) {
        return success(authService.getMenusByClient(clientId));
    }

    @PostMapping("/send-sms-code")
    @PermitAll
    @ApiOperation(value = "发送手机验证码")
    @OperateLog(type = OTHER)
    public CommonResult<Boolean> sendLoginSmsCode(@RequestBody @Valid AuthSmsSendReqVO reqVO) {
        smsSendService.getVerification(reqVO.getMobile(), SmsMessageEnum.get(reqVO.getFlag()));
        return success(true);
    }

    @PostMapping("/mobile-login")
    @PermitAll
    @ApiOperation("使用手机验证码登录")
    public CommonResult<AuthLoginRespVO> codeLogin(@RequestBody @Valid CodeAuthLoginReqVO reqVO) {
        return success(authService.codeLogin(reqVO));
    }

    @PostMapping("/mobile-update")
    @PermitAll
    @ApiOperation("使用手机验证码修改密码")
    public CommonResult<Boolean> codeUpdate(@RequestBody @Valid CodeAuthUpdateReqVO reqVO) {
        authService.codeUpdate(reqVO);
        return success(true);
    }

    @GetMapping("/userid")
    @PermitAll
    @ApiOperation("获取用户id")
    public CommonResult<UserUpdatePasswordRespVO> forgetPassword(AuthForgetPasswordReqVO reqVO) {
        return success(userService.getUserIdResp(reqVO));
    }

    @PostMapping("/update-password")
    @ApiOperation("修改用户密码")
    @PermitAll
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateUserPassword(@Valid @RequestBody AuthUpdatePasswordReqVO reqVO) {
        userService.updateAuthUserPassword(reqVO);
        return success(true);
    }


    @GetMapping("/last-login")
    @ApiOperation("最后登录时间")
    public CommonResult<Boolean> lastLogin() {
        return success(userService.lastLogin());
    }

    // ========== 小程序登录相关 ==========

    /**
     * 获取小程序二维码
     * @return 小程序二维码
     */
    @GetMapping("/getWxXcxQRCode")
    @PermitAll
    @ApiOperation("获取微信小程序二维码")
    public CommonResult<WxXcxQRCodeRespVO> getWxXcxQRCode() {
        return success(wxXcxApiService.getQRCodeBase64());
    }

    /**
     * 根据小程序的uniqueCode获取登录token
     * @return 登录信息
     */
    @PostMapping("/wxXcxLogin")
    @PermitAll
    @ApiOperation("根据小程序的uniqueCode获取登录token")
    public CommonResult<AuthLoginRespVO> wxXcxLogin(@RequestBody WxXcxLoginReqVO loginReqVO) {
        AuthLoginRespVO result = authService.wxXcxLogin(loginReqVO);
        if (Objects.isNull(result)) {
            return error(LOGIN_WAITE);
        }
        return success(result);
    }

    //获取小程序url link
    @GetMapping("/url-link")
    @PermitAll
    @ApiOperation("获取小程序url link")
    public CommonResult<String> urlLink() {
        return success(wxXcxApiService.getUrlLink());
    }

    // ========== 企业微信登录相关 ==========

    /**
     * 获取企业微信二维码
     * @return 小程序二维码
     */
    @GetMapping("/getQywxQRCode")
    @PermitAll
    @ApiOperation("获取企微信二维码")
    public CommonResult<QywxQRCodeRespVO> getQywxQRCode() {
        return success(qywxApiService.getQRCode());
    }

    /**
     * 根据企业微信的uniqueCode获取登录token
     * @return 登录信息
     */
    @PostMapping("/qywxLogin")
    @PermitAll
    @ApiOperation("根据企业微信的uniqueCode获取登录token")
    public CommonResult<AuthLoginRespVO> qywxLogin(@RequestBody WxXcxLoginReqVO loginReqVO) {
        AuthLoginRespVO result = authService.wxXcxLogin(loginReqVO);
        if (Objects.isNull(result)) {
            return error(LOGIN_WAITE);
        }
        return success(result);
    }

    /**
     * 企业微信code登录
     * @param reqVO
     * @return
     */
    @PostMapping("/qywx-code-login")
    @PermitAll
    @ApiOperation("企业微信登录")
    public CommonResult<WxXcxAuthLoginRespVO> qywxCodeLogin(@RequestBody @Valid QywxAuthLoginReqVO reqVO) {
        log.info(reqVO.toString());
        WxXcxAuthLoginRespVO respVO = authService.qywxCodeLogin(reqVO);
        if (Objects.isNull(respVO)) {
            return error(OPENID_NOT_BIND);
        }
        return success(respVO);
    }

    // ========== 社交登录相关 ==========

    @GetMapping("/social-auth-redirect")
    @PermitAll
    @ApiOperation("社交授权的跳转")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "社交类型", required = true, dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "redirectUri", value = "回调路径", dataTypeClass = String.class)
    })
    public CommonResult<String> socialLogin(@RequestParam("type") Integer type,
                                            @RequestParam("redirectUri") String redirectUri) {
        return CommonResult.success(socialUserService.getAuthorizeUrl(type, redirectUri));
    }

    @PostMapping("/social-login")
    @PermitAll
    @ApiOperation(value = "社交快捷登录，使用 code 授权码", notes = "适合未登录的用户，但是社交账号已绑定用户")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public CommonResult<AuthLoginRespVO> socialQuickLogin(@RequestBody @Valid AuthSocialLoginReqVO reqVO) {
        return success(authService.socialLogin(reqVO));
    }


    @Resource
    private TraineeService traineeService;

    @Resource
    PersonnalApi personnalApi;

    @Resource
    OldPersonService oldPersonService;


    @Resource
    YjsAddMapper yjsAddMapper;



    @PostMapping("/getAllIdentity")
    @ApiOperation(value = "获取用户身份", notes = "获取用户身份")
    public CommonResult< Set<UserIdentityVO>> getAllIdentity( @RequestParam(value = "userid", required = false)  Long myuserid ) {



        Long userid = myuserid ==null ?  getLoginUserId() :   myuserid ;

        AdminUserDO adminUserDO =  userService.getUser(userid);

        PersonnalApiDO personnalApiDO   =  personnalApi.getUser(userid);
        List<TraineeUserDO> traineeUserDO =  traineeService.getTraineeUsersByUserId(userid);

        List<OldPersonDO> oldPersonDO =  oldPersonService.getOldPersonsByUserId(userid);

        if(ObjectUtil.isEmpty(oldPersonDO)){

            oldPersonDO =  oldPersonService.getOldPersonsByMobile(adminUserDO.getMobile());

        }



        Set<UserIdentityVO> list = new HashSet<>();

        if (ObjectUtil.isNotEmpty(oldPersonDO)) {


            oldPersonDO.stream().filter(Objects::nonNull).forEach(oldPerson ->{

                UserIdentityVO identityVO = new UserIdentityVO();
                identityVO.setUserId(userid);
                identityVO.setName("教职工");

                if(oldPerson.getUniconId()==null){
                    identityVO.setCode("003" + oldPerson.getEmployeeId());
                }else{
                    identityVO.setCode(oldPerson.getUniconId());
                }




                if(ObjectUtil.equals(adminUserDO.getOthersystemid() , identityVO.getCode())  ){
                    identityVO.setUse(1);
                }

                list.add(identityVO);

            });

        }else{

            if (ObjectUtil.isNotEmpty(personnalApiDO)) {

                UserIdentityVO identityVO = new UserIdentityVO();
                identityVO.setUserId(userid);
                identityVO.setName("教职工");
                identityVO.setCode("003" + personnalApiDO.getUserId());

                if(ObjectUtil.equals(adminUserDO.getOthersystemid() , identityVO.getCode())  ){
                    identityVO.setUse(1);
                }

                list.add(identityVO);

            }


        }


        if (ObjectUtil.isNotEmpty(traineeUserDO)) {


            AtomicInteger temp = new AtomicInteger(0);
            traineeUserDO.stream().filter(Objects::nonNull).forEach(it->{

                UserIdentityVO identityVO = new UserIdentityVO();
                identityVO.setUserId(userid);
                if(StrUtil.isEmpty(it.getClassstr())){
                    identityVO.setName("学员" +  it.getTraineeId() );
                }else{
                    identityVO.setName("学员" +  it.getClassstr() );
                }

                identityVO.setCode( it.getTraineeId());

                if(ObjectUtil.equals(adminUserDO.getOthersystemid() , identityVO.getCode())  ){
                    identityVO.setUse(1);
                }


                list.add(identityVO);

            });



        }

        List<YjsAddDO> yjsAddDOList =  yjsAddMapper.selectList("phone" ,  adminUserDO.getMobile());

        if (CollectionUtil.isNotEmpty(yjsAddDOList)) {

            yjsAddDOList.stream().filter(Objects::nonNull).forEach(it->{


                UserIdentityVO identityVO = new UserIdentityVO();
                identityVO.setUserId(userid);
                identityVO.setName("研究生");
                identityVO.setCode( it.getOtherSysId());

                if(ObjectUtil.equals(adminUserDO.getOthersystemid() , identityVO.getCode())  ){
                    identityVO.setUse(1);
                }

                list.add(identityVO);

            });



        }






        if(CollectionUtil.isEmpty(list)){
            //啥身份都没有
            UserIdentityVO identityVO = new UserIdentityVO();
            identityVO.setUserId(userid);


            if(adminUserDO.getOthersystemid()!=null){
                identityVO.setCode(adminUserDO.getOthersystemid());
            }else{
                identityVO.setCode("003" + userid);
            }

            if(identityVO.getCode().startsWith("003")){
                identityVO.setName("教职工");
            }

            if(identityVO.getCode().startsWith("009")){
                identityVO.setName("研究生");
            }

            if(identityVO.getCode().startsWith("002")){
                identityVO.setName("学员");
            }

            identityVO.setUse(1);
            list.add(identityVO);
        }


        return success(list);
    }


    @Resource
    private AdminUserMapper userMapper;

    @PostMapping("/changeykt")
    public CommonResult<Boolean> changeykt(@RequestBody @Valid UserIdentityVO reqVO) {
        Long userid =  getLoginUserId();

        if(reqVO.getUserId().intValue() != userid.intValue()){

            return error(500, "不允许修改他人信息");

        }

        AdminUserDO adminUserDO = new AdminUserDO();
        adminUserDO.setId(userid);
        adminUserDO.setOthersystemid(reqVO.getCode());

        userMapper.updateById(adminUserDO);

        return success(true);
    }

    @GetMapping("/dangjian")
    @ApiOperation("同步党建角色")
    public List<String> dangjian() {
        //党校数据库
        List<String> mobile = dangjianMapper.selectUserMobile();
        //本地数据库
        List<String> localMoblie = userRoleMapper.selectAllList();
        List<String> diff = mobile.stream()
                .filter(m -> !localMoblie.contains(m))
                .collect(Collectors.toList());
        if(!diff.isEmpty()) {
            //154,155,124党务党员党建
            userRoleMapper.insertBatchMobiles(diff);
            //开始同步党务角色
            userRoleMapper.insertAllUser(154, mobile);
            //开始同步党员角色
            userRoleMapper.insertAllUser(155, mobile);
            //开始同步党建角色
            userRoleMapper.insertAllUser(124, mobile);
        }
        return null;
    }


}
