package com.unicom.swdx.module.system.controller.admin.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.apilog.core.filter.ApiAccessLogFilter;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.Aes.AesUtils;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.common.util.collection.MapUtils;
import com.unicom.swdx.framework.datapermission.core.annotation.DataPermission;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.hr.api.PersonnalApi;
import com.unicom.swdx.module.hr.api.dto.PersonnalUpdateReqDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaMessageDTO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthForgetPasswordReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.*;
import com.unicom.swdx.module.system.convert.user.UserConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.PostDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.dept.DeptMapper;
import com.unicom.swdx.module.system.job.yezhong.YeZhongJob;
import com.unicom.swdx.module.system.service.dept.DeptService;
import com.unicom.swdx.module.system.service.dept.PostService;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import com.unicom.swdx.module.system.util.ExcelValidator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertList;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertSet;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.system.enums.user.UserCategoryEnum.MIDDLE_PLAT;

@Api(tags = "管理后台 - 用户")
@RestController
@RequestMapping("/system/user")
@Validated
public class UserController {

    @Resource
    private AdminUserService userService;
    @Resource
    private DeptService deptService;
    @Resource
    private TenantService tenantService;
    @Resource
    private PostService postService;
    @Resource
    private DeptMapper deptMapper;

    @Resource
    private PersonnalApi personnalApi;

    @PostMapping("create")
    @ApiOperation("新增用户")
    @PreAuthorize("@ss.hasPermission('system:user:create')")
    @OperateLog(type = CREATE)
    public CommonResult<Long> createUser(@Valid @RequestBody UserCreateReqVO reqVO) {
        Long id = userService.createUser(reqVO,null, MIDDLE_PLAT.getCategory());
        return success(id);
    }

    @PostMapping("createApply")
    @ApiOperation("新增应聘用户")
    @PermitAll
    @OperateLog(type = CREATE)
    public CommonResult<Long> createApplyUser(@Valid @RequestBody ApplyUserCreateReqVO reqVO) {
        Long id = userService.createApplyUser(reqVO,null);
        return success(id);
    }

    @PostMapping("update")
    @ApiOperation("修改用户")
    @PreAuthorize("@ss.hasPermission('system:user:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateUser(@Valid @RequestBody UserUpdateReqVO reqVO) {
        userService.updateUser(reqVO);
        PersonnalUpdateReqDTO personnalUpdateReqDTO = new PersonnalUpdateReqDTO();
        personnalUpdateReqDTO.setMobile(reqVO.getMobile());
        personnalUpdateReqDTO.setUserId(reqVO.getId());
        personnalUpdateReqDTO.setEmail(reqVO.getEmail());
        personnalUpdateReqDTO.setGender(reqVO.getSex());
        personnalUpdateReqDTO.setName(reqVO.getNickname());
        personnalApi.updateUser(personnalUpdateReqDTO);
        return success(true);
    }

    @PostMapping("delete")
    @ApiOperation("删除用户")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('system:user:delete')")
    @OperateLog(type = DELETE)
    public CommonResult<Boolean> deleteUser(@RequestParam("id") Long id) {
        userService.deleteUser(id);
        personnalApi.deleteUser(id);
        return success(true);
    }

    @PostMapping("/update-password")
    @ApiOperation("重置用户密码")
    @PreAuthorize("@ss.hasPermission('system:user:update-password')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateUserPassword(@Valid @RequestBody UserUpdatePasswordReqVO reqVO) {
        userService.updateUserPassword(reqVO.getId(), reqVO.getPassword());
        return success(true);
    }

    @PostMapping("/reset-password")
    @ApiOperation("重置密码")
    @PreAuthorize("@ss.hasPermission('system:user:update-password')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> resetUserPassword(@RequestParam("userId") Long userId) {
        userService.resetUserPassword(userId);
        return success(true);
    }

    @GetMapping("/getUserIdByPhone")
    @ApiOperation("通过手机号获取用户id")
    @PermitAll
    @OperateLog(type = UPDATE)
    public CommonResult<UserUpdatePasswordRespVO> getUserIdByPhone(String mobile,String verification) {
        AuthForgetPasswordReqVO reqVO = new AuthForgetPasswordReqVO();
        reqVO.setMobile(mobile);
        reqVO.setVerification(verification);
        UserUpdatePasswordRespVO result = userService.getUserIdResp(reqVO);
        return success(result);
    }

    @PostMapping("/update-status")
    @ApiOperation("修改用户状态")
    @PreAuthorize("@ss.hasPermission('system:user:update')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> updateUserStatus(@Valid @RequestBody UserUpdateStatusReqVO reqVO) {
        userService.updateUserStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }
    @PostMapping("/update-appcid")
    @ApiOperation("修改用户APPCID")
    @PreAuthorize("@ss.hasPermission('system:user:update')")
    public CommonResult<Boolean> updateUserAppCid(@Valid @RequestBody UserUpdateAppcidReqVO reqVO) {
        userService.updateUserAppCid(reqVO.getId(), reqVO.getAppCid());
        return success(true);
    }


    @GetMapping("/page")
    @ApiOperation("获得用户分页列表")
    @DataPermission(enable = true)
    public CommonResult<PageResult<UserPageItemRespVO>> getUserPage(@Valid UserPageReqVO reqVO) {
        // 获得用户分页列表
        PageResult<AdminUserDO> pageResult = userService.getUserPage(reqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(new PageResult<>(pageResult.getTotal())); // 返回空
        }
        // 拼接结果返回
        List<UserPageItemRespVO> userList = new ArrayList<>(pageResult.getList().size());
        pageResult.getList().forEach(user -> {
            UserPageItemRespVO respVO = UserConvert.INSTANCE.convert(user);
            respVO.setMobile(respVO.getMobile().substring(0, 3) + "****" + respVO.getMobile().substring(7));
            respVO.setDept(UserConvert.INSTANCE.convert(deptService.getDept(user.getDeptId())));
            // 机构管理员设置标记字段
            if (tenantService.isTenantAdmin(user.getId())) {
                respVO.setIsAdmin(true);
            } else {
                respVO.setIsAdmin(false);
            }
            userList.add(respVO);
        });
        return success(new PageResult<>(userList, pageResult.getTotal()));
    }

    @GetMapping("/pageMsg")
    @ApiOperation("消息中心获得用户分页列表")
    @DataPermission(enable = true)
    public CommonResult<PageResult<UserPageItemRespVO>> getUserPageMsg(@Valid UserPageReqVO reqVO) {
        reqVO.setPageSize(100000);
        // 获得用户分页列表
        PageResult<AdminUserDO> pageResult = userService.getUserPage(reqVO);
        LambdaQueryWrapper<DeptDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeptDO::getTenantId,getTenantId());
        List<DeptDO> deptDOS= deptMapper.selectList(lambdaQueryWrapper);
        Map<Long, DeptDO> deptMap = deptDOS.stream()
                .collect(Collectors.toMap(DeptDO::getId, dept -> dept));
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(new PageResult<>(pageResult.getTotal())); // 返回空
        }
        // 拼接结果返回
        List<UserPageItemRespVO> userList = new ArrayList<>(pageResult.getList().size());
        pageResult.getList().forEach(user -> {
            UserPageItemRespVO respVO = UserConvert.INSTANCE.convert(user);
            if(respVO.getMobile()!=null && PhoneUtil.isMobile(respVO.getMobile())){
                respVO.setMobile(respVO.getMobile().substring(0, 3) + "****" + respVO.getMobile().substring(7));
            }
            respVO.setDept(UserConvert.INSTANCE.convert(deptMap.get(user.getDeptId())));
            // 机构管理员设置标记字段
            if (tenantService.isTenantAdmin(user.getId())) {
                respVO.setIsAdmin(true);
            } else {
                respVO.setIsAdmin(false);
            }
            userList.add(respVO);
        });
        return success(new PageResult<>(userList, pageResult.getTotal()));
    }

    @GetMapping("/pageMsg_children")
    @ApiOperation("消息中心获得本级和子级用户分页列表")
    @DataPermission(enable = true)
    public CommonResult<PageResult<UserPageItemRespVO>> getUserPageMsgChildren(@Valid UserPageReqVO reqVO) {
        reqVO.setPageSize(100000);
        // 获得用户分页列表
        PageResult<AdminUserDO> pageResult = userService.getUserPageManage(reqVO);
        LambdaQueryWrapper<DeptDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeptDO::getTenantId,getTenantId());
        List<DeptDO> deptDOS= deptMapper.selectList(lambdaQueryWrapper);
        Map<Long, DeptDO> deptMap = deptDOS.stream()
                .collect(Collectors.toMap(DeptDO::getId, dept -> dept));
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(new PageResult<>(pageResult.getTotal())); // 返回空
        }
        // 拼接结果返回
        List<UserPageItemRespVO> userList = new ArrayList<>(pageResult.getList().size());
        pageResult.getList().forEach(user -> {
            UserPageItemRespVO respVO = UserConvert.INSTANCE.convert(user);
            if(respVO.getMobile()!=null && PhoneUtil.isMobile(respVO.getMobile())){
                respVO.setMobile(respVO.getMobile().substring(0, 3) + "****" + respVO.getMobile().substring(7));
            }
            respVO.setDept(UserConvert.INSTANCE.convert(deptMap.get(user.getDeptId())));
            // 机构管理员设置标记字段
            if (tenantService.isTenantAdmin(user.getId())) {
                respVO.setIsAdmin(true);
            } else {
                respVO.setIsAdmin(false);
            }
            userList.add(respVO);
        });
        return success(new PageResult<>(userList, pageResult.getTotal()));
    }
    @GetMapping("/pageMsg_authority")
    @ApiOperation("消息中心获得权限")
    @DataPermission(enable = false)
    public CommonResult<PageResult<UserPageItemRespVO>> getUserPageMsgChildrenAuthority(@Valid UserPageReqVO reqVO) {
        reqVO.setPageSize(100000);
        // 获得用户分页列表
        PageResult<AdminUserDO> pageResult = userService.getUserPageManageAuthority(reqVO);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(new PageResult<>(pageResult.getTotal())); // 返回空
        }
        // 拼接结果返回
        List<UserPageItemRespVO> userList = new ArrayList<>(pageResult.getList().size());
        LambdaQueryWrapper<DeptDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeptDO::getTenantId,getTenantId());
        List<DeptDO> deptDOS= deptMapper.selectList(lambdaQueryWrapper);
        Map<Long, DeptDO> deptMap = deptDOS.stream()
                .collect(Collectors.toMap(DeptDO::getId, dept -> dept));
        pageResult.getList().forEach(user -> {
            UserPageItemRespVO respVO = UserConvert.INSTANCE.convert(user);
            if(respVO.getMobile()!=null && PhoneUtil.isMobile(respVO.getMobile())){
                respVO.setMobile(respVO.getMobile().substring(0, 3) + "****" + respVO.getMobile().substring(7));
            }
            respVO.setDept(UserConvert.INSTANCE.convert(deptMap.get(user.getDeptId())));
            // 机构管理员设置标记字段
            if (tenantService.isTenantAdmin(user.getId())) {
                respVO.setIsAdmin(true);
            } else {
                respVO.setIsAdmin(false);
            }
            List<DeptDO> depts = user.getDepartments();
            respVO.setDepts(depts);
            userList.add(respVO);
        });
        return success(new PageResult<>(userList, pageResult.getTotal()));
    }



    @GetMapping("/export")
    @ApiOperation("导出用户")
    @PreAuthorize("@ss.hasPermission('system:user:export')")
    @OperateLog(type = EXPORT)
    @DataPermission(enable = true)
    public void exportUsers(@Validated UserExportReqVO reqVO,
                            HttpServletResponse response) throws IOException {
        // 获得用户列表
        List<AdminUserDO> users = userService.getUserExport(reqVO);

        // 获得拼接需要的数据
        Collection<Long> deptIds = convertList(users, AdminUserDO::getDeptId);
        Map<Long, DeptDO> deptMap = deptService.getDeptMap(deptIds);
        Map<Long, AdminUserDO> deptLeaderUserMap = userService.getUserMap(
                convertSet(deptMap.values(), DeptDO::getLeaderUserId));
        // 拼接数据
        List<UserExcelVO> excelUsers = new ArrayList<>(users.size());
        users.forEach(user -> {
            UserExcelVO excelVO = UserConvert.INSTANCE.convert02(user);
            List<DeptDO> depts = user.getDepartments();
            String departments = depts.stream().map(DeptDO::getName).collect(Collectors.joining(","));
            excelVO.setDeptName(departments);
            // 设置组织
//            MapUtils.findAndThen(deptMap, user.getDeptId(), dept -> {
//                excelVO.setDeptName(dept.getName());
//            });
            // 获得岗位信息
            if (CollUtil.isNotEmpty(user.getPostIds())) {
                List<PostDO> post = postService.getPosts(user.getPostIds());
                String posts = post.stream().map(PostDO::getName).collect(Collectors.joining(","));
                excelVO.setPost(posts);
            }
            excelUsers.add(excelVO);
        });
        // 输出
        ExcelUtils.write(response, "用户信息.xls", "用户列表", UserExcelVO.class, excelUsers);
    }

    @GetMapping("/page-contact")
    @ApiOperation("获得用户分页通讯录")
    @DataPermission(enable = false)
    public CommonResult<PageResult<UserContactVO>> getUserContact(@Valid UserPageReqVO reqVO) {
        // 获得用户分页列表
        PageResult<UserContactVO> userContactPage = userService.getUserContactPage(reqVO);
        if (CollUtil.isEmpty(userContactPage.getList())) {
            return success(new PageResult<>(userContactPage.getTotal())); // 返回空
        }
        return success(userContactPage);
    }

    @GetMapping("/list-all-simple")
    @ApiOperation(value = "获取用户精简信息列表", notes = "只包含被开启的用户，主要用于前端的下拉选项")
    public CommonResult<List<UserSimpleRespVO>> getSimpleUsers() {
        // 获取用户列表，只要开启状态的
        List<AdminUserDO> list = userService.getUsersByStatus(CommonStatusEnum.ENABLE.getStatus());
        // 排序后，返回给前端
        return success(UserConvert.INSTANCE.convertList04(list));
    }

    @GetMapping("/list-tenant-simple")
    @ApiOperation(value = "获取当前机构用户精简信息列表", notes = "只包含被开启的用户，主要用于前端的下拉选项")
    public CommonResult<List<UserSimpleRespVO>> getTenantSimpleUsers(String nickname) {
        return success(userService.getUsersByStatusAndTenantId(nickname));
    }

    @GetMapping("/list-all-simple-deptLeader")
    @ApiOperation(value = "获取当前组织下所有人员精简信息列表", notes = "只包含被开启的用户，主要用于前端新增组织负责人下拉选项")
    @ApiImplicitParam(name = "deptId", value = "上级组织ID", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<List<UserSimpleRespVO>> getSimpleUsersByDeptId(@RequestParam("deptId") Long deptId) {
        // 获用户组织列表，只要开启状态的
        List<AdminUserDO> list = userService.getUsersByStatusAndDeptId(deptId);
        // 排序后，返回给前端
        return success(UserConvert.INSTANCE.convertList04(list));
    }

    @GetMapping("/list-all-deptId")
    @ApiOperation(value = "所有当前部门人员信息（包括岗位）", notes = "只包含被开启的用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "组织ID", required = true, example = "1024", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "userName", value = "人员名称", required = false, example = "1024", dataTypeClass = String.class)
    })
    public CommonResult<List<UserInfoRespVO>> getUsersByDeptId(@RequestParam("deptId") Long deptId,
                                                           @RequestParam(value = "userName", required = false) String userName) {
        // 获用户门列表，只要开启状态的
        List<DeptDO> deptDOList= deptService.getAllChildrenDeptByDeptId(deptId);
        List<Long> deptIds = deptDOList.stream().map(DeptDO::getId).collect(Collectors.toList());
        deptIds.add(deptId);
        List<AdminUserDO> list = userService.getUsersDeptId(deptIds, userName);
        List<UserInfoRespVO> result = new ArrayList<>();

        //获取所有该机构下的岗位
        List<PostDO> postList = postService.getPostList(getTenantId());
        if(postList.isEmpty()){
            list.forEach(user -> {
                UserInfoRespVO userInfoRespVO = new UserInfoRespVO();
                userInfoRespVO.setUserDO(user);
                result.add(userInfoRespVO);
            });
            return success(result);
        }
        Map<Long, PostDO> longPostDOMap = CollectionUtils.convertMap(postList, PostDO::getId);
        list.forEach(user -> {
            UserInfoRespVO userInfoRespVO = new UserInfoRespVO();
            Set<Long> postIds = user.getPostIds();
            if(CollUtil.isNotEmpty(postIds)){
                List<String> posts = new ArrayList<>();
                postIds.forEach(postId->{
                    PostDO postDO = longPostDOMap.get(postId);
                    if(Objects.nonNull(postDO)){
                        posts.add(postDO.getName());
                    }
                });
                userInfoRespVO.setPosts(posts);
                userInfoRespVO.setUserDO(user);
                result.add(userInfoRespVO);
            }
            else {
                userInfoRespVO.setUserDO(user);
                result.add(userInfoRespVO);
            }
        });
        return success(result);
    }

    @GetMapping("/list-users-deptId")
    @ApiOperation(value = "当前部门所有人员信息", notes = "只包含当前部门被开启的用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "组织ID", required = true, example = "1024", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "userName", value = "人员名称", required = false, example = "1024", dataTypeClass = String.class)
    })
    public CommonResult<List<UserRespVO>> getAllUsersByDeptId(@RequestParam("deptId") Long deptId,
                                                           @RequestParam(value = "userName", required = false) String userName) {
        // 获用户门列表，只要开启状态的
        List<Long> deptIds = new ArrayList<>();
        deptIds.add(deptId);
        List<AdminUserDO> list = userService.getUsersDeptId(deptIds, userName);
        List<UserRespVO> userRespVOS = UserConvert.INSTANCE.convertList6(list);
        DeptDO deptDOC = deptService.getDept(deptId);
        for (UserRespVO userRespVO : userRespVOS) {
            List<DeptDO> deptDOS = deptService.getAllParentDeptByDeptId(userRespVO.getDeptId());
            List<String> deptNames = new ArrayList<>();
            deptDOS.stream().forEach(deptDO -> {
                deptNames.add(deptDO.getName());
            });
            deptNames.add(deptDOC.getName());
            userRespVO.setParentDeptName(String.join("—", deptNames));
        }
        // 排序后，返回给前端
        return success(userRespVOS);
    }

    @GetMapping("/list-userInfo-deptId")
    @ApiOperation(value = "当前部门所有人员信息", notes = "只包含当前部门被开启的用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "组织ID", required = true, example = "1024", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "userName", value = "人员名称", required = false, example = "1024", dataTypeClass = String.class)
    })
    public CommonResult<List<UserRespVO>> getAllUserInfoByDeptId(@RequestParam("deptId") Long deptId,
                                                              @RequestParam(value = "userName", required = false) String userName) {
        // 获用户门列表，只要开启状态的
        List<Long> deptIds = new ArrayList<>();
        deptIds.add(deptId);
        List<AdminUserDO> list = userService.getUsersDeptId(deptIds, userName);
        List<UserRespVO> userRespVOS = UserConvert.INSTANCE.convertList6(list);
        DeptDO deptDOC = deptService.getDept(deptId);
        for (UserRespVO userRespVO : userRespVOS) {
            List<DeptDO> deptDOS = deptService.getAllParentDeptByDeptId(userRespVO.getDeptId());
            List<String> deptNames = new ArrayList<>();
            deptDOS.stream().forEach(deptDO -> {
                deptNames.add(deptDO.getName());
            });
            deptNames.add(deptDOC.getName());
            userRespVO.setParentDeptName(String.join("—", deptNames));
        }
        // 排序后，返回给前端
        return success(userRespVOS);
    }


    @GetMapping("/get")
    @ApiOperation("获得用户详情")
    @PreAuthorize("@ss.hasPermission('system:user:get')")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<UserRespVO> getInfo(@RequestParam("id") Long id) {
        UserPageItemRespVO userRespVO = UserConvert.INSTANCE.convert(userService.getUser(id));
        List<DeptDO> depts = new ArrayList<>();
        if(!CollUtil.isEmpty(userRespVO.getDeptIds())) {
            depts = deptService.getDepts(userRespVO.getDeptIds());
        } else {
            List<Long> deptId = new ArrayList<>();
            deptId.add(userRespVO.getDeptId());

            depts = deptService.getDepts(deptId);
        }
        Collections.sort(depts, Comparator.comparing(DeptDO::getSort)
                .thenComparing(DeptDO::getUpdateTime, Comparator.reverseOrder()));
        userRespVO.setDepts(depts);
        // 机构管理员设置标记字段
        if (tenantService.isTenantAdmin(id)) {
            userRespVO.setIsAdmin(true);
        } else {
            userRespVO.setIsAdmin(false);
        }
        return success(userRespVO);
    }

    @GetMapping("/get-mobile")
    @PermitAll
    @ApiOperation("根据用户名获得用户手机号")
    @ApiImplicitParam(name = "username", value = "用户名", required = true, example = "admin", dataTypeClass = String.class)
    public CommonResult<UserBaseVO> getInfoByUsername(@RequestParam("username") String username) {
        AdminUserDO user = userService.getUserByUsername(username);
        UserBaseVO userBaseVO = new UserBaseVO();
        userBaseVO.setMobile(user.getMobile());
        return success(userBaseVO);
    }

    @GetMapping("/get-import-template")
    @ApiOperation("获得导入用户模板")
    @OperateLog(enable = false)
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 输出
        ExcelUtils.write(response, "用户导入模板.xls", "用户列表", UserImportExcelVO.class,Collections.emptyList());
    }

    @GetMapping("/get-import-service-template")
    @ApiOperation("获得服务企业导入用户模板")
    @OperateLog(enable = false)
    public void importServiceTemplate(HttpServletResponse response) throws IOException {
        // 输出
        ExcelUtils.write(response, "用户导入模板.xls", "用户列表", UserImportServiceExcelVO.class,Collections.emptyList());
    }

    @PostMapping("/import")
    @ApiOperation("导入用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class),
            @ApiImplicitParam(name = "updateSupport", value = "是否支持更新，默认为 false", example = "true", dataTypeClass = Boolean.class)
    })
    @PreAuthorize("@ss.hasPermission('system:user:import')")
    @OperateLog(type = IMPORT)
    public CommonResult<UserImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                      @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        List<UserImportExcelVO> list = ExcelUtils.read(file, UserImportExcelVO.class);
        ExcelValidator.valid(list,1);
        return success(userService.importUsers(list, updateSupport));
    }

    @PostMapping("/importService")
    @ApiOperation("导入用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class),
            @ApiImplicitParam(name = "updateSupport", value = "是否支持更新，默认为 false", example = "true", dataTypeClass = Boolean.class)
    })
    @PreAuthorize("@ss.hasPermission('system:user:import')")
    @OperateLog(type = IMPORT)
    public CommonResult<UserImportRespVO> importServiceExcel(@RequestParam("file") MultipartFile file,
                                                      @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        List<UserImportServiceExcelVO> serviceList = ExcelUtils.read(file, UserImportServiceExcelVO.class);
        Long deptId = deptService.getDeptIdByDeptName("服务企业");
        List<UserImportExcelVO> list = UserConvert.INSTANCE.convertServiceList(serviceList);
        for (UserImportExcelVO userImportExcelVO : list) {
            userImportExcelVO.setDeptId(deptId);
        }
        ExcelValidator.valid(list,1);
        return success(userService.importUsers(list, updateSupport));
    }

    @GetMapping("/list-by-tenant")
    @ApiOperation("根据机构id查询用户信息集合")
    public CommonResult<List<UserInfoVo>> getListByTenantId(@RequestParam(value = "id") Long tenantId) {
        return success(UserConvert.INSTANCE.convertList7(userService.getListByTenantId(tenantId)));
    }

    @GetMapping("/list-by-nickname")
    @ApiOperation("根据用户昵称模糊搜索用户信息")
    public CommonResult<List<UserTenantSimpleRespVO>> getUserTenantByNameLike(@RequestParam(required = false) String nickname) {
        return success(userService.getUserTenantByNameLike(nickname));
    }

    @GetMapping("/getAllDept")
    @ApiOperation("根据用户id获取他的所有部门信息")
    public CommonResult<List<DeptDO>> getAllDept(@RequestParam(required = false) Long userId) {
        if (userId == null) {
            userId = getLoginUserId();
        }
        return success(userService.getAllDept(userId));
    }


    @GetMapping("/listall-by-tenant")
    @ApiOperation("根据机构id查询用户信息集合-子系统使用")
    @PermitAll
    public CommonResult<List<UserInfoVo>> getListallByTenantId(@RequestParam(value = "data" ) String data) {

        String parameters =  AesUtils.decryptFromString(data , Mode.CBC, Padding.ZeroPadding);

        if(StrUtil.isEmpty(parameters)){
            throw exception( 500 , "参数解码错误");
        }

        AllPersonInfoReq reqVO  = JSONUtil.toBean(parameters , AllPersonInfoReq.class);

        if(ObjectUtil.isEmpty(reqVO)){
            throw exception(500 ,  "参数解析错误");
        }

        Long time = System.currentTimeMillis() - reqVO.getTimestamp();

        if(time>10*1000){
            throw exception(500 ,  "请求超时");
        }


        return success(UserConvert.INSTANCE.convertList7(userService.getListByTenantId(reqVO.getTenantId())));
    }

    @GetMapping("/delete-openid")
    @ApiOperation("根据用户id清除小程序openid")
    public CommonResult<Boolean> deleteOpenId(@RequestParam String mobile) {
        //改成mobile是适配多租户
        userService.deleteOpenId(mobile);
        return success(true);
    }

    @GetMapping("/get-money")
    @ApiOperation("小程序一卡通获取余额")
    public CommonResult<Float> getMoney(@RequestParam String othersystemid) {
        Float money = userService.getMoney(othersystemid);
        return success(money);
    }


    @Resource
    private YeZhongJob yeZhongJob;


    @PostMapping("/syncJob")
    @ApiOperation("一键同步教师111")
    @PermitAll
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:sync')")
    public CommonResult<Boolean> syncTeacherone(HttpServletRequest request, @RequestParam(value = "tenantId" ,required = false) Long tenantId ) {
        yeZhongJob.performTaskteacher(tenantId);
        return success(true);
    }

    @GetMapping("/syncUser")
    @ApiOperation("同步教师")
    @PermitAll
    public CommonResult<Boolean> getAllPersonInfoById(@RequestParam(required = false) Long tenantId) {
        if (tenantId == null) {
            tenantId = getTenantId();
        }
        yeZhongJob.syncTeacher(tenantId);
        return success(true);
    }

    @PostMapping("/useraccesslog")
    @PermitAll
    public CommonResult<Boolean> useraccesslog() {

        if(ApiAccessLogFilter.logenable ==false){
            ApiAccessLogFilter.logenable =true;
        }else{
            ApiAccessLogFilter.logenable =false;
        }
        return success(ApiAccessLogFilter.logenable);
    }

}
