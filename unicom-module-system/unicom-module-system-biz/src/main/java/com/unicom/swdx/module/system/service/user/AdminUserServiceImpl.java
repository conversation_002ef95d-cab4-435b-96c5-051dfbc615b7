package com.unicom.swdx.module.system.service.user;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.exception.ErrorCode;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.framework.common.util.crypt.sm2.SM2Utils;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.framework.web.core.util.WebFrameworkUtils;
import com.unicom.swdx.module.edu.api.signupunit.SignUpUnitApi;
import com.unicom.swdx.module.hr.api.PersonnalApi;
import com.unicom.swdx.module.hr.api.dto.PersonnalUpdateReqDTO;
import com.unicom.swdx.module.infra.api.file.FileApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.*;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthForgetPasswordReqVO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.AuthUpdatePasswordReqVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.*;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.PostUsersPageReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.RoleUsersPageReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.RoleGroupUsersPageReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.profile.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.trainee.TraineeUserReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.*;
import com.unicom.swdx.module.system.convert.dept.DeptConvert;
import com.unicom.swdx.module.system.convert.user.UserConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.PostDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserDeptDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserPostDO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserVO;
import com.unicom.swdx.module.system.dal.mysql.dept.DeptMapper;
import com.unicom.swdx.module.system.dal.mysql.dept.UserDeptMapper;
import com.unicom.swdx.module.system.dal.mysql.dept.UserPostMapper;
import com.unicom.swdx.module.system.dal.mysql.message.MessageAuthorityMapper;
import com.unicom.swdx.module.system.dal.mysql.permission.RoleMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.dal.mysql.yjs.YktMapper;
import com.unicom.swdx.module.system.dal.redis.auth.LoginBadPasswordDAO;
import com.unicom.swdx.module.system.dal.redis.auth.UUIDRedisDAO;
import com.unicom.swdx.module.system.dal.redis.auth.VerificationRedisDAO;
import com.unicom.swdx.module.system.enums.kafka.person.PersonEventType;
import com.unicom.swdx.module.system.enums.logger.LoginLogTypeEnum;
import com.unicom.swdx.module.system.mq.producer.user.ExternalPersonProducer;
import com.unicom.swdx.module.system.service.auth.AdminAuthService;
import com.unicom.swdx.module.system.service.dept.DeptService;
import com.unicom.swdx.module.system.service.dept.PostService;
import com.unicom.swdx.module.system.service.permission.PermissionService;
import com.unicom.swdx.module.system.service.permission.RoleService;
import com.unicom.swdx.module.system.service.tenant.TenantService;
import com.unicom.swdx.module.system.service.user.dto.TraineeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertList;
import static com.unicom.swdx.framework.common.util.collection.CollectionUtils.convertSet;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.system.enums.ApiConstants.CLIENT_CODE;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.system.enums.kafka.person.PersonEventType.addPersonEventType;
import static com.unicom.swdx.module.system.enums.sms.SmsMessageEnum.*;
import static com.unicom.swdx.module.system.enums.user.UserCategoryEnum.HR_PLAT;
import static com.unicom.swdx.module.system.enums.user.UserCategoryEnum.MIDDLE_PLAT;


/**
 * 后台用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service("adminUserService")
@Slf4j
@RefreshScope
public class AdminUserServiceImpl implements AdminUserService {


    @Resource
    private AdminAuthService adminAuthService;

    @Resource
    private AdminUserApi userApi;

    @Resource
    private AdminUserMapper userMapper;
    @Resource
    private UserDeptMapper userDeptMapper;
    @Resource
    private DeptService deptService;
    @Resource
    private PostService postService;
    @Resource
    private RoleService roleService;
    @Resource
    @Lazy
    private PermissionService permissionService;

    @Resource
    private YktMapper yktMapper;


    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private UserPostMapper userPostMapper;
    @Resource
    private FileApi fileApi;
    @Resource
    private VerificationRedisDAO verificationRedisDAO;
    @Resource
    private UUIDRedisDAO uuidRedisDAO;
    @Resource
    private RoleMapper roleMapper;
    @Resource
    private DeptMapper deptMapper;
    @Resource
    @Lazy
    private TenantService tenantService;

    @Resource
    private LoginBadPasswordDAO loginBadPasswordDAO;

    @Resource
    private AdminUserService userService;

    @Resource
    private MessageAuthorityMapper messageAuthorityMapper;

    @Resource
    private ExternalPersonProducer exPersonProducer;

    @Value("${unicom.password.expired-days:90}")
    private long passwordExpiredDays;

    @Value("${unicom.password.remind-days:10}")
    private long remindDays;

    @Value("${unicom.password.unit-admin-default-password:Hnswdx@2023&Jw_dx#124!}")
    private String unitAdminDefaultPassword;

    @Resource
    private PersonnalApi personnalApi;

    @Resource
    private SignUpUnitApi signUpUnitApi;


    // 缓存30s部门统计避免频繁请求
    Cache<String, List<AdminUserDO>> countCache =
            CacheBuilder.newBuilder()
                    .initialCapacity(128) // 初始容量
                    .maximumSize(128 * 50)   // 设定最大容量
                    .expireAfterWrite(30L, TimeUnit.SECONDS) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();

    // 缓存30s部门统计避免频繁请求
    Cache<String, List<DeptCountVO>> countDeptsCache =
            CacheBuilder.newBuilder()
                    .initialCapacity(128) // 初始容量
                    .maximumSize(128 * 50)   // 设定最大容量
                    .expireAfterWrite(30L, TimeUnit.SECONDS) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();

    // 最大失败次数
    public static final int MAX_FAIL_COUNT = 10;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUserHr(UserCreateReqVO reqVO, Long tenantId) {
        tenantId = SecurityFrameworkUtils.getTenantId();
        AdminUserDO existUser = userMapper.selectByMobileandTenantid(reqVO.getMobile(), tenantId);
        if (ObjectUtil.isNotEmpty(existUser) && existUser.getId() != null) {
            return existUser.getId();
        }
        return createUser(reqVO, tenantId, HR_PLAT.getCategory());
    }


    /**
     * 创建用户
     *
     * @param reqVO 用户信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUser(UserCreateReqVO reqVO, Long tenantId, Integer userCategory) {
        // 校验正确性
        checkCreateOrUpdate(null, reqVO.getUsername(), reqVO.getMobile(), reqVO.getEmail(),
                reqVO.getDeptId(), reqVO.getPostIds());
        AdminUserDO user = UserConvert.INSTANCE.convert(reqVO);
        Long applicant = roleService.getRoleByName("应聘者");
        Long deptId = deptService.getDeptIdByDeptName("服务企业");
        Set<Long> roleIds = new HashSet<>();
        if (!Objects.equals(reqVO.getDeptId(), deptId)) {
            roleIds.add(applicant);
        }
        if (Objects.isNull(tenantId)) {
            if (reqVO.getDeptId() == 0) {// 用户未加入组织
                tenantId = getTenantId();
            } else {// 加入所属组织机构
                tenantId = deptMapper.selectTenantIdByDeptId(reqVO.getDeptId());
            }
        }
        user.setTenantId(tenantId);
        user.setStatus(reqVO.getStatus()); // 默认开启，数据库已加入默认值
        user.setPassword(passwordEncoder.encode(reqVO.getPassword())); // 加密密码
        userMapper.insert(user);
        // 新增用户角色
        permissionService.addUserRole(user.getId(), roleIds);
        // 插入关联岗位
        if (CollectionUtil.isNotEmpty(user.getPostIds())) {
            userPostMapper.insertBatch(convertList(user.getPostIds(),
                    postId -> new UserPostDO().setUserId(user.getId()).setPostId(postId)));
        }
        // 推送创建的用户
        // self.pushCreateToDataTop(user);


        OldPersonDTO oldPerson = null;

        if (tenantId == 25) {
            oldPerson = userApi.getOldPersonByMobile(user.getMobile()).getCheckedData();

            if (oldPerson == null) {
                oldPerson = userApi.getOldPersonByMobileAndName(user.getMobile(), user.getNickname()).getCheckedData();
            }


        }

        if (oldPerson != null) {
            AdminUserDO upduser = new AdminUserDO();
            upduser.setId(user.getId());
            if (oldPerson.getUniconId() == null) {
                upduser.setOthersystemid(PersonEventType.personSender + oldPerson.getEmployeeId());
            } else {
                upduser.setOthersystemid(oldPerson.getUniconId());
            }


            userMapper.updateById(upduser);
        } else {
            AdminUserDO upduser = new AdminUserDO();
            upduser.setId(user.getId());
            upduser.setOthersystemid(PersonEventType.personSender + user.getId());
            userMapper.updateById(upduser);
        }

        // 从业中新增的服务企业的用户，给一卡通发新增人员消息
        if (Objects.equals(25L, tenantId) && Objects.equals(reqVO.getDeptId(), deptId)
                && Objects.equals(userCategory, MIDDLE_PLAT.getCategory())) {

            if (oldPerson == null) {
                exPersonProducer.sendExternalPersonData(addPersonEventType, user);
            }

        }

        return user.getId();
    }

    private static final String DEFULT_STRING = "default";

    @Override
    public Long createUserJW(String mobile, String nickname, Long tennantid) {
        AdminUserDO user = getUserByMobileTenant(mobile, tennantid);
        if (Objects.nonNull(user)) {
            user.setTenantId(tennantid);
            user.setStatus(CommonStatusEnum.ENABLE.getStatus());
            userMapper.updateById(user);
            return user.getId();
        }
        AdminUserDO adminUserDO = new AdminUserDO();
        adminUserDO.setUsername(mobile);
        adminUserDO.setMobile(mobile);
        adminUserDO.setTenantId(tennantid ==null ? SecurityFrameworkUtils.getTenantId() : tennantid);
        // 默认字段赋值
        adminUserDO.setPassword(DEFULT_STRING);
        adminUserDO.setNickname(nickname);
        adminUserDO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        adminUserDO.setIsRealNameAuthentication(false);
        userMapper.insert(adminUserDO);
        return adminUserDO.getId();
    }

    /**
     * 调训系统创建单位管理员用户
     *
     * @param username 用户名
     * @param mobile   手机号
     * @param nickname 用户昵称
     * @return 用户id
     */
    @Override
    public Long createAdminForUnit(String username, String mobile, String nickname) {
        AdminUserDO userByMobile = userMapper.selectByMobile(mobile);

        AdminUserDO adminUserDO = new AdminUserDO();
        if (Objects.nonNull(userByMobile)) {
            // 当前手机号用户已经存在的话
            // 查看该用户是否是单位管理员
            Long unitId = signUpUnitApi.getUnitIdByUserId(userByMobile.getId()).getCheckedData();
            if (Objects.nonNull(unitId)) {
                throw exception(USER_MOBILE_EXISTS);
            }
            adminUserDO = userByMobile;
            adminUserDO.setMobile(mobile);
            adminUserDO.setTenantId(SecurityFrameworkUtils.getTenantId());
            // 设置默认密码
            adminUserDO.setPassword(passwordEncoder.encode(unitAdminDefaultPassword)); // 加密密码
            adminUserDO.setStatus(CommonStatusEnum.ENABLE.getStatus());
            adminUserDO.setIsRealNameAuthentication(false);
            // 初始密码未修改
            adminUserDO.setInitPasswordIsChange(false);
            userMapper.updateById(adminUserDO);
        } else {
            adminUserDO.setUsername(username);
            adminUserDO.setMobile(mobile);
            adminUserDO.setTenantId(SecurityFrameworkUtils.getTenantId());
            // 设置默认密码
            adminUserDO.setPassword(passwordEncoder.encode(unitAdminDefaultPassword)); // 加密密码
            adminUserDO.setNickname(nickname);
            adminUserDO.setStatus(CommonStatusEnum.ENABLE.getStatus());
            adminUserDO.setIsRealNameAuthentication(false);
            // 初始密码未修改
            adminUserDO.setInitPasswordIsChange(false);
            userMapper.insert(adminUserDO);
        }
        return adminUserDO.getId();
    }

    /**
     * 批量创建调训单位管理员用户
     *
     * @param dtos 调训单位管理员用户信息 需要提前校验了调训单位无这些手机号和用户名
     * @return 调训单位管理员用户信息 （包含插入id）
     */
    @Override
    public List<CreateAdminForUnitBatchResultDTO> batchCreateAdminForUnit(CreateAdminForUnitBatchDTO dtos) {
        List<CreateAdminForUnitDTO> userInfoList = dtos.getUserInfoList();
        List<AdminUserDO> userDOList = UserConvert.INSTANCE.convertList09(userInfoList);
        // 查询 手机号列表 用户名列表中的用户
        List<String> newMobiles = userDOList.stream().map(AdminUserDO::getMobile).collect(Collectors.toList());


        List<AdminUserDO> existsUsersList = userMapper.selectByMobileList(newMobiles);
        Map<String, AdminUserDO> existsUserMap = existsUsersList.stream()
                .collect(Collectors.toMap(AdminUserDO::getMobile, o -> o, (k1, k2) -> k1));

        List<CreateAdminForUnitBatchResultDTO> result = new ArrayList<>();
        List<AdminUserDO> insertAdminDOList = new ArrayList<>();
        List<AdminUserDO> updateAdminDOList = new ArrayList<>();
        for (AdminUserDO insertAdminDO : userDOList) {
            CreateAdminForUnitBatchResultDTO resultDTO = new CreateAdminForUnitBatchResultDTO();
            resultDTO.setErrorInfoList(new ArrayList<>());
            if (existsUserMap.containsKey(insertAdminDO.getMobile())) {
                AdminUserDO existUser = existsUserMap.get(insertAdminDO.getMobile());
                existUser.setMobile(insertAdminDO.getMobile());
                existUser.setTenantId(SecurityFrameworkUtils.getTenantId());
                // 设置默认密码
                existUser.setPassword(passwordEncoder.encode(unitAdminDefaultPassword)); // 加密密码
                existUser.setStatus(CommonStatusEnum.ENABLE.getStatus());
                existUser.setIsRealNameAuthentication(false);
                // 初始密码未修改
                existUser.setInitPasswordIsChange(false);
                updateAdminDOList.add(existUser);
                // resultDTO.setUsername(insertAdminDO.getUsername());
                // resultDTO.getErrorInfoList().add("手机号已存在");
            } else {
                if (resultDTO.getErrorInfoList().isEmpty()) {
                    insertAdminDO.setTenantId(SecurityFrameworkUtils.getTenantId());
                    // 设置默认密码
                    insertAdminDO.setPassword(passwordEncoder.encode(unitAdminDefaultPassword)); // 加密密码
                    insertAdminDO.setStatus(CommonStatusEnum.ENABLE.getStatus());
                    insertAdminDO.setIsRealNameAuthentication(false);
                    // 初始密码未修改
                    insertAdminDO.setInitPasswordIsChange(false);
                    insertAdminDOList.add(insertAdminDO);
                } else {
                    result.add(resultDTO);
                }
            }
        }
        // 批量插入
        if (!insertAdminDOList.isEmpty()) {
            // insertAdminDOList 会有插入id
            userMapper.insertBatch(insertAdminDOList);
            for (AdminUserDO adminUserDO : insertAdminDOList) {
                CreateAdminForUnitBatchResultDTO resultDTO2 = new CreateAdminForUnitBatchResultDTO();
                resultDTO2.setId(adminUserDO.getId());
                resultDTO2.setUsername(adminUserDO.getUsername());
                result.add(resultDTO2);
            }
        }
        // 批量更新
        if (!updateAdminDOList.isEmpty()) {
            userMapper.updateBatch(updateAdminDOList);
            for (AdminUserDO adminUserDO : updateAdminDOList) {
                CreateAdminForUnitBatchResultDTO resultDTO2 = new CreateAdminForUnitBatchResultDTO();
                resultDTO2.setId(adminUserDO.getId());
                resultDTO2.setUsername(adminUserDO.getUsername());
                result.add(resultDTO2);
            }
        }
        return result;
    }

    /**
     * 更新单位管理员手机号
     * （需要提前判断了单位管理中手机号是否存在）
     *
     * @param userId 用户id
     * @param mobile 新手机号
     */
    @Override
    public Long updateAdminMobileForUnit(Long userId, String mobile) {
        AdminUserDO adminUserDO = userMapper.selectById(userId);
        if (Objects.isNull(adminUserDO)) {
            throw exception(USER_NOT_EXISTS);
        }
        AdminUserDO existsUserDO = userMapper.selectByMobile(mobile);
        if (Objects.nonNull(existsUserDO) &&
                !existsUserDO.getId().equals(userId)) {
            // 已经存在在system_users中 但是不是单位管理员 则绑定上这个账号
            // 修改用户名
            existsUserDO.setTenantId(adminUserDO.getTenantId());
            existsUserDO.setPassword(adminUserDO.getPassword());
            existsUserDO.setIsRealNameAuthentication(adminUserDO.getIsRealNameAuthentication());
            existsUserDO.setStatus(CommonStatusEnum.ENABLE.getStatus());
            existsUserDO.setInitPasswordIsChange(adminUserDO.getInitPasswordIsChange());
            userMapper.updateById(existsUserDO);
            return existsUserDO.getId();
        }else {
            adminUserDO.setMobile(mobile);
            userMapper.updateById(adminUserDO);
            return adminUserDO.getId();
        }
    }

    /**
     * 删除用户
     *
     * @param userIds 用户id集合
     */
    @Override
    public void deleteByIds(List<Long> userIds) {
        if (CollectionUtil.isEmpty(userIds)) {
            return;
        }
        userMapper.deleteBatchIds(userIds);
    }

    @Override
    public Long createUserJWall(String mobile, String nickname, String systemid) {
        AdminUserDO user = getUserByMobile(mobile);
        if (Objects.nonNull(user)) {
            user.setTenantId(SecurityFrameworkUtils.getTenantId());
            user.setStatus(CommonStatusEnum.ENABLE.getStatus());
            if (systemid != null) {
                user.setSystemId(systemid);
            }
            userMapper.updateById(user);
            return user.getId();
        }
        AdminUserDO adminUserDO = new AdminUserDO();
        adminUserDO.setUsername(mobile);
        adminUserDO.setMobile(mobile);
        adminUserDO.setTenantId(SecurityFrameworkUtils.getTenantId());
        // 默认字段赋值
        adminUserDO.setPassword(DEFULT_STRING);
        adminUserDO.setNickname(nickname);
        adminUserDO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        adminUserDO.setIsRealNameAuthentication(false);
        if (systemid != null) {
            adminUserDO.setSystemId(systemid);
        }
        userMapper.insert(adminUserDO);
        return adminUserDO.getId();
    }

    /**
     *
     * @param userInputList  所有的老师信息，包括userid为null 新增信息， userid不为null 的更新师资信息   systemid 都不为null
     * @param tenantId
     * @return
     */
    @Override
    @TenantIgnore
    public Map<String, Long> createUserBatchByTenant(List<UserInputDTO> userInputList, Long tenantId) {
        // Step 1: Collect all users by mobile numbers to check existing users
        List<String> mobileNumbers = userInputList.stream().map(UserInputDTO::getSystemId).map(it -> it.toString())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if(CollectionUtil.isEmpty(mobileNumbers)){
            return null;
        }

        // Fetch existing users from the database
        Map<String, List<AdminUserDO>> existingUsersMap = userMapper.findBySystemIdList(mobileNumbers)
                .stream()
                .collect(Collectors.groupingBy(AdminUserDO::getSystemId));

        List<AdminUserDO> usersToUpdate = new ArrayList<>();
        List<AdminUserDO> usersToInsert = new ArrayList<>();
        List<AdminUserDO> usersTodelete = new ArrayList<>();

        // Step 2: Process input users, divide into updates and inserts


        Set<UserInputDTO> uniqueUsers = userInputList.stream()
                .collect(Collectors.toMap(
                        UserInputDTO::getSystemId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ))
                .values()
                .stream()
                .collect(Collectors.toSet());

        if(uniqueUsers!=null){
            userInputList = new ArrayList<>(uniqueUsers);
        }



        outerLoop:
        for (UserInputDTO input : userInputList) {

            String mobile = input.getMobile();
            String nickname = input.getNickname();
            String systemId = input.getSystemId().toString();

            List<AdminUserDO> userDOList = existingUsersMap.get(systemId) ;
            if(userDOList!=null){
                userDOList  =userDOList.stream().filter(Objects::nonNull).collect(Collectors.toList());
            }



            if(userDOList ==null){

                AdminUserDO newUser = new AdminUserDO();
                newUser.setUsername(mobile);
                newUser.setMobile(mobile);
                newUser.setTenantId(tenantId);
                newUser.setPassword(DEFULT_STRING); // Assign default password
                newUser.setNickname(nickname);
                newUser.setStatus(CommonStatusEnum.ENABLE.getStatus());
                newUser.setIsRealNameAuthentication(false); // Default value
                newUser.setSystemId(systemId == null ? null : systemId.toString());
                usersToInsert.add(newUser);
                continue;
            }






            AtomicBoolean atomicBoolean = new AtomicBoolean(false); // 是否匹配成功


            for (AdminUserDO user : userDOList) {

                if(user==null||user.getId()==null){
                    continue ;
                }

                if (user != null && (user.getTenantId() != null && (tenantId.equals(user.getTenantId())))) {

                    if(user.getMobile().equals(input.getMobile()) &&  user.getNickname().equals(input.getNickname()) ){

                        if(user.getId().equals(input.getUserid())  ){

                            if(ObjectUtil.isNotEmpty(user.getSystemId())){

                                if(user.getSystemId().equals(input.getSystemId().toString())){
                                    atomicBoolean.set(true);
                                    System.out.println("匹配成功111");
                                    continue;
                                }
                            }

                        }
                    }

                }

                if( user.getId().equals(input.getUserid())){


                    if (user != null && atomicBoolean.get() ==false) {
                        // Update existing user
                        user.setTenantId(tenantId);
                        user.setStatus(CommonStatusEnum.ENABLE.getStatus());
                        user.setMobile(input.getMobile());
                        user.setUsername(input.getMobile());
                        user.setNickname(input.getNickname());
                        user.setSystemId(systemId == null ? null : systemId.toString());
                        usersToUpdate.add(user);
                        atomicBoolean.set(true);
                        System.out.println("匹配成功222");

                    }else{
                        usersTodelete.add(user);
                    }

                }else{

                    usersTodelete.add(user);

                }


            }


            if(atomicBoolean.get() == false){

                // Prepare new user for insertion
                AdminUserDO newUser = new AdminUserDO();
                newUser.setUsername(mobile);
                newUser.setMobile(mobile);
                newUser.setTenantId(tenantId);
                newUser.setPassword(DEFULT_STRING); // Assign default password
                newUser.setNickname(nickname);
                newUser.setStatus(CommonStatusEnum.ENABLE.getStatus());
                newUser.setIsRealNameAuthentication(false); // Default value
                newUser.setSystemId(systemId == null ? null : systemId.toString());
                usersToInsert.add(newUser);
            }


        }

        // Step 3: Perform batch updates and inserts
        if (!usersToUpdate.isEmpty()) {
            userMapper.updateBatch(usersToUpdate);
            System.out.println("upd");
        }
        if (!usersToInsert.isEmpty()) {
            userMapper.insertBatch(usersToInsert);
            System.out.println("usersToInsert "  +   usersToInsert);
        }

        if (!usersTodelete.isEmpty()) {
            userMapper.deleteBatchIds(usersTodelete);
            System.out.println("usersTodelete" +  usersTodelete);
        }

        // Step 4: Collect and return IDs of processed users
        Map<String, Long> userIds = new HashMap<>();
        usersToUpdate.forEach(user -> userIds.put(user.getMobile(), user.getId()));
        usersToInsert.forEach(newUser -> userIds.put(newUser.getMobile(), newUser.getId()));
        return userIds;
    }

    /**
     * 创建应聘用户
     *
     * @param reqVO 用户信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createApplyUser(ApplyUserCreateReqVO reqVO, Long tenantId) {
        Long apply = deptService.getDeptIdByDeptName("招聘网站");
        reqVO.setDeptId(apply);
        // 校验正确性
        checkCreateOrUpdate(null, reqVO.getUsername(), reqVO.getMobile(), null,
                reqVO.getDeptId(), null);
        if (isRegisterVerificationMatch(reqVO.getVerification(), reqVO.getMobile())) {
            AdminUserDO user = UserConvert.INSTANCE.convert(reqVO);
            if (Objects.isNull(tenantId)) {
                if (reqVO.getDeptId() == 0) {// 用户未加入组织
                    tenantId = getTenantId();
                } else {// 加入所属组织机构
                    tenantId = deptMapper.selectTenantIdByDeptId(reqVO.getDeptId());
                }
            }
            user.setTenantId(tenantId);
            user.setStatus(0); // 默认开启，数据库已加入默认值
            user.setPassword(passwordEncoder.encode(reqVO.getPassword())); // 加密密码
            userMapper.insert(user);
            Long applicant = roleService.getRoleByName("应聘者");
            Set<Long> roleIds = new HashSet<>();
            if (applicant != null) {
                roleIds.add(applicant);
            } else {
                throw exception(ROLE_NOT_EXISTS);
            }

            // 新增用户角色
            permissionService.addUserRole(user.getId(), roleIds);


            // 插入关联岗位
            if (CollectionUtil.isNotEmpty(user.getPostIds())) {
                userPostMapper.insertBatch(convertList(user.getPostIds(),
                        postId -> new UserPostDO().setUserId(user.getId()).setPostId(postId)));
            }
            return user.getId();
        } else {
            throw exception(AUTH_MOBILE_VERIFICATION_CODE_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTraineeUser(TraineeUserReqVO reqVO) {

        AdminUserDO existUser = userMapper.selectByMobile(reqVO.getMobile());
        if (existUser == null) {
            // 手机号不存在则直接添加一个新用户
            AdminUserDO user = new AdminUserDO();

            user.setOthersystemid(reqVO.getTraineeId());
            // 设置为学员类型
//        user.setIsTrainee(true);
            user.setUsername(reqVO.getMobile());
            user.setNickname(reqVO.getNickname());
            user.setMobile(reqVO.getMobile());
            user.setDeptId(deptService.getDeptIdByDeptName("学员"));
            user.setSex(reqVO.getSex());
            user.setDeleted(false);
            // 设置所属机构
            user.setTenantId(Long.parseLong(reqVO.getTenantId()));
            // 设置所属部门
            user.setStatus(0);
            // 设置密码
            user.setPassword(passwordEncoder.encode(SM2Utils.USER_PASSWORD)); // 加密密码

            user.setOthersystemid(reqVO.getTraineeId()); // 新建的学员设置他的otherid

            userMapper.insert(user);

            // 设置学员角色
            // 学员小程序角色
            Long traineeXcxRole = roleService.getInnerRoleIdByCode("student-role");
            // 学员web端角色
            Long traineeWebRole = roleService.getInnerRoleIdByCode("student-web-role");
            // 设置首页角色
            Long middleRole = roleService.getInnerRoleIdByCode("DX-Homepage");


            Set<Long> roleIds = new HashSet<>();
            if (middleRole != null) {
                roleIds.add(middleRole);
            }
            if (traineeXcxRole != null) {
                roleIds.add(traineeXcxRole);
            }
            if (traineeWebRole != null) {
                roleIds.add(traineeWebRole);
            }

            roleIds.add(roleService.getInnerRoleIdByCode("yikatongzhuye-role"));
            roleIds.add(roleService.getInnerRoleIdByCode("zhaopiancaiji-role"));
            roleIds.add(roleService.getInnerRoleIdByCode("yikatong-role"));

            // 新增用户角色
            permissionService.addUserRole(user.getId(), roleIds);
            return user.getId();

        } else {

            existUser.setOthersystemid(reqVO.getTraineeId());
            userMapper.updateById(existUser);


            // 手机号已存在，则给该用户添加教务系统学员角色
            // 学员小程序角色
            Long traineeXcxRole = roleService.getInnerRoleIdByCode("student-role");
            // 学员web端角色
            Long traineeWebRole = roleService.getInnerRoleIdByCode("student-web-role");
            // 设置首页角色
            Long middleRole = roleService.getInnerRoleIdByCode("DX-Homepage");
            Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(existUser.getId());
            if (CollUtil.isEmpty(roleIds)) {
                roleIds = new HashSet<>();
            }
            roleIds.add(traineeWebRole);
            roleIds.add(traineeXcxRole);
            roleIds.add(middleRole);


            roleIds.add(roleService.getInnerRoleIdByCode("yikatongzhuye-role"));
            roleIds.add(roleService.getInnerRoleIdByCode("zhaopiancaiji-role"));
            roleIds.add(roleService.getInnerRoleIdByCode("yikatong-role"));


            // 新增用户角色
            permissionService.addUserRole(existUser.getId(), roleIds);
            return existUser.getId();
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createYJSUser(TraineeUserReqVO reqVO) {

        AdminUserDO existUser = userMapper.selectByMobile(reqVO.getMobile());
        if (existUser == null) {
            // 手机号不存在则直接添加一个新用户
            AdminUserDO user = new AdminUserDO();

            user.setOthersystemid(reqVO.getTraineeId());
            // 设置为学员类型
//        user.setIsTrainee(true);
            user.setUsername(reqVO.getMobile());
            user.setNickname(reqVO.getNickname());
            user.setMobile(reqVO.getMobile());
            user.setDeptId(495L);
            user.setSex(reqVO.getSex());
            user.setDeleted(false);
            // 设置所属机构
            user.setTenantId(Long.parseLong(reqVO.getTenantId()));
            // 设置所属部门
            user.setStatus(0);
            // 设置密码
            user.setPassword(passwordEncoder.encode(SM2Utils.USER_PASSWORD)); // 加密密码
            userMapper.insert(user);

//            //设置学员角色
//            //学员小程序角色
//            Long traineeXcxRole = roleService.getInnerRoleIdByCode("student-role");
//            //学员web端角色
//            Long traineeWebRole = roleService.getInnerRoleIdByCode("student-web-role");
            // 设置首页角色
            Long middleRole = roleService.getInnerRoleIdByCode("DX-Homepage");

            // 研究生角色
            Long traineeWebRole = roleService.getInnerRoleIdByCode("jpost");
            // 研究生系统管理员
            Long traineeWebRolea = roleService.getInnerRoleIdByCode("yjs-role");
            // 数据年报大屏test22
            Long traineeWebScreen = roleService.getInnerRoleIdByCode("test22");
            // 其他角色 other-role
            Long traineeWebOther = roleService.getInnerRoleIdByCode("other-role");


            Set<Long> roleIds = new HashSet<>();
            // 党建
            Long dangwu = roleService.getInnerRoleIdByCode("dangwu-role");
            Long dangyuan = roleService.getInnerRoleIdByCode("dangyuan-role");
            Long dangjian = roleService.getInnerRoleIdByCode("dangjian");
            // 网络课堂
            Long wangluoketang = roleService.getInnerRoleIdByCode("wangluoketang-role");
            Long wlktmobile = roleService.getInnerRoleIdByCode("wlktmobile-role");
            // 一卡通
            Long yikatong = roleService.getInnerRoleIdByCode("yikatong-role");
            Long yikatongzhuye = roleService.getInnerRoleIdByCode("yikatongzhuye-role");
            Long zhaopiancaiji = roleService.getInnerRoleIdByCode("zhaopiancaiji-role");
            // 红麓
            Long honglumobile = roleService.getInnerRoleIdByCode("honglumobile-role");
            Long honglu = roleService.getInnerRoleIdByCode("honglu-role");


            if (honglumobile != null) {
                roleIds.add(honglumobile);
            }
            if (honglu != null) {
                roleIds.add(honglu);
            }
            if (yikatong != null) {
                roleIds.add(yikatong);
            }
            if (yikatongzhuye != null) {
                roleIds.add(yikatongzhuye);
            }
            if (zhaopiancaiji != null) {
                roleIds.add(zhaopiancaiji);
            }

            if (wangluoketang != null) {
                roleIds.add(wangluoketang);
            }
            if (wlktmobile != null) {
                roleIds.add(wlktmobile);
            }
            if (dangwu != null) {
                roleIds.add(dangwu);
            }
            if (dangyuan != null) {
                roleIds.add(dangyuan);
            }
            if (dangjian != null) {
                roleIds.add(dangjian);
            }
            if (middleRole != null) {
                roleIds.add(middleRole);
            }
            if (traineeWebRole != null) {
                roleIds.add(traineeWebRole);
            }
            if (traineeWebRolea != null) {
                roleIds.add(traineeWebRolea);
            }
            if (traineeWebScreen != null) {
                roleIds.add(traineeWebScreen);
            }
            if (traineeWebOther != null) {
                roleIds.add(traineeWebOther);
            }

            roleIds.add(roleService.getInnerRoleIdByCode("yikatongzhuye-role"));
            roleIds.add(roleService.getInnerRoleIdByCode("zhaopiancaiji-role"));
            roleIds.add(roleService.getInnerRoleIdByCode("yikatong-role"));

            // 新增用户角色
            permissionService.addUserRole(user.getId(), roleIds);
            return user.getId();

        } else {
            // 手机号已存在，则给该用户添加教务系统学员角色
//            //学员小程序角色
//            Long traineeXcxRole = roleService.getInnerRoleIdByCode("student-role");
//            //学员web端角色
//            Long traineeWebRole = roleService.getInnerRoleIdByCode("student-web-role");

            // 研究生角色
            Long traineeWebRole = roleService.getInnerRoleIdByCode("jpost");
            // 研究生系统管理员
            Long traineeWebRolea = roleService.getInnerRoleIdByCode("yjs-role");
            // 数据年报大屏test22
            Long traineeWebScreen = roleService.getInnerRoleIdByCode("test22");
            // 其他角色 other-role
            Long traineeWebOther = roleService.getInnerRoleIdByCode("other-role");

            // 设置首页角色
            Long middleRole = roleService.getInnerRoleIdByCode("DX-Homepage");
            Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(existUser.getId());
            if (CollUtil.isEmpty(roleIds)) {
                roleIds = new HashSet<>();
            }
            // 党建
            Long dangwu = roleService.getInnerRoleIdByCode("dangwu-role");
            Long dangyuan = roleService.getInnerRoleIdByCode("dangyuan-role");
            Long dangjian = roleService.getInnerRoleIdByCode("dangjian");
            if (dangwu != null) {
                roleIds.add(dangwu);
            }
            if (dangyuan != null) {
                roleIds.add(dangyuan);
            }
            if (dangjian != null) {
                roleIds.add(dangjian);
            }
            // 网络课堂
            Long wangluoketang = roleService.getInnerRoleIdByCode("wangluoketang-role");
            Long wlktmobile = roleService.getInnerRoleIdByCode("wlktmobile-role");

            if (wangluoketang != null) {
                roleIds.add(wangluoketang);
            }
            if (wlktmobile != null) {
                roleIds.add(wlktmobile);
            }
            // 一卡通
            Long yikatong = roleService.getInnerRoleIdByCode("yikatong-role");
            Long yikatongzhuye = roleService.getInnerRoleIdByCode("yikatongzhuye-role");
            Long zhaopiancaiji = roleService.getInnerRoleIdByCode("zhaopiancaiji-role");
            // 红麓
            Long honglumobile = roleService.getInnerRoleIdByCode("honglumobile-role");
            Long honglu = roleService.getInnerRoleIdByCode("honglu-role");


            if (honglumobile != null) {
                roleIds.add(honglumobile);
            }
            if (honglu != null) {
                roleIds.add(honglu);
            }
            if (yikatong != null) {
                roleIds.add(yikatong);
            }
            if (yikatongzhuye != null) {
                roleIds.add(yikatongzhuye);
            }
            if (zhaopiancaiji != null) {
                roleIds.add(zhaopiancaiji);
            }

            roleIds.add(traineeWebRole);
            roleIds.add(traineeWebRolea);
            roleIds.add(middleRole);
            roleIds.add(traineeWebScreen);
            roleIds.add(traineeWebOther);


            roleIds.add(roleService.getInnerRoleIdByCode("yikatongzhuye-role"));
            roleIds.add(roleService.getInnerRoleIdByCode("zhaopiancaiji-role"));
            roleIds.add(roleService.getInnerRoleIdByCode("yikatong-role"));


            // 新增用户角色
            permissionService.addUserRole(existUser.getId(), roleIds);
            return existUser.getId();
        }
    }


    @Override
    public void updateTraineeUserById(TraineeUserReqVO traineeUser) {
        AdminUserDO user = new AdminUserDO();
        // 校验是否已经存在手机号码
        AdminUserDO existUser = userMapper.selectByMobile(traineeUser.getMobile());
        // 手机号已存在
        if (existUser != null && !Objects.equals(existUser.getId(), traineeUser.getUserId())) {
            return;
        }

        user.setMobile(traineeUser.getMobile());

        if (StrUtil.isNotEmpty(traineeUser.getNickname())) {
            user.setNickname(traineeUser.getNickname());
        }

        if (traineeUser.getSex() != null) {
            user.setSex(traineeUser.getSex());
        }

        user.setId(traineeUser.getUserId());

        userMapper.updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UserUpdateReqVO reqVO) {
        if (reqVO.getDeptId() == null) {
            reqVO.setDeptId(reqVO.getDeptIds().get(0));
        }
        // 校验正确性
        checkCreateOrUpdate(reqVO.getId(), reqVO.getUsername(), reqVO.getMobile(), reqVO.getEmail(),
                reqVO.getDeptId(), reqVO.getPostIds());
        // 更新用户
        AdminUserDO updateObj = UserConvert.INSTANCE.convert(reqVO);
        userMapper.updateById(updateObj);
        // 更新岗位
        updateUserPost(reqVO, updateObj);
        // 推送更新的用户
        // self.pushUpdateToDataTop(updateObj);
        // 更新多部门信息
        LambdaQueryWrapperX<UserDeptDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(UserDeptDO::getUserId, reqVO.getId());
        userDeptMapper.delete(wrapperX);
        List<UserDeptDO> userDeptDOS = new ArrayList<>();
        for (Long deptId : reqVO.getDeptIds()) {
            UserDeptDO userDeptDO = new UserDeptDO();
            userDeptDO.setDeptId(deptId);
            userDeptDO.setUserId(reqVO.getId());
            userDeptDOS.add(userDeptDO);
        }
        userDeptMapper.insertBatch(userDeptDOS);
    }

    /**
     * 修改密码
     *
     * @param id       用户编号
     * @param password 密码
     */
    @Override
    public void updateUserPassword(Long id, String password) {
        // 校验用户存在
        checkUserExists(id);
        // 更新密码
        AdminUserDO updateObj = new AdminUserDO();
        updateObj.setId(id);
        updateObj.setPassword(encodePassword(password)); // 加密密码
        updateObj.setInitPasswordIsChange(true);
        updateObj.setPasswordUpdateTime(LocalDateTime.now());
        userMapper.updateById(updateObj);
    }

    @Override
    public void codeUpdateUserPassword(Long id, String newPassword) {
        // 校验用户存在
        checkUserExists(id);
        // 更新密码
        AdminUserDO updateObj = new AdminUserDO();
        updateObj.setId(id);
        updateObj.setPassword(newPassword); // 加密密码
        updateObj.setInitPasswordIsChange(true);
        updateObj.setPasswordUpdateTime(LocalDateTime.now());
        userMapper.updateById(updateObj);
    }

    @Override
    public void resetUserPassword(Long userId) {
        AdminUserDO updateObj = new AdminUserDO();
        if (userId == null) {
            updateObj.setId(getLoginUserId());
        } else {
            updateObj.setId(userId);
        }
        updateObj.setPassword(encodePassword(unitAdminDefaultPassword)); // 加密密码
        updateObj.setInitPasswordIsChange(true);
        updateObj.setPasswordUpdateTime(LocalDateTime.now());
        userMapper.updateById(updateObj);
    }

    private void updateUserPost(UserUpdateReqVO reqVO, AdminUserDO updateObj) {
        Long userId = reqVO.getId();
        Set<Long> dbPostIds = convertSet(userPostMapper.selectListByUserId(userId), UserPostDO::getPostId);
        // 计算新增和删除的岗位编号
        Set<Long> postIds = updateObj.getPostIds();
        if (Objects.isNull(postIds)) {
            postIds = new HashSet<>();
        }
        Collection<Long> createPostIds = CollUtil.subtract(postIds, dbPostIds);
        Collection<Long> deletePostIds = CollUtil.subtract(dbPostIds, postIds);
        // 执行新增和删除。对于已经授权的菜单，不用做任何处理
        if (!CollectionUtil.isEmpty(createPostIds)) {
            userPostMapper.insertBatch(convertList(createPostIds,
                    postId -> new UserPostDO().setUserId(userId).setPostId(postId)));
        }
        if (!CollectionUtil.isEmpty(deletePostIds)) {
            userPostMapper.deleteByUserIdAndPostId(userId, deletePostIds);
        }
    }

    @Async
    @Override
    public void updateUserLogin(Long id, String loginIp) {
        // 不能去更新时间，所以只能单独写更新
        userMapper.updateUserLogin(new AdminUserDO().setId(id).setLoginIp(loginIp).setLoginDate(LocalDateTime.now()));
    }

    @Override
    public AuthLoginRespVO updateUserDefault(Long userid) {


        AdminUserDO userDO = getUser(userid);

        if (userDO == null) {
            return null;
        }

        List<AdminUserDO> users = userService.getUsersByMobile(userDO.getMobile());


        Long loginuserid = SecurityFrameworkUtils.getLoginUserId();

        AtomicBoolean isnext = new AtomicBoolean(false);


        if (CollectionUtil.isNotEmpty(users)) {

            users.forEach(it -> {

                if (NumberUtil.equals(it.getId(), loginuserid)) {
                    isnext.set(true);
                }

            });

        }

        if (isnext.get() == false) {
            throw exception(new ErrorCode(500, "没有切换权限"));
        }


        AuthLoginRespVO authLoginRespVO = null;

        if (CollectionUtil.isNotEmpty(users) && users.size() > 1) {

            for (int i = 0; i < users.size(); i++) {

                AdminUserDO it = users.get(i);

                TenantDO tenantDO = tenantService.getTenant(it.getTenantId());


                if (NumberUtil.equals(it.getId(), userid)) {
                    authLoginRespVO = adminAuthService.createTokenAfterLoginSuccess(it.getId(), it.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);
                    it.setLogindefualt(true);
                    authLoginRespVO.setTenantName(tenantDO.getName());


                } else {
                    it.setLogindefualt(false);
                }

                userMapper.updateById(it);
            }


        } else {

            if (CollectionUtil.isNotEmpty(users) && users.size() == 1) {
                AdminUserDO it = users.get(0);


                TenantDO tenantDO = tenantService.getTenant(it.getTenantId());

                authLoginRespVO = adminAuthService.createTokenAfterLoginSuccess(it.getId(), it.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);
                authLoginRespVO.setTenantName(tenantDO.getName());
                it.setLogindefualt(true);
                userMapper.updateById(it);
            }

        }

        return authLoginRespVO;
    }

    @Override
    public void updateUserProfile(Long id, UserProfileUpdateReqVO reqVO) {
        // 校验正确性
        checkUserExists(id);
        checkEmailUnique(id, reqVO.getEmail());
        checkMobileUnique(id, reqVO.getMobile(), SecurityFrameworkUtils.getTenantId());
        AdminUserDO user = UserConvert.INSTANCE.convert(reqVO).setId(id);
        // 执行更新
        userMapper.updateById(user);
        // 用户界面只能改性别和邮箱//头像是另外的接口
        personnalApi.updateUserProfile(id, reqVO.getSex(), reqVO.getEmail());
        // 推送更新的用户
        // self.pushUpdateToDataTop(user);
    }

    @Override
    public void updateUserPassword(Long id, UserProfileUpdatePasswordReqVO reqVO) {
        // 校验旧密码密码
        checkOldPassword(id, reqVO.getOldPassword());
        // 执行更新
//        AdminUserDO updateObj = new AdminUserDO().setId(id);
//        updateObj.setPassword(encodePassword(reqVO.getNewPassword())); // 加密密码
//        userMapper.updateById(updateObj);
        updateUserPassword(id, reqVO.getNewPassword());
    }

    /**
     * 根据用户id更新用户信息
     *
     * @param user 用户信息
     */
    @Override
    public void updateUserById(AdminUserDO user) {
        checkMobileUnique(user.getId(), user.getMobile(), user.getTenantId());
        userMapper.updateById(user);
    }

    @Override
    public String updateUserMobile(UserUpdateMobileReqVO reqVO) {
        checkOldMobile(reqVO.getId(), reqVO.getNewMobile());
        checkMobileUnique(reqVO.getId(), reqVO.getNewMobile(), SecurityFrameworkUtils.getTenantId());
        String very = verificationRedisDAO.get(RESET_MOBILE, reqVO.getNewMobile());
        if (very == null) {
            throw exception(AUTH_VERIFICATION_EXPIRED);
        }
        if (reqVO.getVerification().equals(very)) {
            userMapper.updateUserMobile(reqVO);
            PersonnalUpdateReqDTO personnalUpdateReqDTO = new PersonnalUpdateReqDTO();
            personnalUpdateReqDTO.setMobile(reqVO.getNewMobile());
            personnalUpdateReqDTO.setUserId(reqVO.getId());
            personnalApi.updateUser(personnalUpdateReqDTO);
        } else {
            throw exception(AUTH_MOBILE_VERIFICATION_CODE_ERROR);
        }
        // 推送更新的用户
        return reqVO.getNewMobile();
    }

    @Override
    public String updateAppUserMobile(UserProfileUpdateMobileReqVO reqVO) {
        checkOldMobile(reqVO.getId(), reqVO.getNewMobile());
        userMapper.updateAppUserMobile(reqVO);
        // 推送更新的用户
        return reqVO.getNewMobile();
    }

    @Override
    public String updateUserAvatar(Long id, InputStream avatarFile) throws Exception {
        checkUserExists(id);
        // 存储文件
        String avatar = fileApi.createFile(IoUtil.readBytes(avatarFile));
        // 更新路径
        AdminUserDO sysUserDO = new AdminUserDO();
        sysUserDO.setId(id);
        sysUserDO.setAvatar(avatar);
        userMapper.updateById(sysUserDO);
        // 更新人事表
        personnalApi.updateUserAvatar(id, avatar);
        return avatar;
    }

    @Override
    public String updateUserAvatarApp(Long id, String baseImage, String fileType) {
        if (baseImage.split(",").length < 1) {
            return "";
        }
        String str = baseImage.split(",")[1];
        byte[] base64A = Base64Decoder.decode(str);
        // fileToBytes(base64A, fileUploadTemp ,id + "." + fileType);

        try {
            return updateUserAvatar(id, new ByteArrayInputStream(base64A));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public void updateUserStatus(Long id, Integer status) {
        // 校验用户存在
        checkUserExists(id);
        // 更新状态
        AdminUserDO updateObj = new AdminUserDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        userMapper.updateById(updateObj);
    }

    @Override
    public void updateUserAppCid(Long id, String appCid) {
        // 校验用户存在
        checkUserExists(id);
        // 更新状态
        AdminUserDO updateObj = new AdminUserDO();
        updateObj.setId(id);
        updateObj.setAppCid(appCid);
        userMapper.updateById(updateObj);
    }

    /**
     * 删除普通用户
     * （机构管理员不能删除，自己不能删除自己）
     *
     * @param id 用户编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(Long id) {
        // todo 机构管理员不能删除，自己不能删除自己
        // 校验用户存在
        checkUserExists(id);
        // 删除用户
        userMapper.deleteById(id);
        // 删除用户关联数据
        permissionService.processUserDeleted(id);
        // 删除用户岗位
        userPostMapper.deleteByUserId(id);
        // 删除部门中有关联的负责人
        deptService.clearLeader(id);
    }

    /**
     * 删除机构管理员用户
     *
     * @param id 机构管理员id
     */
    @Override
    public void deleteAdminUser(Long id) {
        // 校验管理员用户是否存在
        checkUserExists(id);
        // 删除用户
        userMapper.deleteById(id);
        // 删除用户关联数据
        permissionService.processUserDeleted(id);
        // 删除用户岗位
        userPostMapper.deleteByUserId(id);
    }

    @Override
    public AdminUserDO getUserByUsername(String username) {
        AdminUserDO adminUserDO = userMapper.selectByUsername(username);
        if (Objects.isNull(adminUserDO)) {
            throw exception(USER_NOT_EXISTS);
        }
        // todo 去掉多余校验
        if (adminUserDO.getStatus().equals(1)) {
            throw exception(USER_IS_DISABLE, username);
        }
        if (adminUserDO.getMobile().isEmpty()) {
            throw exception(USER_MOBILE_NOT_EXISTS);
        }
        return adminUserDO;
    }

    /**
     * 根据用户名从单位表获取用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Override
    public AdminUserDO getUserByUnitUsername(String username) {
        AdminUserDO adminUserDO = userMapper.selectByUnitUsername(username);
        if (Objects.isNull(adminUserDO)) {
            throw exception(USER_NOT_EXISTS);
        }
        if (StringUtils.isBlank(adminUserDO.getMobile())) {
            throw exception(USER_MOBILE_NOT_EXISTS);
        }
        return adminUserDO;
    }

    @Override
    public AdminUserDO getUserByUsernameForUnit(String username) {
        AdminUserDO adminUserDO = userMapper.selectByUsername(username);
        if (Objects.isNull(adminUserDO)) {
            throw exception(USER_NOT_EXISTS);
        }
        if (adminUserDO.getStatus().equals(1)) {
            throw exception(USER_IS_DISABLE, username);
        }
        return adminUserDO;
    }

    @Override
    public List<AdminUserDO> getUsersByUsername(String username) {
        List<AdminUserDO> adminUserDOs = userMapper.selectUsersByUsername(username);
        if (Objects.isNull(adminUserDOs)) {
            throw exception(USER_NOT_EXISTS);
        }

        adminUserDOs.forEach(adminUserDO -> {
            // todo 去掉多余校验
            if (adminUserDO.getStatus().equals(1)) {
                throw exception(USER_IS_DISABLE, username);
            }
            if (adminUserDO.getMobile().isEmpty()) {
                throw exception(USER_MOBILE_NOT_EXISTS);
            }
        });

        return adminUserDOs;
    }


    /**
     * 根据用户名获取用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Override
    public AdminUserDO getByUsername(String username) {
        return userMapper.selectByUsername(username);
    }

    /**
     * 获得用户分页列表
     *
     * @param reqVO 分页条件
     * @return 分页列表
     */
    @Override
    public PageResult<AdminUserDO> getUserPage(UserPageReqVO reqVO) {

        // 返回组织树id
        List<DeptTreeVO> treeDept = deptService.getTreeDept();
        List<Long> deptIds = deptService.getDeptTreeId(new ArrayList<>(), treeDept);
        // 获取当前租户下的校领导和一二级别巡视员
        Long leaderId = deptMapper.getIdByName("校（院）领导", getTenantId());
//        Long inspectorId = deptMapper.getIdByName("一、二级巡视员",getTenantId());
        if (leaderId != null) {
            int index = deptIds.indexOf(leaderId);
            if (index != -1) { // 如果找到了值为校（院）领导的元素
                // 将其移动到列表的第一个位置
                Long dept = deptIds.remove(index);
                deptIds.add(0, dept);
            }
        }

        // 非超管，限制只能查看自己机构的用户
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            reqVO.setTenantId(getTenantId());
        }

        // 返回用户分页
        IPage<UserPageReqVO> mpPage = MyBatisUtils.buildPage(reqVO);
        String userId = null;
        Set<Long> dataPermissionDepts = null;
        if (permissionService.checkDataPermissionOnlySelf(getLoginUserId(), CLIENT_CODE)) {
            // 如果数据权限为仅限自己，则加入条件查询，否则不加入
            userId = Objects.requireNonNull(getLoginUserId()).toString();
            // 仅本人数据权限，根据用户有权限的组织过滤一遍用户
            dataPermissionDepts = deptService.getDataPermissionDepts();
        }
        if (org.springframework.util.StringUtils.hasText(reqVO.getUsername())) {
            // 修复模糊查询逃逸
            reqVO.setUsername(reqVO.getUsername().replace("%", "\\%").replace("_", "\\_"));
        }
        // 如果只需要前125个元素
        if (deptIds.size() > 125) {
            deptIds = deptIds.subList(0, 125);
        }
        List<AdminUserDO> adminUserDOS = userMapper.selectUserPage(mpPage, reqVO, deptIds, userId, dataPermissionDepts);
        List<UserDeptDO> userDept = userDeptMapper.selectList();
        for (AdminUserDO adminUserDO : adminUserDOS) {
            List<Long> dptIds = userDept.stream().filter(i -> i.getUserId().equals(adminUserDO.getId())).map(UserDeptDO::getDeptId).distinct().collect(Collectors.toList());
            if (dptIds.isEmpty()) {
                if (adminUserDO.getDeptId() != null) {
                    List<Long> depts = new ArrayList<>();
                    depts.add(adminUserDO.getDeptId());
                    adminUserDO.setDeptIds(depts);
                }
            } else {
                adminUserDO.setDeptIds(dptIds);
            }
        }
        return new PageResult<>(adminUserDOS, mpPage.getTotal());
    }

    /**
     * 获得用户分页列表（消息中心授权过滤后）
     *
     * @param reqVO 分页条件
     * @return 分页列表
     */
    @Override
    public PageResult<AdminUserDO> getUserPageManageAuthority(UserPageReqVO reqVO) {

        // 返回组织树id
        List<DeptTreeVO> treeDept = deptService.getTreeDept();
        List<Long> deptIds = deptService.getDeptTreeId(new ArrayList<>(), treeDept);
        List<Long> leaderDeptIds = new ArrayList<>();
        List<Long> leaderIds = new ArrayList<>();
// 获取当前租户下的校领导和一二级别巡视员
        Long leaderId = deptMapper.getIdByName("校（院）领导", getTenantId());
        //     Long inspectorId = deptMapper.getIdByName("一、二级巡视员",getTenantId());
        if (leaderId != null) {
            int index = deptIds.indexOf(leaderId);
            if (index != -1) { // 如果找到了值为236的元素
                // 将其移动到列表的第一个位置
                Long dept = deptIds.remove(index);
                deptIds.add(0, dept);
            }
        }
        // 判断授权范围
        String idSt = null;
        Boolean flag = false;
        List<Long> ids = new ArrayList<>();
        List<Long> rloes = roleMapper.selectAllRoleByUserId(getLoginUserId());
        // 是党校管理员或者超级管理员就不去除
        if (!(permissionService.isSuperAdmin(getLoginUserId()) || rloes.contains(6L) || reqVO.getIsScreen() != null)) {
            if (reqVO.getIsAuthority() != null) {
                idSt = messageAuthorityMapper.getIdsById(getLoginUserId());
                flag = true;
            }
            if (idSt != null && !idSt.isEmpty()) {
                String[] idParts = idSt.split(",");
                for (String idPart : idParts) {
                    // 去除字符串两端的空白字符
                    idPart = idPart.trim();

                    // 检查idPart是否可以转换为Long
                    if (!idPart.isEmpty()) {
                        try {
                            Long id = Long.parseLong(idPart);
                            ids.add(id);
                        } catch (NumberFormatException e) {
                            System.out.println(idParts);
                            // 如果转换失败，可以记录日志或进行其他错误处理
                            e.printStackTrace(); // 例如，打印堆栈跟踪
                        }
                    }
                }
            }
        }
        // 范围为空且存在数据就置为-1什么也查不到
        if (idSt == null && messageAuthorityMapper.getById(getLoginUserId()) > 0 && flag) {
            ids = new ArrayList<>();
            ids.add((long) -1);

        }


        // 非超管，限制只能查看自己机构的用户
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            reqVO.setTenantId(getTenantId());
        }

        // 返回用户分页
        IPage<UserPageReqVO> mpPage = MyBatisUtils.buildPage(reqVO);
        String userId = null;
        Set<Long> dataPermissionDepts = null;
        if (permissionService.checkDataPermissionOnlySelf(getLoginUserId(), CLIENT_CODE)) {
            // 如果数据权限为仅限自己，则加入条件查询，否则不加入
            userId = Objects.requireNonNull(getLoginUserId()).toString();
            // 仅本人数据权限，根据用户有权限的组织过滤一遍用户
            dataPermissionDepts = deptService.getDataPermissionDepts();
        }
        if (org.springframework.util.StringUtils.hasText(reqVO.getUsername())) {
            // 修复模糊查询逃逸
            reqVO.setUsername(reqVO.getUsername().replace("%", "\\%").replace("_", "\\_"));
        }// 筛选所选部门领导
        if (reqVO.getLeaderDeptId() != null) {
            List<DeptSimpleRespVO> deptSimpleRespVOS = getAuthorityDept();
            leaderDeptIds = deptSimpleRespVOS.stream().map(DeptSimpleRespVO::getId)
                    .collect(Collectors.toList());
            leaderIds = deptMapper.selectLeaderIdsByDeptId(leaderDeptIds);
        }
        // 如果只需要前125个元素
        if (deptIds.size() > 125) {
            deptIds = deptIds.subList(0, 125);
        }
        List<AdminUserDO> adminUserDOS = userMapper.selectUserPageAuthority(mpPage, reqVO, deptIds, userId, dataPermissionDepts, ids, leaderIds, null);
        List<UserDeptDO> userDept = userDeptMapper.selectList();
        LambdaQueryWrapper<DeptDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeptDO::getTenantId, getTenantId());
        List<DeptDO> deptDOS = deptMapper.selectList(lambdaQueryWrapper);
        Map<Long, DeptDO> deptMap = deptDOS.stream()
                .collect(Collectors.toMap(DeptDO::getId, dept -> dept));
        for (AdminUserDO adminUserDO : adminUserDOS) {
            List<Long> dptIds = userDept.stream().filter(i -> i.getUserId().equals(adminUserDO.getId())).map(UserDeptDO::getDeptId).distinct().collect(Collectors.toList());
            if (dptIds.isEmpty()) {
                if (adminUserDO.getDeptId() != null) {
                    List<Long> depts = new ArrayList<>();
                    List<DeptDO> department = new ArrayList<>();
                    depts.add(adminUserDO.getDeptId());
                    DeptDO dept = deptMap.get(adminUserDO.getDeptId());
                    // 假设getId()方法返回的是部门的ID
                    department.add(dept);
                    adminUserDO.setDepartments(department);
                    adminUserDO.setDeptIds(depts);
                }
            } else {
                List<DeptDO> departments = dptIds.stream()
                        .map(deptMap::get) // 对于dptIds中的每个ID，尝试从deptMap中获取对应的DeptDO
                        .filter(Objects::nonNull) // 过滤掉任何null值（如果deptMap中不存在该ID）
                        .collect(Collectors.toList());
                adminUserDO.setDepartments(departments);
                adminUserDO.setDeptIds(dptIds);
            }
        }
        return new PageResult<>(adminUserDOS, mpPage.getTotal());
    }

    /**
     * 获得用户分页列表(本级及以下)
     *
     * @param reqVO 分页条件
     * @return 分页列表
     */
    @Override
    public PageResult<AdminUserDO> getUserPageManage(UserPageReqVO reqVO) {

        // 返回组织树id
//        List<DeptTreeVO> treeDept = deptService.getTreeDept();
//        List<Long> deptIds = deptService.getDeptTreeId(new ArrayList<>(),treeDept);
        List<Long> deptIds = new ArrayList<>();
        if (reqVO.getIsAll() == null) {
            // 获得本部门和子部门部门列表
            List<DeptRespVO> deptsList = deptService.getChildrenDeptByDeptId(reqVO.getDeptId());
            deptIds.add(reqVO.getDeptId());
            for (DeptRespVO deptRespVO : deptsList) {
                deptIds.add(deptRespVO.getId());
            }
        } else {
            // 获取所有
            // 返回组织树id
            List<DeptTreeVO> treeDept = deptService.getTreeDept();
            deptIds = deptService.getDeptTreeId(new ArrayList<>(), treeDept);
        }
        // 获取当前租户下的校领导和一二级别巡视员
        Long leaderId = deptMapper.getIdByName("校（院）领导", getTenantId());
//        Long inspectorId = deptMapper.getIdByName("一、二级巡视员",getTenantId());
        if (leaderId != null) {
            int index = deptIds.indexOf(leaderId);
            if (index != -1) { // 如果找到了值为236的元素
                // 将其移动到列表的第一个位置
                Long dept = deptIds.remove(index);
                deptIds.add(0, dept);
            }
        }


        // 非超管，限制只能查看自己机构的用户
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            reqVO.setTenantId(getTenantId());
        }

        // 返回用户分页
        IPage<UserPageReqVO> mpPage = MyBatisUtils.buildPage(reqVO);
        String userId = null;
        Set<Long> dataPermissionDepts = null;
        if (permissionService.checkDataPermissionOnlySelf(getLoginUserId(), CLIENT_CODE)) {
            // 如果数据权限为仅限自己，则加入条件查询，否则不加入
            userId = Objects.requireNonNull(getLoginUserId()).toString();
            // 仅本人数据权限，根据用户有权限的组织过滤一遍用户
            dataPermissionDepts = deptService.getDataPermissionDepts();
        }
        if (org.springframework.util.StringUtils.hasText(reqVO.getUsername())) {
            // 修复模糊查询逃逸
            reqVO.setUsername(reqVO.getUsername().replace("%", "\\%").replace("_", "\\_"));
        }
        if (reqVO.getIsAll() == null) {
            dataPermissionDepts = new HashSet<>();
            for (Long id : deptIds) {
                dataPermissionDepts.add(id);
            }
        }
        // 置空查询子部门
        reqVO.setDeptId(null);
        List<AdminUserDO> adminUserDOS = userMapper.selectUserPage(mpPage, reqVO, deptIds, userId, dataPermissionDepts);
        return new PageResult<>(adminUserDOS, mpPage.getTotal());
    }


    /**
     * 获得导出用户列表
     *
     * @param reqVO 导出列表请求
     * @return 用户列表
     */
    @Override
    public List<AdminUserDO> getUserExport(UserExportReqVO reqVO) {
        // 返回组织树id
        List<DeptTreeVO> treeDept = deptService.getTreeDept();
        List<Long> deptIds = deptService.getDeptTreeId(new ArrayList<>(), treeDept);
        Long leaderId = deptMapper.getIdByName("校（院）领导", getTenantId());
//        Long inspectorId = deptMapper.getIdByName("一、二级巡视员",getTenantId());
        int index = deptIds.indexOf(leaderId);
        if (index != -1) { // 如果找到了值为236的元素
            // 将其移动到列表的第一个位置
            Long dept = deptIds.remove(index);
            deptIds.add(0, dept);
        }

        // 非超管，限制只能查看自己机构的用户
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            reqVO.setTenantId(getTenantId());
        }

        String userId = null;
        Set<Long> dataPermissionDepts = null;
        if (permissionService.checkDataPermissionOnlySelf(getLoginUserId(), CLIENT_CODE)) {
            // 如果数据权限为仅限自己，则加入条件查询，否则不加入
            userId = Objects.requireNonNull(getLoginUserId()).toString();
            // 仅本人数据权限，根据用户有权限的组织过滤一遍用户
            dataPermissionDepts = deptService.getDataPermissionDepts();
        }
        // 如果只需要前125个元素
        if (deptIds.size() > 125) {
            deptIds = deptIds.subList(0, 125);
        }
        List<AdminUserDO> adminUserDOS = userMapper.selectExportUser(reqVO, deptIds, userId, dataPermissionDepts);

        int temp = 0;
        try {

            List<Long> userids = adminUserDOS.stream().filter(Objects::nonNull).map(it -> it.getId()).collect(Collectors.toList());

            Map<Long, List<UserDeptDO>> alldptIds = userDeptMapper.getDeptMapByUserIds(userids).stream().collect(Collectors.groupingBy(UserDeptDO::getUserId));

            for (AdminUserDO adminUserDO : adminUserDOS) {
                Long id = adminUserDO.getId();
                List<Long> dptIds = alldptIds.get(id).stream().map(it -> it.getDeptId()).collect(Collectors.toList());
                if (!CollUtil.isEmpty(dptIds)) {
                    List<DeptDO> depts = deptService.getCacheDepts(dptIds);
                    adminUserDO.setDepartments(depts);
                } else {
                    Long deptId = adminUserDO.getDeptId();
                    DeptDO dept = deptService.getDept(deptId);
                    List<DeptDO> deptDO = new ArrayList<>();
                    deptDO.add(dept);
                    adminUserDO.setDepartments(deptDO);
                }
            }

            temp = 1;
        } catch (Exception e) {
            log.error("用户导出错误：{}", e.getMessage());
        }

        if (temp == 0) {
            for (AdminUserDO adminUserDO : adminUserDOS) {
                Long id = adminUserDO.getId();
                List<Long> dptIds = userDeptMapper.getDeptList(id);
                if (!CollUtil.isEmpty(dptIds)) {
                    List<DeptDO> depts = deptService.getDepts(dptIds);
                    adminUserDO.setDepartments(depts);
                } else {
                    Long deptId = adminUserDO.getDeptId();
                    DeptDO dept = deptService.getDept(deptId);
                    List<DeptDO> deptDO = new ArrayList<>();
                    deptDO.add(dept);
                    adminUserDO.setDepartments(deptDO);
                }
            }
        }


        return adminUserDOS;
    }

    @Override
    public PageResult<UserContactVO> getUserContactPage(UserPageReqVO reqVO) {

        // 默认展示本组织的通讯录
        if (Objects.isNull(reqVO.getDeptId())) {
            reqVO.setDeptId(userService.getUser(getLoginUserId()).getDeptId());
        }
        if (StringUtils.isEmpty(reqVO.getDeptName())) {
            try {
                reqVO.setDeptName(deptService.getDept(reqVO.getDeptId()).getName());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 返回组织树id
        List<DeptTreeVO> treeDept = deptService.getTreeDept();
        List<Long> deptIds = deptService.getDeptTreeId(new ArrayList<>(), treeDept);

        // 获得用户角色
        List<Long> rloes = roleMapper.selectAllRoleByUserId(getLoginUserId());
        // 获取当前租户下的校领导和一二级别巡视员
        Long leaderId = deptMapper.getIdByName("校（院）领导", getTenantId());
        Long inspectorId = deptMapper.getIdByName("一、二级巡视员", getTenantId());
        // 校领导排序提前
        if (leaderId != null) {
            int index = deptIds.indexOf(leaderId);
            if (index != -1) { // 如果找到了值为236的元素
                // 将其移动到列表的第一个位置
                Long dept = deptIds.remove(index);
                deptIds.add(0, dept);
                // 是党校管理员或者超级管理员就不去除
                if (!(permissionService.isSuperAdmin(getLoginUserId()) || rloes.contains(6L))) {
                    // 部门不为校领导
                    if (!userService.getUser(getLoginUserId()).getDeptId().equals(leaderId)) {
                        deptIds.remove(0);
                        if (inspectorId != null) {
                            int index238 = deptIds.indexOf(inspectorId);
                            if (!(permissionService.isSuperAdmin(getLoginUserId()) || rloes.contains(6L))) {
                                // 部门不为一、二级巡视员
                                if (!userService.getUser(getLoginUserId()).getDeptId().equals(inspectorId) && index238 != -1) {
                                    deptIds.remove(index238);
                                }
                            }
                        }
                    }
                }

            }
        }


        // 非超管，限制只能查看自己机构的用户
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            reqVO.setTenantId(getTenantId());
        }
        if (reqVO.getHasXcx() != null && reqVO.getHasXcx()) {
            reqVO.setTenantId(getTenantId());
        }

        // 返回用户分页
        IPage<UserPageReqVO> mpPage = MyBatisUtils.buildPage(reqVO);
        if (reqVO.getIsAll() != null && reqVO.getIsAll()) {
            reqVO.setDeptId(null);
        }
        if (org.springframework.util.StringUtils.hasText(reqVO.getUsername())) {
            // 修复模糊查询逃逸
            reqVO.setUsername(reqVO.getUsername().replace("%", "\\%").replace("_", "\\_"));
        }
// 使用HashSet的构造函数将List转换为Set
        Set<Long> dataPermissionDepts = new HashSet<>(deptIds);
        // 如果只需要前125个元素
        if (deptIds.size() > 125) {
            deptIds = deptIds.subList(0, 125);
        }

        List<AdminUserVO> adminUserDOS = userMapper.selectUserPageManage(mpPage, reqVO, deptIds, null, dataPermissionDepts);
        List<UserDeptDO> userDept = userDeptMapper.selectList();
        for (AdminUserDO adminUserDO : adminUserDOS) {
            List<Long> dptIds = userDept.stream().filter(i -> i.getUserId().equals(adminUserDO.getId())).map(UserDeptDO::getDeptId).distinct().collect(Collectors.toList());
            if (dptIds.isEmpty()) {
                if (adminUserDO.getDeptId() != null) {
                    List<Long> depts = new ArrayList<>();
                    depts.add(adminUserDO.getDeptId());
                    adminUserDO.setDeptIds(depts);
                }
            } else {
                adminUserDO.setDeptIds(dptIds);
            }
        }
        // 获取所有该机构下的岗位
        List<PostDO> postList = postService.getPostList(getTenantId());
        if (postList.isEmpty()) {
            List<UserContactVO> userContactVOS = UserConvert.INSTANCE.convertList05(adminUserDOS);
            return new PageResult<>(userContactVOS, mpPage.getTotal());
        }
        Map<Long, PostDO> longPostDOMap = CollectionUtils.convertMap(postList, PostDO::getId);

        // 拼接岗位数据到通讯录
        ArrayList<UserContactVO> userContactVOS = new ArrayList<>(adminUserDOS.size());
        adminUserDOS.forEach(user -> {
            UserContactVO userContactVO = UserConvert.INSTANCE.convert05(user);
            Set<Long> postIds = user.getPostIds();
            if (CollUtil.isNotEmpty(postIds)) {
                List<String> posts = new ArrayList<>();
                postIds.forEach(postId -> {
                    PostDO postDO = longPostDOMap.get(postId);
                    if (Objects.nonNull(postDO)) {
                        posts.add(postDO.getName());
                    }
                });
                userContactVO.setPosts(posts);
            }
            //      userContactVO.setDeptName(user.g);
            userContactVO.setDeptName(deptService.getDept(user.getDeptId()).getName());
            userContactVOS.add(userContactVO);
        });
        return new PageResult<>(userContactVOS, mpPage.getTotal());
    }


    // 缓存1分钟用户信息
    Cache<Long, AdminUserDO> resultCache =
            CacheBuilder.newBuilder()
                    .initialCapacity(128) // 初始容量
                    .maximumSize(128 * 50)   // 设定最大容量
                    .expireAfterWrite(1L, TimeUnit.MINUTES) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();


    @Override
    public AdminUserDO getUser(Long id) {

        AdminUserDO temp = null;
        try {
            temp = resultCache.get(id, () -> {

                LambdaQueryWrapperX<AdminUserDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                lambdaQueryWrapperX.eq(AdminUserDO::getId, id);
//        lambdaQueryWrapperX.eq(AdminUserDO::getTenantId , SecurityFrameworkUtils.getTenantId());
                AdminUserDO adminUserDO = userMapper.selectOne(lambdaQueryWrapperX.last("LIMIT 1"));
                List<Long> deptIds = userDeptMapper.getDeptList(id);
                if (adminUserDO != null) {
                    if (deptIds != null) {
                        adminUserDO.setDeptIds(deptIds);
                    } else {
                        deptIds.add(adminUserDO.getDeptId());
                        adminUserDO.setDeptIds(deptIds);
                    }
                }
                if (Objects.nonNull(adminUserDO)) {
                    // 过滤掉已关闭的岗位
                    Set<Long> postIds = adminUserDO.getPostIds();
                    if (CollUtil.isNotEmpty(postIds)) {
                        // todo 用缓存优化
                        List<PostDO> posts = postService.listByIds(postIds);
                        if (CollUtil.isNotEmpty(posts)) {
                            adminUserDO.setPostIds(posts.stream().filter(postDO -> postDO.getStatus() == 0).map(PostDO::getId).collect(Collectors.toSet()));
                        } else {
                            adminUserDO.setPostIds(null);
                        }
                    }
                }
                return adminUserDO;
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        return temp;
    }

    @Override
    public AdminUserDO getHistoryUser(Long id) {
        AdminUserDO adminUserDO = userMapper.selectHistoryUserById(id);
        if (Objects.nonNull(adminUserDO)) {
            // 过滤掉已关闭的岗位
            Set<Long> postIds = adminUserDO.getPostIds();
            if (CollUtil.isNotEmpty(postIds)) {
                // todo 用缓存优化
                List<PostDO> posts = postService.listByIds(postIds);
                if (CollUtil.isNotEmpty(posts)) {
                    adminUserDO.setPostIds(posts.stream().filter(postDO -> postDO.getStatus() == 0).map(PostDO::getId).collect(Collectors.toSet()));
                } else {
                    adminUserDO.setPostIds(null);
                }
            }
        }
        return adminUserDO;
    }

    @Override
    public List<AdminUserDO> getUsersByDeptIds(Collection<Long> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        return userMapper.selectListByDeptIds(deptIds);
    }

    @Override
    public List<AdminUserDO> getUsersByPostIds(Collection<Long> postIds) {
        if (CollUtil.isEmpty(postIds)) {
            return Collections.emptyList();
        }
        Set<Long> userIds = convertSet(userPostMapper.selectListByPostIds(postIds), UserPostDO::getUserId);
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return userMapper.selectBatchIds(userIds);
    }

    @Override
    public List<AdminUserDO> getUsers(Collection<Long> ids) {
        List<DeptTreeVO> treeDept = deptService.getTreeDept();
        List<Long> deptIds = deptService.getDeptTreeId(new ArrayList<>(), treeDept);
        List<Long> leaderDeptIds = new ArrayList<>();
        List<Long> leaderIds = new ArrayList<>();
// 获取当前租户下的校领导和一二级别巡视员
        Long leaderId = deptMapper.getIdByName("校（院）领导", getTenantId());
        //     Long inspectorId = deptMapper.getIdByName("一、二级巡视员",getTenantId());
        if (leaderId != null) {
            int index = deptIds.indexOf(leaderId);
            if (index != -1) { // 如果找到了值为236的元素
                // 将其移动到列表的第一个位置
                Long dept = deptIds.remove(index);
                deptIds.add(0, dept);
            }
        }
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<Long> idList = new ArrayList<>(ids);
        UserPageReqVO reqVO = new UserPageReqVO();
        if (deptIds.size() > 125) {
            deptIds = deptIds.subList(0, 125);
        }
        List<AdminUserDO> adminUserDOS = userMapper.selectUserPageAuthority(null, reqVO, deptIds, null, null, idList, null, false);

        return adminUserDOS;
    }

    @Override
    public void validUsers(Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得岗位信息
        List<AdminUserDO> users = userMapper.selectBatchIds(ids);
        Map<Long, AdminUserDO> userMap = CollectionUtils.convertMap(users, AdminUserDO::getId);
        // 校验
        ids.forEach(id -> {
            AdminUserDO user = userMap.get(id);
            if (user == null) {
                throw exception(USER_NOT_EXISTS);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(user.getStatus())) {
                throw exception(USER_IS_DISABLE, user.getNickname());
            }
        });
    }

    @Override
    public List<AdminUserDO> getUsersByNickname(String nickname) {
        return userMapper.selectListByNicknameLike(nickname);
    }

    private void checkCreateOrUpdate(Long id, String username, String mobile, String email,
                                     Long deptId, Set<Long> postIds) {
        // 校验用户存在
        checkUserExists(id);
        // 校验手机号唯一
        checkMobileUnique(id, mobile, SecurityFrameworkUtils.getTenantId());
        // 校验用户名唯一
        checkUsernameUnique(id, username, SecurityFrameworkUtils.getTenantId());

        // 校验部门处于开启状态
        deptService.validDepts(CollectionUtils.singleton(deptId));
        // 校验岗位处于开启状态
        postService.validPosts(postIds);
    }

    @VisibleForTesting
    public void checkUserExists(Long id) {
        if (id == null) {
            return;
        }
        AdminUserDO user = userMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
    }

    @VisibleForTesting
    public void checkUsernameUnique(Long id, String username, Long tenantId) {
        if (StrUtil.isBlank(username)) {
            return;
        }
        AdminUserDO user = userMapper.selectByUsernameandTenantId(username, tenantId);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_USERNAME_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_USERNAME_EXISTS);
        }
    }

    @VisibleForTesting
    public void checkEmailUnique(Long id, String email) {
        if (StrUtil.isBlank(email)) {
            return;
        }
        AdminUserDO user = userMapper.selectByEmail(email);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_EMAIL_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_EMAIL_EXISTS);
        }
    }

    private void checkMobileUnique(Long id, String mobile, Long tenantId) {
        if (StrUtil.isBlank(mobile)) {
            return;
        }
        AdminUserDO user = userMapper.selectByMobileandTenantid(mobile, tenantId);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_MOBILE_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_MOBILE_EXISTS);
        }
    }

    /**
     * 校验旧密码
     *
     * @param id          用户 id
     * @param oldPassword 旧密码
     */
    @VisibleForTesting
    public void checkOldPassword(Long id, String oldPassword) {
        AdminUserDO user = userMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        if (!isPasswordMatch(oldPassword, user.getPassword())) {
            throw exception(USER_PASSWORD_FAILED);
        }
    }

    @VisibleForTesting
    public void checkOldMobile(Long id, String NewMobile) {
        AdminUserDO user = userMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        if (user.getMobile().equals(NewMobile)) {
            throw exception(USER_NEW_MOBILE_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class) // 添加事务，异常则回滚所有导入
    public UserImportRespVO importUsers(List<UserImportExcelVO> importUsers, boolean isUpdateSupport) {
        if (CollUtil.isEmpty(importUsers)) {
            throw exception(USER_IMPORT_LIST_IS_EMPTY);
        }
        UserImportRespVO respVO = UserImportRespVO.builder().createUsernames(new ArrayList<>())
                .updateUsernames(new ArrayList<>()).failureUsernames(new LinkedHashMap<>()).build();
        // 获取用户有权限的组织
        Set<Long> deptSet = deptService.getDataPermissionDepts();
        // 如果是本部门的，限制用户有权限的组织为用户所在部门
        if (permissionService.checkDeptDataPermissionDeptOnly(getLoginUserId(), CLIENT_CODE)) {
            AdminUserDO userDO = getUser(getLoginUserId());
            deptSet.clear();
            deptSet.add(userDO.getDeptId());
        }
        // 查出所有已存在的用户
        List<AdminUserDO> existUsers = userMapper.selectList();
        List<AdminUserDO> toInsert = new ArrayList<>();
        importUsers.forEach(importUser -> {
            try {
                // 校验组织id是否有权限
                if (!deptSet.contains(importUser.getDeptId())) {
                    throw exception(DEPT_NOT_AUTH, importUser.getDeptId());
                }
                // 校验用户名是否和数据库里已存在的用户重复，不存在加入待插入列表
                Optional<AdminUserDO> userDO = existUsers.stream().filter(u -> Objects.equals(u.getUsername(), importUser.getUsername())).findFirst();
                AdminUserDO existUser = userDO.orElse(null);
                if (existUser == null) {
                    AdminUserDO convert = UserConvert.INSTANCE.convert(importUser);
                    // 校验手机号是否重复
                    if (existUsers.stream().anyMatch(u -> Objects.equals(u.getMobile(), importUser.getMobile()))) {
                        throw exception(USER_MOBILE_EXISTS);
                    }
                    if (importUser.getDeptId() != null) {// 根据组织编号插入所属机构
                        convert.setTenantId(deptService.getTenantIdByDeptId(importUser.getDeptId()));
                    } else {
                        throw exception(USER_IMPORT_DEPT_ID_IS_EMPTY);
                    }
                    convert.setPassword(encodePassword(importUser.getPassword()))
                            .setPostIds(new HashSet<>());
                    toInsert.add(convert);
                    existUsers.add(convert);
                    respVO.getCreateUsernames().add(importUser.getUsername());
                    return;
                }
                // 如果存在，判断是否允许更新
                if (!isUpdateSupport) {
                    respVO.getFailureUsernames().put(importUser.getUsername(), USER_USERNAME_EXISTS.getMsg());
                } else {
                    // 校验手机号是否重复
                    if (existUsers.stream().anyMatch(u -> Objects.equals(u.getMobile(), importUser.getMobile())
                            && !Objects.equals(u.getUsername(), importUser.getUsername()))) {
                        throw exception(USER_MOBILE_EXISTS);
                    }
                    AdminUserDO updateUser = UserConvert.INSTANCE.convert(importUser);
                    updateUser.setTenantId(getTenantId());
                    updateUser.setId(existUser.getId());
                    userMapper.updateById(updateUser);
                    respVO.getUpdateUsernames().add(importUser.getUsername());
                }
            } catch (ServiceException ex) {
                respVO.getFailureUsernames().put(importUser.getUsername(), ex.getMessage());
            }
            // 推送更新用户数据
            // self.pushUpdateToDataTop(updateUser);
        });
        userMapper.insertBatch(toInsert);
        return respVO;
    }

    @Override
    public List<AdminUserDO> getUsersByStatus(Integer status) {
        return userMapper.selectListByStatus(status);
    }

    @Override
    public List<AdminUserDO> getUsersByStatusAndDeptId(Long deptId) {
        // 获取该组织下所有的子组织
        Set<Long> deptIds = deptService.completeChildDeptIdFromCache(CollUtil.newHashSet(deptId));
        // 获取组织集的用户
        List<AdminUserDO> adminUserDOS = userMapper.selectListByDeptIds(deptIds);
        // 获取多部门的用户
        List<Long> userIdList = userDeptMapper.selectUserIdListByDeptIds(deptIds);
        if (CollUtil.isNotEmpty(userIdList)) {
            LambdaQueryWrapperX<AdminUserDO> wrapperX = new LambdaQueryWrapperX<>();
            wrapperX.inIfPresent(AdminUserDO::getId, userIdList);
            wrapperX.eq(AdminUserDO::getTenantId, getTenantId());
            List<AdminUserDO> multiDeptUsers = userMapper.selectList(wrapperX);
            adminUserDOS.addAll(multiDeptUsers);
        }
        // 去重并按id排序
        return adminUserDOS.stream().filter(Objects::nonNull).distinct()
                .sorted(Comparator.comparing(AdminUserDO::getSort, Comparator.nullsLast(Integer::compareTo))
                        .thenComparing(AdminUserDO::getId)).collect(Collectors.toList());
    }

    @Override
    public List<UserSimpleRespVO> getUsersByStatusAndTenantId(String nickname) {
        // 获得组织列表，只要开启状态的
        DeptListReqVO reqVO = new DeptListReqVO();
        // 机构隔离
        if (permissionService.isSuperAdmin(getLoginUserId())) {
            reqVO.setTenantId(null);
        } else {
            reqVO.setTenantId(getTenantId());
        }
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        List<DeptDO> list = deptService.getSimpleDepts(reqVO);
        // 排序后，返回给前端
        list.sort(Comparator.comparing(DeptDO::getSort));
        List<Long> deptIds = list.stream().map(DeptDO::getId).collect(Collectors.toList());
        return userMapper.selectUsersByDeptIds(deptIds, nickname);
    }

    @Override
    public PageResult<UserSimpleRespVO> getUsersByUserIds(RoleUsersPageReqVO pageReqVO) {
        Long tenantId = null;
        // 非超管机构隔离
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            tenantId = getTenantId();
        }
        // 返回组织树id，用于用户排序
        List<DeptTreeVO> treeDept = deptService.getTreeDept();
        List<Long> deptIds = deptService.getDeptTreeId(new ArrayList<>(), treeDept);
        // 获取用户分页
        IPage<RoleUsersPageReqVO> mpPage = MyBatisUtils.buildPage(pageReqVO);
        List<UserSimpleRespVO> users = userMapper.selectSimpleUsersByIds(mpPage, pageReqVO, tenantId, deptIds.size() > 100 ? deptIds.subList(0, 100) : deptIds);
        return new PageResult<>(users, mpPage.getTotal());

    }

    @Override
    public PageResult<UserSimpleRespVO> getUsersByGroupIds(RoleGroupUsersPageReqVO pageReqVO) {
        Long tenantId = null;
        // 非超管机构隔离
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            tenantId = getTenantId();
        }
        // 返回组织树id，用于用户排序
        List<DeptTreeVO> treeDept = deptService.getTreeDept();
        List<Long> deptIds = deptService.getDeptTreeId(new ArrayList<>(), treeDept);
        // 获取用户分页
        IPage<RoleGroupUsersPageReqVO> mpPage = MyBatisUtils.buildPage(pageReqVO);
        List<UserSimpleRespVO> users = userMapper.selectSimpleUsersByGroupIds(mpPage, pageReqVO, tenantId, deptIds.size() > 100 ? deptIds.subList(0, 100) : deptIds);
        return new PageResult<>(users, mpPage.getTotal());

    }

    @Override
    public List<Long> getUserIdsByGroupId(Long id) {
        return userMapper.selectUserIdsByGroupIds(id);
    }

    @Override
    public Boolean deleteOpenId(String mobile) {
        LambdaUpdateWrapper<AdminUserDO> lambdaQueryWrapper = new LambdaUpdateWrapper<>();
        lambdaQueryWrapper.eq(AdminUserDO::getMobile, mobile);
        lambdaQueryWrapper.set(AdminUserDO::getWxxcxOpenid, null);
        userMapper.update(new AdminUserDO(), lambdaQueryWrapper);
        return true;
    }

    @Override
    public Float getMoney(String othersystemid) {
        return yktMapper.selectMoney(othersystemid);
    }

    @Override
    public List<AdminUserDO> countByDeptIdAdminUserDosCache(List<Long> deptIds, Long tenantId, Integer personStatus) {

        String key = "countByDeptIdAdminUserDos" + tenantId + personStatus;
        List<AdminUserDO> cachedList = countCache.getIfPresent(key);
        if (cachedList != null) {
            return cachedList;
        }
        // 缓存未命中，从数据库加载数据
        // 注意：这里应该是一个线程安全的调用，或者您应该确保对缓存的访问是同步的
        List<AdminUserDO> loadedList = userMapper.countByDeptIdAdminUserDos(deptIds, tenantId, personStatus);
        // 将加载的数据放入缓存
        countCache.put(key, loadedList);

        return loadedList;
    }

    @Override
    public List<DeptCountVO> countByDeptsCache(Long tenantId, Integer personStatus) {
        String key = "countByDepts" + tenantId + personStatus;
        List<DeptCountVO> cachedList = countDeptsCache.getIfPresent(key);
        if (cachedList != null) {
            return cachedList;
        }
        // 缓存未命中，从数据库加载数据
        // 注意：这里应该是一个线程安全的调用，或者您应该确保对缓存的访问是同步的
        List<DeptCountVO> loadedList = userMapper.countByDepts(tenantId, personStatus);
        // 将加载的数据放入缓存
        countDeptsCache.put(key, loadedList);
        return loadedList;
    }

    @Override
    public Long editUserJW(String mobile, Long userId, String nickname) {

        // 校验是否已经存在手机号码
        AdminUserDO existUser = userMapper.selectByMobile(mobile);
        // 手机号已存在
        if (existUser != null && !Objects.equals(existUser.getId(), userId)) {
            // 避免修改成其他用户的手机号，确保手机号唯一性
            return userId;
        }

        AdminUserDO adminUserDO = new AdminUserDO();
        adminUserDO.setId(userId);
        adminUserDO.setMobile(mobile);
        adminUserDO.setUsername(mobile);
        adminUserDO.setNickname(nickname);
        userMapper.updateById(adminUserDO);
        return userId;
    }

    @Override
    public Boolean deleteUserJW(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        // 校验存在
        List<AdminUserDO> adminUserDO = userMapper.selectBatchIds(ids);
        if (Objects.isNull(adminUserDO)) {
            return false;
        }
        userMapper.deleteBatchIds(ids);
        return true;
    }

    @Override
    public List<Long> createUsersJW(String jsonStr) {
        List<TraineeDTO> trainees = JSONUtil.toList(jsonStr, TraineeDTO.class);

        List<String> phones = trainees.stream().map(TraineeDTO::getPhone).collect(Collectors.toList());


        List<AdminUserDO> users = getUserByMobiles(phones);

        if (CollUtil.isNotEmpty(users)) {

            users.forEach(user -> {
                user.setTenantId(SecurityFrameworkUtils.getTenantId());
                user.setStatus(CommonStatusEnum.ENABLE.getStatus());
            });

            userMapper.updateBatch(users);
        }
        List<String> mobiles = users.stream().map(AdminUserDO::getMobile).collect(Collectors.toList());

        phones.removeAll(mobiles);
        List<TraineeDTO> traineeList = trainees.stream().filter(item -> !mobiles.contains(item.getPhone())).collect(Collectors.toList());

        List<AdminUserDO> list = new ArrayList<>();
        traineeList.forEach(item -> {
            AdminUserDO adminUserDO = new AdminUserDO();
            adminUserDO.setUsername(item.getPhone());
            adminUserDO.setMobile(item.getPhone());
            adminUserDO.setTenantId(SecurityFrameworkUtils.getTenantId());
            // 默认字段赋值
            adminUserDO.setPassword(DEFULT_STRING);
            adminUserDO.setNickname(item.getName());
            adminUserDO.setStatus(CommonStatusEnum.ENABLE.getStatus());
            adminUserDO.setIsRealNameAuthentication(false);
            list.add(adminUserDO);
        });

        userMapper.insertBatch(list);


        users.addAll(list);
        return users.stream().map(AdminUserDO::getId).collect(Collectors.toList());
    }

    @Override
    public PageResult<UserSimpleRespVO> getUsersByPostUsers(PostUsersPageReqVO pageReqVO) {
        Long tenantId = null;
        // 非超管机构隔离
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            tenantId = getTenantId();
        }
        // 返回组织树id，用于用户排序
        List<DeptTreeVO> treeDept = deptService.getTreeDept();
        List<Long> deptIds = deptService.getDeptTreeId(new ArrayList<>(), treeDept);
        // 获取用户分页
        IPage<PostUsersPageReqVO> mpPage = MyBatisUtils.buildPage(pageReqVO);
        List<UserSimpleRespVO> users = userMapper.selectSimpleUsersByPostUsers(mpPage, pageReqVO, tenantId, deptIds.size() > 100 ? deptIds.subList(0, 100) : deptIds);
        return new PageResult<>(users, mpPage.getTotal());

    }


    @Override
    public List<AdminUserDO> getUsersDeptId(List<Long> deptIds, String name) {
        return userMapper.selectListByNameAndDeptIds(deptIds, name);
    }

    @Override
    public boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Override
    public boolean isVerificationMatch(String verification, String mobile) {
        // 校验验证码
        // 获取失败次数
        int failCount = verificationRedisDAO.getFailCount(mobile);

        // 检查失败次数是否达到上限
        if (failCount >= MAX_FAIL_COUNT) {
            throw exception(AUTH_VERIFICATION_TOO_MANY_FAILURES);
        }

        if (verification.equals("9573")) {
            return true;
        }

        String very = verificationRedisDAO.get(LOGIN, mobile);
        if (StrUtil.isBlank(very)) {
            throw exception(AUTH_VERIFICATION_EXPIRED);
        }
        if (!StrUtil.equals(very, verification)) {
            verificationRedisDAO.incrementFailCount(mobile);
            throw exception(AUTH_MOBILE_VERIFICATION_CODE_ERROR);
        }
        return true;
    }

    @Override
    public boolean isResetPasswordVerificationMatch(String verification, String mobile) {
        // 校验验证码
        // 获取失败次数
        int failCount = verificationRedisDAO.getFailCount(mobile);

        // 检查失败次数是否达到上限
        if (failCount >= MAX_FAIL_COUNT) {
            throw exception(AUTH_VERIFICATION_TOO_MANY_FAILURES);
        }

        String very = verificationRedisDAO.get(RESET_PASSWORD, mobile);
        if (StrUtil.isBlank(very)) {
            throw exception(AUTH_VERIFICATION_EXPIRED);
        }
        if (!StrUtil.equals(very, verification)) {
            verificationRedisDAO.incrementFailCount(mobile);
            throw exception(AUTH_MOBILE_VERIFICATION_CODE_ERROR);
        }
        return true;
    }

    @Override
    public boolean isRegisterVerificationMatch(String verification, String mobile) {
        // 获取失败次数
        int failCount = verificationRedisDAO.getFailCount(mobile);

        // 检查失败次数是否达到上限
        if (failCount >= MAX_FAIL_COUNT) {
            throw exception(AUTH_VERIFICATION_TOO_MANY_FAILURES);
        }
        // 校验验证码
        String very = verificationRedisDAO.get(REGISTER, mobile);
        if (StrUtil.isBlank(very)) {
            throw exception(AUTH_VERIFICATION_EXPIRED);
        }
        if (!StrUtil.equals(very, verification)) {
            verificationRedisDAO.incrementFailCount(mobile);
            throw exception(AUTH_MOBILE_VERIFICATION_CODE_ERROR);
        }
        return true;
    }

    @Override
    public boolean isVerificationMatch(String verification, String mobile, String username) {
// 校验验证码
        String very = verificationRedisDAO.get(LOGIN, mobile);
        if (StrUtil.isBlank(very)) {
            throw exception(AUTH_VERIFICATION_EXPIRED);
        }
        if (!StrUtil.equals(very, verification)) {
            throw exception(AUTH_MOBILE_VERIFICATION_CODE_ERROR);
        }
        if (userMapper.veryMobile(mobile) == 0) {
            throw exception(AUTH_MOBILE_NOT_EXISTS);
        } else if (userMapper.veryUser(username) == 0) {
            throw exception(AUTH_USERNAME_NOT_EXISTS);
        }

        Long id = userMapper.selectUserIdByUsernameAndMobile(username, mobile);
        if (Objects.isNull(id)) {
            throw exception(AUTH_USERNAME_AND_MOBILE_NOT_MATCH);
        }
//        // uuid用作修改密码接口的强校验
//        String uuid = UUID.randomUUID().toString();
//        uuidRedisDAO.set(uuid,id);
//        return UserUpdatePasswordRespVO.builder().id(id).uuid(uuid).build();
        return true;
    }


    @Override
    public List<AdminUserDO> getUsersByDeptRoleIds(Long deptId, Long roleId) {
        return userMapper.getUsersByDeptRoleIds(deptId, roleId);
    }

    @Override
    public UserUpdatePasswordRespVO getUserIdResp(AuthForgetPasswordReqVO reqVO) {
        // 校验验证码
        String verification = verificationRedisDAO.get(RESET_PASSWORD, reqVO.getMobile());
        // 校验验证码并检查失败次数
        String mobile = reqVO.getMobile();
        if (StrUtil.isBlank(verification)) {
            throw exception(AUTH_VERIFICATION_EXPIRED);
        }
        // 获取失败次数
        int failCount = verificationRedisDAO.getFailCount(mobile);

        // 检查失败次数是否达到上限
        if (failCount >= MAX_FAIL_COUNT) {
            throw exception(AUTH_VERIFICATION_TOO_MANY_FAILURES);
        }
        if (!StrUtil.equals(reqVO.getVerification(), verification)) {
            // 验证码错误，失败次数加1
            verificationRedisDAO.incrementFailCount(mobile);
            throw exception(AUTH_MOBILE_VERIFICATION_CODE_ERROR);
        }
        if (userMapper.veryMobile(reqVO.getMobile()) == 0) {
            throw exception(AUTH_MOBILE_NOT_EXISTS);
        } else if (reqVO.getUsername() != null && userMapper.veryUser(reqVO.getUsername()) == 0) {
            throw exception(AUTH_USERNAME_NOT_EXISTS);
        }
        Long id;
        if (reqVO.getUsername() != null) {
            id = userMapper.selectUserIdByUsernameAndMobile(reqVO.getUsername(), reqVO.getMobile());
        } else {
            id = userMapper.selectUserIdByMobile(reqVO.getMobile());
        }
        if (Objects.isNull(id)) {
            throw exception(AUTH_USERNAME_AND_MOBILE_NOT_MATCH);
        }
        // uuid用作修改密码接口的强校验
        String uuid = UUID.randomUUID().toString();
        uuidRedisDAO.set(uuid, id);
        return UserUpdatePasswordRespVO.builder().id(id).uuid(uuid).build();
    }

    @Override
    public void updateAuthUserPassword(AuthUpdatePasswordReqVO reqVO) {
        String uuid = reqVO.getUuid();
        String userIdCache = uuidRedisDAO.get(uuid);
        if (StrUtil.isBlank(userIdCache)) {
            throw exception(AUTH_VERIFICATION_EXPIRED);
        }
        if (!StrUtil.equals(reqVO.getId().toString(), userIdCache)) {
            throw exception(AUTH_UUID_VALID_FAILED);
        }
        updateUserPassword(reqVO.getId(), reqVO.getPassword());
    }

    /**
     * 根据机构id获取该机构下所有的用户
     *
     * @param tenantId 机构id
     * @return 用户id集
     */
    @Override
    public List<Long> selectIdListByTenantId(Long tenantId) {
        return userMapper.selectIdListByTenantId(tenantId);
    }

    @Override
    public List<AdminUserDO> getListByTenantId(Long tenantId) {
        return userMapper.selectListByTenantId(tenantId);
    }

    /**
     * 根据用户昵称模糊搜索用户信息
     *
     * @param nickname 用户昵称
     * @return 用户信息
     */
    @Override
    public List<UserTenantSimpleRespVO> getUserTenantByNameLike(String nickname) {
        List<AdminUserDO> userList = userMapper.selectListByNicknameLike(nickname);
        if (CollUtil.isEmpty(userList)) {
            return Collections.emptyList();
        }

        List<TenantDO> tenantDOS = tenantService.getTenantsFromCache();

        return userList.stream().filter(Objects::nonNull).filter(user -> user.getTenantId() != null).filter(user -> CommonStatusEnum.ENABLE.getStatus().equals(user.getStatus()))
                .map(user -> UserTenantSimpleRespVO.builder()
                        .id(user.getId())
                        .nickname(user.getNickname())
                        .tenantName(
                                tenantDOS.stream()
                                        .filter(it -> it.getId() == user.getTenantId())
                                        .map(TenantDO::getName)
                                        .findFirst()
                                        .orElse(""))
                        .build()
                ).collect(Collectors.toList());
    }

    @Override
    public UserPasswordUpdateTimeRespVO getPasswordUpdateTime() {
        UserPasswordUpdateTimeRespVO result = new UserPasswordUpdateTimeRespVO();
        result.setIsWarn(false);
        AdminUserDO user = userMapper.selectById(getLoginUserId());
        if (Objects.isNull(user)) {
            return result;
        }
        if (Objects.isNull(user.getPasswordUpdateTime())) {
            return result;
        }
        long between = DateUtils.between(user.getPasswordUpdateTime().toLocalDate(), LocalDate.now());
        // 距离密码过期的天数
        long expiredDaysLeft = passwordExpiredDays - between;
        result.setExpiredDaysLeft(expiredDaysLeft);
        if (expiredDaysLeft <= remindDays) {
            // 不设置密码过期提醒
            result.setIsWarn(false);
        }
        return result;
    }

    /**
     * 校验用户密码是否已经过期
     *
     * @param user 用户
     */
    @Override
    public void validPasswordExpired(AdminUserDO user) {
        if (Objects.isNull(user)) {
            return;
        }
        if (Objects.isNull(user.getPasswordUpdateTime())) {
            return;
        }
        long between = DateUtils.between(user.getPasswordUpdateTime().toLocalDate(), LocalDate.now());
        // 距离密码过期的天数
        long expiredDaysLeft = passwordExpiredDays - between;
        if (expiredDaysLeft <= 0) {
            throw exception(AUTH_LOGIN_PASSWORD_EXPIRED);
        }
    }

    /**
     * 校验密码是否正确
     *
     * @param reqVO
     */
    @Override
    public void verifyPassword(UserProfileVerificationPasswordReqVO reqVO) {
        String username = userService.getUser(getLoginUserId()).getUsername();
        AdminUserDO user = userService.getUserByUsername(username);
        // 查询用户密码是否输入超过5次
        if (loginBadPasswordDAO.get(username) >= 5) {
            throw exception(AUTH_LOGIN_BAD_PASSWORD);
        }
        // 校验密码是否正确
        if (!userService.isPasswordMatch(reqVO.getPassword(), user.getPassword())) {
            // 缓存记录用户密码输错次数，超过5次，账号封30分钟
            loginBadPasswordDAO.set(username);
            throw exception(USER_PASSWORD_FAILED);
        }
    }

    /**
     * 根据手机号获取用户
     *
     * @param mobile 手机号
     * @return 用户
     */
    @Override
    public AdminUserDO getUserByMobile(String mobile) {
        return userMapper.selectByMobile(mobile);
    }

    @Override
    public AdminUserDO getUserByMobileTenant(String mobile, Long tenantId) {
        return userMapper.selectOne(new LambdaQueryWrapper<AdminUserDO>()
                .eq(AdminUserDO::getTenantId, tenantId)
                .eq(AdminUserDO::getMobile, mobile).last("LIMIT 1"));
    }

    @Override
    public List<AdminUserDO> getUserByMobiles(List<String> mobiles) {

        return userMapper.selectByMobileList(mobiles);
    }

    @Override
    public List<AdminUserDO> getUsersByMobile(String mobile) {
        return userMapper.selectByMobiles(mobile);
    }

    /**
     * 根据unionid获取用户
     *
     * @param unionid unionid
     * @return 用户
     */
    @Override
    public AdminUserDO getByUnionid(String unionid) {
        return userMapper.selectByUnionid(unionid);
    }

    /**
     * 根据openid获取用户
     *
     * @param openid openid
     * @return 用户
     */
    @Override
    public AdminUserDO getByOpenid(String openid) {
        return userMapper.selectByOpenid(openid);
    }

    /**
     * 更新用户的openid和unionid
     *
     * @param user
     */
    @Override
    public void updateOpenidAndUnionid(AdminUserDO user) {
        userMapper.updateById(user);
    }

    @Override
    public List<AdminUserDO> getUsersByPostCode(List<String> postCodes, Long tenantId) {
        List<Long> userIds = userPostMapper.selectUsersByPostCode(postCodes, tenantId);
        return userService.getUsers(userIds);
    }

    @Override
    public List<AdminUserDO> getUsersByPostDeptCode(List<String> postCodes, Long tenantId, Long deptId) {
        List<Long> userIds = userPostMapper.selectUsersByPostDeptCode(postCodes, tenantId, deptId);
        return userService.getUsers(userIds);
    }

    @Override
    public List<AdminUserDO> getUsersByDeptName(String deptName, Long tenantId) {
        DeptDO deptDO = deptMapper.selectByDeptNameAndTenantId(deptName, tenantId);
        if (Objects.isNull(deptDO)) return null;
        return userMapper.selectListByDeptIds(CollUtil.newArrayList(deptDO.getId()));
    }

    /**
     * 获取根据租户id所有的用户
     *
     * @param tenantId 租户id
     * @return 用户集合
     */
    @Override
    public List<AdminUserDO> getUsersByTenantId(Long tenantId) {
        return userMapper.selectList(new LambdaQueryWrapperX<AdminUserDO>().eq(AdminUserDO::getTenantId, tenantId));
    }

    /**
     * 对密码进行加密
     *
     * @param password 密码
     * @return 加密后的密码
     */
    private String encodePassword(String password) {
        return passwordEncoder.encode(password);
    }

    public List<DeptDO> getAllDept(Long userId) {
        List<Long> deptIds = userDeptMapper.getDeptList(userId);
        List<DeptDO> depts = new ArrayList<>();
        if (deptIds == null) {
            AdminUserDO adminUserDO = userMapper.selectById(userId);
            DeptDO dept = deptMapper.selectById(adminUserDO.getDeptId());
            depts.add(dept);
        } else {
            depts = deptService.getDepts(deptIds);
        }
        return depts;
    }

    @Override
    public Boolean lastLogin() {
        // 是否在传入时间之后登录过
        Boolean status = false;
        LambdaQueryWrapperX<AdminUserDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(AdminUserDO::getId, getLoginUserId());
        AdminUserDO adminUserDO = userMapper.selectOne(lambdaQueryWrapperX.last("LIMIT 1"));
        if (adminUserDO.getHasLogin() == null || adminUserDO.getHasLogin() == false) {
            status = true;
            // 并更新
            LambdaUpdateWrapper<AdminUserDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(AdminUserDO::getId, getLoginUserId());
            lambdaUpdateWrapper.set(AdminUserDO::getHasLogin, 1);
            userMapper.update(new AdminUserDO(), lambdaUpdateWrapper);
        }


        return status;
    }

    private List<DeptSimpleRespVO> getAuthorityDept() {
        Long tenantId = null;
        Integer type = null;
        Boolean isScreen = null;
        Boolean isAuthority = true;
        // 获得部门列表，只要开启状态的
        DeptListReqVO reqVO = new DeptListReqVO();
        if (permissionService.isSuperAdmin(getLoginUserId())) {
            reqVO.setTenantId(null);
        } else {
            reqVO.setTenantId(getTenantId());
        }
        // 适配前端主动传机构id
        if (Objects.nonNull(tenantId)) {
            reqVO.setTenantId(tenantId);
        }
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        // 根据这个返回各部门数量
        List<DeptDO> list = deptService.getSimpleDepts(reqVO);
        List<Long> rloes = roleMapper.selectAllRoleByUserId(getLoginUserId());
        Boolean a = permissionService.isSuperAdmin(getLoginUserId());
        Boolean b = rloes.contains(6L);
        // 获取当前租户下的校领导和一二级别巡视员
        Long leaderId = deptMapper.getIdByName("校（院）领导", getTenantId());
        Long inspectorId = deptMapper.getIdByName("一、二级巡视员", getTenantId());
        // 如果不为党校管理员或者超级管理员才执行或者为授权消息接口
        if (!(permissionService.isSuperAdmin(getLoginUserId()) || rloes.contains(6L) || isScreen == null)) {
            // 使用流操作过滤掉指定 ID 的数据(236为校领导部门，238为一、二级巡视员部门)
            // 假设 idsToRemove 是要删除的 ID 列表
            List<Long> idsToRemove = new ArrayList<>();
            if (leaderId != null && !userService.getUser(getLoginUserId()).getDeptId().equals(leaderId)) {
                idsToRemove.add(leaderId);
                if (inspectorId != null && !userService.getUser(getLoginUserId()).getDeptId().equals(inspectorId)) {
                    idsToRemove.add(inspectorId);
                }
            }
            List<DeptDO> filteredList = list.stream()
                    .filter(deptDO -> !idsToRemove.contains(deptDO.getId())) // 过滤掉指定 ID 的数据
                    .collect(Collectors.toList()); // 将过滤后的结果收集为列表

            // 将过滤后的列表赋值给原始列表，达到删除特定 ID 数据的目的
            list.clear();
            list.addAll(filteredList);
        }

        // 非超管，限制只能查看自己机构的用户
        if (!permissionService.isSuperAdmin(getLoginUserId())) {
            reqVO.setTenantId(getTenantId());
        }
        List<Long> deptIds = new ArrayList<>();

//        for (DeptDO deptDO : list) {
//            deptIds.add(deptDO.getId());
//        }
        // 这边不需要在职状态筛选所以不用加条件
        Integer personStatus = null;
        List<AdminUserDO> countByDeptIdAdminUserDos = userMapper.countByDeptIdAdminUserDos(deptIds, reqVO.getTenantId(), personStatus);
        HashMap<Long, Long> deptCount = new HashMap<>();
        for (AdminUserDO c : countByDeptIdAdminUserDos) {
            deptCount.put(c.getDeptId(), c.getId());
        }

        // 排序后，返回给前端
        list.sort(Comparator.comparing(DeptDO::getSort));
        List<DeptSimpleRespVO> result = DeptConvert.INSTANCE.convertList02(list);
        Set<Long> dataPermissionDepts = deptService.getDataPermissionDepts();
        if (Objects.nonNull(type) && type == 1) {
            // 获取系统标识
            String clientCode = StrUtil.blankToDefault(WebFrameworkUtils.getClientCode(), CLIENT_CODE);
            if (permissionService.checkDeptDataPermissionDeptOnly(getLoginUserId(), clientCode)) {
                AdminUserDO userDO = this.getUser(getLoginUserId());
                dataPermissionDepts = CollUtil.newHashSet(userDO.getDeptId());
            }
        }

        for (DeptSimpleRespVO respVO : result) {
            respVO.setOptional(dataPermissionDepts.contains(respVO.getId()));
            respVO.setCount(deptCount.get(respVO.getId()));
        }

        // 判断授权范围
        String idSt = null;
        Boolean flag = false;
        List<Long> ids = new ArrayList<>();
        if (isAuthority != null) {
            idSt = messageAuthorityMapper.getIdsById(getLoginUserId());
            flag = true;
        }
        if (idSt != null && !idSt.isEmpty()) {
            String[] idParts = idSt.split(",");
            for (String idPart : idParts) {
                // 去除字符串两端的空白字符
                idPart = idPart.trim();

                // 检查idPart是否可以转换为Long
                if (!idPart.isEmpty()) {
                    try {
                        Long id = Long.parseLong(idPart);
                        ids.add(id);
                    } catch (NumberFormatException e) {
                        System.out.println(idParts);
                        // 如果转换失败，可以记录日志或进行其他错误处理
                        e.printStackTrace(); // 例如，打印堆栈跟踪
                    }
                }
            }
            // 查出相应部门
            if (ids != null) {
                List<Long> authorityDeptIds = userMapper.getDeptIdsByUserIds(ids);
                result = result.stream()
                        .filter(dept -> authorityDeptIds.contains(dept.getId()))
                        .collect(Collectors.toList());
            }

        }

        if (!(permissionService.isSuperAdmin(getLoginUserId()) || rloes.contains(6L))) {
            // 范围为空且存在数据就置为-1什么也查不到
            if ((idSt == null && flag)
                // || (idSt == null && messageAuthorityMapper.getById(getLoginUserId())>0)
            ) {
                result = new ArrayList<>();

            }
        }
        return result;
    }
}
