package com.unicom.swdx.module.system.controller.admin.pay;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.egzosn.pay.common.api.PayMessageInterceptor;
import com.egzosn.pay.common.bean.DefaultNoticeRequest;
import com.egzosn.pay.spring.boot.core.PayServiceManager;
import com.egzosn.pay.spring.boot.core.bean.MerchantPayOrder;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.pay.config.WxPayMessageHandler;
import com.unicom.swdx.module.system.dal.dataobject.payMessage.PayMessageDO;
import com.unicom.swdx.module.system.service.payMessage.PayMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;
import org.springframework.web.util.UriComponentsBuilder;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static me.chanjar.weixin.common.util.http.URIUtil.encodeURIComponent;

/**
 * <AUTHOR>
 *         email <EMAIL>
 *         date 2019/5/26.20:10
 */
@RestController
@RequestMapping("/system/Mepay")
@Slf4j
public class PayMerchantController {

    @Autowired
    private PayServiceManager manager;

    @Resource
    private StringRedisTemplate sRedisTemplate;

    @Resource
    private PayMessageService messageService;

    /**
     * 网页支付
     *
     * @param detailsId 列表id
     * @param
     * @return 网页
     */
    @ResponseBody
    @RequestMapping(value = "toPay.html", produces = "text/html;charset=UTF-8")
    @PermitAll
    public String toPay(String detailsId, BigDecimal price, String openid, String userAgent, String callBackUrl, String title) {

        if(StrUtil.isBlank(callBackUrl)){
            log.info("支付地址异常");
            return "支付地址异常，请重试";
        }

//        BigDecimal yf = new BigDecimal("0.01");

        String wayTrade;
        String payHtml;

//        if ((userAgent != null) && userAgent.contains("wxwork")) {
//            log.info("当前使用的是企业微信");
//            wayTrade = "MWEB";
//            MerchantPayOrder payOrder = new MerchantPayOrder(detailsId, wayTrade, title, "摘要",
//                    null == price ? new BigDecimal("0.01") : price, UUID.randomUUID().toString().replace("-", ""));
//            payOrder.setOpenid(openid);
//
//            payHtml = manager.toPay(payOrder);
//
//        } else if ((userAgent != null) && userAgent.contains("MicroMessenger")) {
        log.info("当前使用的是微信，付款人openId:{}", openid);

        wayTrade = "JSAPI";
        String payOf = getInfoByDetailId(detailsId);
        MerchantPayOrder payOrder = new MerchantPayOrder(detailsId, wayTrade, title, payOf,
                price, UUID.randomUUID().toString().replace("-", ""));
        payOrder.setOpenid(openid);
        payOrder.setAddition("新系统"+payOf);
        // 产生预订单
        Map<String, Object> map = manager.getOrderInfo(payOrder);

        sRedisTemplate.opsForValue().set("Key:WxPayOutTradeNo:" + payOrder.getOutTradeNo(), callBackUrl, 1, TimeUnit.HOURS);

        String params = JSONUtil.toJsonStr(map);
        log.info("当前使用的是微信 产生预订单  微信返回值 map ： {} ", params);

        String url = "https://portal.hnswdx.gov.cn/H5NavToWx.html?payData=" + params;
        payHtml = "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <title>跳转到支付中转页面</title>\n" +
                "    <script type=\"text/javascript\">\n" +
                "        window.location.href = 'https://portal.hnswdx.gov.cn/H5NavToWx.html?payData="
                + encodeURIComponent(params) + "';\n" +
                "    </script>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <p>如果没有自动跳转，请 <a href=" + url + ">点击这里</a>。</p>\n" +
                "</body>\n" +
                "</html>";
        return payHtml;

        // 交给微信唤起支付
//            payHtml = "<script>function onBridgeReady() {\r\n"
//                    + "    WeixinJSBridge.invoke('getBrandWCPayRequest', {\r\n" + "        \"appId\": \""
//                    + map.get("appId") + "\",\r\n" + "        \"timeStamp\": \"" + map.get("timeStamp") + "\",\r\n"
//                    + "        \"nonceStr\": \"" + map.get("nonceStr") + "\",\r\n" + "        \"package\": \""
//                    + map.get("package") + "\",\r\n" + "        \"signType\": \"MD5\",\r\n" + "        \"paySign\": \""
//                    + map.get("paySign") + "\"\r\n" + "    }, function(res) {\r\n"
//                    + "        if (res.err_msg == \"get_brand_wcpay_request:ok\") { alert('支付成功！');}else {\r\n"
//                    + "                            alert('支付失败：' + res.err_msg);\r\n" + "                        }\r\n"
//                    + "                        WeixinJSBridge.call('closeWindow');\r\n" + "    });\r\n" + "}\r\n"
//                    + "if (typeof WeixinJSBridge == \"undefined\") {\r\n" + "    if (document.addEventListener) {\r\n"
//                    + "        document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);\r\n"
//                    + "    } else if (document.attachEvent) {\r\n"
//                    + "        document.attachEvent('WeixinJSBridgeReady', onBridgeReady);\r\n"
//                    + "        document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);\r\n" + "    }\r\n"
//                    + "} else {\r\n" + "    onBridgeReady();\r\n" + "}</script>";

//        }else{
//            log.info("当前使用的是网页支付");
//
//            wayTrade = "MWEB";
//            MerchantPayOrder payOrder = new MerchantPayOrder(detailsId, wayTrade, "订单title", "摘要", null == price ? new BigDecimal(0.01) : price, UUID.randomUUID().toString().replace("-", ""));
//            payOrder.setOpenid(openid);
//
//            payHtml = manager.toPay(payOrder);
//        }
//
//        String html = "<!DOCTYPE HTML><html><head><meta charset='htf-8' /><meta name='viewport' content='width=device-width, initial-scale=1.0'/><title>YC支付中心</title><style> body{font-family: 'Microsoft YaHei';} #amount,#error{height: 80px; line-height: 80px; text-align: center; color: #f00; font-size: 30px; font-weight: bold;} #error{font-size: 20px;} #info{padding: 0 10px; font-size: 12px;} table{width: 100%; border-collapse: collapse;} tr{border: 1px solid #ddd;} td{padding: 10px;} .fr{text-align: right; font-weight: bold;}</style><script src='//cdn.jsdelivr.net/jquery/1.12.1/jquery.min.js'></script></head><body><div id='amount'>¥ "
//                + price + "</div><div id='info'><table><tr><td>购买商品</td><td class='fr'>"
//                + "商品标题" + "</td></tr><tr><td>收款方</td><td class='fr'>" + "收款方名称"
//                + "</td></tr></table></div>";
//        html += payHtml + "</body></html>";
//
//        return html;
    }

    private String getInfoByDetailId(String detailsId) {
        switch (detailsId){
            case "3":
                return "一卡通余额充值";
            case "4":
                return "党费充值";
            default:
                return "充值";
        }
    }

    /**
     * 二维码
     *
     * @param detailsId 列表id
     * @param wayTrade  交易方式
     * @return 二维码
     */
    @ResponseBody
    @PermitAll
    @RequestMapping(value = "toQrPay.jpg", produces = "image/jpeg;charset=UTF-8")
    public byte[] toQrPay(String detailsId, String wayTrade, BigDecimal price) throws IOException {
        MerchantPayOrder payOrder = new MerchantPayOrder(detailsId, wayTrade, "订单title", "摘要", null == price ? new BigDecimal(0.01) : price, UUID.randomUUID().toString().replace("-", ""));
        return manager.toQrPay(payOrder);
    }

    /**
     * 二维码信息
     *
     * @param detailsId 列表id
     * @param wayTrade  交易方式
     * @return 二维码信息
     */
    @ResponseBody
    @RequestMapping(value = "getQrPay.json")
    @PermitAll
    public String getQrPay(String detailsId, String wayTrade, BigDecimal price) {
        MerchantPayOrder payOrder = new MerchantPayOrder(detailsId, wayTrade, "订单title", "摘要", null == price ? new BigDecimal(0.01) : price, UUID.randomUUID().toString().replace("-", ""));
        return manager.getQrPay(payOrder);
    }


    /**
     * 支付回调地址
     *
     * @param request   请求
     * @param detailsId 列表id
     * @return 支付是否成功
     * @throws IOException IOException
     *                     拦截器相关增加， 详情查看{@link com.egzosn.pay.common.api.PayService#addPayMessageInterceptor(PayMessageInterceptor)}
     *                     <p>
     *                     业务处理在对应的PayMessageHandler里面处理，在哪里设置PayMessageHandler，详情查看{@link com.egzosn.pay.common.api.PayService#setPayMessageHandler(com.egzosn.pay.common.api.PayMessageHandler)}
     *                     </p>
     *                     如果未设置 {@link com.egzosn.pay.common.api.PayMessageHandler} 那么会使用默认的 {@link com.egzosn.pay.common.api.DefaultPayMessageHandler}
     */
    @RequestMapping(value = "payBack{detailsId}.json")
    @PermitAll
    public ResponseEntity<String> payBack(HttpServletRequest request,HttpServletResponse response , @PathVariable String detailsId) throws IOException {
        //业务处理在对应的PayMessageHandler里面处理，在哪里设置PayMessageHandler，详情查看com.egzosn.pay.common.api.PayService.setPayMessageHandler()
//        return manager.payBack(detailsId, request.getParameterMap(), request.getInputStream());
        String resInfo = manager.payBack(detailsId,
                new DefaultNoticeRequest(request.getParameterMap(), request.getInputStream()));
        log.info("----------返回信息:{}", resInfo);
        /**
         * <xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[{appid=wx8168
         * d772ae460e32, attach=附加信息, bank_type=OTHERS, cash_fee=1, fee_type=CNY, is_subscribe=N, mch_id=**********, n
         * once_str=218fb881eb8c4202bb70c8a2bb059e97, openid=oSmKW65E5OsALtWur3p_rHNgiCQw, out_trade_no=1f7ff4fd7eda421891
         * abb9edb1f8f451, result_code=SUCCESS, return_code=SUCCESS, sign=053D05D7F84EF43B4C48FD5EB981426C, time_end=20240
         * *********, total_fee=1, trade_type=JSAPI, transaction_id=4200002198202406248581981201}]]></return_msg></xml>
         */


        // 使用Hutool解析XML
        Document document = XmlUtil.parseXml(resInfo);
        Element rootElement = XmlUtil.getRootElement(document);


        String code = "";
        String message = "";
        if (resInfo.startsWith("<xml>")) {
            code = StringUtils.substringBetween(resInfo, "<return_code><![CDATA[", "]]></return_code>");//SUCCESS
            message = StringUtils.substringBetween(resInfo, "<return_msg><![CDATA[", "]]></return_msg>");
        } else {
            JSONObject jsonObject = JSONUtil.parseObj(resInfo);
            code = jsonObject.getStr("code");//SUCCESS
            message = jsonObject.getStr("message");
        }
        if ("SUCCESS".equals(code)) {
            //回调一卡通
            messageService.handlePayNotify(message);


            if("4".equals(detailsId)){
                //党建
                String openid = XmlUtil.elementText(rootElement, "openid");

                response.sendRedirect("https://dw.hnswdx.gov.cn/mobile/subPackages/pmAdmin/pmAdminHome/pmAdminHome?state=54a21e58e82c11ea831cb42e9958c96d&accountType=1&openId=" + openid);
                return null;
            }

            return ResponseEntity.status(HttpStatus.OK.value()).body(null);
        } else {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST.value()).body("{\"code\":\"FAIL\",\"message\":\"失败\"}");
        }
    }

}
