package com.unicom.swdx.module.system.api.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.datapermission.core.annotation.DataPermission;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.api.user.dto.*;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserCreateReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserUpdateReqVO;
import com.unicom.swdx.module.system.convert.user.UserConvert;
import com.unicom.swdx.module.system.dal.dataobject.dept.UserDeptDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.dataobject.user.OldPersonDO;
import com.unicom.swdx.module.system.dal.mysql.dangjian.DangjianMapper;
import com.unicom.swdx.module.system.dal.mysql.dept.UserDeptMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import com.unicom.swdx.module.system.service.user.OldPersonService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.system.enums.ApiConstants.VERSION;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@DubboService(version = VERSION) // 提供 Dubbo RPC 接口，给 Dubbo Consumer 调用
@Validated
public class AdminUserApiImpl implements AdminUserApi {

    @Resource
    private AdminUserService userService;

    @Resource
    private OldPersonService oldPersonService;

    @Resource
    private UserDeptMapper userDeptMapper;

    @Resource
    private AdminUserMapper userMapper;

    @Resource
    private DangjianMapper dangjianMapper;

    @DataPermission(enable = false)
    @Override
    public CommonResult<AdminUserRespDTO> getUser(Long id) {
        AdminUserDO user = userService.getUser(id);
        return success(UserConvert.INSTANCE.convert4(user));
    }

    @DataPermission(enable = false)
    @Override
    public CommonResult<AdminUserRespDTO> getHistoryUser(Long id) {
        AdminUserDO user = userService.getHistoryUser(id);
        return success(UserConvert.INSTANCE.convert4(user));
    }

    @DataPermission(enable = false)
    @Override
    public CommonResult<List<AdminUserRespDTO>> getUsers(Collection<Long> ids) {
        List<AdminUserDO> users = userService.getUsers(ids);
        return success(UserConvert.INSTANCE.convertList4(users));
    }

    @DataPermission(enable = false)
    @Override
    public CommonResult<List<AdminUserRespDTO>> getUsersIgnorePermission(Collection<Long> ids) {
        List<AdminUserDO> users = userService.getUsers(ids);
        return success(UserConvert.INSTANCE.convertList4(users));
    }

    @Override
    public CommonResult<List<AdminUserRespDTO>> getUsersByDeptIds(Collection<Long> deptIds) {
        List<AdminUserDO> users = userService.getUsersByDeptIds(deptIds);
        return success(UserConvert.INSTANCE.convertList4(users));
    }

    @Override
    public CommonResult<List<AdminUserRespDTO>> getUsersByDeptName(String deptName, Long tenantId) {
        List<AdminUserDO> users = userService.getUsersByDeptName(deptName, tenantId);
        return success(UserConvert.INSTANCE.convertList4(users));
    }

    @Override
    public CommonResult<List<AdminUserRespDTO>> getUsersByPostIds(Collection<Long> postIds) {
        List<AdminUserDO> users = userService.getUsersByPostIds(postIds);
        return success(UserConvert.INSTANCE.convertList4(users));
    }

    @Override
    public CommonResult<Boolean> validUsers(Set<Long> ids) {
        userService.validUsers(ids);
        return success(true);
    }

    @Override
    public CommonResult<List<AdminUserRespDTO>> getUsersByDeptRoleIds(Long deptId, Long roleId) {
        List<AdminUserDO> users = userService.getUsersByDeptRoleIds(deptId, roleId);
        return success(UserConvert.INSTANCE.convertList4(users));
    }

    @Override
    public CommonResult<List<AdminUserRespDTO>> getUsersByPostCode(Long loginUserId, List<String> postCodes) {
        Long tenantId = userService.getUser(loginUserId).getTenantId();
        List<AdminUserDO> users = userService.getUsersByPostCode(postCodes, tenantId);
        return success(UserConvert.INSTANCE.convertList4(users));
    }

    @Override
    public CommonResult<List<AdminUserRespDTO>> getDeptUsersByPostCode(Long loginUserId, Long deptId, List<String> postCodes) {
        Long tenantId = userService.getUser(loginUserId).getTenantId();
        if (deptId == null) {
            deptId = userService.getUser(loginUserId).getDeptId();
        }
        List<AdminUserDO> users = userService.getUsersByPostDeptCode(postCodes, tenantId, deptId);
        return success(UserConvert.INSTANCE.convertList4(users));
    }

    @Override
    public CommonResult<Long> createPersonal(AdminUserReqDTO reqVO) {
        UserCreateReqVO user = UserConvert.INSTANCE.convert(reqVO);
        Long id = userService.createUserHr(user, null);
        return success(id);
    }

    @Override
    public CommonResult<Boolean> updatePersonal(AdminUserRespDTO reqVO) {
        UserUpdateReqVO user = UserConvert.INSTANCE.convert(reqVO);
        userService.updateUser(user);
        return success(true);
    }

    @Override
    public CommonResult<AdminUserRespDTO> getPersonal(Long id) {
        AdminUserRespDTO userRespVO = UserConvert.INSTANCE.convert4(userService.getUser(id));
        return success(userRespVO);
    }

    @Override
    public CommonResult<OldPersonDTO> getOldPersonByUserId(Long userId) {
        OldPersonDTO oldPersonDTO = new OldPersonDTO();
        OldPersonDO oldPersonDO = oldPersonService.getOldPersonByUserId(userId);
        if (Objects.isNull(oldPersonDO)) {
            return success(null);
        } else {
            BeanUtil.copyProperties(oldPersonDO, oldPersonDTO);
            return success(oldPersonDTO);
        }
    }

    @Override
    public CommonResult<OldPersonDTO> getOldPersonByMobile(String mobile) {
        OldPersonDTO oldPersonDTO = new OldPersonDTO();
        LambdaQueryWrapperX<OldPersonDO> wrapper = new LambdaQueryWrapperX<OldPersonDO>()
                .eq(OldPersonDO::getMobilePhone, mobile);
        OldPersonDO oldPersonDO = oldPersonService.getOne(wrapper.last("limit 1"));
        if (Objects.isNull(oldPersonDO)) {
            return success(null);
        } else {
            BeanUtil.copyProperties(oldPersonDO, oldPersonDTO);
            return success(oldPersonDTO);
        }
    }

    @Override
    public CommonResult<OldPersonDTO> getOldPersonByMobileAndName(String mobile, String name) {

        OldPersonDTO oldPersonDTO = new OldPersonDTO();

        OldPersonDO oldPersonDO = oldPersonService.getOldPersonsByMobileOrName(mobile, name);

        if (Objects.isNull(oldPersonDO)) {
            return success(null);
        } else {
            BeanUtil.copyProperties(oldPersonDO, oldPersonDTO);
            return success(oldPersonDTO);
        }
    }

    @Override
    public CommonResult<Boolean> updateOldPerson(OldPersonDTO oldPerson) {
        OldPersonDO oldPersonDO = new OldPersonDO();
        BeanUtil.copyProperties(oldPerson, oldPersonDO);
        oldPersonService.updateById(oldPersonDO);
        return success(true);
    }

    @Override
    public CommonResult<List<UserDeptDTO>> selectUserDeptList() {
        List<UserDeptDO> userDeptDOS = userDeptMapper.selectList();
        List<UserDeptDTO> userDeptDTOS = UserConvert.INSTANCE.convertToDTO(userDeptDOS);
        return success(userDeptDTOS);
    }

    @Override
    public void createUserDeptBatch(Long userId, List<Long> deptIds) {
        List<UserDeptDO> userDeptDOS = new ArrayList<>();
        for (Long deptId : deptIds) {
            UserDeptDO userDeptDO = new UserDeptDO();
            userDeptDO.setDeptId(deptId);
            userDeptDO.setUserId(userId);
            userDeptDOS.add(userDeptDO);
        }
        userDeptMapper.insertBatch(userDeptDOS);
    }

    @Override
    public void createUserDept(UserDeptDTO userDept) {
        UserDeptDO userDeptDO = new UserDeptDO();
        userDeptDO.setDeptId(userDept.getDeptId());
        userDeptDO.setUserId(userDept.getUserId());
        userDeptMapper.insert(userDeptDO);
    }

    @Override
    public CommonResult<List<Long>> getDeptList(Long userId) {
        return success(userDeptMapper.getDeptList(userId));
    }

    @Override
    public CommonResult<List<HrDeptDTO>> getDeptDetailList(Long userId) {
        return success(userDeptMapper.getDeptDetailList(userId));
    }

    public CommonResult<AdminUserRespDTO> getUserByNickname(@RequestParam("nickname") String nickname) {
        LambdaQueryWrapper<AdminUserDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AdminUserDO::getNickname, nickname);
        lambdaQueryWrapper.eq(AdminUserDO::getTenantId, getTenantId());
        AdminUserDO adminUserDO = userMapper.selectOne(lambdaQueryWrapper.last("limit 1"));
        return success(UserConvert.INSTANCE.convert4(adminUserDO));
    }

    @Override
    public void updateUserDeptBatch(Long userId, List<Long> deptIds) {
        LambdaQueryWrapperX<UserDeptDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(UserDeptDO::getUserId, userId);
        userDeptMapper.delete(wrapperX);
        createUserDeptBatch(userId, deptIds);
    }

    @Override
    public void deleteUserDeptBatch(Long userId) {
        LambdaQueryWrapperX<UserDeptDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(UserDeptDO::getUserId, userId);
        userDeptMapper.delete(wrapperX);
    }

    @Override
    public void updateDept(Long userId, Long deptId) {
        // 创建一个LambdaUpdateWrapper实例
        LambdaUpdateWrapper<AdminUserDO> updateWrapper = new LambdaUpdateWrapper<>();
        // 设置更新条件：userId等于传入的userId
        updateWrapper.eq(AdminUserDO::getId, userId);
        // 设置要更新的字段：deptId设置为传入的deptId
        updateWrapper.set(AdminUserDO::getDeptId, deptId);
        userMapper.update(null, updateWrapper);
    }

    @Override
    public List<String> getUsersByDeptId(Long deptId) {
        // 获用户组织列表，只要开启状态的
        List<AdminUserDO> list = userService.getUsersByStatusAndDeptId(deptId);
        // 排序后，返回给前端
        List<String> nicknames = list.stream().map(AdminUserDO::getNickname).collect(Collectors.toList());

        return nicknames;
    }

    @Override
    public CommonResult<DangJianDTO> getDangJian(String mobile) {
        return success(dangjianMapper.selectUserInfo(mobile));
    }

    @Override
    public CommonResult<List<String>> getDangJianMobile() {
        return success(dangjianMapper.selectUserMobile());
    }

    @Override
    public CommonResult<Long> createUser(String mobile, String nickname, Long tennantid) {
        return success(userService.createUserJW(mobile, nickname, tennantid));
    }

    @Override
    public CommonResult<Long> createAdminForUnit(String username, String mobile, String nickname) {
        return success(userService.createAdminForUnit(username, mobile, nickname));
    }

    @Override
    public CommonResult<List<CreateAdminForUnitBatchResultDTO>> batchCreateAdminForUnit(CreateAdminForUnitBatchDTO dtos) {
        return success(userService.batchCreateAdminForUnit(dtos));
    }

    @Override
    public CommonResult<Long> updateAdminMobileForUnit(Long userId, String mobile) {
        return success(userService.updateAdminMobileForUnit(userId, mobile));
    }

    @Override
    public CommonResult<Boolean> deleteByIds(List<Long> userIds) {
        userService.deleteByIds(userIds);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> deleteBySystemId(Long systemId) {
        LambdaUpdateWrapper<AdminUserDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(AdminUserDO::getSystemId,systemId.toString());
        userMapper.delete(lambdaUpdateWrapper);
        return success(true);
    }

    @Override
    public CommonResult<Long> createUserAll(String mobile, String nickname, String systemid) {
        return success(userService.createUserJWall(mobile, nickname, systemid));
    }

    @Override
    public CommonResult<Map<String, Long>> createUserBatchByTenant(CreateUserBatchTenantDTO dto) {

        return success(userService.createUserBatchByTenant(dto.getUserInputList(), dto.getTenantId()));
    }

    @Override
    public CommonResult<Integer> updateSystemId(Long userId, String yzUserId) {
        AdminUserDO user = new AdminUserDO();
        user.setId(userId);
        user.setSystemId(yzUserId);
        return success(userMapper.updateById(user));
    }

    @Override
    public CommonResult<Boolean> updateSystemIdBatchById(String returnIds) {
        List<AdminUserDO> users = new ArrayList<>();
        String[] map = returnIds.split(",");
        for (String s : map) {
            String[] ids = s.split("-");
            AdminUserDO user = new AdminUserDO();
            user.setId(Long.parseLong(ids[0]));
            user.setSystemId(ids[1]);
            users.add(user);
        }
        return success(userMapper.updateBatch(users));
    }

    @Override
    public CommonResult<Long> editUser(String phone, Long userId, String nickname) {
        return success(userService.editUserJW(phone, userId, nickname));
    }

    @Override
    public CommonResult<Boolean> deleteUser(String ids) {
        if (StrUtil.isNotBlank(ids)) {
            String[] idArray = ids.split(",");
            List<Long> idList = Arrays.stream(idArray).map(Long::parseLong).collect(Collectors.toList());
            return success(userService.deleteUserJW(idList));
        }
        return success(false);

    }

    @Override
    public CommonResult<List<Long>> getErrorUserIdList() {

        List<Long> userIds = userMapper.getErrorUserIdList();

        if (CollUtil.isEmpty(userIds)) {
            return success(Collections.emptyList());
        } else {
            return success(userIds);
        }
    }

    @Override
    public CommonResult<List<Long>> createUsers(String jsonStr) {
        return success(userService.createUsersJW(jsonStr));
    }

    @Override
    public CommonResult<Boolean> resetUserPassword(Long userId) {
        userService.resetUserPassword(userId);
        return success(true);
    }
}
