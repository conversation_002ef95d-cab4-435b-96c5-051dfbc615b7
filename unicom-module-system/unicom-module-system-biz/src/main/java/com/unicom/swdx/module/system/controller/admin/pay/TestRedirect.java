package com.unicom.swdx.module.system.controller.admin.pay;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.view.RedirectView;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static me.chanjar.weixin.common.util.http.URIUtil.encodeURIComponent;

@RestController
@RequestMapping("/system/testRedirect")
@Slf4j
public class TestRedirect {

    @GetMapping("/redirect")
    public RedirectView redirect() {
        // 替换为你的小程序的 AppID 和页面路径
        String miniAppURL = "https://servicewechat.com/wx8168d772ae460e32/pagesPay/pay/index";

        // 添加查询参数
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(miniAppURL)
                .queryParam("param1", "value1")
                .queryParam("param2", "value2");

        return new RedirectView(miniAppURL);
    }


    @RequestMapping("/redirectone")
    public RedirectView redirectone() {
        System.out.println("redirectone 11111111111111111111111");


        String miniAppURL = "http://10.32.23.130:80/H5NavToWx.html";

        // 添加查询参数
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(miniAppURL)
                .queryParam("param1", "value1")
                .queryParam("param2", "value2");

        return new RedirectView(miniAppURL);

    }

    @ResponseBody
    @RequestMapping(value = "/test")
    public RedirectView test() {
        // String testUrl = "hello";
        Map<String, String> map = new HashMap<>();
        map.put("appId","12345678");
        map.put("nonceStr","12345678");
        String jsonStr = JSONUtil.toJsonStr(map);
        String testUrl = "https://portal.hnswdx.gov.cn/H5NavToWx.html?payData=" + jsonStr;
        return new RedirectView(testUrl);
    }

    @ResponseBody
    @RequestMapping(value = "/test3")
    public String test3() {
        // String testUrl = "hello";
        Map<String, String> map = new HashMap<>();
        map.put("appId","12345678");
        map.put("nonceStr","12345678");

        return "https://portal.hnswdx.gov.cn/H5NavToWx.html?payData=" + JSONUtil.toJsonStr(map);
    }

    @ResponseBody
    @RequestMapping(value = "/test4")
    public String test4() {
        Map<String, String> map = new HashMap<>();
        map.put("appId","wx8168d772ae460e32");
        map.put("nonceStr","tcx1vXnev6ZsuspL");
        map.put("package","prepay_id=wx17104802176048b466ecd761cd66130001");
        map.put("paySign","9DAD0EC72B387F06D5086777BE155724");
        map.put("signType","MD5");
        map.put("timestamp","1715914082");

        String params = JSONUtil.toJsonStr(map);

        String url ="https://portal.hnswdx.gov.cn/H5NavToWx.html?payData=" + params;
        String html = "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <title>跳转到支付中转页面</title>\n" +
                "    <script type=\"text/javascript\">\n" +
                "        window.location.href = 'https://portal.hnswdx.gov.cn/H5NavToWx.html?payData="
                + encodeURIComponent(params) + "';\n" +
                "    </script>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <p>如果没有自动跳转，请 <a href=" + url + ">点击这里</a>。</p>\n" +
                "</body>\n" +
                "</html>";
        return html;
    }


    /**
     * 方式二：使用HttpServletResponse#sendRedirect()，支持内部接口、外部URL
     */
    @GetMapping("/test1")
    public void test1(HttpServletResponse response) throws IOException {
        // String testUrl = "hello";
        String testUrl = "https://www.baidu.com";
        response.sendRedirect(testUrl);
    }

    /**
     * 方式三：使用redirect:，支持内部接口、外部URL
     */
    @GetMapping("/test2")
    public String test2() {
        // return "redirect:hello";
        return "redirect:https://www.baidu.com";
    }

}
