package com.unicom.swdx.module.system.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.mybatis.core.type.JsonLongSetTypeHandler;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.enums.common.SexEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 管理后台的用户 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_users", autoResultMap = true) // 由于 SQL Server 的 system_user 是关键字，所以使用 system_users
@KeySequence("system_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminUserDO extends TenantBaseDO {

    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户账号
     */
    private String username;
    /**
     * 加密后的密码
     *
     * 因为目前使用 {@link BCryptPasswordEncoder} 加密器，所以无需自己处理 salt 盐
     */
    private String password;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 备注
     */
    private String remark;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 岗位编号数组
     */
    @TableField(typeHandler = JsonLongSetTypeHandler.class)
    private Set<Long> postIds;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 用户性别
     *
     * 枚举类 {@link SexEnum}
     */
    private Integer sex;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 帐号状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 最后登录IP
     */
    private String loginIp;
    /**
     * 最后登录时间
     */
    private LocalDateTime loginDate;
    /**
     * 是否实名认证
     */
    private Boolean isRealNameAuthentication;
    /**
     * 密码更新时间
     */
    private LocalDateTime passwordUpdateTime;

    /**
     * 显示排序
     */
    private Integer sort;
    /**
     * 政务app 绑定设备
     */
    private String appCid;

    /**
     * 微信小程序openid
     */
    private String wxxcxOpenid;

    /**
     * 微信unionid
     */
    private String wxUnionid;

    //全局子系统id

    private String othersystemid;

    //版本更新是否为第一次登录
    private Boolean hasLogin;

    @TableField(exist = false)
    private List<Long> deptIds;

    @ApiModelProperty(value = "多部门")
    @TableField(exist = false)
    private List<DeptDO> departments;

    @TableField(exist = false)
    private Integer personnalStatus;

    @TableField(exist = false)
    private Integer personnalClassification;



    private Boolean logindefualt;

    private String systemId;
    /**
     * 初始密码是否修改
     */
    private Boolean initPasswordIsChange;

}
