package com.unicom.swdx.module.system.controller.admin.businesscenter;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.system.api.businesscenter.dto.OAuth2UserInfosRespDTO;
import com.unicom.swdx.module.system.controller.admin.businesscenter.vo.BusinessCenterPersonnalRespVO;
import com.unicom.swdx.module.system.controller.admin.businesscenter.vo.BusinessCenterPersonnalSimpleRespVO;
import com.unicom.swdx.module.system.controller.admin.businesscenter.vo.BusinessCenterUserSimpleRespVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptSimpleRespVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserRespVO;
import com.unicom.swdx.module.system.dal.redis.oauth2.OAuth2AccessTokenRedisDAO;
import com.unicom.swdx.module.system.service.businesscenter.BusinessCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "远程调用业中接口")
@RestController
@RequestMapping("/system/businesscenter")
@Validated
@Slf4j
public class BusinessCenterController {

    @Resource
    private BusinessCenterService businessCenterService;

    @Resource
    private OAuth2AccessTokenRedisDAO oauth2AccessTokenRedisDAO;

    @ApiOperation("获取业中部门列表")
    @GetMapping("/edu-get-dept")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "机构id", required = false, example = "1", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "type", value = "区分是否用户管理的部门列表查询", required = false, example = "1", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "token", value = "当前登录用户的token", example = "1", dataTypeClass = String.class),

    })
    public CommonResult<List<DeptSimpleRespVO>> getDept(@RequestParam(required = false) Long tenantId,
                                                        @RequestParam(required = false) Integer type,
                                                        @RequestParam String token) throws Exception {
        List<DeptSimpleRespVO> deptSimpleRespVOS = businessCenterService.getDept(tenantId, type, token);
        return success(deptSimpleRespVOS);
    }

    @ApiOperation("获取业中某个部门的所有下级部门")
    @GetMapping("/get-dept-all-children")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "机构id", required = false, example = "1", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "type", value = "区分是否用户管理的部门列表查询", required = false, example = "1", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "deptId", value = "部门ID", required = true, example = "1", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "token", value = "当前登录用户的token", example = "1", dataTypeClass = String.class),

    })
    public CommonResult<List<DeptSimpleRespVO>> getDeptAllChildren(@RequestParam(required = false) Long tenantId,
                                                        @RequestParam(required = false) Integer type,
                                                        @RequestParam Long deptId,
                                                        @RequestParam String token) throws Exception {
        List<DeptSimpleRespVO> deptSimpleRespVOS = businessCenterService.getDeptAllChildren(tenantId, type, deptId, token);
        return success(deptSimpleRespVOS);
    }

    @ApiOperation("根据登录token获取业中权限部门ID列表")
    @GetMapping("/get-user-dept-permission-by-token")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "机构id", required = false, example = "1", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "type", value = "区分是否用户管理的部门列表查询", required = false, example = "1", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "token", value = "当前登录用户的token", example = "1", dataTypeClass = String.class),

    })
    public CommonResult<List<Long>> getUserDeptPermission(@RequestParam(required = false) Long tenantId,
                                                           @RequestParam(required = false) Integer type,
                                                           @RequestParam String token) throws Exception {
        List<Long> deptIds = businessCenterService.getUserDeptPermissionByToken(tenantId, type, token);
        return success(deptIds);
    }

    @GetMapping("/get-user-dept-permissiom-by-userId")
    @ApiOperation("根据登录token获取某个业中用户权限部门ID列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "机构id", required = false, example = "1", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "type", value = "区分是否用户管理的部门列表查询", required = false, example = "1", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, example = "1", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "token", value = "当前登录用户的token", example = "1", dataTypeClass = String.class),

    })
    public CommonResult<List<Long>> getUserDeptPermissionByUserId(@RequestParam(required = false) Long tenantId,
                                                          @RequestParam(required = false) Integer type,
                                                          @RequestParam Long userId,
                                                          @RequestParam String token) throws Exception {
        List<Long> deptIds = businessCenterService.getUserDeptPermissionByUserId(tenantId, type, userId, token);
        return success(deptIds);
    }


    @GetMapping("/edu-get-user")
    @ApiOperation("查询业中用户")
    public CommonResult<List<BusinessCenterUserSimpleRespVO>> getUser(@RequestParam Map<String, String> param, String token) throws Exception {
        List<BusinessCenterUserSimpleRespVO> userSimpleRespVOS = businessCenterService.getUser(param, token);
        return success(userSimpleRespVOS);
    }

    @GetMapping("/edu-get-user-info-by-id")
    @ApiOperation("获得业中用户详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "用户id", required = true, example = "1024", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "token", value = "当前登录用户的token", example = "1", dataTypeClass = String.class)
    })
    public CommonResult<UserRespVO> getUserInfoByUserId(@RequestParam("id") Long id) throws Exception {
        UserRespVO userRespVO = businessCenterService.getUserInfoByUserId(id);
        return success(userRespVO);
    }

    @GetMapping("/edu-get-user-info-by-token")
    @ApiOperation("获得业中用户详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "当前登录用户的token", example = "1", dataTypeClass = String.class)
    })
    public CommonResult<OAuth2UserInfosRespDTO> getUserInfoByToken(@RequestParam String token) throws Exception {
        OAuth2UserInfosRespDTO userRespVO = businessCenterService.getUserInfoByToken(token);
        return success(userRespVO);
    }

    @GetMapping("/edu-get-personnal")
    @ApiOperation("查询业中教职工信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "机构id", required = false, example = "1", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "token", value = "当前登录用户的token", example = "1", dataTypeClass = String.class)

    })
    public CommonResult<List<BusinessCenterPersonnalRespVO>> getPersonnalList(@RequestParam(required = false) Long tenantId,
                                                                                @RequestParam String token) throws Exception {
        List<BusinessCenterPersonnalRespVO> personnalRespVOS = businessCenterService.getPersonnalList(tenantId, token);
        return success(personnalRespVOS);
    }

    @GetMapping("/edu-get-personnal-simple")
    @ApiOperation("查询业中教职工信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "机构id", required = false, example = "1", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "deptId", value = "部门id", required = false, example = "1", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "username", value = "查询的姓名/手机号", example = "1", dataTypeClass = String.class),
            @ApiImplicitParam(name = "token", value = "当前登录用户的token", example = "1", dataTypeClass = String.class)

    })
    public CommonResult<PageResult<BusinessCenterPersonnalSimpleRespVO>> getPersonnalSimple(@RequestParam(required = false) Long tenantId,
                                                                                            @RequestParam(required = false) Integer deptId,
                                                                                            @RequestParam(required = false) String username,
                                                                                            @RequestParam(required = false) Integer pageNo,
                                                                                            @RequestParam(required = false) Integer pageSize,
                                                                                            @RequestParam String token) throws Exception {
        PageResult<BusinessCenterPersonnalSimpleRespVO> personnalSimpleRespVOS = businessCenterService.getPersonnalSimple(tenantId, deptId, username, token, pageNo, pageSize);
        return success(personnalSimpleRespVOS);
    }

    @GetMapping("/edu-get-personnal-by-id")
    @ApiOperation("查询业中教职工信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "人事id", required = false, example = "1", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "token", value = "当前登录用户的token", example = "1", dataTypeClass = String.class)

    })
    public CommonResult<BusinessCenterPersonnalRespVO> getPersonnal(@RequestParam(required = false) Long id,
                                                                    @RequestParam String token) throws Exception {
        BusinessCenterPersonnalRespVO personnalRespVO = businessCenterService.getPersonnalById(id, token);
        return success(personnalRespVO);
    }
}
