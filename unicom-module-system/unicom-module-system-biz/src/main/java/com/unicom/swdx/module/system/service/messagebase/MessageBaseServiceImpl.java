package com.unicom.swdx.module.system.service.messagebase;

import com.unicom.swdx.module.system.controller.admin.message.vo.MassageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 短信通知 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessageBaseServiceImpl implements MessageBaseService {

    /**
     * 多个号码发相同内容
     * type类型为 1
     */
    static final String TEMPLATE_ONE = "1";
    /**
     * 多个号码发不同内容
     * type类型为 2
     */
    static final String TEMPLATE_TWO = "2";

    /**
     * 响应成功
     * 状态码
     */
    static final String CODE_SUCCESSFUL = "0";

    /**
     * 响应失败
     * 状态码
     */
    static final String CODE_LOSE = "-1003";

    /**
     * 随机种子
     */
    private static final String CHAR_SET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    /**
     * 静态随机函数
     */
    private static final Random RANDOM = new Random();

    /**
     * 短信群发
     * url
     */
    public static final String API_URL_MASSAGE = "http://qxt.fungo.cn/Recv_center?";

    /**
     * 短信余额查询
     * url
     */
    public static final String API_URL_BALANCE = "http://qxt.fungo.cn/QueryBlance?";

    /**
     * 自定义消息id
     * SMS_ID
     */
    public static final String SMS_ID = "smsId";

    /**
     * 返回码
     * code
     */
    public static final String CODE = "code";

    /**
     * 账户
     * CP_NAME
     */
    public static final String CP_NAME = "cpName";

    /**
     * MD5 加密
     * SecretSign
     */
    public static final String SECRET_SIGN = "SecretSign";

    private String cpName = "fzgj";

    private String cpPassword = "220324";

    private String extCode = "10001";

    /**
     * 短信通知
     * sendSms 方法实现
     */
    @Override
    public Map<String, Object> sendSms(MassageVO massageVO) {
        HashMap<String, Object> returnData = new HashMap<>();
        String cpName = massageVO.getCpName();
        String cpPassword = massageVO.getCpPassword();
        String desMobile = massageVO.getDesMobile();
        String content = massageVO.getContent();
        String extCode = massageVO.getExtCode();

        String[] mobileNumbers = desMobile.split(",");

        // 检查 desMobile 字段是否为空
        if (!StringUtils.isNotBlank(desMobile)) {
            returnData.put("error:","手机号码不能为空！");
            return returnData;
        } else {
            // 检查手机号格式是否正确
            for (String mobile : mobileNumbers) {
                if (!isValidMobileFormat(mobile)) {
                    returnData.put("phone:"+ mobile, "手机号码格式不正确!");
                    return returnData;
                }
            }
        }

        // 多个号码发相同内容，多个号码之间用半角逗号分隔，最多2000个
        if(massageVO.getType().equals(TEMPLATE_ONE)){
            if(mobileNumbers.length > 2000){
                returnData.put("error:","号码选择数量超出最大限制范围！");
                return returnData;
            }
            // code参数，smsId参数，secret参数

            try {
                for (int i = 0; i < mobileNumbers.length; i++) {

                    batchSender(API_URL_MASSAGE,cpName,cpPassword,mobileNumbers[i],content,extCode);

                    //测试
//                    System.out.println(mobileNumbers[i]);
//                    System.out.println(content);

                }
                String secretSign = generateSignature(CODE_SUCCESSFUL,massageVO.getCpName(),massageVO.getSmsId());
                massageVO.setSecretSign(secretSign);
                returnData.put(SMS_ID, massageVO.getSmsId());
                returnData.put(CODE, CODE_SUCCESSFUL);
                returnData.put(CP_NAME, massageVO.getCpName());
                returnData.put(SECRET_SIGN, massageVO.getSecretSign());

            } catch (Exception e) {
                String secretSign = generateSignature(CODE_LOSE,massageVO.getCpName(),massageVO.getSmsId());
                massageVO.setSecretSign(secretSign);
                returnData.put(SMS_ID, massageVO.getSmsId());
                returnData.put(CODE, CODE_LOSE);
                returnData.put(CP_NAME, massageVO.getCpName());
                returnData.put(SECRET_SIGN, massageVO.getSecretSign());
                e.printStackTrace();
            }
        }

        if(massageVO.getType().equals(TEMPLATE_TWO)){
            String[] contents = content.split("-~-");

            //多个号码发不同内容，多个号码之间用半角逗号分割，多个下发内容直接用 -~- 分割（注意请保证手机个数与下发内容的顺序与个数一致)
            try {
                for (int i = 0; i < mobileNumbers.length; i++) {

                    batchSender(API_URL_MASSAGE,cpName,cpPassword,mobileNumbers[i],contents[i],extCode);

                    //测试
//                    System.out.println(mobileNumbers[i]);
//                    System.out.println(contents[i]);

                }
                String secretSign = generateSignature(CODE_SUCCESSFUL,massageVO.getCpName(),massageVO.getSmsId());
                massageVO.setSecretSign(secretSign);
                returnData.put(SMS_ID, massageVO.getSmsId());
                returnData.put(CODE, CODE_SUCCESSFUL);
                returnData.put(CP_NAME, massageVO.getCpName());
                returnData.put(SECRET_SIGN, massageVO.getSecretSign());

            } catch (Exception e) {
                String secretSign = generateSignature(CODE_LOSE,massageVO.getCpName(),massageVO.getSmsId());
                massageVO.setSecretSign(secretSign);
                returnData.put(SMS_ID, massageVO.getSmsId());
                returnData.put(CODE, CODE_LOSE);
                returnData.put(CP_NAME, massageVO.getCpName());
                returnData.put(SECRET_SIGN, massageVO.getSecretSign());
                e.printStackTrace();
            }
        }

        //剩余短信条数
        returnData.put("count",queryBlance(API_URL_BALANCE,cpName,cpPassword));
        return returnData;


    }

    @Override
    public void sendSingleMessage(String mobile, String msg) {
        MassageVO massageVO = new MassageVO();
        massageVO.setCpName(cpName);
        massageVO.setCpPassword(cpPassword);
        massageVO.setDesMobile(mobile);
        massageVO.setContent(msg);
        massageVO.setExtCode(extCode);
        massageVO.setType("1");
        log.info("发送短信：mobile={},短信内容=【{}】",mobile,msg);
        this.sendSms(massageVO);
    }

    @Override
    public void uplinkMessage(String mobile, String msg,String cpMoblie) {
        //先转发然后插入数据库表
        MassageVO massageVO = new MassageVO();
        massageVO.setCpName(cpName);
        massageVO.setCpPassword(cpPassword);
        massageVO.setDesMobile(mobile);
        massageVO.setContent(msg);
        massageVO.setExtCode(extCode);
        massageVO.setType("1");
        log.info("发送短信：mobile={},短信内容=【{}】",mobile,msg);
        this.sendSms(massageVO);
    }

    /**
     * 短信群发
     *
     * @param url
     *            企信通短信群发接口
     * @param cpName
     *            账号，String类型，企信通指定
     * @param cpPassword
     *            密码，String类型，企信通指定
     * @param desMobile
     *            目标号码，String类型。 1.多个号码发相同内容，多个号码之间用半角逗号分隔，最多1000个。
     *            2.多个号码发不同内容，多个号码之间用半角逗号分割，多个下发内容直接用 -~-
     *            分割（注意请保证手机个数与下发内容的顺序与个数一致）
     * @param content
     *            发送内容，String类型，UTF-8编码
     * @param extCode
     *            扩展码 String,最长8位数字,可选填
     * @return 响应结果，json格式，示例:{
     *         "smsid":"E7254F86ED07C231CDC7657D0F9BC420","code": "0","CpName":
     *         "***", "SecretSign": "adsdd87ythljhuyw3456kjuiwryui832"}
     *         返回结果介绍：smsid：消息id; code:状态码，0：受理成功，其他为失败，详见 附：常见状态对照表;
     *                        CpName账号;
     *         SecretSign:加密签名，生成算法如下：MD5(code+CpName+smsid+secret).toLowerCase()。
     *         secret为系统分配的私钥
     *
     *
     */
    public static String batchSender(String url, String cpName, String cpPassword,
                                     String desMobile,String content,String extCode) {

        String result = "UNKNOWN";

        try {
            URL sendUrl = new URL(url);
            // 装换编码格式
            String sendContent = URLEncoder.encode(content, "UTF-8");

            // 构造参数
            String param = "CpName=" + cpName + "&CpPassword=" + cpPassword + "&DesMobile=" + desMobile
                    + "&Content=" + sendContent + "&ExtCode=" + extCode;

            // 建立连接
            HttpURLConnection con = (HttpURLConnection) sendUrl.openConnection();
            con.setDoOutput(true);
            con.setDoInput(true);
            con.setRequestMethod("POST");
            con.connect();

            // 输出
            BufferedWriter out = new BufferedWriter(new OutputStreamWriter(con.getOutputStream(), "GBK"));
            out.write(param);
            out.flush();
            out.close();

            // 接收响应
            BufferedReader br = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String line = "";
            StringBuffer sb = new StringBuffer();
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }

            result = sb.toString();
        } catch (Exception e) {
            result = "TimeOut";
        }

        return result;
    }


    /**
     * 余额查询
     *
     * @param url
     *            企信通余额查询接口
     * @param cpName
     *            账号，String类型，企信通指定
     * @param cpPassword
     *            密码，String类型，企信通指定
     * @return 响应结果，数字，示例:20
     *
     *
     */
    public static String queryBlance(String url, String cpName, String cpPassword) {

        String result = "UNKNOWN";

        try {
            URL sendUrl = new URL(url);

            // 构造参数
            String param = "CpName=" + cpName + "&CpPassword=" + cpPassword ;

            // 建立连接
            HttpURLConnection con = (HttpURLConnection) sendUrl.openConnection();
            con.setDoOutput(true);
            con.setDoInput(true);
            con.setRequestMethod("POST");
            con.connect();

            // 输出
            BufferedWriter out = new BufferedWriter(new OutputStreamWriter(con.getOutputStream(), "GBK"));
            out.write(param);
            out.flush();
            out.close();

            // 接收响应
            BufferedReader br = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String line = "";
            StringBuffer sb = new StringBuffer();
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }

            result = sb.toString();
        } catch (Exception e) {
            result = "TimeOut";
        }

        return result;
    }


    /**
     * 加密 MD5 加密
     *
     * @param code
     *            响应码
     * @param cpName
     *            账号，String类型，企信通指定
     * @param smsId
     *            消息id，自定义消息id
     * @return 响应结果，数字，示例:20
     */
    public static String generateSignature(String code, String cpName, String smsId) {

        //随机生成长度为10的字符创
        String secret = generateRandomString(10);
        String data = code + cpName + smsId + secret;
        String signature = null;

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(data.getBytes());
            byte[] digest = md.digest();

            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }

            signature = sb.toString().toLowerCase();

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        return signature;
    }

    /**
     * 检查手机号格式是否正确
     * 正确返回true
     * 错误返回false
     */
    private boolean isValidMobileFormat(String mobile) {
        //判断手机号码的不正确长度
        if(mobile.length() != 11){
            return false;
        }
        // 以1开头，后面跟10位数字，符合中国大陆手机号的基本格式
        String regex = "^1\\d{10}$";
        // 使用正则表达式匹配手机号格式
        return mobile.matches(regex);
    }

    /**
     * 密码随机生成器
     * @param length
     * 长度限制
     */
    public static String generateRandomString(int length) {

        StringBuilder randomString = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int randomIndex = RANDOM.nextInt(CHAR_SET.length());
            randomString.append(CHAR_SET.charAt(randomIndex));
        }

        return randomString.toString();
    }
}
