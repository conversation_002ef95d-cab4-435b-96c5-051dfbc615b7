package com.unicom.swdx.module.system.dal.dataobject.tenant;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.mybatis.core.type.ListLongTypeHandler;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import lombok.*;

import java.util.List;

/**
 * 租户 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_tenant", autoResultMap = true)
@KeySequence("system_tenant_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantDO extends BaseDO {

    /**
     * 租户编号，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 机构名称
     */
    private String name;
    /**
     * 所在地区划
     */
    @TableField(typeHandler = ListLongTypeHandler.class)
    private List<Long> locationRegion;
    /**
     * 所在地地址
     */
    private String locationAddress;
    /**
     * 机构用户类型
     */
    @TableField(typeHandler = ListLongTypeHandler.class)
    private List<Long> instUserType;
    /**
     * 机构管理员的用户编号
     *
     * 关联 {@link AdminUserDO#getId()}
     */
    private Long contactUserId;
    /**
     * 机构管理员姓名
     */
    private String contactNickname;
    /**
     * 用户名
     */
    private String contactName;
    /**
     * 用户名密码
     */
    private String contactPassword;
    /**
     * 机构管理员手机号码
     */
    private String contactMobile;
    /**
     * 机构状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    private String tenantCode;

    /**
     * 是否打开午别考勤规则
     */
    private Boolean checkByPeriodRule;

    /**
     * 是否打开考勤保护
     */
    private Boolean enableAttendanceProtection;

}
