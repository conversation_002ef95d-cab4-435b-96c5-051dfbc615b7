package com.unicom.swdx.module.system.dal.mysql.tenant;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import com.unicom.swdx.module.system.dal.dataobject.tenant.TenantDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 租户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantMapper extends BaseMapperX<TenantDO> {

    default PageResult<TenantDO> selectPage(TenantPageReqVO reqVO) {
        LambdaQueryWrapperX<TenantDO> query = new LambdaQueryWrapperX<TenantDO>()
                .likeIfPresent(TenantDO::getName,reqVO.getName())
                .likeIfPresent(TenantDO::getContactMobile,reqVO.getContactMobile())
                .likeIfPresent(TenantDO::getContactNickname,reqVO.getContactNickname())
                .betweenIfPresent(TenantDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(TenantDO::getStatus,reqVO.getStatus())
                .orderByDesc(TenantDO::getId);

        return selectPage(reqVO, query);
    }

    default List<TenantDO> selectExcelList(TenantPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TenantDO>()
                .likeIfPresent(TenantDO::getName,reqVO.getName())
                .likeIfPresent(TenantDO::getContactMobile,reqVO.getContactMobile())
                .likeIfPresent(TenantDO::getContactNickname,reqVO.getContactNickname())
                .betweenIfPresent(TenantDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(TenantDO::getStatus,reqVO.getStatus())
                .orderByDesc(TenantDO::getId));
    }

    default TenantDO selectByContactUserId(Long userId) {
        return selectOne(TenantDO::getContactUserId,userId);
    }

    default List<TenantDO> selectByContactUserIds(Collection<Long> contactUserIds) {
        if (CollUtil.isEmpty(contactUserIds)) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapper<TenantDO>()
                .in(TenantDO::getContactUserId, contactUserIds));
    }


    default TenantDO selectByTenantCode(String tenantCode) {
        return selectOne(new LambdaQueryWrapper<TenantDO>()
                .eq(TenantDO::getTenantCode, tenantCode)
                .last("LIMIT 1"));
    }
    default TenantDO selectByCode(String code) {
        return selectOne(TenantDO::getTenantCode,code);
    }


    default TenantDO selectByName(String name) {
        return selectOne(TenantDO::getName, name);
    }




    default List<Long> selectIdListByNameLike(String tenantName) {
        List<TenantDO> tenantDOS = selectList(new LambdaQueryWrapperX<TenantDO>()
                .eq(TenantDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .likeIfPresent(TenantDO::getName,tenantName)
        );
        if (CollUtil.isEmpty(tenantDOS)) {
            return Collections.emptyList();
        }
        return tenantDOS.stream().map(TenantDO::getId).collect(Collectors.toList());
    }

}
