package com.unicom.swdx.module.system.wx.xcx;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.module.system.api.xcx.dto.WxXcxSignQRCodeRespVO;
import com.unicom.swdx.module.system.controller.admin.auth.vo.WxXcxQRCodeRespVO;
import com.unicom.swdx.module.system.controller.xcx.auth.vo.WxxcxCode2SessionVO;
import com.unicom.swdx.module.system.wx.xcx.vo.WxXcxAccessTokenVO;
import com.unicom.swdx.module.system.wx.xcx.vo.WxXcxUserPhoneVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @date 2024/1/23 12:40
 **/
@Slf4j
@Service
@EnableConfigurationProperties(WxXcxProperties.class)
public class WxXcxApiService {

    private final String REDISKEY_ACCESSTOKEN = "wxxcx:access_token";

    private final String URL_ACCESSTOKEN = "https://api.weixin.qq.com/cgi-bin/token";
    private final String URL_STABLE_ACCESSTOKEN = "https://api.weixin.qq.com/cgi-bin/stable_token";

    private static final String URL_QRCODE = "https://api.weixin.qq.com/wxa/getwxacodeunlimit";

    private final String URL_GET_PHONE = "https://api.weixin.qq.com/wxa/business/getuserphonenumber";

    private final String URL_CODE2SESSION = "https://api.weixin.qq.com/sns/jscode2session";

    private final String GENERATE_URL_LINK = "https://api.weixin.qq.com/wxa/generate_urllink";

    private final String SEND_MESSAGE = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=";


    @Value("${wx.miniapp.envVersion:trial}")
    private String XCX_ENV_VERSION;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private WxXcxProperties properties;


    public void sendXcxMessage(){
        JSONObject params = new JSONObject();
        params.set("template_id","_rhcadJ8nJ9xCA8E6pf_DidxKEFc0Rr3IloE0doKnbY");
        params.set("touser","oSmKW65E5OsALtWur3p_rHNgiCQw");
        params.set("miniprogram_state", "trial");
        params.set("lang","zh_CN");
        JSONObject data = new JSONObject();
        data.set("thing2","你有一条消息待签收");
        String time = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm").format(LocalDateTime.now());
        data.set("time3",time);
        data.set("thing4","请注意及时查看");
        params.set("data",data);
        try (HttpResponse response = HttpUtil.createPost(SEND_MESSAGE + this.getAccessToken())
                .body(params.toString()).execute()) {
            log.info(response.toString());
        }catch (Exception e){
            log.info("sendXcxMessageWrong"+e.getMessage());
        }
    }


    /**
     * 获取accessToken
     * @return 获取accessToken
     */
    public String getAccessToken() {
        // 先从缓存中获取
        Object accessTokenObj = redisUtil.get(REDISKEY_ACCESSTOKEN);
        if (Objects.nonNull(accessTokenObj)) {
            return accessTokenObj.toString();
        }
        Map<String,Object> params = new HashMap<>();
        params.put("grant_type","client_credential");
        params.put("appid", properties.getAppid());
        params.put("secret", properties.getSecret());
        String resp = HttpUtil.post(URL_STABLE_ACCESSTOKEN, JSONUtil.toJsonStr(params));
        if (StrUtil.isBlank(resp)) {
            throw  exception(ACCESS_TOKEN_ERROR);
        }
        WxXcxAccessTokenVO respVO = null;
        try {
            respVO = JSONUtil.toBean(resp, WxXcxAccessTokenVO.class);
        } catch (Exception e) {
            throw exception(ACCESS_TOKEN_ERROR);
        }
        redisUtil.set(REDISKEY_ACCESSTOKEN,respVO.getAccessToken(),respVO.getExpiresIn());
        return respVO.getAccessToken();
    }

    /**
     * 获取小程序登录的二维码图片
     * @return 小程序二维码图片base64
     */
    public WxXcxQRCodeRespVO getQRCodeBase64() {
        // scene为二维码唯一标识
        String uniqueCode = IdUtil.fastSimpleUUID();
        log.info("scene={}", uniqueCode);
        Map<String,Object> params = new HashMap<>();
        params.put("scene",uniqueCode);
        // todo 这个字段换到nacos配置中心
        params.put("env_version",XCX_ENV_VERSION);
        params.put("page", "pages/login");
        params.put("check_path",false);
        HttpResponse response = HttpUtil.createPost(URL_QRCODE + "?access_token=" + this.getAccessToken()).body(JSONUtil.toJsonStr(params)).execute();
        WxXcxQRCodeRespVO qrCodeRespVO = new WxXcxQRCodeRespVO();
        qrCodeRespVO.setUniqueCode(uniqueCode);
        qrCodeRespVO.setQrCodeBase64(Base64Encoder.encode(response.bodyBytes()));
        return qrCodeRespVO;
    }

    /**
     * 获取小程序签字的二维码图片
     * @return 小程序二维码图片base64
     */
    public WxXcxSignQRCodeRespVO getSignQRCodeBase64() {
        // scene为二维码唯一标识
        String uniqueCode = IdUtil.fastSimpleUUID();
        log.info("scene={}", uniqueCode);
        Map<String,Object> params = new HashMap<>();
        params.put("scene",uniqueCode);
        params.put("env_version",XCX_ENV_VERSION);
        params.put("page", "pages-oa/signature/index");
        params.put("check_path",false);
        HttpResponse response = HttpUtil.createPost(URL_QRCODE + "?access_token=" + this.getAccessToken()).body(JSONUtil.toJsonStr(params)).execute();
        WxXcxSignQRCodeRespVO qrCodeRespVO = new WxXcxSignQRCodeRespVO();
        qrCodeRespVO.setUniqueCode(uniqueCode);
        qrCodeRespVO.setQrCodeBase64(Base64Encoder.encode(response.bodyBytes()));
        return qrCodeRespVO;
    }

    /**
     * 根据code获取用户手机号
     * @param code code
     * @return 手机号
     */
    public String getMobile(String code) {
        Map<String,Object> params = new HashMap<>();
        params.put("code",code);
        String respStr = HttpUtil.post(URL_GET_PHONE + "?access_token=" + this.getAccessToken(),JSONUtil.toJsonStr(params));
        if (StrUtil.isBlank(respStr)) {
            throw exception(CODE_TO_PHONE_ERROR);
        }
        log.info(respStr);
        WxXcxUserPhoneVO resp = null;
        try {
            resp = JSONUtil.toBean(respStr, WxXcxUserPhoneVO.class);
        } catch (Exception e) {
            throw exception(CODE_TO_PHONE_ERROR);
        }
        if (0 == resp.getErrcode() && Objects.nonNull(resp.getPhoneInfo())) {
            return resp.getPhoneInfo().getPhoneNumber();
        }
        throw exception(CODE_TO_PHONE_ERROR);
    }

    /**
     * 根据code获取用户openid和unionid
     * @param code
     * @return
     */
    public WxxcxCode2SessionVO getWxUserId(String code) {
        Map<String,Object> params = new HashMap<>();
        params.put("appid",properties.getAppid());
        params.put("secret",properties.getSecret());
        params.put("js_code",code);
        params.put("grant_type","authorization_code");
        String respStr = HttpUtil.get(URL_CODE2SESSION, params);
        if (StrUtil.isBlank(respStr)) {
            throw exception(OPENID_NOT_EXIST);
        }
        //log.info(respStr);
        WxxcxCode2SessionVO resp = null;
        try {
            resp = JSONUtil.toBean(respStr, WxxcxCode2SessionVO.class);
        } catch (Exception e) {
            throw exception(OPENID_NOT_EXIST);
        }
        return resp;
    }

    public String getUrlLink() {
        Map<String,Object> params = new HashMap<>();
        params.put("grant_type","client_credential");
        params.put("appid",properties.getAppid());
        params.put("secret",properties.getSecret());
        String respStr = HttpUtil.get(URL_ACCESSTOKEN, params);
        if (StrUtil.isBlank(respStr)) {
            throw exception(GET_TOKEN_FAIL);
        }
        WxXcxAccessTokenVO respVO = null;
        try {
            respVO = JSONUtil.toBean(respStr, WxXcxAccessTokenVO.class);
        } catch (Exception e) {
            throw exception(GET_TOKEN_FAIL);
        }
        Map<String,Object> bodyParam = new HashMap<>();
        bodyParam.put("path","/pages/login");
        String resp = HttpUtil.post(GENERATE_URL_LINK + "?access_token=" + respVO.getAccessToken(),
                JSONUtil.toJsonStr(bodyParam));
        String urlLink = null;
        try {
            urlLink = JSONUtil.parseObj(resp).getStr("url_link");
        } catch (Exception e) {
            throw exception(GET_URL_LINK_FAIL);
        }
        return urlLink;
    }
}
