package com.unicom.swdx.module.system.dal.mysql.user;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.system.controller.admin.dept.vo.dept.DeptCountVO;
import com.unicom.swdx.module.system.controller.admin.dept.vo.post.PostUsersPageReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.role.RoleUsersPageReqVO;
import com.unicom.swdx.module.system.controller.admin.permission.vo.roleGroup.RoleGroupUsersPageReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.profile.UserProfileUpdateMobileReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserExportReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserPageReqVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserSimpleRespVO;
import com.unicom.swdx.module.system.controller.admin.user.vo.user.UserUpdateMobileReqVO;
import com.unicom.swdx.module.system.dal.dataobject.dept.DeptDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.*;
import java.util.stream.Collectors;

@Mapper
public interface AdminUserMapper extends BaseMapperX<AdminUserDO> {

    @Select("select count(1) from system_users where deleted = 0 and status = 0 and mobile = #{mobile}")
    Integer veryMobile(@Param("mobile") String mobile);
    @Select("select count(1) from system_users where deleted = 0 and status = 0 and username = #{username}")
    Integer veryUser(@Param("username") String username);
    Integer updateUserMobile(@Param("param") UserUpdateMobileReqVO reqVO);
    Integer updateAppUserMobile(@Param("param") UserProfileUpdateMobileReqVO reqVO);
    Long selectTenantIdByUserId(@Param("id") Long id);

    AdminUserDO selectHistoryUserById(@Param("id") Long id);
    default AdminUserDO selectByUsername(String username) {
        return selectOne(new LambdaQueryWrapper<AdminUserDO>()
                .eq(AdminUserDO::getUsername, username).last("limit 1"));
    }

    AdminUserDO selectByUnitUsername(@Param("username") String username);

    default AdminUserDO selectByUsernameandTenantId(String username ,Long tenantId ) {
        return selectOne(new LambdaQueryWrapper<AdminUserDO>()
                .eq(AdminUserDO::getUsername, username).eq(tenantId!=null ,AdminUserDO::getTenantId, tenantId).last("limit 1"));
    }


    default List<AdminUserDO> selectUsersByUsername(String username) {
        return selectList(new LambdaQueryWrapper<AdminUserDO>()
                .eq(AdminUserDO::getUsername, username));
    }

    default AdminUserDO selectByEmail(String email) {
        return selectOne(new LambdaQueryWrapper<AdminUserDO>().eq(AdminUserDO::getEmail, email).last("limit 1"));
    }

    default AdminUserDO selectByMobile(String mobile) {
        return selectOne(new LambdaQueryWrapper<AdminUserDO>().eq(AdminUserDO::getMobile, mobile).last("LIMIT 1"));
    }

    default List<AdminUserDO> selectByMobileList(List<String> mobiles) {
        if (mobiles.isEmpty()){
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapper<AdminUserDO>().in(AdminUserDO::getMobile, mobiles));
    }

    default List<AdminUserDO> selectByUsernameList(List<String> usernames) {
        if (usernames.isEmpty()){
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapper<AdminUserDO>().in(AdminUserDO::getUsername, usernames));
    }

    default AdminUserDO selectByMobileandTenantid(String mobile , Long tenantId) {
        return selectOne(new LambdaQueryWrapper<AdminUserDO>().eq(AdminUserDO::getMobile, mobile).eq(tenantId!=null ,AdminUserDO::getTenantId, tenantId).last("LIMIT 1"));
    }

    default List<AdminUserDO> selectByMobiles(String mobile) {
        return selectList(new LambdaQueryWrapper<AdminUserDO>().eq(AdminUserDO::getMobile, mobile));
    }

    default AdminUserDO selectByUnionid(String unionid) {
        return selectOne(new LambdaQueryWrapper<AdminUserDO>().eq(AdminUserDO::getWxUnionid, unionid).last("limit 1"));
    }

    default AdminUserDO selectByOpenid(String openid) {
        return selectOne(new LambdaQueryWrapper<AdminUserDO>().eq(AdminUserDO::getWxxcxOpenid, openid).last("limit 1"));
    }

    List<AdminUserDO> selectUserPage(IPage ipage,@Param("param") UserPageReqVO reqVO,
                                     @Param("deptIds") List<Long> deptIds,
                                     @Param("userId") String userId,
                                     @Param("dataPermissionDepts") Set<Long> dataPermissionDepts
    );

    List<AdminUserDO> selectUserPageAuthority(IPage ipage,@Param("param") UserPageReqVO reqVO,
                                     @Param("deptIds") List<Long> deptIds,
                                     @Param("userId") String userId,
                                     @Param("dataPermissionDepts") Set<Long> dataPermissionDepts,
                                     @Param("ids") List<Long> userIds,
                                              @Param("leaderIds") List<Long> leaderIds,
                                              @Param("filter") Boolean filter
    );

    //通讯录各部门人数统计
    List<AdminUserDO> countByDeptIdAdminUserDos(@Param("deptIds") List<Long> deptIds,@Param("tenantId") Long tenantId,
                                                @Param("personStatus") Integer personStatus);
    //通讯录各部门人数统计多部门补充
    List<DeptCountVO> countByDepts(@Param("tenantId") Long tenantId,@Param("personStatus") Integer personStatus);

    //通讯录分页返回新字段
    List<AdminUserVO> selectUserPageManage(IPage ipage, @Param("param") UserPageReqVO reqVO,
                                           @Param("deptIds") List<Long> deptIds,
                                           @Param("userId") String userId,
                                           @Param("dataPermissionDepts") Set<Long> dataPermissionDepts);

    List<AdminUserDO> selectExportUser(@Param("param") UserExportReqVO reqVO,
                                       @Param("deptIds") List<Long> deptIds,
                                       @Param("userId") String userId,
                                       @Param("dataPermissionDepts") Set<Long> dataPermissionDepts);

    default List<AdminUserDO> selectListByNicknameLike(String nickname) {
        return selectList(new LambdaQueryWrapperX<AdminUserDO>().likeIfPresent(AdminUserDO::getNickname, nickname));
    }

    default List<AdminUserDO> selectListByStatus(Integer status) {
        return selectList(AdminUserDO::getStatus, status);
    }

    default List<AdminUserDO> selectListByTenantId(Long tenantId) {
        return selectList(AdminUserDO::getTenantId, tenantId);
    }

    default List<AdminUserDO> selectListByDeptIds(Collection<Long> deptIds) {
        return selectList(new LambdaQueryWrapperX<AdminUserDO>()
                .eq(AdminUserDO::getStatus, 0)
                .inIfPresent(AdminUserDO::getDeptId, deptIds));
    }

    List<UserSimpleRespVO> selectUsersByDeptIds(@Param("deptIds") Collection<Long> deptIds,@Param("nickname") String nickname);

    List<UserSimpleRespVO> selectSimpleUsersByIds(IPage iPage,@Param("param") RoleUsersPageReqVO pageReqVO,
    @Param("tenantId") Long tenantId,@Param("deptIds") List<Long> deptIds);

    default List<AdminUserDO> selectListByNameAndDeptIds(Collection<Long> deptIds, String name) {
        return selectList(new LambdaQueryWrapperX<AdminUserDO>()
                .eq(AdminUserDO::getStatus, 0)
                .likeIfPresent(AdminUserDO::getNickname, name)
                .inIfPresent(AdminUserDO::getDeptId, deptIds));
    }

    /**
     * 通过组织 角色查找用户
     * @param deptId
     * @param roleId
     * @return
     */
    List<AdminUserDO> getUsersByDeptRoleIds(@Param("deptId") Long deptId, @Param("roleId") Long roleId);

    default Long selectCountByDeptId(Long id) {
        return selectCount(new LambdaQueryWrapperX<AdminUserDO>().eq(AdminUserDO::getDeptId,id));
    }
    Long selectCountByDeptIdList(@Param("deptIds") Collection<Long> deptIds);


    default List<Long> selectIdListByTenantId(Long tenantId) {
        return selectList(AdminUserDO::getTenantId,tenantId)
                .stream().map(AdminUserDO::getId).collect(Collectors.toList());
    }


    default Long getTenantId(Long userId) {
        return selectById(userId).getTenantId();
    }

    default Long selectUserIdByUsernameAndMobile(String username,String mobile) {
        AdminUserDO adminUserDO = selectOne(new LambdaQueryWrapperX<AdminUserDO>()
                .eq(AdminUserDO::getUsername, username)
                .eq(AdminUserDO::getMobile, mobile)
                .eq(AdminUserDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .last("limit 1")
        );
        return adminUserDO == null ? null : adminUserDO.getId();
    }

    void updateUserLogin(@Param("param") AdminUserDO adminUserDO);

    default Long selectUserIdByMobile(String mobile) {
        AdminUserDO adminUserDO = selectOne(new LambdaQueryWrapperX<AdminUserDO>()
                .eq(AdminUserDO::getMobile, mobile)
                .eq(AdminUserDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .last("limit 1")
        );
        return adminUserDO == null ? null : adminUserDO.getId();
    }


    //通过用户id列表查询
    List<Long> getDeptIdsByUserIds(@Param("ids") List<Long> ids);

    List<UserSimpleRespVO> selectSimpleUsersByGroupIds(IPage iPage,@Param("param") RoleGroupUsersPageReqVO pageReqVO,
                                                       @Param("tenantId") Long tenantId,@Param("deptIds") List<Long> deptIds);

    List<UserSimpleRespVO> selectSimpleUsersByPostUsers(IPage iPage,@Param("param") PostUsersPageReqVO pageReqVO,
                                                        @Param("tenantId") Long tenantId,@Param("deptIds") List<Long> deptIds);

    List<Long> selectUserIdsByGroupIds(@Param("groupId") Long id);


    AdminUserDO getUserBySystemId(String id);

    AdminUserDO getUserByEmployeeId(String employeeId);

    default Collection<AdminUserDO> findByMobileList(List<String> mobileNumbers,Long tenantid ){{
        if(CollectionUtil.isNotEmpty(mobileNumbers)){
            return selectList(new LambdaQueryWrapper<AdminUserDO>().in(AdminUserDO::getMobile, mobileNumbers).eq(AdminUserDO::getTenantId , tenantid).eq(AdminUserDO::getDeleted,0));}
        }

        return new ArrayList<>();
    }


    default Collection<AdminUserDO> findBySystemIdList(List<String> systemids  ){{
        if(CollectionUtil.isNotEmpty(systemids)){
            return selectList(new LambdaQueryWrapper<AdminUserDO>().in(AdminUserDO::getSystemId, systemids).eq(AdminUserDO::getDeleted,0));}
    }

        return new ArrayList<>();
    }


    List<Long> getErrorUserIdList();

}
