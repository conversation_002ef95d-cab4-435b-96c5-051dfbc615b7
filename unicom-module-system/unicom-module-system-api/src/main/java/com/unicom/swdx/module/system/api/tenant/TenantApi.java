package com.unicom.swdx.module.system.api.tenant;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.system.api.tenant.dto.TenantInfoRespDTO;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 多租户")
public interface TenantApi {

    String PREFIX = ApiConstants.PREFIX + "/tenant";

    @GetMapping(PREFIX + "/id-list")
    @ApiOperation("获得所有租户编号")
    CommonResult<List<Long>> getTenantIds();

    @GetMapping(PREFIX + "/valid")
    @ApiOperation("校验租户是否合法")
    @ApiImplicitParam(name = "id", value = "租户编号", required = true, example = "1024", dataTypeClass = Long.class)
    CommonResult<Boolean> validTenant(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/getTenantCodeById")
    @ApiOperation("获得租户编码")
    CommonResult<String> getTenantCodeById(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/getTenantCodeByUserId")
    @ApiOperation("获得租户编码")
    CommonResult<String> getTenantCodeByUserId(@RequestParam("userId") Long userId);

    @GetMapping(PREFIX + "/getTenantByContactUserIds")
    @ApiOperation("根据租户管理员用户id列表获得租户信息")
    CommonResult<List<TenantInfoRespDTO>> getTenantByContactUserIds(@RequestParam("tenantAdminUserIds") Collection<Long> tenantAdminUserIds);

    @GetMapping(PREFIX + "/tenantAdmin")
    @ApiOperation("获得所有租户")
    CommonResult<Boolean> tenantAdmin(@RequestParam("id") Long userId);

    @GetMapping(PREFIX + "/getTenantCheckRuleById")
    @ApiOperation("获得租户信息-是否打开午别考勤规则")
    CommonResult<Boolean> getTenantCheckRuleById(@RequestParam("id") Long id);
}
