package com.unicom.swdx.module.system.api.user;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.collection.CollectionUtils;
import com.unicom.swdx.module.system.api.user.dto.*;
import com.unicom.swdx.module.system.enums.ApiConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Api(tags = "RPC 服务 - 管理员用户")
public interface AdminUserApi {

    String PREFIX = ApiConstants.PREFIX + "/user";

    @GetMapping(PREFIX + "/get")
    @ApiOperation("通过用户 ID 查询用户")
    @ApiImplicitParam(name = "id", value = "用户编号", example = "1", required = true, dataTypeClass = Long.class)
    CommonResult<AdminUserRespDTO> getUser(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/getHistory")
    @ApiOperation("通过用户 ID 查询用户,无论用户是否删除")
    @ApiImplicitParam(name = "id", value = "用户编号", example = "1", required = true, dataTypeClass = Long.class)
    CommonResult<AdminUserRespDTO> getHistoryUser(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/list")
    @ApiOperation("通过用户 ID 查询用户们")
    @ApiImplicitParam(name = "ids", value = "部门编号数组", example = "1,2", required = true, allowMultiple = true,dataTypeClass = List.class)
    CommonResult<List<AdminUserRespDTO>> getUsers(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/list-ignore-dp")
    @ApiOperation("通过用户 ID 查询用户们（忽略数据权限）")
    @ApiImplicitParam(name = "ids", value = "部门编号数组", example = "1,2", required = true, allowMultiple = true,dataTypeClass = List.class)
    CommonResult<List<AdminUserRespDTO>> getUsersIgnorePermission(@RequestParam("ids") Collection<Long> ids);

    @GetMapping(PREFIX + "/list-by-dept-id")
    @ApiOperation("获得指定部门的用户数组")
    @ApiImplicitParam(name = "deptIds", value = "部门编号数组", example = "1,2", required = true, allowMultiple = true,dataTypeClass = List.class)
    CommonResult<List<AdminUserRespDTO>> getUsersByDeptIds(@RequestParam("deptIds") Collection<Long> deptIds);

    @GetMapping(PREFIX + "/list-by-dept-name")
    @ApiOperation("获得指定部门的用户数组")
    @ApiImplicitParam(name = "deptName", value = "部门名称", example = "1,2", required = true, allowMultiple = true,dataTypeClass = List.class)
    CommonResult<List<AdminUserRespDTO>> getUsersByDeptName(@RequestParam("deptName") String deptName,@RequestParam("tenantId") Long tenantId);

    @GetMapping(PREFIX + "/list-by-post-id")
    @ApiOperation("获得指定岗位的用户数组")
    @ApiImplicitParam(name = "postIds", value = "岗位编号数组", example = "2,3", required = true, allowMultiple = true,dataTypeClass = List.class)
    CommonResult<List<AdminUserRespDTO>> getUsersByPostIds(@RequestParam("postIds") Collection<Long> postIds);

    /**
     * 获得用户 Map
     *
     * @param ids 用户编号数组
     * @return 用户 Map
     */
    default Map<Long, AdminUserRespDTO> getUserMap(Collection<Long> ids) {
        return CollectionUtils.convertMap(getUsers(ids).getCheckedData(), AdminUserRespDTO::getId);
    }

    @GetMapping(PREFIX + "/valid")
    @ApiOperation("校验用户们是否有效")
    @ApiImplicitParam(name = "ids", value = "用户编号数组", example = "3,5", required = true,dataTypeClass = Set.class)
    CommonResult<Boolean> validUsers(@RequestParam("ids") Set<Long> ids);

    @GetMapping(PREFIX + "/list-by-dept-role-id")
    @ApiOperation("获得指定部门角色的用户数组")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门ID", example = "1", required = false,dataTypeClass = Long.class),
            @ApiImplicitParam(name = "roleId", value = "角色ID", example = "1", required = true,dataTypeClass = Long.class)
    })
    CommonResult<List<AdminUserRespDTO>> getUsersByDeptRoleIds(@RequestParam(value = "deptId", required = false) Long deptId,@RequestParam("roleId") Long roleId);


    @PostMapping(PREFIX + "/list-by-post-code")
    @ApiOperation("获得用户所在机构的指定岗位标识用户数组")
    CommonResult<List<AdminUserRespDTO>> getUsersByPostCode(@RequestParam("loginUserId") Long loginUserId,
                                                            @RequestBody List<String> postCodes);

    @PostMapping(PREFIX + "/list-by-post-dept-code")
    @ApiOperation("获得用户所在机构的指定岗位标识用户数组")
    CommonResult<List<AdminUserRespDTO>> getDeptUsersByPostCode(@RequestParam("loginUserId") Long loginUserId,
                                                                @RequestParam("deptId") Long deptId,
                                                            @RequestBody List<String> postCodes);

    @PostMapping(PREFIX + "/create-personnal")
    @ApiOperation("创建人事系统教职工用户信息")
    @ApiImplicitParam(name = "reqVO", value = "职工账号信息",  required = true, dataTypeClass = AdminUserReqDTO.class)
    CommonResult<Long> createPersonal(@RequestBody AdminUserReqDTO reqVO);
    @PostMapping(PREFIX + "/update-personnal")
    @ApiOperation("更新人事系统教职工用户信息")
    @ApiImplicitParam(name = "reqVO", value = "职工账号信息",  required = true, dataTypeClass = UserUpdateReqDTO.class)
    CommonResult<Boolean> updatePersonal(@RequestBody AdminUserRespDTO reqVO);
    @PostMapping(PREFIX + "/get-personnal")
    @ApiOperation("获得人事系统教职工用户信息")
    @ApiImplicitParam(name = "id", value = "用户编号", example = "3", required = true,dataTypeClass = Long.class)
    CommonResult<AdminUserRespDTO> getPersonal(@RequestParam("id") Long id);

    @GetMapping(PREFIX + "/getOldPersonByUserId")
    @ApiOperation("获得老系统用户")
    CommonResult<OldPersonDTO> getOldPersonByUserId(@RequestParam("userId") Long userId);


    @GetMapping(PREFIX + "/getOldPersonByMobile")
    @ApiOperation("获得老系统用户通过手机号")
    CommonResult<OldPersonDTO> getOldPersonByMobile(@RequestParam("mobile") String mobile);


    @GetMapping(PREFIX + "/getOldPersonByMobileAndName")
    @ApiOperation("获得老系统用户通过手机号or名称")
    CommonResult<OldPersonDTO> getOldPersonByMobileAndName(@RequestParam("mobile") String mobile ,@RequestParam("name") String name);


    @PostMapping(PREFIX + "/updateOldPerson")
    @ApiOperation("更新老系统用户")
    CommonResult<Boolean> updateOldPerson(@RequestBody OldPersonDTO oldPerson);

    @PostMapping(PREFIX + "/selectUserDeptList")
    @ApiOperation("获取多部门信息")
    CommonResult<List<UserDeptDTO>> selectUserDeptList();

    @PostMapping(PREFIX + "/createUserDeptBatch")
    @ApiOperation("批量增加多部门信息")
    void createUserDeptBatch(@RequestParam("userId") Long userId, @RequestBody List<Long> deptIds);

    @PostMapping(PREFIX + "/createUserDept")
    @ApiOperation("增加多部门信息")
    void createUserDept(@RequestBody UserDeptDTO userDept);

    @PostMapping(PREFIX + "/getDeptList")
    @ApiOperation("根据用户id获取对应的多部门Id")
    CommonResult<List<Long>> getDeptList(@RequestParam("userId") Long userId);

    @PostMapping(PREFIX + "/updateUserDeptBatch")
    @ApiOperation("批量修改多部门信息")
    void updateUserDeptBatch(@RequestParam("userId")Long userId, @RequestBody List<Long> deptIds);

    @PostMapping(PREFIX + "/deleteUserDeptBatch")
    @ApiOperation("批量删除多部门信息")
    void deleteUserDeptBatch(@RequestParam("userId")Long userId);

    @GetMapping(PREFIX + "/updateDept")
    @ApiOperation("更新用户部门")
    void updateDept(@RequestParam("userId") Long userId,@RequestParam("deptId") Long deptId);

    @PostMapping(PREFIX + "/getDeptDetailList")
    @ApiOperation("根据用户id获取对应的多部门Id和相关信息")
    CommonResult<List<HrDeptDTO>> getDeptDetailList(@RequestParam("userId") Long userId);

    @GetMapping(PREFIX + "/getByNickname")
    @ApiOperation("通过用户 nickname 查询用户")
    CommonResult<AdminUserRespDTO> getUserByNickname(@RequestParam("nickname") String nickname);

    @GetMapping(PREFIX + "/getUsersByDeptId")
    @ApiOperation("通过用户 nickname 查询用户")
     List<String> getUsersByDeptId(@RequestParam("deptId") Long deptId);

    @GetMapping(PREFIX + "/getDangJian")
    @ApiOperation("通过用户手机号查询修改数据")
    CommonResult<DangJianDTO> getDangJian(@RequestParam("mobile") String mobile);

    @GetMapping(PREFIX + "/getDangJianMobile")
    @ApiOperation("通过用户手机号查询修改数据")
    CommonResult<List<String>> getDangJianMobile();

    @PostMapping(PREFIX + "/create")
    @ApiOperation("教务系统新增用户")
    CommonResult<Long> createUser(@RequestParam("mobile") String mobile, @RequestParam("nickname") String nickname, @RequestParam("tennantid") Long tennantid);

    @PostMapping(PREFIX + "/createAdminForUnit")
    @ApiOperation("调训新增单位管理员用户")
    CommonResult<Long> createAdminForUnit(@RequestParam("username") String username,
                                          @RequestParam("mobile") String mobile,
                                          @RequestParam("nickname") String nickname);

    @PostMapping(PREFIX + "/batchCreateAdminForUnit")
    @ApiOperation("批量新增调训单位管理员用户")
    CommonResult<List<CreateAdminForUnitBatchResultDTO>> batchCreateAdminForUnit(@RequestBody CreateAdminForUnitBatchDTO dtos);

    @PostMapping(PREFIX + "/updateAdminMobileForUnit")
    @ApiOperation("更新调训单位管理员用户手机号")
    CommonResult<Long> updateAdminMobileForUnit(@RequestParam("userId") Long userId,
                                          @RequestParam("mobile") String mobile);

    @PostMapping(PREFIX + "/deleteByIds")
    @ApiOperation("根据用户id删除")
    CommonResult<Boolean> deleteByIds(@RequestParam("userIds") List<Long> userIds);

    @PostMapping(PREFIX + "/deleteBySystemId")
    @ApiOperation("根据用户systemId删除")
    CommonResult<Boolean> deleteBySystemId(@RequestParam("systemId") Long systemId);

    @PostMapping(PREFIX + "/createall")
    @ApiOperation("教务系统新增用户all")
    CommonResult<Long> createUserAll(@RequestParam("mobile") String mobile, @RequestParam("nickname") String nickname,@RequestParam("systemid") String systemid);

    @PostMapping(PREFIX + "/createUserBatchByTenant")
    @ApiOperation("教务系统某个租户新增用户批量")
    CommonResult<Map<String , Long>> createUserBatchByTenant(@RequestBody CreateUserBatchTenantDTO dto);

    @PostMapping(PREFIX + "/updateSystemId")
    @ApiOperation("更新教务系统用户表systemid为业中的userid")
    CommonResult<Integer> updateSystemId(@RequestParam("userId")Long userId, @RequestParam("yzUserId")String yzUserId);

    @PostMapping(PREFIX + "/updateSystemIdBatchById")
    @ApiOperation("更新教务系统用户表systemid为业中的userid")
    CommonResult<Boolean> updateSystemIdBatchById(@RequestParam("returnIds") String returnIds);

    @PostMapping(PREFIX + "/edit")
    @ApiOperation("教务系统新增用户")
    CommonResult<Long> editUser(@RequestParam("phone") String phone, @RequestParam("userId") Long userId, @RequestParam("nickname") String nickname);

    @PostMapping(PREFIX + "/deleteUserByTraineeId")
    @ApiOperation("删除教务账号")
    CommonResult<Boolean> deleteUser(@RequestParam("ids") String ids);


    @GetMapping(PREFIX + "/getErrorUserIdList")
    @ApiOperation("获取异常用户id列表")
    CommonResult<List<Long>> getErrorUserIdList();
    @PostMapping(PREFIX + "/createUsers")
    @ApiOperation("创建用户")
    CommonResult<List<Long>> createUsers(@RequestParam("jsonStr") String jsonStr);

    @PostMapping(PREFIX + "/resetUserPassword")
    @ApiOperation("重制用户密码")
    CommonResult<Boolean> resetUserPassword(@RequestParam("userId") Long userId);
}
