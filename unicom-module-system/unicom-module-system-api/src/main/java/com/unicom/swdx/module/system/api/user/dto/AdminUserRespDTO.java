package com.unicom.swdx.module.system.api.user.dto;

import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

/**
 * Admin 用户 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class AdminUserRespDTO {

    /**
     * 用户ID
     */
    private Long id;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 帐号状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 部门ID
     */
    private Long deptId;

    private List<Long> deptIds;
    /**
     * 岗位编号数组
     */
    private Set<Long> postIds;
    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 用户账号
     */
    private String username;

    /**
     * 用户头像
     */
    private String avatar;


    /**
     * 用户头像
     */
    private String appCid;


    private Long tenantId;

    @ApiModelProperty(value = "用户性别", example = "1", notes = "参见 SexEnum 枚举类")
    private Integer sex;

    @ApiModelProperty(value = "用户邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过 50 个字符")
    private String email;

    @ApiModelProperty(value = "业中id")
    private String systemId;
}
