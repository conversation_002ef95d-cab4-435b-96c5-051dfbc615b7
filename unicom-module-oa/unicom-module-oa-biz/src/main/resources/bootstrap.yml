spring:
  application:
    name: oa-server
  cloud:
    nacos:
      server-addr: 10.32.23.132:8848
      username: nacos
      password: nacos
      discovery:
        #本地开发配置
        namespace: a413eab0-d2d4-4c23-8b0c-5a199482e5bc
        #正式环境数据库配置
#        namespace: c6ae999d-8e47-461b-ad68-f380bacf6f0f
        # 本地开发时，更改自定义的group_id，避免访问到其他人的服务，现默认使用系统用户名
        group: ${user.name}
      config:
        name: OA
        namespace: a413eab0-d2d4-4c23-8b0c-5a199482e5bc
#        namespace: c6ae999d-8e47-461b-ad68-f380bacf6f0f
        group: DEFAULT_GROUP
        enable-remote-sync-config: true
        file-extension: yaml
# 日志文件配置。注意，如果 logging.file.name 不放在 bootstrap.yaml 配置文件，而是放在 application.yaml 中，会导致出现 LOG_FILE_IS_UNDEFINED 文件
logging:
  file:
    path: ./logs
