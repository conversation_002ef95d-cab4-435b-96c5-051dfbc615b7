package com.unicom.swdx.framework.common.util.date;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils {

    /**
     * 时区 - 默认
     */
    public static final String TIME_ZONE_DEFAULT = "GMT+8";

    /**
     * 秒转换成毫秒
     */
    public static final long SECOND_MILLIS = 1000;

    public static final String FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND = "yyyy-MM-dd HH:mm:ss";

    public static final String FORMAT_HOUR_MINUTE = "HH:mm";

    public static final String FORMAT_YEAR_MONTH_DAY = "yyyy-MM-dd";

    public static final String FORMAT_YEARMONTHDAY = "yyyyMMdd";

    public static final LocalDate excelDate = parseLocalDate("1900-01-01");

    public static final String FORMAT_HOUR_MINUTE_SECOND = "HH:mm:ss";

    /**
     * 将 LocalDateTime 转换成 Date
     *
     * @param date LocalDateTime
     * @return LocalDateTime
     */
    public static Date of(LocalDateTime date) {
        // 将此日期时间与时区相结合以创建 ZonedDateTime
        ZonedDateTime zonedDateTime = date.atZone(ZoneId.systemDefault());
        // 本地时间线 LocalDateTime 到即时时间线 Instant 时间戳
        Instant instant = zonedDateTime.toInstant();
        // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
        return Date.from(instant);
    }


    /**
     * 将时间截取到分钟
     */
    public static LocalDateTime truncateToMinute(LocalDateTime time) {
        if (time == null) {
            return null;
        }
        return time.truncatedTo(ChronoUnit.MINUTES);
    }

    /**
     * 将 Date 转换成 LocalDateTime
     *
     * @param date Date
     * @return LocalDateTime
     */
    public static LocalDateTime of(Date date) {
        // 转为时间戳
        Instant instant = date.toInstant();
        // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    public static Date addTime(Duration duration) {
        return new Date(System.currentTimeMillis() + duration.toMillis());
    }

    public static boolean isExpired(Date time) {
        return System.currentTimeMillis() > time.getTime();
    }

    public static boolean isExpired(LocalDateTime time) {
        LocalDateTime now = LocalDateTime.now();
        return now.isAfter(time);
    }

    public static long diff(Date endTime, Date startTime) {
        return endTime.getTime() - startTime.getTime();
    }

    /**
     * 创建指定时间
     *
     * @param year  年
     * @param mouth 月
     * @param day   日
     * @return 指定时间
     */
    public static Date buildTime(int year, int mouth, int day) {
        return buildTime(year, mouth, day, 0, 0, 0);
    }

    public static LocalDateTime buildLocalDateTime(int year, int mouth, int day) {
        return LocalDateTime.of(year, mouth, day, 0, 0, 0);
    }

    /**
     * 创建指定时间
     *
     * @param year   年
     * @param mouth  月
     * @param day    日
     * @param hour   小时
     * @param minute 分钟
     * @param second 秒
     * @return 指定时间
     */
    public static Date buildTime(int year, int mouth, int day,
                                 int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, mouth - 1);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        calendar.set(Calendar.MILLISECOND, 0); // 一般情况下，都是 0 毫秒
        return calendar.getTime();
    }

    public static Date max(Date a, Date b) {
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.compareTo(b) > 0 ? a : b;
    }

    public static LocalDateTime max(LocalDateTime a, LocalDateTime b) {
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.isAfter(b) ? a : b;
    }

    public static boolean beforeNow(Date date) {
        return date.getTime() < System.currentTimeMillis();
    }

    public static boolean afterNow(Date date) {
        return date.getTime() >= System.currentTimeMillis();
    }

    public static boolean afterNow(LocalDateTime localDateTime) {
        return localDateTime.isAfter(LocalDateTime.now());
    }

    /**
     * 计算当期时间相差的日期
     *
     * @param field  日历字段.<br/>eg:Calendar.MONTH,Calendar.DAY_OF_MONTH,<br/>Calendar.HOUR_OF_DAY等.
     * @param amount 相差的数值
     * @return 计算后的日志
     */
    public static Date addDate(int field, int amount) {
        return addDate(null, field, amount);
    }

    /**
     * 计算当期时间相差的日期
     *
     * @param date   设置时间
     * @param field  日历字段 例如说，{@link Calendar#DAY_OF_MONTH} 等
     * @param amount 相差的数值
     * @return 计算后的日志
     */
    public static Date addDate(Date date, int field, int amount) {
        if (amount == 0) {
            return date;
        }
        Calendar c = Calendar.getInstance();
        if (date != null) {
            c.setTime(date);
        }
        c.add(field, amount);
        return c.getTime();
    }

    /**
     * 是否今天
     *
     * @param date 日期
     * @return 是否
     */
    public static boolean isToday(Date date) {
        if (date == null) {
            return false;
        }
        return DateUtil.isSameDay(date, new Date());
    }

    /**
     * 是否今天
     *
     * @param date 日期
     * @return 是否
     */
    public static boolean isToday(LocalDateTime date) {
        return LocalDateTimeUtil.isSameDay(date, LocalDateTime.now());
    }

    public static LocalDateTime parseLocalDateTime(String datetime) {
        return parseLocalDateTime(datetime, DatePattern.NORM_DATETIME_PATTERN);
    }

    public static LocalDateTime parseLocalDateTime(String datetime, String pattern) {
        return LocalDateTime.parse(datetime, DateTimeFormatter.ofPattern(pattern));
    }

    public static LocalDate parseLocalDate(String date) {
        return parseLocalDate(date, DatePattern.NORM_DATE_PATTERN);
    }

    public static LocalDate parseLocalDate(String date, String pattern) {
        return LocalDate.parse(date, DateTimeFormatter.ofPattern(pattern));
    }

    public static LocalTime parseLocalTime(String time) {
        return LocalTime.parse(time, DateTimeFormatter.ofPattern(DatePattern.NORM_TIME_PATTERN));
    }

    public static LocalTime parseLocalTime(String time, String pattern) {
        return LocalTime.parse(time, DateTimeFormatter.ofPattern(pattern));
    }

    public static String format(LocalDateTime dateTime, String pattern) {
        return dateTime.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String format(LocalDateTime dateTime) {
        return format(dateTime, DatePattern.NORM_DATETIME_PATTERN);
    }

    public static String format(LocalDate date, String pattern) {
        return date.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String format(LocalDate date) {
        return format(date, DatePattern.NORM_DATE_PATTERN);
    }

    public static String format(LocalTime time, String pattern) {
        return time.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String format(LocalTime time) {
        return format(time, DatePattern.NORM_TIME_PATTERN);
    }

    public static String datestamp() {
        return format(LocalDate.now(), DatePattern.PURE_DATE_PATTERN);
    }

    /**
     * 获取年月
     *
     * @param date 日期
     * @return
     */
    public static String getYearMonth(LocalDate date) {
        return getYearMonth(date, "-");
    }

    /**
     * 获取年月
     *
     * @param date 日期
     * @param sep  分隔符
     * @return
     */
    public static String getYearMonth(LocalDate date, String sep) {
        int month = date.getMonthValue();
        if (month < 10) {
            return date.getYear() + sep + "0" + month;
        }
        return date.getYear() + sep + month;
    }

    public static boolean equalsWeek(LocalDate date, DayOfWeek week) {
        Assert.notNull(date);
        return date.getDayOfWeek().equals(week);
    }

    /**
     * 获取两个日期之间包含的天数，包括头尾
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return 包含的天数
     */
    public static long dayLength(LocalDate start, LocalDate end) {
        return Math.abs(end.toEpochDay() - start.toEpochDay()) + 1;
    }

    public static LocalDate ofExcelDay(long day) {
        return excelDate.plusDays(day - 2);
    }

    /**
     * 当天开始时间
     *
     * @return
     */
    public static Date getDayStartTime(Date date) {
        Calendar start = Calendar.getInstance();
        start.setTime(date);
        start.set(Calendar.HOUR_OF_DAY, 0);
        start.set(Calendar.MINUTE, 0);
        start.set(Calendar.SECOND, 0);
        start.set(Calendar.MILLISECOND, 0);
        return start.getTime();
    }

    /**
     * 当天结束时间
     *
     * @return
     */
    public static Date getDayEndTime(Date date) {
        Calendar end = Calendar.getInstance();
        end.setTime(date);
        end.set(Calendar.HOUR_OF_DAY, 23);
        end.set(Calendar.MINUTE, 59);
        end.set(Calendar.SECOND, 59);
        end.set(Calendar.MILLISECOND, 999);
        return end.getTime();
    }

    /**
     * 计算两个日期相差天数
     *
     * @param start 日期1
     * @param end   日期2
     * @return 相差天数
     */
    public static long between(LocalDate start, LocalDate end) {
        return end.toEpochDay() - start.toEpochDay();
    }

    /**
     * 判断两个时间段是否有交集。
     *
     * @param start1 第一个时间段的开始时间
     * @param end1 第一个时间段的结束时间
     * @param start2 第二个时间段的开始时间
     * @param end2 第二个时间段的结束时间
     * @return 如果两个时间段有交集，则返回 true；否则返回 false
     */
    public static boolean isTimeRangesOverlap(LocalDateTime start1, LocalDateTime end1,
                                              LocalDateTime start2, LocalDateTime end2) {
        // 检查第一个时间段的结束时间是否在第二个时间段的开始时间之后
        boolean end1AfterStart2 = !end1.isBefore(start2);

        // 检查第二个时间段的结束时间是否在第一个时间段的开始时间之后
        boolean end2AfterStart1 = !end2.isBefore(start1);

        // 如果上述两个条件都满足，则两个时间段有交集
        return end1AfterStart2 && end2AfterStart1;
    }

}
