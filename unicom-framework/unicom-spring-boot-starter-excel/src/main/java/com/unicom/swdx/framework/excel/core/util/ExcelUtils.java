package com.unicom.swdx.framework.excel.core.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.unicom.swdx.framework.common.exception.ServiceException;
import com.unicom.swdx.framework.excel.core.handler.SelectedColumnWriteHandler;
import com.unicom.swdx.framework.excel.core.listener.HeadCheckListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.enums.ErrorCodeConstants.EXPORT_FAILED;
import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * Excel 工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtils {

    public static final String HEAD_ERROR_MSG = "模板表头字段错误";

    public static final int ERROR_CODE = 1011111000;

    /**
     * 将列表以 Excel 响应给前端
     *
     * @param response  响应
     * @param filename  文件名
     * @param sheetName Excel sheet 名
     * @param head      Excel head 头
     * @param data      数据列表哦
     * @param <T>       泛型，保证 head 和 data 类型的一致性
     * @throws IOException 写入失败的情况
     */
    public static <T> void write(HttpServletResponse response, String filename, String sheetName,
                                 Class<T> head, List<T> data) throws IOException {
//        writeByIncludeColumnIndexes(response, filename, sheetName, head, data, null);

        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 输出 Excel
        try {
            EasyExcel.write(response.getOutputStream(), head)
                    // 不要自动关闭，交给 Servlet 自己处理
                    .autoCloseStream(false)
                    // 基于 column 长度，自动适配。最大 255 宽度
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet(sheetName).doWrite(data);
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }

    /**
     * 将指定列的列表以 Excel 响应给前端
     *
     * @param response             响应
     * @param filename             文件名
     * @param sheetName            Excel sheet 名
     * @param head                 Excel head 头
     * @param data                 数据列表哦
     * @param includeColumnIndexes 指定导出列索引，为空则全部导出
     * @param <T>                  泛型，保证 head 和 data 类型的一致性
     * @throws IOException 写入失败的情况
     */
    public static <T> void writeByIncludeColumnIndexes(HttpServletResponse response, String filename, String sheetName,
                                                       Class<T> head, List<T> data, Collection<Integer> includeColumnIndexes) throws IOException {
        writeByIncludeColumnIndexes(response, filename, sheetName, head, null, data, includeColumnIndexes);
    }

    /**
     * 将指定列的列表以 Excel 响应给前端
     *
     * @param response             响应
     * @param filename             文件名
     * @param sheetName            Excel sheet 名
     * @param head                 Excel head 头
     * @param heads                Excel heads 自定义头（优先于head、默认样式）
     * @param data                 数据列表哦
     * @param includeColumnIndexes 指定导出列索引，为空则全部导出
     * @param <T>                  泛型，保证 head 和 data 类型的一致性
     * @throws IOException 写入失败的情况
     */
    public static <T> void writeByIncludeColumnIndexes(HttpServletResponse response, String filename, String sheetName,
                                                       Class<T> head, List<List<String>> heads, List<T> data, Collection<Integer> includeColumnIndexes) throws IOException {
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 输出 Excel
        try {
            ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(response.getOutputStream())
                    .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy());
            if (Objects.nonNull(heads) && !heads.isEmpty()) {
                excelWriterBuilder.head(heads);
            } else {
                excelWriterBuilder.head(head);
            }
            // 指定导出列索引
            if (Objects.nonNull(includeColumnIndexes) && !includeColumnIndexes.isEmpty()) {
                excelWriterBuilder.includeColumnIndexes(includeColumnIndexes);
            }
            excelWriterBuilder // 基于 column 长度，自动适配。最大 255 宽度
                    .sheet(sheetName).doWrite(data);
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }

    }

    /**
     * 将指定列的列表以 Excel 响应给前端
     *
     * @param response              响应
     * @param filename              文件名
     * @param sheetName             Excel sheet 名
     * @param head                  Excel head 头
     * @param data                  数据列表哦
     * @param selectedCellColumnMap 需要设置的下拉框列和对应可选值 {列索引值：下拉框列表值}
     * @param excludeColumnIndexes  指定不导出列索引
     * @param <T>                   泛型，保证 head 和 data 类型的一致性
     * @throws IOException 写入失败的情况
     */
    public static <T> void write(HttpServletResponse response, String filename, String sheetName,
                                 Class<T> head, List<T> data, Map<Integer, List<String>> selectedCellColumnMap, Collection<Integer> excludeColumnIndexes) throws IOException {
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 输出 Excel
        try {
            ExcelWriterBuilder writerBuilder = EasyExcel.write(response.getOutputStream(), head);
            if (Objects.nonNull(excludeColumnIndexes) && !excludeColumnIndexes.isEmpty()) {
                writerBuilder.excludeColumnIndexes(excludeColumnIndexes);
            }
            if (Objects.nonNull(selectedCellColumnMap)) {
                // 创建下拉框实现Handler 并传入数据
                WriteHandler selectedColumnWriteHandler = new SelectedColumnWriteHandler(selectedCellColumnMap);
                writerBuilder.registerWriteHandler(selectedColumnWriteHandler);
            }
            writerBuilder.autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())// 基于 column 长度，自动适配。最大 255 宽度
                    .sheet(sheetName).doWrite(data);
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }

    /**
     * 将指定列的列表以 Excel 响应给前端
     *
     * @param response      响应
     * @param filename      文件名
     * @param sheetDataList 多页sheet数据
     * @throws IOException 写入失败的情况
     */
    public static void write(HttpServletResponse response, String filename, List<ExcelDataWriteDTO<?>> sheetDataList) throws IOException {
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 输出 Excel
        try {
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
            for (int i = 0; i < sheetDataList.size(); i++) {
                ExcelDataWriteDTO<?> sheetData = sheetDataList.get(i);
                // 设置sheet名 、 列名
                ExcelWriterSheetBuilder writerSheetBuilder = EasyExcel.writerSheet(i, sheetData.getSheetName());
                if (Objects.nonNull(sheetData.getHead())) {
                    writerSheetBuilder.head(sheetData.getHead());
                }

                // 指定不导出列索引
                if (Objects.nonNull(sheetData.getExcludeColumnIndexes()) && !sheetData.getExcludeColumnIndexes().isEmpty()) {
                    writerSheetBuilder.excludeColumnIndexes(sheetData.getExcludeColumnIndexes());
                }

                // 指定导出列索引
                if (Objects.nonNull(sheetData.getIncludeColumnIndexes()) && !sheetData.getIncludeColumnIndexes().isEmpty()) {
                    writerSheetBuilder.includeColumnIndexes(sheetData.getIncludeColumnIndexes());
                }

                // 自定义WriteHandler
                if (Objects.nonNull(sheetData.getWriteHandlerList()) && !sheetData.getWriteHandlerList().isEmpty()) {
                    for (WriteHandler writeHandler : sheetData.getWriteHandlerList()) {
                        writerSheetBuilder.registerWriteHandler(writeHandler);
                    }
                }
                excelWriter.write(sheetData.getData(), writerSheetBuilder.build());
            }
            excelWriter.finish();
            response.flushBuffer();
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }


    /**
     * 动态表头
     */
    public static <T> void write2(HttpServletResponse response, String filename, List<List<String>> heads, String sheetName,
                                  Class<T> head, List<T> data, WriteHandler localHandler) throws IOException {
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 输出 Excel
        try {
            EasyExcel.write(response.getOutputStream(), head).head(heads)
                    .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                    .registerWriteHandler(localHandler) // 基于 column 长度，自动适配。最大 255 宽度
                    .sheet(sheetName).doWrite(data);
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }


    public static <T> List<T> read(MultipartFile file, Class<T> head) throws IOException {
        return EasyExcel.read(file.getInputStream(), head, null)
                .autoCloseStream(false)  // 不要自动关闭，交给 Servlet 自己处理
                .doReadAllSync();
    }

    public static <T> List<T> readFirstSheetAndCheckHead(MultipartFile file, Class<T> head) throws IOException {
        HeadCheckListener<T> headCheckListener = new HeadCheckListener<>();
        List<T> data = EasyExcel.read(file.getInputStream(), head, headCheckListener)
                .autoCloseStream(false)  // 不要自动关闭，交给 Servlet 自己处理
                .sheet(0)
                .doReadSync();
        checkExcelHead(headCheckListener.getExcelHeadList(), head);
        return data;
    }

    /**
     * 第3行为数据，第2行为表头
     */
    public static <T> List<T> readFirstSheetAndCheckHead1(MultipartFile file, Class<T> head) throws IOException {
        HeadCheckListener<T> headCheckListener = new HeadCheckListener<>();
        List<T> data = EasyExcel.read(file.getInputStream(), head, headCheckListener)
                .autoCloseStream(false)  // 不要自动关闭，交给 Servlet 自己处理
                .sheet(0)
                .headRowNumber(2)
                .doReadSync();
        checkExcelHead(headCheckListener.getExcelHeadList(), head);
        return data;
    }

    /**
     * 校验excel表头 （模板校验）
     * 目前仅支持单行的简单表头校验，复杂表头将会停止校验
     * 依赖于@ExcelProperty注解的value和index参数
     *
     * @param excelHeadList excel文件的表头
     * @param headClass     excel对应class
     * @param <T>           泛型
     */
    public static <T> void checkExcelHead(List<String> excelHeadList, Class<T> headClass) {
        // 存储模板表头信息
        List<String> templateHead = getTemplate(headClass);
        if (templateHead.size() != excelHeadList.size()) {
            throw new ServiceException(ERROR_CODE, HEAD_ERROR_MSG);
        }
        // 校验模板表头
        for (int i = 0; i < excelHeadList.size(); i++) {
            if (!templateHead.get(i).equals(excelHeadList.get(i))) {
                throw new ServiceException(ERROR_CODE, HEAD_ERROR_MSG);
            }
        }
    }

    private static <T> List<String> getTemplate(Class<T> headClass) {
        List<String> templateHead = new ArrayList<>();
        // 获取类的所有属性
        List<Field> fields = getAllField(headClass);
        for (Field field : fields) {
            // 获取注解上的excelProperty注解
            ExcelProperty[] excelProperties = field.getAnnotationsByType(ExcelProperty.class);
            // 如果不存在则跳过
            if (excelProperties.length == 0) {
                continue;
            }
            // 获取规定模板表头的value
            String[] headValues = excelProperties[0].value();
            if (headValues.length > 1) {
                log.warn("===警告=== 表头校验目前仅支持单行表头");
                throw new ServiceException(ERROR_CODE, "多行复杂表头不支持校验");
            }
            if (headValues.length == 1) {
                templateHead.add(headValues[0]);
            }
        }
        return templateHead;
    }

    /**
     * 获取子类及其父类的所有属性
     *
     * @param clazz class
     * @return 属性list
     */
    private static List<Field> getAllField(Class<?> clazz) {
        List<Field> list = new ArrayList<>();
        while (clazz != Object.class) {
            list.addAll(Arrays.stream(clazz.getDeclaredFields()).collect(Collectors.toList()));
            // 获取父类
            clazz = clazz.getSuperclass();
        }
        return list;
    }

}
