package com.unicom.swdx.module.edu.enums.attendance;

/**
 * <AUTHOR>
 * @Description: 班主任更新学员打卡状态枚举 0-未到 1-正常 2-迟到 3-事假 4-病假 5-五会假
 * @date 2024-11-05
 */
public enum UpdateAttendanceStatusEnum {

    /**
     * 0 - 未到
     */
    NOT_ARRIVE(0, "未打卡"),

    /**
     * 1 - 已到
     */
    ARRIVED(1, "已打卡"),

    /**
     * 2 - 迟到
     */
    LATE(2, "迟到"),

    /**
     * 3 - 事假
     */
    PERSONAL_LEAVE(3, "事假"),

    /**
     * 4 - 病假
     */
    SICK_LEAVE(4, "病假"),

    /**
     * 5 - 五会假
     */
    FIVE_MEETING_LEAVE(5, "五会假");

    private final Integer status;

    private final String desc;

    UpdateAttendanceStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态获取描述
     *
     * @param status 状态
     * @return 描述
     */
    public static String getDescByStatus(Integer status) {
        for (UpdateAttendanceStatusEnum item : UpdateAttendanceStatusEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取状态
     *
     * @param desc 描述
     * @return 状态
     */
    public static Integer getStatusByDesc(String desc) {
        for (UpdateAttendanceStatusEnum item : UpdateAttendanceStatusEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getStatus();
            }
        }
        return null;
    }

}
