package com.unicom.swdx.module.edu.enums.classmanagement;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学期枚举
 */
@Getter
@AllArgsConstructor
public enum SemesterEnum {

    /**
     * 上学期
     */
    FIRST_SEMESTER(1, "上学期"),

    /**
     * 下学期
     */
    SECOND_SEMESTER(2, "下学期");


    private final Integer code;

    private final String desc;

    /**
     * 根据值获取对应的描述
     * @param period 值
     * @return 描述
     */
    public static String getDescByCode(Integer period) {
        for (SemesterEnum item : values()) {
            if (item.getCode().equals(period)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取对应的值
     * @param desc 描述
     * @return 值
     */
    public static Integer getCodeByDesc(String desc) {
        for (SemesterEnum item : values()) {
            if (item.getDesc().equals(desc)) {
                return item.getCode();
            }
        }
        return null;
    }

}
