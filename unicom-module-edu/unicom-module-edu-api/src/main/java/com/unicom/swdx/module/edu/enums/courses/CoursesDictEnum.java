package com.unicom.swdx.module.edu.enums.courses;


/**
 * <AUTHOR>
 * @Description: 课程相关的一些字典的type
 * @date 2024-10-11
 */
public enum CoursesDictEnum {

    THEME("edu_course_classification", "专题分类"),
    OPTIONAL_THEME("edu_select_classification", "课程分类"),
    EDUCATE_FORM("edu_teaching_form", "教学形式"),
    TEACHING_METHOD("edu_teaching_method", "教学方式"),
    EDU_ACTIVITY("edu_activity", "教学活动");

    private final String type;

    private final String desc;

    CoursesDictEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getThemeTypeByCoursesType(Integer coursesType) {
        switch (coursesType) {
            case 1:
                return THEME.getType();
            case 2:
                return OPTIONAL_THEME.getType();
            default:
                return null;
        }
    }

    /**
     * 根据类型获取描述
     *
     * @param type 类型
     * @return 描述
     */
    public static String getDescByType(String type) {
        for (CoursesDictEnum item : CoursesDictEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取类型
     *
     * @param desc 描述
     * @return 类型值
     */
    public static String getTypeByDesc(String desc) {
        for (CoursesDictEnum item : CoursesDictEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getType();
            }
        }
        return null;
    }


}
