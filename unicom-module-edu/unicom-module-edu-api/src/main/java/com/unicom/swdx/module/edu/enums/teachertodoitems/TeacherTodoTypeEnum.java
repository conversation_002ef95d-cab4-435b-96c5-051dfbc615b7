package com.unicom.swdx.module.edu.enums.teachertodoitems;


/**
 * <AUTHOR>
 * @Description: 班主任待办事项类型枚举
 * @date 2024-10-11
 */
public enum TeacherTodoTypeEnum {
    /**
     * 0 - 请假申请
     */
    LEAVE_APPLY(0, "请假申请"),
    /**
     * 1 - 报名确认
     */
    ENROLLMENT_CONFIRMATION(1, "报名确认");

    private final Integer type;

    private final String desc;

    TeacherTodoTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据类型获取描述
     * @param type 类型
     * @return 描述
     */
    public static String getDescByType(Integer type) {
        for (TeacherTodoTypeEnum item : TeacherTodoTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述获取类型
     * @param desc 描述
     * @return 类型值
     */
    public static Integer getTypeByDesc(String desc) {
        for (TeacherTodoTypeEnum item : TeacherTodoTypeEnum.values()) {
            if (item.getDesc().equals(desc)) {
                return item.getType();
            }
        }
        return null;
    }


}
