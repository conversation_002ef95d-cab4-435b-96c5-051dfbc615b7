<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.evaluationresponse.EvaluationResponseMapper">
    <update id="updateEvaluation">
        update pg_evaluation_response
        set handle = 1,
            score = #{submitVO.score},
            grade = #{submitVO.grade},
            update_time = now(),
            submit_time = #{submitVO.submitTime},
            revocable_time = #{submitVO.revocableTime},
            low_score_reason = #{submitVO.lowScoreReason}
        where student_id = #{userId}
        and class_course_id = #{submitVO.classCourseId}
        and questionnaire_id = #{submitVO.questionnaireId}
    </update>
    <update id="modifyEvaluation"
            parameterType="com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.EvaluationModifyVO">
        update pg_evaluation_response
        set handle = #{reqVO.handle},
            score = #{reqVO.score}
        where
            questionnaire_id = #{reqVO.questionnaireId}
          and class_course_id = #{reqVO.classCourseId}
          and student_id = #{reqVO.studentId}
    </update>
    <update id="revoke" parameterType="java.lang.Long">
        update pg_evaluation_response
        set handle = 0,
            score = 0
        where id = #{id}
    </update>
    <update id="distribute">
        update pg_evaluation_response
        set handle = 0,
            score = 0,
            expire_time = #{expireTime}
        where id = #{id}
    </update>
    <update id="revokeEvaluated">
        update pg_evaluation_response
        set handle = 0,
--             submit_time = null,
            revocable_time = null
        where id = #{id}
    </update>

    <delete id="batchDeleteByPairOfTraineeIdAndClassCourseId">
        delete from pg_evaluation_response
        WHERE (student_id, class_course_id) IN
        <foreach item="item" collection="pairs" open="(" separator="," close=")">
            (#{item.key}, #{item.value})
        </foreach>
    </delete>

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getMyEvaluationPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.MyEvaluationPageRespVO">
        select
        t1.classCourseId classCourseId,
        t1.courseId courseId,
        ecct.teacher_id teacherId,
        case
            when ecc.department = false then eti."name"
            when ecc.department = true then ecct.dept_name
        end as teacherName,
        case
            when ecc.department = false then eti.dept_names
            when ecc.department = true then ecct.dept_name
        end as deptNames,
        ecc.department department,
        ec."name" courseName,
        ec.educate_form_id educateFormId,
        ec.courses_type courseType,
        ecc.begin_time classStartTime,
        ecc.end_time classEndTime,
        ecc.period dayPeriod,
        ecc."date" classDate,
        ecc.class_id classId,
        ecm.class_name className,
        t1.actualCount actualCount,
        t1.expectedCount expectedCount,
        t1.ratio as ratio,
        t1.averageScore averageScore
        from
        (
        select
        per.class_course_id classCourseId,
        per.course_id courseId,
        count(1) expectedCount,
        count(per.handle = 1 or null) actualCount,
        actualCount / coalesce(nullif(expectedCount,0),1) as ratio,
        ifnull(AVG(case when per.handle = 1 then per.score end),0) as averageScore
        from
        pg_evaluation_response per
        join edu_class_course ecc on
        ecc.id = per.class_course_id
        and ecc.deleted = 0
        join edu_trainee et on
        et.id = per.student_id
        and et.deleted = 0
        where
        per.deleted = 0
        and per.remarktype = 0
        group by
        per.class_course_id,per.course_id
        ) t1
        join edu_class_course ecc on
        ecc.id = t1.classCourseId
        and ecc.deleted = 0

        join edu_class_course_teacher ecct on
        t1.classCourseId = ecct.class_course_id
        and t1.courseId = ecct.course_id
        and ecct.deleted = 0

        join edu_courses ec on
        ecct.course_id = ec.id
        and ec.deleted = 0

        join edu_class_management ecm on
        ecc.class_id = ecm.id
        and ecm.deleted = 0

        left join edu_teacher_information eti on
        eti.id = ecct.teacher_id
        and eti.deleted = 0
        where 1=1
        <if test="reqVO.startTime != null">
            and cast(ecc."date" AS date) &gt;= #{reqVO.startTime}
        </if>
        <if test="reqVO.endTime != null">
            and cast(ecc."date" AS date) &lt;= #{reqVO.endTime}
        </if>
        <if test="reqVO.courseEndTime != null">
            and ecc.end_time &lt;= #{reqVO.courseEndTime}
        </if>
        order by
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            classStartTime
        </if>
        <if test="reqVO.sortField == 1">
            courseName
        </if>
        <if test="reqVO.sortField == 2 or reqVO.sortField == 5">
            averageScore
        </if>
        <if test="reqVO.sortField == 3">
            teacherName
        </if>
        <if test="reqVO.sortField == 4">
            deptNames
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
    </select>

    <select id="getTeacherEvaluationPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.TeacherEvaluationPageRespVO">
        select *
        from (
        select
            classCourseId,
            courseId,
            courseName,
            educateFormId,
            classStartTime,
            classEndTime,
            dayPeriod,
            classDate,
            classId,
            className,
            CASE
            WHEN department = false THEN teacherId2
            WHEN department = true THEN tempDeptId
            END AS teacherId,
            CASE
            WHEN department = false THEN teacherName2
            WHEN department = true THEN deptNames
            END AS teacherName,
            case
            when department = false then eti.dept_names
            when department = true then deptNames
            end as deptNames,
            tempDeptId,
            department,
            expectedCount,
            actualCount,
            ratio,
            averageScore
        from(
        select
        per.class_course_id classCourseId,
        min(ecc.course_id) courseId,
        min(ec."name" ) courseName,
        min(ec.educate_form_id) educateFormId,
        min(ecc.begin_time) classStartTime,
        min(ecc.end_time) classEndTime,
        min(ecc.period) dayPeriod,
        min(ecc."date") classDate,
        min(ecc.class_id ) classId,
        min(ecm.class_name) className,
        unnest(array_agg(distinct eti.id)) teacherId2,
        unnest(array_agg(distinct eti.name)) teacherName2,
        min(ecct.dept_name) deptNames,
        min(ecct.teacher_id) tempDeptId,
        min(ecc.department) department,
        count(distinct per.student_id) expectedCount,
        count(DISTINCT CASE WHEN per.handle = 1 OR per.handle IS NULL THEN per.student_id END) actualCount,
        actualCount /COALESCE(NULLIF(expectedCount, 0), 1) as ratio,
        ifnull(AVG(CASE WHEN per.handle = 1 THEN per.score END),0) AS averageScore
        from pg_evaluation_response per
        join edu_class_course ecc on ecc.id = per.class_course_id and ecc.deleted = 0
        join edu_courses ec on per.course_id = ec.id and ec.deleted = 0
        join edu_class_management ecm on ecc.class_id = ecm.id and ecm.deleted = 0
        join edu_class_course_teacher ecct on ecct.class_course_id = ecc.id and ecct.deleted = 0
        left join edu_teacher_information eti on eti.id = ecct.teacher_id and eti.deleted = 0
        join edu_trainee et on et.id = per.student_id and et.deleted=0
        where per.deleted = 0
        and per.remarktype = 0
        <if test="reqVO.startTime != null">
            and cast(ecc."date" AS date) &gt;= #{reqVO.startTime}
        </if>
        <if test="reqVO.endTime != null">
            and cast(ecc."date" AS date) &lt;= #{reqVO.endTime}
        </if>
        group by per.class_course_id
        )
        left join edu_teacher_information eti on
            eti.id = teacherId2
            and eti.deleted = 0
        )
        where 1=1
        <if test="reqVO.teacherName != null and reqVO.teacherName != ''">
            and teacherName like concat('%',#{reqVO.teacherName},'%')
        </if>
        <if test="reqVO.deptName != null and reqVO.deptName != ''">
            and deptNames like concat('%',#{reqVO.deptName},'%')
        </if>
        <if test="reqVO.classId != null">
            and classId = #{reqVO.classId}
        </if>
        <if test="reqVO.courseName != null and reqVO.courseName != ''">
            and courseName like concat('%',#{reqVO.courseName},'%')
        </if>
        <if test="reqVO.teacherId != null">
            and teacherId = #{reqVO.teacherId}
        </if>
        <if test="reqVO.ids != null and reqVO.ids.size() > 0">
            and classCourseId in
            <foreach item="id" collection="reqVO.ids" separator="," open="(" close=")" index="">
                #{id}
            </foreach>
        </if>
        <if test="reqVO.teacherIdList != null and reqVO.teacherIdList.size() > 0">
            and teacherId in
            <foreach item="teacherId" collection="reqVO.teacherIdList" separator="," open="(" close=")" index="">
                #{teacherId}
            </foreach>
        </if>
        <if test="reqVO.manyIds != null and reqVO.manyIds.size() > 0">
            and (teacherId, classCourseId) in
            <foreach item="item" collection="reqVO.manyIds" separator="," open="(" close=")" index="">
                (#{item.teacherId}, #{item.classCourseId})
            </foreach>
        </if>
        order by
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            classStartTime
        </if>
        <if test="reqVO.sortField == 1">
            courseName
        </if>
        <if test="reqVO.sortField == 2 or reqVO.sortField == 5">
            averageScore
        </if>
        <if test="reqVO.sortField == 3">
            teacherName
        </if>
        <if test="reqVO.sortField == 4">
            deptNames
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
    </select>

    <select id="getMyEvaluationDetail"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.MyEvaluationDetailPageRespVO">
        select
            per.score score,
            count(1) "count"
        from pg_evaluation_response per
        join edu_trainee et on et.id = per.student_id
            and et.deleted=0
        where per.deleted = 0
          and per.remarktype = '0'
          and per.class_course_id = #{reqVO.classCourseId}
          and per.course_id = #{reqVO.courseId}
          and per.handle = 1
        group by per.score
        order by score desc
    </select>
    <select id="getDeptEvaluationPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationPageRespVO">

    select * from (
        select
        t1.teacher_id,
        CASE
        WHEN min(t1.department) = false THEN t1.deptId
        when min(t1.department) = true THEN t1.teacher_id
        END AS deptId,
        CASE
        WHEN min(t1.department) = false then min(t1.deptName)
        when min(t1.department) = true THEN min(t1.courseDeptName)
        END AS deptName,
        CASE
        WHEN min(t1.department) = false then min(t1.teacherName)
        when min(t1.department) = true THEN min(t1.courseDeptName)
        END AS teacherName,
        min(t1.department) department,
        count(distinct t1.classId) classCount,
        count(distinct t1.classCourseId) teachingCount,
        AVG(t1.averageScore) as averageScore
        from (
        select
        ecct.teacher_id ,
        per.class_course_id classCourseId,
        min(ecc.class_id) classId,
        min(eti.dept_name) deptName,
        min(ecc.department) department,
        min(ecct.dept_name) courseDeptName,
        eti.dept_id deptId,
        min(eti."teacher_name") teacherName,
        ifnull(AVG(case when per.handle = 1 then per.score end),
        0) as averageScore
        from
        pg_evaluation_response per
        join edu_class_course ecc on
        ecc.id = per.class_course_id
        and ecc.deleted = 0
        join edu_courses ec on per.course_id = ec.id and ec.deleted = 0
        join edu_class_management ecm on ecc.class_id = ecm.id and ecm.deleted = 0
        join edu_trainee et on et.id = per.student_id and et.deleted=0
        join edu_class_course_teacher ecct on ecct.class_course_id = ecc.id and ecct.deleted = 0
                                                  and ecct.course_id = per.course_id
        left join edu_teacher_dept eti on
        eti.teacher_id = ecct.teacher_id
        where
        per.deleted = 0
        and per.remarktype = '0'
                <if test="reqVO.name != null and reqVO.name != ''">
                    and eti."teacher_name" like concat('%',#{reqVO.name},'%')
                </if>
                <if test="reqVO.startTime != null">
                    and cast(ecc."date" AS date) &gt;= #{reqVO.startTime}
                </if>
                <if test="reqVO.endTime != null">
                    and cast(ecc."date" AS date) &lt;= #{reqVO.endTime}
                </if>
            group by
                ecct.teacher_id,
                eti.dept_id,
                per.class_course_id
            ) t1
            group by t1.teacher_id, t1.deptId
            order by
                <if test="reqVO.sortField == null or reqVO.sortField == 0">
                    teacherName
                </if>
                <if test="reqVO.sortField == 1">
                    teachingCount
                </if>
                <if test="reqVO.sortField == 2">
                    classCount
                </if>
                <if test="reqVO.sortField == 3">
                    averageScore
                </if>
                <if test="reqVO.sortField == 4">
                    deptName
                </if>
                <if test="reqVO.isDesc == null or reqVO.isDesc == true">
                    DESC
                </if>
            )
            where 1=1
            <if test="reqVO.deptName != null and reqVO.deptName != ''">
                and deptName like concat('%',#{reqVO.deptName},'%')
            </if>
            <if test="reqVO.deptId != null">
                and deptId = #{reqVO.deptId}
            </if>
            <if test="reqVO.ids != null and reqVO.ids.size() > 0">
                and teacher_id in
                <foreach item="id" collection="reqVO.ids" separator="," open="(" close=")" index="">
                    #{id}
                </foreach>
            </if>
            <if test="reqVO.manyIds != null and reqVO.manyIds.size() > 0">
                and (teacher_id, deptId) in
                <foreach item="item" collection="reqVO.manyIds" separator="," open="(" close=")" index="">
                    (#{item.teacherId}, #{item.deptId})
                </foreach>
            </if>
            <if test="reqVO.deptIdList != null and reqVO.deptIdList.size() > 0">
                and deptId in
                <foreach item="deptId" collection="reqVO.deptIdList" separator="," open="(" close=")" index="">
                    #{deptId}
                </foreach>
            </if>
    </select>
    <select id="getClassEvaluationStatsPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.ClassEvaluationStatsPageRespVO">
        select
            t1.class_id,
            min(t1.campus) campus,
            min(t1.class_name_code) classNameCode,
            min(t1.class_name) className,
            min(t1.teacher_id) teacherId,
            min(t1.teacher_name) teacherName,
            min(t1."sort") "sort",
            min(t1.class_open_time) classOpenTime,
            min(t1.completion_time) completionTime,
            avg(t1.ratio) as averageRatio,
            avg(t1.avgScore) averageScore
        from
            (
                select
                    ecc.class_id,
                    per.class_course_id,
                    min(ecm.campus) campus,
                    min(ecm.class_name_code) class_name_code,
                    min(ecm.class_name) class_name,
                    min(eti.id) teacher_id,
                    min(eti."name") teacher_name,
                    min(ecm.class_open_time) class_open_time,
                    min(ecm.completion_time) completion_time,
                    min(ecm."sort") "sort",
                    count(1) expectedCount,
                    IFNULL(COUNT(per.handle = 1 or null), 0) actualCount,
                    actualCount / expectedCount as ratio,
                    COALESCE(SUM(CASE WHEN per.handle = 1 THEN per.score END), 0) /
                    IF(actualCount=0,1,actualCount) avgScore
                from
                    pg_evaluation_response per
                        join edu_class_course ecc on
                        ecc.id = per.class_course_id
                            and ecc.deleted = 0
                        join edu_class_management ecm on
                        ecm.id = ecc.class_id
                            and ecm.deleted = 0
                        join edu_teacher_information eti on
                        eti.id = ecm.class_teacher_lead
                            and eti.deleted = 0
                        join edu_trainee et on et.id = per.student_id
                                                          and et.deleted=0
                        join edu_courses ec on per.course_id = ec.id and ec.deleted = 0
        where
                    per.deleted = 0
                  and per.remarktype = '0'
                    <if test="reqVO.className != null and reqVO.className != ''">
                        AND ecm."class_name" LIKE CONCAT('%', #{reqVO.className}, '%')
                    </if>
                    <if test="reqVO.campus != null">
                        AND ecm."campus" like #{reqVO.campus}
                    </if>

                    <if test="reqVO.endTime != null and reqVO.endTime != '' and reqVO.startTime != null and reqVO.startTime != ''" >
                        AND  cast(ecm."class_open_time" as date) &lt;= #{reqVO.endTime}
                        AND  cast(ecm."class_open_time" as date) &gt;= #{reqVO.startTime}
                    </if>

                    <if test="reqVO.classStatus != null">
                        --  报名中
                        <if test="reqVO.classStatus == 0">
                            AND ecm."registration_start_time" &lt; NOW()
                            AND ecm."registration_end_time" &gt; NOW()
                            AND ecm."publish" = '1'
                            AND ecm."class_open_time" != ecm."registration_start_time"
                        </if>
                        --   报名结束
                        <if test="reqVO.classStatus == 1">
                            AND ecm."registration_end_time" &lt; NOW()
                            AND ecm."class_open_time" &gt; NOW()
                            AND ecm."publish" = '1'
                        </if>
                        --   开班中
                        <if test="reqVO.classStatus == 2">
                            AND ecm."class_open_time" &lt; NOW()
                            AND ecm."completion_time" &gt; NOW()
                            AND ecm."publish" = '1'
                        </if>
                        --   已结束
                        <if test="reqVO.classStatus == 3">
                            AND ecm."completion_time" &lt; NOW()
                            AND ecm."publish" = '1'
                        </if>
                        --  已发布 未开始的班级
                        <if test="reqVO.classStatus == 4">
                            AND NOW() &lt; ecm."class_open_time"
                            AND ecm."publish" = '1'
                        </if>
                    </if>
                    <if test="reqVO.ids != null and reqVO.ids.size() > 0">
                        and ecc.class_id in
                        <foreach collection="reqVO.ids" item="id" separator="," open="(" close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="reqVO.classIdList != null and reqVO.classIdList.size() > 0">
                        and ecc.class_id in
                        <foreach collection="reqVO.classIdList" item="id" separator="," open="(" close=")">
                            #{id}
                        </foreach>
                    </if>
                group by
                    ecc.class_id,
                    per.course_id,
                    per.class_course_id
            ) t1
        group by
            t1.class_id
        order by
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            "sort"
        </if>
        <if test="reqVO.sortField == 1">
            classNameCode
        </if>
        <if test="reqVO.sortField == 2">
            className
        </if>
        <if test="reqVO.sortField == 3">
            classOpenTime
        </if>
        <if test="reqVO.sortField == 4">
            averageRatio
        </if>
        <if test="reqVO.sortField == 5">
            averageScore
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
    </select>
    <select id="getTraineeEvaluationDetailPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.TraineeEvaluationDetailPageRespVO">
        select
            per.student_id traineeId,
            min(et."name" ) traineeName,
            count(1) expectedCount,
            count(per.handle = 1 or null) actualCount,
            actualCount / expectedCount as ratio,
            min(et.create_time) createTime
        from
            pg_evaluation_response per
                join edu_class_course ecc on
                ecc.id = per.class_course_id
                    and ecc.deleted = 0
                join edu_trainee et on et.id = per.student_id and et.deleted = 0
<!--                join edu_courses ec on per.course_id = ec.id and ec.deleted = 0-->
                join edu_class_management ecm on ecc.class_id = ecm.id and ecm.deleted = 0
                left join edu_teacher_information eti on eti.id = ecc.teacher_id and eti.deleted = 0
                left join edu_trainee_group etg on et.group_id = etg.id and etg.deleted = 0
        where
            per.deleted = 0
          and per.remarktype = '0'
          and ecc.class_id = #{reqVO.classId}
          <if test="reqVO.traineeName != null and reqVO.traineeName != ''">
              AND et."name" LIKE CONCAT('%', #{reqVO.traineeName}, '%')
          </if>
          <if test="reqVO.ids != null and reqVO.ids.size() > 0">
              and per.student_id in
              <foreach collection="reqVO.ids" item="id" separator="," open="(" close=")">
                  #{id}
              </foreach>
          </if>
        group by
            per.student_id, etg.sort,et.group_sort
        order by
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            etg.sort, et.group_sort,traineeId
        </if>
        <if test="reqVO.sortField == 1">
            traineeName
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
    </select>
    <select id="selectStudentEvaluation"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.EvaluationRespVO">
        select
            min(per.id) id,
            ec.name courseName,
            group_concat(ecct.teacher_id) teacherIds,
            min(per.department) department,
            min(per.handle) handle,
            min(per.score) score,
            ecc.id classCourseId,
            min(per.expire_time) expireTime,
            ecc.begin_time beginTime,
            min(per.questionnaire_id) questionnaireId,
            CASE WHEN min(per.revocable_time > now()) IS NOT NULL THEN min(per.revocable_time > now()) ELSE false END as revocable,
            CASE WHEN min(per.submit_time) IS NOT NULL THEN true ELSE false END AS revoked
        from pg_evaluation_response per
        join edu_class_course ecc on per.class_course_id = ecc.id and ecc.deleted = 0
        join edu_class_course_teacher ecct on ecc.id = ecct.class_course_id and ecct.deleted = 0
            and ecct.course_id = per.course_id
        join edu_courses ec on ecct.course_id = ec.id and ec.deleted = 0
        <where>
            per.deleted = 0
            <if test="handle != null">
                and per.handle = #{handle}
            </if>
            <if test="courseName != null and courseName != ''">
                and ec.name like concat('%', #{courseName}, '%')
            </if>
            <if test="userId != null">
                and per.student_id = #{userId}
            </if>
        </where>
        group by ecc.id, ec.id
        order by id desc
    </select>
    <select id="getTraineeEvaluatedAndUnEvaluatedDetailPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.TraineeEvaluatedAndUnEvaluatedDetailPageRespVO">
        select
            per.class_course_id,
            ec.id courseId ,
            ec."name" courseName,
            t.teacher_ids teacherIds ,
            CASE
                WHEN ecc.department = false THEN t."teacher_names"
                WHEN ecc.department = true THEN t.course_dept_name
            END AS teacherName,
            ecc.begin_time classStartTime,
            ecc.end_time classEndTime,
            ecc."date" classDate,
            ecc.period dayPeriod
        from
            pg_evaluation_response per
                join edu_class_course ecc on
                ecc.id = per.class_course_id
                    and ecc.deleted = 0
                join edu_courses ec on ec.id = per.course_id and ec.deleted = 0
                left join (select ecct.class_course_id   class_course_id,
                    ecct.course_id course_id,
                    group_concat(eti.name) teacher_names,
                    group_concat(ecct.teacher_id)   teacher_ids,
                    group_concat(ecct.dept_name)  course_dept_name
                    from edu_class_course_teacher ecct
                    left join edu_teacher_information eti
                    on eti.id = ecct.teacher_id and eti.deleted = 0
                    where ecct.deleted = 0
                group by ecct.class_course_id, ecct.course_id) t on t.class_course_id = ecc.id and t.course_id = per.course_id
                join edu_trainee et on et.id = per.student_id and et.deleted=0
                join edu_class_management ecm on ecc.class_id = ecm.id and ecm.deleted = 0
        where
            per.deleted = 0
          and per.remarktype = '0'
          and per.student_id = #{reqVO.traineeId}
          and per.handle = #{reqVO.status}
          <if test="reqVO.courseName != null and reqVO.courseName != ''">
              AND ec."name" LIKE CONCAT('%', #{reqVO.courseName}, '%')
          </if>
          <if test="reqVO.ids != null and reqVO.ids.size() > 0">
            and per.class_course_id in
            <foreach collection="reqVO.ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
          </if>
        order by ecc.begin_time
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
    </select>
    <select id="getCourseTraineeEvaluationDetailPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.CourseTraineeEvaluationDetailPageRespVO">
        select
            per.id,
            per.student_id traineeId,
            et."name" traineeName,
            et.sex ,
            per.score ,
            et.phone ,
            per.expire_time ,
            per.update_time evaluationTime,
            per.handle "status"
        from
            pg_evaluation_response per
                join edu_class_course ecc on ecc.id = per.class_course_id and ecc.deleted = 0
                join edu_courses ec on per.course_id = ec.id and ec.deleted = 0
                join edu_class_management ecm on ecc.class_id = ecm.id and ecm.deleted = 0
                left join edu_teacher_information eti on eti.id = ecc.teacher_id and eti.deleted = 0
                join edu_trainee et on et.id = per.student_id and et.deleted=0
        where
            per.deleted = 0
          and per.remarktype = '0'
          and per.class_course_id = #{reqVO.classCourseId}
          <if test="reqVO.traineeName != null and reqVO.traineeName != ''">
              AND et."name" LIKE CONCAT('%', #{reqVO.traineeName}, '%')
          </if>
        order by et."name"
    </select>

    <select id="getDeptEvaluationStatsPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationPageRespVO">
        select
            t2.deptId,
            min(t2.deptName) deptName,
            sum(t2.classCount) classCount,
            sum(t2.teachingCount) teachingCount,
            avg(t2.averageScore) averageScore
        from
            (
                select
                    t1.teacher_id,
                    CASE
                    WHEN min(t1.department)  = false THEN t1.deptId
                    when min(t1.department)  = true THEN t1.teacher_id
                    END AS deptId,
                    CASE
                    WHEN min(t1.department)  = false then min(t1.deptName)
                    when min(t1.department)  = true THEN min(t1.courseDeptName)
                    END AS deptName,
                    count(distinct t1.classId) classCount,
                    count(distinct t1.classCourseId) teachingCount,
                    AVG(t1.averageScore) as averageScore
                from (
                    select
                        ecct.teacher_id ,
                        per.class_course_id classCourseId,
                        min(ecc.class_id) classId,
                        min(eti.dept_name) deptName,
                        min(ecc.department) department,
                        min(ecct.dept_name) courseDeptName,
                        eti.dept_id deptId,
                        ifnull(AVG(case when per.handle = 1 then per.score end),
                        0) as averageScore
                    from
                        pg_evaluation_response per
                        join edu_class_course ecc on
                        ecc.id = per.class_course_id
                        and ecc.deleted = 0
                        join edu_courses ec on per.course_id = ec.id and ec.deleted = 0
                        join edu_class_management ecm on ecc.class_id = ecm.id and ecm.deleted = 0
                        join edu_trainee et on et.id = per.student_id and et.deleted=0
                        join edu_class_course_teacher ecct on ecct.class_course_id = ecc.id and ecct.deleted = 0
                                                                  and per.course_id = ecct.course_id
                        left join edu_teacher_dept eti on
                        eti.teacher_id = ecct.teacher_id
                        where
                            per.deleted = 0
                            and per.remarktype = '0'
                            <if test="reqVO.startTime != null">
                                and cast(ecc."date" AS date) &gt;= #{reqVO.startTime}
                            </if>
                            <if test="reqVO.endTime != null">
                                and cast(ecc."date" AS date) &lt;= #{reqVO.endTime}
                            </if>
                        group by
                            ecct.teacher_id,
                            eti.dept_id,
                            per.class_course_id
                    ) t1
                group by
                    t1.teacher_id,
                    t1.deptId
            ) t2
        group by
            t2.deptId
        having 1= 1
        <if test="reqVO.deptName != null and reqVO.deptName != ''">
            and min(t2.deptName) like concat('%',#{reqVO.deptName},'%')
        </if>
        <if test="reqVO.deptId != null">
            and deptId = #{reqVO.deptId}
        </if>
        <if test="reqVO.ids != null and reqVO.ids.size() > 0">
            and deptId in
            <foreach item="id" collection="reqVO.ids" separator="," open="(" close=")" index="">
                #{id}
            </foreach>
        </if>
        <if test="reqVO.deptIdList != null and reqVO.deptIdList.size() > 0">
            and deptId in
            <foreach item="deptId" collection="reqVO.deptIdList" separator="," open="(" close=")" index="">
                #{deptId}
            </foreach>
        </if>
        order by
            <if test="reqVO.sortField == 3">
                averageScore
            </if>
            <if test="reqVO.sortField == null or reqVO.sortField != 3">
                deptName
            </if>
            <if test="reqVO.isDesc == null or reqVO.isDesc == true">
                DESC
            </if>
    </select>
    <select id="selectAllPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.AllEvaluationPageRespVO">
        select et.name studentName,
               per.handle handle,
               per.score score,
               ecm.class_name className,
               ec.name courseName,
               ecc.begin_time beginTime,
               ecc.end_time endTime,
               per.student_id studentId,
               per.class_course_id classCourseId,
               per.questionnaire_id questionnaireId,
               per.teacher_id teacherId,
               per.department department
        from pg_evaluation_response per
        left join edu_class_course ecc on ecc.id = per.class_course_id
        left join edu_class_management ecm on ecc.class_id = ecm.id
        left join edu_courses ec on per.course_id = ec.id
        left join edu_trainee et on et.id =  per.student_id
        where per.deleted = 0
        <if test="reqVO.studentName != null and reqVO.studentName != ''">
            and et.name like CONCAT('%',#{reqVO.studentName},'%')
        </if>
        <if test="reqVO.startTime != null and reqVO.endTime != null" >
            AND  ecc.begin_time >= #{reqVO.startOfDay}
            AND  ecc.end_time  &lt;= #{reqVO.endOfDay}
        </if>
        <if test="reqVO.year != null">
            and YEAR(ecc.end_time) = #{reqVO.year}
        </if>
        order by ecc.begin_time desc, et.name asc
    </select>

    <select id="countUnhandled" resultType="java.lang.Long">
        select count(*)
        from pg_evaluation_response
        where student_id = #{userId}
          and handle = 0
          and deleted = false
          and ((expire_time is not null and expire_time > #{now}) or expire_time is null)
    </select>
    <select id="selectExistEvaluation"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse.EvaluationResponseDO">
        select *
        from pg_evaluation_response
        where student_id = #{studentId}
          and class_course_id = #{classCourseId}
          and deleted = false
    </select>
    <select id="getExistEvaluationTrainee" resultType="java.lang.Long">
        select student_id
        from pg_evaluation_response
        where class_course_id = #{classCourseId}
          and deleted = false
    </select>
    <select id="getEvaluationSummaryPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.EvaluationSummaryPageRespVO">
        select
        per.class_course_id,
        per.student_id,
        per.score,
        per.teacher_id,
        eti.name teacherName,
        eti.dept_names deptName,
        ec.name courseName,
        ecm.class_name className,
        per.teacher_id teacherIds
        from pg_evaluation_response per
        left join edu_class_course ecc on ecc.id = per.class_course_id
        left join edu_class_management ecm on ecc.class_id = ecm.id
        left join edu_courses ec on ec.id = per.course_id
        left join edu_teacher_information eti on CONCAT(',', per.teacher_id, ',') LIKE CONCAT('%,', eti.id, ',%')
        WHERE
        per.deleted = false
        and per.handle = 1
        and per.department = false
        <if test="reqVO.className != null and reqVO.className != ''">
            and ecm.class_name LIKE CONCAT('%',#{reqVO.className},'%')
        </if>
        <if test="reqVO.courseName != null and reqVO.courseName != ''">
            and ec.name LIKE CONCAT('%',#{reqVO.courseName},'%')
        </if>
        <if test="reqVO.deptName != null and reqVO.deptName != ''">
            and eti.dept_names like concat('%',#{reqVO.deptName},'%')
        </if>
        <if test="reqVO.teacherName != null and reqVO.teacherName != ''">
            and eti.name LIKE CONCAT('%',#{reqVO.teacherName},'%')
        </if>
        order by per.update_time desc

    </select>
    <select id="getDeptRankDetailVOList"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.RankDetailVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterReqVO">
        select
            t2.dept_id deptId,
            count(t2.class_course_id) courseCount,
            AVG(t2.average_score) avgScore
        from
            (
            select
                t1.dept_id dept_id,
                t1.class_course_id,
                ifnull(AVG(t1.score),
                0) as average_score
            from
            (
                select
                    ecc.id class_course_id,
                    -- 排课id
                    case
                        when ecc.department = false then etd.dept_id
                        when ecc.department = true then ecct.teacher_id
                    end as dept_id,
                    -- 部门id
                    case
                        when ecc.department = false then etd.dept_name
                        when ecc.department = true then ecct.dept_name
                    end as dept_name,
                    -- 部门名称
                    per.score score,
                    ecc.department department
                    -- 是否部门授课
                from
                edu_class_course ecc
                join edu_class_course_teacher ecct on
                    ecct.class_course_id = ecc.id
                    and ecct.deleted = 0
                left join edu_teacher_dept etd on
                    ecct.teacher_id = etd.teacher_id
                left join pg_evaluation_response per on
                    per.class_course_id = ecc.id
                join edu_class_management ecm on ecm.id = ecc.class_id
                    and ecm.deleted = 0
                where
                ecc.deleted = 0

                <if test="reqVO.classTerm != null">
                    and ecm.semester = #{reqVO.classTerm}
                </if>

                and per.deleted = 0
                and per.handle = 1
                <if test="reqVO.tenantId != null">
                    and ecc.tenant_id = #{reqVO.tenantId}
                </if>
                <if test="reqVO.courseEndTime != null">
                    and ecc.end_time &lt;= #{reqVO.courseEndTime}
                </if>
                <if test="reqVO.startTime != null">
                    AND cast(ecc."date" AS date) &gt;= #{reqVO.startTime}
                </if>
                <if test="reqVO.endTime != null">
                    AND cast(ecc."date" AS date) &lt;= #{reqVO.endTime}
                </if>
                and ecct.teacher_id is not null
            ) t1
            where
                1=1
            <if test="reqVO.deptIdList != null and reqVO.deptIdList.size() > 0">
                and t1.dept_id in
                <foreach item="deptId" collection="reqVO.deptIdList" separator="," open="(" close=")" index="">
                    #{deptId}
                </foreach>
            </if>
            group by
            t1.dept_id,
            t1.class_course_id
            ) t2
        group by
            t2.dept_id
        order by
            avgScore desc ,
            courseCount desc
    </select>
    <select id="getCourseEvaluationRateAndAvgScore"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterRespVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterReqVO">
        select
        CASE
        WHEN COUNT(1) = 0 THEN 1
        ELSE COUNT(CASE WHEN t1.handle = 1 THEN 1 END) / COUNT(1)
        END AS evalRate, -- 评课率
--         ifnull(AVG(t1.score),0) as courseAvgScore -- 课程平均分
        ifnull(AVG(CASE WHEN t1.handle = 1 THEN t1.score END), 0) as courseAvgScore
        from(
        select
        ecc.id class_course_id,
        -- 排课id
        case
        when ecc.department = false then etd.dept_id
        when ecc.department = true then ecct.teacher_id
        end as dept_id,
        case
        when ecc.department = false then etd.teacher_id
        when ecc.department = true then ecct.teacher_id
        end as teacher_id,
        per.score score,
        per.handle handle
        from
        edu_class_course ecc
        join edu_class_course_teacher ecct on
        ecct.class_course_id = ecc.id
        and ecct.deleted = 0
        left join edu_teacher_dept etd on
        ecct.teacher_id = etd.teacher_id
        left join pg_evaluation_response per on
        per.class_course_id = ecc.id
        join edu_class_management ecm on ecm.id = ecc.class_id
        and ecm.deleted = 0
        where
        ecc.deleted = 0
        <if test="reqVO.courseEndTime != null">
            and ecc.end_time &lt;= #{reqVO.courseEndTime}
        </if>
        <if test="reqVO.classTerm != null">
            and ecm.semester = #{reqVO.classTerm}
        </if>
        and per.deleted = 0
        <if test="reqVO.tenantId != null">
            and ecc.tenant_id = #{reqVO.tenantId}
        </if>

        <if test="reqVO.startTime != null">
            AND cast(ecc."date" AS date) &gt;= #{reqVO.startTime}
        </if>
        <if test="reqVO.endTime != null">
            AND cast(ecc."date" AS date) &lt;= #{reqVO.endTime}
        </if>
        and ecct.teacher_id is not null
        ) t1
        where
        1=1
        <if test="reqVO.deptIdList != null and reqVO.deptIdList.size() > 0">
            and t1.dept_id in
            <foreach item="deptId" collection="reqVO.deptIdList" separator="," open="(" close=")" index="">
                #{deptId}
            </foreach>
        </if>
        <if test="reqVO.deptId != null">
            and t1.dept_id = #{reqVO.deptId}
        </if>
        <if test="reqVO.teacherId != null">
            and t1.teacher_id = #{reqVO.teacherId}
        </if>
    </select>
    <select id="getTeacherRankDetailVOList"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.RankDetailVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterReqVO">
        select
        t2.teacher_id teacherId,
        min(t2.teacher_name) teacherName,
        count(t2.class_course_id) courseCount,
        AVG(t2.average_score) avgScore,
        min(t2.department) isDeptTeaching
        from(
        select
        t1.teacher_id teacher_id,
        t1.class_course_id,
        min(t1.teacher_name) teacher_name,
--         ifnull(AVG(t1.score),0) as average_score,
        ifnull(AVG(CASE WHEN t1.handle = 1 THEN t1.score END), 0) as average_score ,
        min(t1.department) department
        from
        (
        select
        ecc.id class_course_id,
        case
        when ecc.department = false then etd.dept_id
        when ecc.department = true then ecct.teacher_id
        end as dept_id,
        case
        when ecc.department = false then etd.teacher_id
        when ecc.department = true then ecct.teacher_id
        end as teacher_id,
        case
        when ecc.department = false then etd.teacher_name
        when ecc.department = true then ecct.dept_name
        end as teacher_name,
        per.score score,
        ecc.department department
        from
        edu_class_course ecc
        join edu_class_course_teacher ecct on
        ecct.class_course_id = ecc.id
        and ecct.deleted = 0
        left join edu_teacher_dept etd on
        ecct.teacher_id = etd.teacher_id
        left join pg_evaluation_response per on
        per.class_course_id = ecc.id
        join edu_class_management ecm on ecm.id = ecc.class_id
        and ecm.deleted = 0
        where
        ecc.deleted = 0
        <if test="reqVO.classTerm != null">
            and ecm.semester = #{reqVO.classTerm}
        </if>
        and per.deleted = 0
        and per.handle = 1
        <if test="reqVO.courseEndTime != null">
            and ecc.end_time &lt;= #{reqVO.courseEndTime}
        </if>
        <if test="reqVO.tenantId != null">
            and ecc.tenant_id = #{reqVO.tenantId}
        </if>

        <if test="reqVO.startTime != null">
            AND cast(ecc."date" AS date) &gt;= #{reqVO.startTime}
        </if>
        <if test="reqVO.endTime != null">
            AND cast(ecc."date" AS date) &lt;= #{reqVO.endTime}
        </if>
        and ecct.teacher_id is not null
        ) t1
        where
        1=1
        <if test="reqVO.deptId != null">
            and t1.dept_id = #{reqVO.deptId}
        </if>
        <if test="reqVO.teacherId != null">
            and t1.teacher_id = #{reqVO.teacherId}
        </if>
        group by
        t1.teacher_id,
        t1.class_course_id
        ) t2
        group by
        t2.teacher_id
        order by
        avgScore desc ,
        courseCount desc
    </select>

    <select id="getTopicCourseEvaluation"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.CourseEvaluationResponseVO">
        select
            ecc.id classCourseId,
            min(ec."name") courseName,
            min(per.questionnaire_id) questionnaireId,
            ecc."date" classDate,
            ecc.begin_time classBeginTime,
            min(eti.teacher_names) teacherName,
            count(1) expectedCount,
            count(per.handle = 1 or null) evaluatedCount,
            count(per.handle = 0 or null) unevaluatedCount
        from pg_evaluation_response per
                 join edu_class_course ecc on ecc.id = per.class_course_id
            and ecc.deleted = 0
                 join edu_courses ec on ec.id  = ecc.course_id and ec.deleted = 0
                 left join (
            select ecc.id classCourseId,
                   group_concat(
                           case
                               when ecc.department = false then eti."name"
                               when ecc.department = true then ecct.dept_name
                               end
                   ) as teacher_names
            from edu_class_course ecc
                     join edu_class_course_teacher ecct on ecc.id = ecct.class_course_id and ecct.deleted = 0
                     left join edu_teacher_information eti on eti.id  = ecct.teacher_id and eti.deleted = 0
            where ecc.deleted = 0
              and ecc.class_id = #{classId}
            group by ecc.id
        ) eti on eti.classCourseId = ecc.id

        where per.deleted = 0
          and ecc.class_id = #{classId}
        group by ecc.id
        having count(per.handle = 0 or null)
        <if test="isDone == true">
            = 0
        </if>
        <if test="isDone == false">
            != 0
        </if>
        order by classBeginTime desc
    </select>

    <select id="getElectiveCourseEvaluation"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.OptionalCourseEvaluationDetailVO"
            parameterType="java.lang.Long">
        select
            ecc.id classCourseId,
            eerc2.course_id courseId,
            min(ec."name") courseName,
            min(per.questionnaire_id) questionnaireId,
            min(eerc2.create_time) courseCreateTime,
            ecc."date" classDate,
            ecc.begin_time classBeginTime,
            min(eti."name") teacherName,
            count(1) expectedCount,
            count(per.handle = 1 or null) evaluatedCount,
            count(per.handle = 0 or null) unevaluatedCount
        from pg_evaluation_response per
                 join edu_class_course ecc on per.class_course_id = ecc.id
            and ecc.deleted = 0
                 join edu_elective_release_classes eerc
                      on eerc.class_course_id = per.class_course_id
                          and eerc.deleted =0
                 join edu_elective_release_courses eerc2
                      on eerc2.release_id = eerc.release_id
                          and eerc2.deleted = 0
                 join edu_elective_trainee_selection eets
                      on eets.trainee_id = per.student_id
                          and eets.release_id =eerc.release_id
                          and eets.release_course_id = eerc2.id
                          and eets.deleted = 0
                 join edu_teacher_information eti on eti.id = eerc2.teacher_id
            and eti.deleted = 0
                 join edu_courses ec on ec.id = eerc2.course_id and ec.deleted = 0
        where per.deleted = 0
          and eerc.class_id = #{classId}
        group by ecc.id ,eerc2.course_id
        order by courseCreateTime
    </select>

    <select id="getElectiveCourseEvaluationTraineeDetailForApp"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.CourseEvaluationTraineeDetailVO">
        select
            et."id" traineeId,
            et."name" traineeName,
            et.phone phone,
            et.sex sex,
            et.photo avatar
        from pg_evaluation_response per
                 join edu_class_course ecc on per.class_course_id = ecc.id
            and ecc.deleted = 0
                 join edu_elective_release_classes eerc
                      on eerc.class_course_id = per.class_course_id
                          and eerc.deleted =0
                 join edu_elective_release_courses eerc2
                      on eerc2.release_id = eerc.release_id
                          and eerc2.deleted = 0
                 join edu_elective_trainee_selection eets
                      on eets.trainee_id = per.student_id
                          and eets.release_id =eerc.release_id
                          and eets.release_course_id = eerc2.id
                          and eets.deleted = 0
                 join edu_trainee et on et.deleted = 0 and et.id = per.student_id
        where per.deleted = 0
          and per.class_course_id = #{classCourseId}
          and eerc2.course_id = #{courseId}
          and per.handle = #{isDone}
        order by per.update_time desc, et.id desc
    </select>

    <select id="getTopicCourseEvaluationTraineeDetailForApp"
            resultType="com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.CourseEvaluationTraineeDetailVO">
        select
            et."id" traineeId,
            et."name" traineeName,
            et.phone phone,
            et.sex sex,
            et.photo avatar
        from pg_evaluation_response per
                 join edu_trainee et on et.deleted = 0 and et.id = per.student_id
        where per.deleted = 0
          and per.class_course_id = #{classCourseId}
          and per.handle = #{isDone}
        order by per.update_time desc, et.id desc

    </select>
</mapper>
