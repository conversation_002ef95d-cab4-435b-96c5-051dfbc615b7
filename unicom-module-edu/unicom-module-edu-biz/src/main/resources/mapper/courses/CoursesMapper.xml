<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.courses.CoursesMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <sql id="selectCoursesRespVO">
        SELECT
        ec.id id,
        ec.courses_type coursesType,
        ec."name" name,
        ec.short_name shortName,
        ec.theme_id themeId,
        ec.educate_form_id educateFormId,
        ec.teaching_method_id teachingMethodId,
        ec.management_dept_id managementDeptId,
        ec.status status,
        ec."date" "date",
        ec.activity_type activityType,
        ec.create_time createTime,
        string_agg(eti.name, '，') teacherNameList,
        string_agg(eti.id, '，') teacherIdListStr
        FROM
        edu_courses ec
        LEFT JOIN edu_teacher_course_information etci on etci.courses_id = ec.id AND etci.deleted = 0
        LEFT JOIN edu_teacher_information eti on eti.id = etci.teacher_id AND eti.deleted = 0

        <where>
            ec.deleted = 0
            <if test="reqVO.coursesType != null">
                AND ec.courses_type = #{reqVO.coursesType}
            </if>
            <if test="reqVO.name != null and reqVO.name != ''">
                AND ec."name" LIKE CONCAT('%',#{reqVO.name},'%')
            </if>
            <if test="reqVO.themeId != null">
                AND ec.theme_id = #{reqVO.themeId}
            </if>
            <if test="reqVO.educateFormId != null">
                AND ec.educate_form_id = #{reqVO.educateFormId}
            </if>
            <if test="reqVO.teachingMethodId != null">
                AND ec.teaching_method_id = #{reqVO.teachingMethodId}
            </if>
            <if test="reqVO.activityType != null">
                AND ec.activity_type = #{reqVO.activityType}
            </if>
            <if test="reqVO.status != null">
                AND ec.status = #{reqVO.status}
            </if>
            <if test="reqVO.startDate != null">
                AND ec."date" &gt;= #{reqVO.startDate}
            </if>
            <if test="reqVO.endDate != null">
                AND ec."date" &lt;= #{reqVO.endDate}
            </if>
        </where>
        GROUP BY ec.id
        <if test="reqVO.teacherName != null and reqVO.teacherName != ''">
            HAVING teacherNameList LIKE CONCAT('%',#{reqVO.teacherName},'%')
        </if>
        ORDER BY
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            ec.create_time
        </if>
        <if test="reqVO.sortField != null and reqVO.sortField == 1">
            ec."name"
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
    </sql>

    <select id="selectPageByReqVO" resultType="com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesRespVO"
        parameterType="com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesPageReqVO">
        <include refid="selectCoursesRespVO"/>
    </select>

    <select id="selectListByReqVO" resultType="com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesRespVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesPageReqVO">
        <include refid="selectCoursesRespVO"/>
    </select>

    <select id="selectListByIds" resultType="com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesRespVO">
        SELECT
            ec.id id,
            ec.courses_type coursesType,
            ec."name" name,
            ec.short_name shortName,
            ec.theme_id themeId,
            ec.educate_form_id educateFormId,
            ec.teaching_method_id teachingMethodId,
            ec.management_dept_id managementDeptId,
            ec.status status,
            ec."date" "date",
            ec.activity_type activityType,
            ec.create_time createTime,
            string_agg(eti.name, '，') teacherNameList,
            string_agg(eti.id, '，') teacherIdListStr
        FROM
            edu_courses ec
            LEFT JOIN edu_teacher_course_information etci ON etci.courses_id = ec.id AND etci.deleted = 0
            LEFT JOIN edu_teacher_information eti ON eti.id = etci.teacher_id AND eti.deleted = 0
        WHERE
            ec.deleted = 0
            AND ec.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY ec.id
    </select>
    <select id="selectPageForCourseTeachingRecord"
            resultType="com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesTeachingRecordRespVO"
            parameterType="com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesTeachingRecordReqVO">
        select
            ecc.id classCourseId,
            ecc.department,
            min(ec.id) courseId,
            min(ec."name") courseName,
            min(ecm.id) classId,
            min(ecm.class_name) className,
            CASE
                WHEN ecc.department = false THEN group_concat(eti.id)
                WHEN ecc.department = true THEN ''
            END AS teacherIds,
            CASE
                WHEN ecc.department = false THEN group_concat(eti."name")
                WHEN ecc.department = true THEN ecc.teacher_id_string
            END AS teacherNames,
            min(ecc.begin_time) classStartTime,
            min(ecc.end_time) classEndTime,
            min(ecc."date") classDate,
            min(ecc.period) dayPeriod
        from edu_class_course ecc
            left join edu_courses ec on ecc.course_id = ec.id
            left join edu_class_course_teacher ecct on ecct.class_course_id = ecc.id and ecc.department = false and ecct.deleted = 0
            left join edu_teacher_information eti on eti.id = ecct.teacher_id and eti.deleted = 0
            left join edu_class_management ecm on ecm.id = ecc.class_id
            LEFT JOIN edu_plan ep on ecc."plan_id" = ep."id"
        where
            ecc.deleted = 0
            and ec.deleted = 0
            and ecm.deleted = 0
            and ecc.is_temporary = false
--             and ep.status = '1'
            and ecc.course_id = #{reqVO.courseId}
          <if test="reqVO.className != null">
            and ecm.class_name like concat('%',#{reqVO.className},'%')
          </if>
          <if test="reqVO.classStartTime != null">
            and ecc."begin_time" &gt;= #{reqVO.classStartTime}
          </if>
          <if test="reqVO.classEndTime != null">
            and ecc."end_time" &lt;= #{reqVO.classEndTime}
          </if>
            and ecc.end_time &lt; #{currentTime}
        group by ecc.id
        <if test="reqVO.teacherName != null">
            HAVING teacherNames LIKE concat('%',#{reqVO.teacherName},'%')
        </if>
        order by
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            classEndTime
        </if>
        <if test="reqVO.sortField != null and reqVO.sortField == 1">
            className
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
    </select>
    <select id="selectListForTeachingRecordByReqVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesTeachingRecordRespVO">
        select
            ecc.id classCourseId,
            ecc.department,
            min(ec.id) courseId,
            min(ec."name") courseName,
            min(ecm.id) classId,
            min(ecm.class_name) className,
            CASE
                WHEN ecc.department = false THEN group_concat(eti.id)
                WHEN ecc.department = true THEN ''
            END AS teacherIds,
            CASE
                WHEN ecc.department = false THEN group_concat(eti."name")
                WHEN ecc.department = true THEN ecc.teacher_id_string
            END AS teacherNames,
            min(ecc.begin_time) classStartTime,
            min(ecc.end_time) classEndTime,
            min(ecc."date") classDate,
            min(ecc.period) dayPeriod,
            min(ec.create_time) courseCreateTime
        from edu_class_course ecc
            left join edu_courses ec on ecc.course_id = ec.id
            left join edu_class_course_teacher ecct on ecct.class_course_id = ecc.id and ecc.department = false and ecct.deleted = 0
            left join edu_teacher_information eti on eti.id = ecct.teacher_id and eti.deleted = 0
            left join edu_class_management ecm on ecm.id = ecc.class_id
            LEFT JOIN edu_plan ep on ecc."plan_id" = ep."id"
        where
            ecc.deleted = 0
            and ec.deleted = 0
            and ecm.deleted = 0
            and ecc.is_temporary = false
--             and ep.status = '1'
            and ecc.course_id in
            <foreach collection="reqVO.ids" item="courseId" open="(" separator="," close=")">
                #{courseId}
            </foreach>
            and ecc.end_time &lt; #{currentTime}
        group by ecc.id
        order by
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            courseCreateTime
        </if>
        <if test="reqVO.sortField != null and reqVO.sortField == 1">
            courseName
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
        , classEndTime DESC
    </select>


</mapper>
