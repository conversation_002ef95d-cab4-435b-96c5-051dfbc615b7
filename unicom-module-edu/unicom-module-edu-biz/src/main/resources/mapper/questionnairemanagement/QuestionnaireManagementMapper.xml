<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.questionnairemanagement.QuestionnaireManagementMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <sql id="selectQuestionnaireManagementRespVO">
        SELECT
        pqm.id id,
        pqm.title title,
        pqm.is_default isDefault,
        pqm.status status,
        pqm.create_time createTime,
        pqm.built_in builtIn
        FROM
        pg_questionnaire_management pqm

        <where>
            pqm.deleted = 0
            <if test="reqVO.title != null and reqVO.title != ''">
                AND pqm.title like CONCAT('%', #{reqVO.title}, '%')
            </if>
            <if test="reqVO.status != null and reqVO.status != ''">
                AND pqm.status = #{reqVO.status}
            </if>
            <if test="reqVO.builtIn !=null">
                AND pqm.built_in = #{reqVO.builtIn}
            </if>
        </where>
        ORDER BY
            pqm.built_in DESC,
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            pqm.id
        </if>
        <if test="reqVO.sortField != null and reqVO.sortField == 1">
            pqm.status
        </if>
        <if test="reqVO.isDesc != null and reqVO.isDesc == false">
            ASC
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>

    </sql>


    <select id="selectPageByPageVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionnaireManagementRespVO">
        <include refid="selectQuestionnaireManagementRespVO"/>
    </select>
    <select id="countDefault" resultType="java.lang.Integer">
        select count(*) from pg_questionnaire_management where is_default = '1' and status = 1 and deleted = 0 and tenant_id = #{tenantId}
    </select>
    <select id="getByEducateForm" parameterType="java.lang.Long" resultType="java.lang.Long">
        select id from pg_questionnaire_management where topic_educate_form like CONCAT('%', #{educateFormId}, '%') and tenant_id = #{tenantId} and status = '1' and deleted = '0'
    </select>
    <select id="getDefaultQuestionnaire" resultType="java.lang.Long">
        select id from pg_questionnaire_management where is_default = '1' and status = '1' and deleted = false and tenant_id = #{tenantId} limit 1
    </select>
    <select id="countByEducateForm" resultType="java.lang.Long" parameterType="java.lang.String">
        select count(*) from pg_questionnaire_management where topic_educate_form like CONCAT('%', #{educateForm}, '%') and status = '1' and deleted = 0
    </select>
    <select id="getCollectingEducateForm" resultType="java.lang.String" parameterType="java.lang.Long">
        select topic_educate_form from pg_questionnaire_management where status = '1' and deleted = false and tenant_id = #{tenantId}
    </select>
    <select id="countBuiltId" resultType="java.lang.Long">
        select count(*) from pg_questionnaire_management where built_in = true and deleted = false
    </select>
</mapper>