<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.electivetraineeselection.ElectiveTraineeSelectionMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <resultMap id="getCourseSelectedTraineePage" type="com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo.ElectiveCourseTraineeSelectedRespVO">
        <result column="card_no" property="cardNo" typeHandler="com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler"/>
    </resultMap>

    <select id="getCourseSelectedTraineePage"
            resultMap="getCourseSelectedTraineePage">
        select
            eets.release_id releaseId,
            eets.release_course_id releaseCourseId,
            eets.trainee_id traineeId,
            e."name" "name",
            e.sex sex,
            e.card_no,
            e.phone phone,
            e.unit_name unitName,
            e.educational_level educationalLevel,
            e."position" "position",
            e.political_identity politicalIdentity,
            eets.create_time
        from edu_elective_trainee_selection eets
                 left join edu_trainee e on eets.trainee_id = e.id and e.deleted = 0
        where eets.deleted = 0
            and eets.release_course_id = #{reqVO.releaseCourseId}
            <if test="reqVO.traineeName != null and reqVO.traineeName != ''">
                and e.name like concat('%',#{reqVO.traineeName},'%')
            </if>
            <if test="reqVO.classIdList != null and reqVO.classIdList.size() > 0">
                and e.class_id in
                <foreach collection="reqVO.classIdList" item="classId" separator="," open="(" close=")">
                    #{classId}
                </foreach>
            </if>
        order by eets.create_time
        <if test="reqVO.isSerialDesc == null or reqVO.isSerialDesc == false">
            desc
        </if>
    </select>
    <select id="selectSelectionTraineeInfoList"
            resultType="com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo.AppElectiveTraineeSelectionSimpleRespVO">
        select
            eets.release_id releaseId,
            eets.release_course_id releaseCourseId,
            eets.trainee_id traineeId,
            e."name" "name",
            e.sex sex,
            e.phone phone,
            eets.create_time,
            ec.id courseId,
            ec."name" courseName
        from edu_elective_trainee_selection eets
            left join edu_trainee e on eets.trainee_id = e.id and e.deleted = 0
            left join edu_elective_release_courses eerc on eerc.id = eets.release_course_id and eerc.deleted = 0
            left join edu_courses ec on ec.id = eerc.course_id and ec.deleted = 0
        where eets.deleted = 0
          <if test="reqVO.classId != null">
              and e.class_id = #{reqVO.classId}
          </if>
          <if test="reqVO.releaseId != null">
             and eets.release_id = #{reqVO.releaseId}
          </if>
        order by eets.create_time desc
    </select>
    <select id="getSelectedCoursesListByTraineeId"
            resultType="com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.dto.ElectiveTraineeSelectedCoursesAndReleaseDTO">
            select
                eer."name" releaseName,
                eer.selection_start_time,
                eer.selection_end_time ,
                eer.class_date ,
                eer.day_period ,
                eer.class_start_time ,
                eer.class_end_time ,
                eerc.create_time releaseCreateTime,
                eerc.release_id releaseId,
                eerc.id id,
                ec.id courseId,
                ec."name" courseName,
                eti.id teacherId,
                eti."name" teacherName,
                ecl.id classroomId,
                ecl.class_name classroomName,
                eerc.create_time createTime
            from edu_elective_trainee_selection eets
                     left join edu_elective_release eer on eer.id = eets.release_id
                     left join edu_elective_release_courses eerc on eerc.id = eets.release_course_id
                     left join edu_courses ec on ec.id = eerc.course_id
                     left join edu_teacher_information eti on eti.id = eerc.teacher_id
                     left join edu_classroom_library ecl on eerc.classroom_id = ecl.id
            where eets.deleted = 0
              and eer.deleted = 0
              and eerc.deleted = 0
              and ec.deleted = 0
              and eti.deleted = 0
              and ecl.deleted = 0
              and eets.trainee_id = #{traineeId}
            order by eets.create_time desc
    </select>
    <select id="getSelectedTrainee"
            resultType="com.unicom.swdx.module.edu.dal.dataobject.electivetraineeselection.ElectiveTraineeSelectionDO">
        select *
        from edu_elective_trainee_selection
        where deleted = 0 and
            is_distributed = false and
            create_time between #{endTime} and #{now}
    </select>
</mapper>
