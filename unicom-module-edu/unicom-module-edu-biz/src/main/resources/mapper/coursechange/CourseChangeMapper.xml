<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.coursechange.CourseChangeMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPageByReqVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.coursechange.vo.CourseChangeRespVO">
            select id,
--                    class_id,
                   class_name,
                   change_time,
                   change_type,
                   change_user_id,
                   change_user_name,
                   change_reason,
                   class_info_before,
                   class_info_after
            from edu_course_change
            where deleted = 0
            <if test="reqVO.className != null and reqVO.className != ''">
                and class_name like concat('%', #{reqVO.className}, '%')
            </if>
            <if test="reqVO.changeType != null">
                and change_type = #{reqVO.changeType}
            </if>
            order by create_time desc
    </select>

    <select id="getWeekTimetable"
            resultType="com.unicom.swdx.module.edu.controller.admin.coursechange.vo.WeekTimetableInfoDTO">
        select
            ecc.id,
            ecc.class_id,
            ecc.date,
            ecc.period,
            ecc.course_id,
            ecc.is_temporary,
            ecc.is_merge,
            ec.name courseName,
            CASE
                WHEN ec.courses_type = 1 THEN '专题课'
                WHEN ec.courses_type = 2 THEN '选修课'
                WHEN ec.courses_type = 3 THEN '教学活动'
                ELSE NULL
            END AS courseType,
            ec.educate_form_id,
            ec.activity_type,
            ecc.begin_time,
            ecc.end_time,
            ecc.teacher_id,
            ecc.teacher_id_string,
            ecc.department,
            eti.name teacherName,
            ecc.classroom_id,
            ecl.class_name classroom
        from edu_class_course ecc
        left join edu_courses ec on ecc.course_id = ec.id
        left join edu_teacher_information eti on ecc.teacher_id = eti.id
        left join edu_classroom_library ecl on ecc.classroom_id = ecl.id
        where ecc.deleted = '0'
        <if test="reqVO.classId != null">
            and ecc.class_id = #{reqVO.classId}
        </if>
        <if test="reqVO.dateBeg != null and reqVO.dateBeg!= ''">
            and ecc.date &gt;= #{reqVO.dateBeg}
        </if>
        <if test="reqVO.dateEnd != null and reqVO.dateEnd!= ''">
            and ecc.date &lt;= #{reqVO.dateEnd}
        </if>
        order by ecc.begin_time asc
    </select>

    <select id="getClassInfo"
            resultType="com.unicom.swdx.module.edu.controller.admin.coursechange.vo.ClassCourseInfoDTO">
        select ecc.id,
               ecc.class_id,
               ecc.course_id,
               ecc."date",
               ecc.period,
               ecc.begin_time,
               ecc.end_time,
               ecc.teacher_id,
               ecc.teacher_id_string,
               ecc.department,
               ecc.classroom_id,
               ecc.is_temporary,
               ecc.is_merge,
               ecc.is_change,
               eti.name teacher_name,
               ecl.class_name classroom,
               ec.name course_name,
               ec.courses_type course_type,
               ecm.class_name
        from edu_class_course ecc
                 left join edu_courses ec on ecc.course_id = ec.id
                 left join edu_class_management ecm on ecc.class_id = ecm.id
                 left join edu_teacher_information eti on ecc.teacher_id = eti.id
                 left join edu_classroom_library ecl on ecc.classroom_id = ecl.id
        where ecc.id = #{applyId}
    </select>

    <select id="selectInfoByClassCourseId"
            resultType="com.unicom.swdx.module.edu.controller.admin.coursechange.vo.CourseChangeRespVO">
        select id,
        class_name,
        change_time,
        change_type,
        change_user_id,
        change_user_name,
        change_reason,
        class_info_before,
        class_info_after
        from edu_course_change
        where deleted = 0
        <if test="classCourseId != null">
            and class_course_id = #{classCourseId}
        </if>
        order by create_time desc
    </select>


</mapper>
