<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.graduationcertificatenumbers.GraduationCertificateNumbersMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPage"
            resultType="com.unicom.swdx.module.edu.controller.admin.graduationcertificatenumbers.vo.GraduationCertificateNumbersPageRespVO">
        select
            et.id traineeId ,
            et."name" traineeName,
            egcn.certificate_number ,
            ecm.class_open_time classOpenDate,
            ecm.completion_time completionDate,
            egcn.update_time generateDate
        from edu_trainee et
                 left join edu_graduation_certificate_numbers egcn
                           on et.id = egcn.trainee_id and egcn.deleted = 0
                 left join edu_trainee_group etg on et.group_id = etg.id and etg.deleted = 0
                 left join edu_class_management ecm on ecm.id = et.class_id and ecm.deleted = 0
        where et.deleted = 0
          and et.class_id = #{reqVO.classId}
          and et.status in
          <foreach collection="traineeStatusList" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
          <if test="reqVO.traineeName != null and reqVO.traineeName != ''">
              and et."name" like concat('%',#{reqVO.traineeName},'%')
          </if>
        order by etg.sort, et.group_sort,et.id desc
    </select>
</mapper>
