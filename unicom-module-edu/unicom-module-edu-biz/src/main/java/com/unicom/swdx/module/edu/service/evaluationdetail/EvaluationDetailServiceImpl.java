package com.unicom.swdx.module.edu.service.evaluationdetail;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.redis.util.RedisUtil;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.EvaluationResponseSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.MyEvaluationPageRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionnaireManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse.EvaluationResponseDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.courses.CoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.evaluationresponse.EvaluationResponseMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.service.questionnairemanagement.QuestionnaireManagementService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationdetail.EvaluationDetailDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;

import com.unicom.swdx.module.edu.dal.mysql.evaluationdetail.EvaluationDetailMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.edu.utils.serialnumber.PageDataSerialNumberUtil.generateSerialNumberList;
import org.apache.commons.lang3.StringUtils;

/**
 * 评估详情 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EvaluationDetailServiceImpl implements EvaluationDetailService {

    @Resource
    private EvaluationDetailMapper evaluationDetailMapper;

    @Resource
    private EvaluationResponseMapper evaluationResponseMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private ClassCourseMapper classCourseMapper;

    @Resource
    private CoursesMapper coursesMapper;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;

    @Resource
    private QuestionnaireManagementService questionnaireManagementService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public Long createEvaluationDetail(EvaluationDetailSaveReqVO createReqVO) {
        // 插入
        EvaluationDetailDO evaluationDetail = BeanUtils.toBean(createReqVO, EvaluationDetailDO.class);
        evaluationDetailMapper.insert(evaluationDetail);


        String cacheKey = "unhandled:" + createReqVO.getStudentId();
        redisUtil.del(cacheKey);


        // 返回
        return evaluationDetail.getId();
    }

    @Override
    public void updateEvaluationDetail(EvaluationDetailSaveReqVO updateReqVO) {
        // 校验存在
        EvaluationDetailDO temp =  validateEvaluationDetailExists(updateReqVO.getId());
        // 更新
        EvaluationDetailDO updateObj = BeanUtils.toBean(updateReqVO, EvaluationDetailDO.class);
        evaluationDetailMapper.updateById(updateObj);

        String cacheKey = "unhandled:" + temp.getStudentId();
        redisUtil.del(cacheKey);

    }

    @Override
    public void deleteEvaluationDetail(Long id) {
        // 校验存在
        EvaluationDetailDO temp =  validateEvaluationDetailExists(id);
        // 删除
        evaluationDetailMapper.deleteById(id);

        String cacheKey = "unhandled:" + temp.getStudentId();
        redisUtil.del(cacheKey);
    }

    private EvaluationDetailDO validateEvaluationDetailExists(Long id) {
        EvaluationDetailDO temp =  evaluationDetailMapper.selectById(id) ;
        if (temp == null) {
            throw exception(EVALUATION_DETAIL_NOT_EXISTS);
        }

        return temp;
    }

    @Override
    public EvaluationDetailDO getEvaluationDetail(Long id) {
        return evaluationDetailMapper.selectById(id);
    }

    @Override
    public PageResult<EvaluationDetailDO> getEvaluationDetailPage(EvaluationDetailPageReqVO pageReqVO) {
        return evaluationDetailMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<CommentRespVO> getComment(CommentPageReqVO reqVO) {
        IPage<CommentRespVO> page = MyBatisUtils.buildPage(reqVO);
        return new PageResult<>(evaluationDetailMapper.selectCommentPage(page, reqVO), page.getTotal());
    }

    @Override
    public List<EvaluationRespVO> getEvaluationList(Boolean handle, String courseName, Long userId, Long studentId) {
        if (userId == null) {
            userId = getLoginUserId();
        }
        LocalDateTime now = LocalDateTime.now();
        TraineeDO traineeDO = traineeMapper.selectTraineeByUserId(userId, studentId);
        List<EvaluationRespVO> resultList = evaluationResponseMapper.selectStudentEvaluation(traineeDO.getId(), handle, courseName);

        resultList.forEach(result -> {
            if (result.getDepartment()) {
                result.setTeacherName(result.getTeacherIds());
            } else {
                List<Long> teacherIds = Arrays.stream(result.getTeacherIds().split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                List<TeacherInformationRespVO> teacherInformation = teacherInformationMapper.selectListByIds(teacherIds);
                List<String> teacherNames = teacherInformation.stream().map(TeacherInformationRespVO::getName).collect(Collectors.toList());
                String teacherName = teacherNames.stream().map(String::valueOf).collect(Collectors.joining(","));
                result.setTeacherName(teacherName);
                result.setTeacherId(teacherIds);
            }
            if (result.getExpireTime() != null && result.getExpireTime().isBefore(now)) {
                result.setExpired(true);
            } else {
                result.setExpired(false);
            }
            if (result.getBeginTime() != null) {
                result.setClassTime(result.getBeginTime().toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
        });
        return resultList;
    }

    @Override
    public Long countStudentUnhandled(Long userId, Long studentId) {
        if (userId == null) {
            userId = getLoginUserId();
        }
        TraineeDO traineeDO = traineeMapper.selectTraineeByUserId(userId, studentId);
        LocalDateTime now = LocalDateTime.now();
        if (Objects.isNull(traineeDO)) {
            throw exception(NOT_TRAINEE);
        }



        Long traineeId = traineeDO.getId();
        String cacheKey = "unhandled:" + traineeId;

        // 2. 先查缓存（第一次检查）
        Long  cachedUnhandled = redisUtil.getgeneric(cacheKey);

        if (cachedUnhandled != null) {
            return cachedUnhandled;
        }

        // 3. 分布式锁，加锁 traineeId
        String lockKey = "lock:unhandled:" + traineeId;
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        try {
            // 4. 再查一遍缓存（第二次检查）
            cachedUnhandled = redisUtil.getgeneric(cacheKey);
            if (cachedUnhandled != null) {
                return cachedUnhandled;
            }

            // 5. 真正查数据库
            Long unhandled = evaluationResponseMapper.countUnhandled(traineeId, now);

            // 6. 存入缓存，设置一个合理的过期时间（比如5分钟）
            redisUtil.set(cacheKey, unhandled, 5*60);

            return unhandled;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void modifyEvaluationDetail(EvaluationModifyVO updateReqVO) {
        evaluationResponseMapper.modifyEvaluation(updateReqVO);
    }

    @Override
    public void distributeEvaluation(EvaluationDistributeVO distributeVO) {
        ClassCourseDO classCourseDO = classCourseMapper.selectById(distributeVO.getClassCourseId());

        CoursesDO coursesDO = coursesMapper.selectById(classCourseDO.getCourseId());

        if (!coursesDO.getCoursesType().equals(1)) {
            throw exception(COURSE_TYPE_MISMATCH);
        }

        if (distributeVO.getClassCourseId() != null && distributeVO.getStudentId() != null) {
            EvaluationResponseDO evaluation = evaluationResponseMapper.selectExistEvaluation(distributeVO.getStudentId(), distributeVO.getClassCourseId());
            if (evaluation != null) {
                throw exception(EVALUATION_DUPLICATION);
            }
        }


        try {
            // 获取对应问卷信息
            Long questionnaireId = distributeVO.getQuestionnaireId();
            // 获取课程教学形式对应问卷的id
            Long educateFormQuestionnaireId = questionnaireManagementService.getByEducateForm(coursesDO.getEducateFormId(), coursesDO.getTenantId());
            // 如果传入参数未指定问卷
            if (questionnaireId == null) {
                if (educateFormQuestionnaireId == null) { // 如果没有对应课程教学形式的问卷
                    questionnaireId = questionnaireManagementService.getDefaultQuestionnaireId(coursesDO.getTenantId());
                } else {
                    questionnaireId = educateFormQuestionnaireId;
                }
            }
            LocalDateTime now = LocalDateTime.now();
            QuestionnaireManagementRespVO questionnaire = questionnaireManagementService.getQuestionnaireManagement(questionnaireId,null,null);
            List<QuestionManagementRespVO> questions = questionnaire.getQuestions();


            EvaluationResponseSaveReqVO responseSaveReqVO = new EvaluationResponseSaveReqVO();
            responseSaveReqVO.setClassCourseId(distributeVO.getClassCourseId());
            responseSaveReqVO.setQuestionnaireId(questionnaireId);
            responseSaveReqVO.setStudentId(distributeVO.getStudentId());
            responseSaveReqVO.setIssuer(classCourseDO.getTeacherIdString());
            responseSaveReqVO.setTeacherId(classCourseDO.getTeacherIdString());
            responseSaveReqVO.setHandle(false);
            responseSaveReqVO.setTenantId(getTenantId());
            responseSaveReqVO.setDepartment(classCourseDO.getDepartment());
            responseSaveReqVO.setCourseId(classCourseDO.getCourseId());
            if (Boolean.TRUE.equals(questionnaire.getTimeTag())) {
                LocalDateTime expireTime = now.plusDays(questionnaire.getTimeLimit());
                responseSaveReqVO.setExpireTime(expireTime);
            }

            // 下发问卷
            EvaluationResponseDO evaluationResponse = BeanUtils.toBean(responseSaveReqVO, EvaluationResponseDO.class);
            evaluationResponseMapper.insert(evaluationResponse);
            questions.forEach(question -> {
                EvaluationDetailSaveReqVO detailSaveReqVO = new EvaluationDetailSaveReqVO();
                detailSaveReqVO.setQuestionnaireId(questionnaire.getId());
                detailSaveReqVO.setQuestionId(question.getId());
                detailSaveReqVO.setQuestionType(question.getQuestionType());
                detailSaveReqVO.setStudentId(distributeVO.getStudentId());
                detailSaveReqVO.setClassCourseId(distributeVO.getClassCourseId());
                detailSaveReqVO.setTenantId(getTenantId());
                detailSaveReqVO.setCourseId(classCourseDO.getCourseId());

                // 记录每个学员的选项
                this.createEvaluationDetail(detailSaveReqVO);
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 删除学员对应课程未评的问卷
     * @param traineeId 学员id
     * @param classCourseId 评课课程id
     */
    @Override
    public void revokeQuestionnaire(Long traineeId, Long classCourseId) {
//        evaluationDetailMapper.delete(new LambdaQueryWrapperX<EvaluationDetailDO>()
//                .eq(EvaluationDetailDO::getClassCourseId, classCourseId)
//                .eq(EvaluationDetailDO::getStudentId, traineeId));
        evaluationResponseMapper.delete(new LambdaQueryWrapperX<EvaluationResponseDO>()
                .eq(EvaluationResponseDO::getClassCourseId, classCourseId)
                .eq(EvaluationResponseDO::getStudentId, traineeId)
                .eq(EvaluationResponseDO::getHandle, 0));
    }

    /**
     * 批量删除学员对应课程的所有问卷
     * @param traineeIdAndClassCourseIdPairs <学员id, 评课课程id>对列表
     */
    @Override
    public void batchRevokeQuestionnaireAnyHandle(List<Pair<Long, Long>> traineeIdAndClassCourseIdPairs) {
        if (CollectionUtils.isEmpty(traineeIdAndClassCourseIdPairs)) {
            return;
        }
        evaluationResponseMapper.batchDeleteByPairOfTraineeIdAndClassCourseId(traineeIdAndClassCourseIdPairs);
    }


    @Override
    public void deleteByClassCourseId(List<Long> classCourseIds) {
        evaluationDetailMapper.delete(new LambdaQueryWrapperX<EvaluationDetailDO>()
                .in(EvaluationDetailDO::getClassCourseId, classCourseIds));
    }

    @Override
    public PageResult<EvaluationSummaryPageRespVO> getEvaluationSummaryPage(EvaluationSummaryPageReqVO pageReqVO) {
        Page<EvaluationSummaryPageRespVO> page = MyBatisUtils.buildPage(pageReqVO);
        page.setOptimizeCountSql(false);
        List<EvaluationSummaryPageRespVO> list = evaluationResponseMapper.getEvaluationSummaryPage(page, pageReqVO);
        List<Long> classCourseIdList = list.stream().map(EvaluationSummaryPageRespVO::getClassCourseId).collect(Collectors.toList());
        if (!classCourseIdList.isEmpty()) {
            List<EvaluationCommentRespVO> commentRespVO = evaluationDetailMapper.selectCommentByClassCourseIdList(classCourseIdList);

            list.forEach(evaluation -> {
                List<String> comment = commentRespVO.stream()
                        .filter(commentResp -> Objects.equals(commentResp.getClassCourseId(), evaluation.getClassCourseId()) && Objects.equals(commentResp.getStudentId(), evaluation.getStudentId()))
                        .map(EvaluationCommentRespVO::getComment) // 或自定义转换逻辑
                        .collect(Collectors.toList());
                if (comment.isEmpty()) {
                    evaluation.setComment(" ");
                } else {
                    evaluation.setComment(String.join(", ", comment));
                }
            });
        }

        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(false,
                page.getTotal(),
                pageReqVO,
                list.size());
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSerialNumber(serialNumberList.get(i));
        }
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public void submitEvaluation(EvaluationSubmitVO submitVO) {
        Long loginUserId = getLoginUserId();
        List<QuestionAnswerSubmitVO> answers = submitVO.getAnswers();
        TraineeDO traineeDO = traineeMapper.selectByUserId(loginUserId);

        // 获取问卷信息，检查最低分设置
        QuestionnaireManagementRespVO questionnaire = questionnaireManagementService
                .getQuestionnaireManagement(submitVO.getQuestionnaireId(),null,null);

        // 如果启用了最低分限制，进行校验
        if (Boolean.TRUE.equals(questionnaire.getLowscoreTag()) && questionnaire.getLowscore() != null) {
            Integer lowScore = questionnaire.getLowscore();

//            // 如果分数低于最低分，不允许提交（一键否决场景除外）
//            if (submitVO.getScore() < lowScore && StringUtils.isEmpty(submitVO.getLowScoreReason())) {
//                throw exception(EVALUATION_LOW_SCORE_REASON_REQUIRED);
//            }
//
//            // 如果分数等于最低分，需要提供理由说明
//            if (submitVO.getScore().equals(lowScore) && StringUtils.isEmpty(submitVO.getLowScoreReason())) {
//                throw exception(EVALUATION_LOW_SCORE_REASON_REQUIRED);
//            }
        }

        // 设置提交时间为当前时间
        LocalDateTime now = LocalDateTime.now();
        submitVO.setSubmitTime(now);

        // 如果问卷配置了撤回时限，设置撤回截止时间
        if (Boolean.TRUE.equals(questionnaire.getRevocableTag()) && questionnaire.getRevocableTimeLimit() != null) {
            // 计算撤回截止时间（将天数转换为小时）
            LocalDateTime revocableTime = now.plusHours(Math.round(questionnaire.getRevocableTimeLimit() * 24));
            submitVO.setRevocableTime(revocableTime);
        }

        answers.forEach(answer -> {
            answer.setQuestionnaireId(submitVO.getQuestionnaireId());
            answer.setClassCourseId(submitVO.getClassCourseId());
        });

        if(CollectionUtil.isNotEmpty(answers)){
            // 批量提交答题记录
            evaluationDetailMapper.batchSubmitEvaluation(answers, traineeDO.getId());
        }

        // 更新问卷状态为已评卷
        evaluationResponseMapper.updateEvaluation(submitVO, traineeDO.getId());
    }
}
