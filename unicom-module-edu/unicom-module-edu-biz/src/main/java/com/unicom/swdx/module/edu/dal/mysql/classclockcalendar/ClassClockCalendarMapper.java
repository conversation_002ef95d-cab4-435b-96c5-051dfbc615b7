package com.unicom.swdx.module.edu.dal.mysql.classclockcalendar;

import java.time.LocalDate;
import java.util.*;

import cn.hutool.core.collection.CollUtil;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.dal.dataobject.classclockcalendar.ClassClockCalendarDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 班级考勤日历 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassClockCalendarMapper extends BaseMapperX<ClassClockCalendarDO> {

    /**
     *  查询每月的日历考勤数据数据
     * @param reqVO
     * @return
     */
    List<ClassClockCalendarDO> selectDateRangeList(@Param("reqVO") ClassClockCalendarListReqVO reqVO);

    /**
     * 删除对应班的考勤数据
     * @param classId
     */
    void deleteByClassId(@Param("classId") Long classId);

    /**
     * 查询班某段时间内的考勤日历
     * @param classIdList 班级id列表
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 考勤日历
     */
    default List<ClassClockCalendarDO> selectListByClassOneYear(List<Long> classIdList, LocalDate startDate, LocalDate endDate){
        return selectList(new LambdaQueryWrapperX<ClassClockCalendarDO>()
                .in(ClassClockCalendarDO::getClassId, classIdList)
                .between(ClassClockCalendarDO::getClockDate, startDate, endDate));
    }

    default List<ClassClockCalendarDO> selectListByDates(List<LocalDate> dates){
        if (CollUtil.isEmpty(dates)){
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapperX<ClassClockCalendarDO>()
                .in(ClassClockCalendarDO::getClockDate, dates));
    }
}
