package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.unicom.swdx.framework.excel.core.convert.TrimConvert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
// 设置 chain = false，避免导入有问题
@Accessors(chain = false)
public class TeacherInformationImportExcelVO {

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名", index = 0, converter = TrimConvert.class)
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String name;

    /**
     * 生日
     */
    @ExcelProperty(value = "出生日期", index = 1)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String birthdayStr;

    /**
     * 民族 字典 nation
     */
    @ExcelProperty(value = "民族", index = 2)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String nationStr;

    /**
     * 性别 字典设置 system_user_sex
     */
    @ExcelProperty(value = "性别", index = 3)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String genderStr;

    /**
     * 政治面貌 字典 political_identity
     */
    @ExcelProperty(value = "政治面貌", index = 4)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String politicalStatusStr;


    /**
     * 籍贯
     */
    @ExcelProperty(value = "籍贯", index = 5)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String nativePlace;

    /**
     * 来校年月
     */
    @ExcelProperty(value = "来校年月", index = 6)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String arrivalTimeStr;

    /**
     * 联系方式
     */
    @ExcelProperty(value = "联系方式", index = 7)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String contactInformation;

    /**
     * 通讯地址
     */
    @ExcelProperty(value = "通讯地址", index = 8, converter = TrimConvert.class)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String mailingAddress;

    /**
     * 邮政编码
     */
    @ExcelProperty(value = "邮政编码", index = 9, converter = TrimConvert.class)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String postalCode;

    /**
     * 电子邮箱
     */
    @ExcelProperty(value = "电子邮箱", index = 10, converter = TrimConvert.class)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String email;

    /**
     * 所在单位
     */
    @ExcelProperty(value = "所在单位", index = 11)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String workUnit;

    /**
     * 学历 字典 person_education
     */
    @ExcelProperty(value = "学历", index = 12)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String educationStr;

    /**
     * 学位 person_academic_degree
     */
    @ExcelProperty(value = "学位", index = 13)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String degreeStr;

    /**
     * 职级 person_rank
     */
    @ExcelProperty(value = "职级", index = 14)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String rankStr;

    /**
     * 职称
     */
    @ExcelProperty(value = "职务", index = 15)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String professionalTitle;

    /**
     * 行政级别 person_administrative_position_rank
     */
    @ExcelProperty(value = "行政级别", index = 16)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String administrativeLevel;

    /**
     * 所属部门
     */
    @ExcelProperty(value = "所属部门", index = 17)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String deptNames;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 18)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String remark;

}
