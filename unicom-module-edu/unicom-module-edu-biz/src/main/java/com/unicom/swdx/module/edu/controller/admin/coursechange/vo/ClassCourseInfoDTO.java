package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import lombok.*;

import java.time.LocalDateTime;

/**
 * 排课信息
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassCourseInfoDTO {

    private Long id;
    /**
     * 班级id
     */
    private Long classId;
    /**
     * 班级名
     */
    private String className;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 课程名
     */
    private String courseName;
    /**
     * 课程类型
     */
    private String courseType;
    /**
     * 教师id
     */
    private Long teacherId;
    /**
     * 教师id
     */
    private String teacherIdString;
    /**
     * 教师名称
     */
    private String teacherName;
    /**
     * 教室id
     */
    private Integer classroomId;
    /**
     * 教室名称
     */
    private String classroom;
    /**
     * 日期
     */
    private String date;
    /**
     * 时间段（0上午，1下午，2晚上）
     */
    private String period;
    /**
     * 开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 是否暂存
     */
    private Boolean isTemporary;
    /**
     * 是否合班授课
     */
    private Boolean isMerge;
    /**
     * 是否调课
     */
    private Boolean isChange;
    /**
     * 是否部门授课
     */
    private Boolean department;
}
