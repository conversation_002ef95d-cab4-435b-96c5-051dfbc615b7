package com.unicom.swdx.module.edu.controller.admin.courses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.unicom.swdx.framework.excel.core.convert.TrimConvert;
import lombok.AllArgsConstructor;
import lombok.Data;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 选修课库 导入Excel VO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
// 设置 chain = false，避免导入有问题
@Accessors(chain = false)
public class CoursesOptionalImportExcelVO {

    @ExcelProperty(value = "选修课名称", index = 0, converter = TrimConvert.class)
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String name;

    @ExcelProperty(value = "课程分类", index = 1)
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String theme;

    @ExcelProperty(value = "教学形式", index = 2)
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String educateForm;

    @ExcelProperty(value = "教学方式", index = 3)
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String teachingMethod;

    @ExcelProperty(value = "管理部门", index = 4)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String managementDept;

    @ExcelProperty(value = "授课教师", index = 5)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String teacherNameList;

}
