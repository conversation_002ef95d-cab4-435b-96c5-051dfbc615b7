package com.unicom.swdx.module.edu.service.evaluationdetail;

import java.util.*;
import javax.validation.*;

import cn.hutool.core.lang.Pair;
import com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationdetail.EvaluationDetailDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;

/**
 * 评估详情 Service 接口
 *
 * <AUTHOR>
 */
public interface EvaluationDetailService {

    /**
     * 创建评估详情
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEvaluationDetail(@Valid EvaluationDetailSaveReqVO createReqVO);

    /**
     * 更新评估详情
     *
     * @param updateReqVO 更新信息
     */
    void updateEvaluationDetail(@Valid EvaluationDetailSaveReqVO updateReqVO);

    /**
     * 删除评估详情
     *
     * @param id 编号
     */
    void deleteEvaluationDetail(Long id);

    /**
     * 获得评估详情
     *
     * @param id 编号
     * @return 评估详情
     */
    EvaluationDetailDO getEvaluationDetail(Long id);

    /**
     * 获得评估详情分页
     *
     * @param pageReqVO 分页查询
     * @return 评估详情分页
     */
    PageResult<EvaluationDetailDO> getEvaluationDetailPage(EvaluationDetailPageReqVO pageReqVO);

    PageResult<CommentRespVO> getComment(CommentPageReqVO reqVO);

    List<EvaluationRespVO> getEvaluationList(Boolean handle, String courseName, Long userId, Long studentId);

    void submitEvaluation(EvaluationSubmitVO submitVO);

    Long countStudentUnhandled(Long userId, Long studentId);

    void modifyEvaluationDetail(EvaluationModifyVO updateReqVO);

    void distributeEvaluation(EvaluationDistributeVO distributeVO);

    void revokeQuestionnaire(Long traineeId, Long classCourseId);

    /**
     * 批量删除学员对应课程的所有问卷
     * @param traineeIdAndClassCourseIdPairs <学员id, 评课课程id>对列表
     */
    void batchRevokeQuestionnaireAnyHandle(List<Pair<Long, Long>> traineeIdAndClassCourseIdPairs);

    void deleteByClassCourseId(List<Long> classCourseIds);

    PageResult<EvaluationSummaryPageRespVO> getEvaluationSummaryPage(EvaluationSummaryPageReqVO pageReqVO);
}