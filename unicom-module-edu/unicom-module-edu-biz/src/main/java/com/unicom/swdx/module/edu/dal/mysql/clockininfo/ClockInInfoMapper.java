package com.unicom.swdx.module.edu.dal.mysql.clockininfo;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.dto.attendancerate.TraineeAttendanceInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.*;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.*;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate.AttendanceDetailsVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate.AttendanceRateForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassClockInDO;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;
import com.unicom.swdx.module.edu.enums.attendance.AttendanceTypeEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.ClockRateRuleEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 考勤签到 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ClockInInfoMapper extends BaseMapperX<ClockInInfoDO> {

    default PageResult<ClockInInfoDO> selectPage(ClockInInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ClockInInfoDO>()
                .eqIfPresent(ClockInInfoDO::getClassId, reqVO.getClassId())
                .eqIfPresent(ClockInInfoDO::getTraineeId, reqVO.getTraineeId())
                .eqIfPresent(ClockInInfoDO::getClassCourseId, reqVO.getClassCourseId())
                .eqIfPresent(ClockInInfoDO::getType, reqVO.getType())
                .eqIfPresent(ClockInInfoDO::getMealPeriod, reqVO.getMealPeriod())
                .eqIfPresent(ClockInInfoDO::getTraineeStatus, reqVO.getTraineeStatus())
                .orderByDesc(ClockInInfoDO::getId));
    }

    /**
     * 获取签到详情-未签到、已签到人员信息
     *
     * @param classId       班级id
     * @param classCourseId 课程id
     * @param status        签到状态筛选 0-未签到 1-已签到
     * @return 签到详情学员详情列表
     */
    List<AppTraineeGroupRespVO> getLectureAttendanceTraineeInfo(@Param("classId") Long classId,
                                                                @Param("classCourseId") Long classCourseId,
                                                                @Param("status") Integer status);

    //    default List<ClockInInfoDO> selectListByTraineeId(MyClockInInfoReqVO reqVO) {
//
//        String startDate = reqVO.getDate() + " 00:00:00";
//        String endDate = reqVO.getDate() + " 23:59:59";
//
//        LambdaQueryWrapperX<ClockInInfoDO> wrapper = new LambdaQueryWrapperX<>();
//        wrapper.eq(ClockInInfoDO::getTraineeId, reqVO.getTraineeId());
//        wrapper.ge(ClockInInfoDO::getCreateTime,startDate);
//        wrapper.le(ClockInInfoDO::getCreateTime,endDate);
//        return selectList(wrapper);
//    }
    List<CourseVO> selectListByTraineeId(@Param("reqVO") MyClockInInfoReqVO reqVO);
    List<CourseVO> selectListByTraineeId1(@Param("reqVO") MyClockInInfoReqVO reqVO);

    /**
     * 到课
     * 获取考勤详细数据
     *
     * @param buildPage
     * @param reqVO
     * @return
     */
    List<ClockInInfoReturnVO> selectPageClockInList(IPage<ClockInInfoReturnVO> buildPage, @Param("reqVO") ClockInInfoPageReqVO reqVO);

    /**
     * 就餐
     * 获取考勤详细数据
     *
     * @param buildPage
     * @param reqVO
     * @return
     */
    List<ClockInInfoReturnVO> selectPageListMealAttendance(IPage<ClockInInfoReturnVO> buildPage, @Param("reqVO") ClockInInfoPageReqVO reqVO);

    /**
     * 住宿
     * 获取考勤详细数据
     *
     * @param buildPage
     * @param reqVO
     * @return
     */
    List<ClockInInfoReturnVO> selectPageListCheckIn(IPage<ClockInInfoReturnVO> buildPage, @Param("reqVO") ClockInInfoPageReqVO reqVO);

    /**
     * 获取学员三类考勤信息
     *
     * @param reqVO 查询参数
     * @param ruleType 规则类型 {@link ClockRateRuleEnum}
     * @return 考勤信息
     */
    List<TraineeAttendanceInfoDTO> getTraineeAttendanceInfoList(@Param("reqVO") AttendanceRateReqVO reqVO,
                                                                @Param("ruleType") Integer ruleType);

    /**
     * 获取学员到课考勤信息
     *
     * @param reqVO 查询参数
     * @return 考勤信息
     */
    List<TraineeAttendanceInfoDTO> getTraineeClassAttendanceInfoList(@Param("reqVO") AttendanceRateReqVO reqVO,
                                                                     @Param("ruleType") Integer ruleType);

    /**
     * 获取学员就餐考勤信息
     *
     * @param reqVO 查询参数
     * @return 考勤信息
     */
    List<TraineeAttendanceInfoDTO> getTraineeMealAttendanceInfoList(@Param("reqVO") AttendanceRateReqVO reqVO,
                                                                    @Param("ruleType") Integer ruleType);

    /**
     * 获取学员住宿考勤信息
     *
     * @param reqVO 查询参数
     * @return 考勤信息
     */
    List<TraineeAttendanceInfoDTO> getTraineeAccommodationAttendanceInfoList(@Param("reqVO") AttendanceRateReqVO reqVO,
                                                                             @Param("ruleType") Integer ruleType);

    /**
     * 获取学生上课未到、请假详情列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    List<AttendanceTraineeClassInfoRespVO> getClassTraineeNotArrivedAndLeaveInfo(@Param("reqVO") AttendanceNotArrivedAndLeaveInfoReqVO reqVO);

    /**
     * 获取学生就餐未到、请假详情列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    List<AttendanceTraineeMealInfoRespVO> getMealTraineeNotArrivedAndLeaveInfo(@Param("reqVO") AttendanceNotArrivedAndLeaveInfoReqVO reqVO);

    /**
     * 获取学生住宿未到、请假详情列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    List<AttendanceTraineeAccommodationInfoRespVO> getAccommodationTraineeNotArrivedAndLeaveInfo(@Param("reqVO") AttendanceNotArrivedAndLeaveInfoReqVO reqVO);

    /**
     * 根据班级id和课表id获取到课签到记录
     *
     * @param classId       班级id
     * @param classCourseId 课表id
     * @return 到课签到记录
     */
    default List<ClockInInfoDO> selectListByClassIdAndClassCourseId(Long classId, Long classCourseId) {
        return selectList(new LambdaQueryWrapperX<ClockInInfoDO>()
                .eq(ClockInInfoDO::getClassId, classId)
                .eq(ClockInInfoDO::getClassCourseId, classCourseId)
                .eq(ClockInInfoDO::getType, AttendanceTypeEnum.CLASS_ATTENDANCE.getStatus()));
    }

    /**
     * 根据课表id和学员考勤状态获取签到记录
     *
     * @param classCourseId 课表id
     * @param traineeStatusList 学员考勤状态列表
     * @return 到课签到记录
     */
    default List<ClockInInfoDO> selectListByClassCourseIdAndTraineeStatusList(Long classCourseId, List<Integer> traineeStatusList) {
        return selectList(new LambdaQueryWrapperX<ClockInInfoDO>()
                .eq(ClockInInfoDO::getClassCourseId, classCourseId)
                .inIfPresent(ClockInInfoDO::getTraineeStatus, traineeStatusList));
    }

    /**
     * 到课
     * 学员详细考勤详细情况
     *
     * @param buildPage
     * @param reqVO
     * @return
     */
    List<ClockInInfoStudentStatusVO> getStudentClockingStatusPage(IPage<ClockInInfoReturnVO> buildPage, @Param("reqVO") ClockInInfoPageReqVO reqVO);

    /**
     * 到课
     * 获取考勤详细数据
     * @param
     * @param reqVO
     * @return
     */
    List<ClockInInfoReturnVO> selectClockInList(@Param("reqVO") ClockInInfoExcelExportReqVO reqVO);

    /**
     * 就餐
     * 获取考勤详细数据
     * @param
     * @param reqVO
     * @return
     */
    List<ClockInInfoReturnVO> selectListMealAttendance(@Param("reqVO") ClockInInfoExcelExportReqVO reqVO);
    /**
     * 住宿
     * 获取考勤详细数据
     * @param
     * @param reqVO
     * @return
     */
    List<ClockInInfoReturnVO> selectListCheckIn(@Param("reqVO") ClockInInfoExcelExportReqVO reqVO);

    /**
     * 获取一个月异常考勤日期列表
     * @param monthStart 每月第一天
     * @param monthEnd 第二个月第一天
     * @param classId 班级id
     * @return 考勤异常日期列表
     */
    List<LocalDate> getAbnormalAttendanceDateList(@Param("monthStart") LocalDate monthStart,
                                                  @Param("monthEnd") LocalDate monthEnd,
                                                  @Param("classId") Long classId);
    /**
     * 获取班级某天的到课考勤详情
     * @param date 日期
     * @param classId 班级id
     * @return 住宿考勤详情
     */
    List<AppAttendanceDetailsRespVO> getClassDetails(@Param("date") LocalDate date,
                                                     @Param("classId") Long classId);

    /**
     * 获取班级某天的就餐考勤详情
     * @param date 日期
     * @param classId 班级id
     * @return 住宿考勤详情
     */
    List<AppAttendanceDetailsRespVO> getMealDetails(@Param("date") LocalDate date,
                                                    @Param("classId") Long classId);

    /**
     * 获取班级某天的住宿考勤详情
     * @param date 日期
     * @param classId 班级id
     * @return 住宿考勤详情
     */
    List<AppAttendanceDetailsRespVO> getAccommodationDetails(@Param("date") LocalDate date,
                                                             @Param("classId") Long classId);

    /**
     * 获取考勤项学员考勤具体情况列表
     *
     * @param reqVO 请求参数
     * @return 详情列表
     */
    List<AppTraineeAttendanceDetailsRespVO> getTraineeAttendanceDetails(@Param("reqVO") AppTraineeAttendanceDetailsReqVO reqVO);

    List<CourseVO> selectErrorClock(@Param("reqVO") MyErrorClockInInfoReqVO reqVO);

    /**
     * 某个时间段完全覆盖的学员打卡记录
     * @param traineeId 学员id
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 打卡记录
     */
    List<ClockInInfoDO> selectListByTraineeIdAndLeaveTime(@Param("traineeId") Long traineeId,
                                                          @Param("beginTime") LocalDateTime beginTime,
                                                          @Param("endTime") LocalDateTime endTime);

    AttendanceCheckTime getAttendanceCheckTime(@Param("classId") Long classId);

    Integer getTraineeStatus(@Param("classCourseId") Long classCourseId, @Param("traineeId") String studentId);

    /**
     * 获取迟到的学员信息
     * @param reqVO 请求参数
     * @param ruleType 规则类型 {@link ClockRateRuleEnum}
     * @return 迟到的学员信息
     */
    List<TraineeAttendanceInfoDTO> getLateInfo(@Param("reqVO") AttendanceRateReqVO reqVO,
                                               @Param("ruleType") Integer ruleType);

    List<Long> selectUnchecked(@Param("classCourseId") Long classCourseId);

    List<Long> selectChecked(@Param("classCourseId") Long classcourseId);

    /**
     * 根据大课考勤id获取大课考勤签到的记录
     * @param attendanceId 大课考勤id
     * @return 大课考勤签到的记录
     */
    default List<ClockInInfoDO> selectListByLargeAttendanceId(Long attendanceId){
        return selectList(new LambdaQueryWrapperX<ClockInInfoDO>()
                .eq(ClockInInfoDO::getLargeAttendanceId, attendanceId));
    }

    /**
     * 业中调用获取考勤三率
     *
     * @param reqVO 请求对象，包含请求参数
     * @return AttendanceRateForYZRespVO 响应数据
     */
    List<AttendanceDetailsVO> selectAttendanceRateForYZ(@Param("reqVO") AttendanceRateForBusinessCenterReqVO reqVO,
                                                        @Param("currentTime") LocalDateTime currentTime);

    /**
     * 考勤保护开关是否开启
     * @param tenantId 租户id
     * @return 考勤保护开关是否开启
     */
    @TenantIgnore
    Boolean isEnableAttendanceProtection(@Param("tenantId") Long tenantId);

    /**
     * 根据考勤日期查询考勤记录
     * @param clockDate 考勤日期
     * @return 考勤记录列表
     */
    default List<ClockInInfoDO> selectListByClockDateAndClassId(LocalDate clockDate, Long classId, List<Integer> traineeStatusList) {
        return selectList(new LambdaQueryWrapperX<ClockInInfoDO>()
                .eq(ClockInInfoDO::getDate, clockDate)
                .eqIfPresent(ClockInInfoDO::getClassId, classId)
                .inIfPresent(ClockInInfoDO::getTraineeStatus, traineeStatusList));
    }


    default List<ClockInInfoDO> selectListByClockDatesAndClassId(List<LocalDate> dates, Long classId, List<Integer> traineeStatusList) {
        if (CollUtil.isEmpty(dates)) {
            return new ArrayList<>();
        }

        return selectList(new LambdaQueryWrapperX<ClockInInfoDO>()
                .in(ClockInInfoDO::getDate, dates)
                .eqIfPresent(ClockInInfoDO::getClassId, classId)
                .inIfPresent(ClockInInfoDO::getTraineeStatus, traineeStatusList));
    }

    /**
     * 根据排课id删除考勤记录
     * @param classCourseIds 排课id列表
     */
    default void deleteByClassCourseIds(List<Long> classCourseIds){
        if (CollUtil.isEmpty(classCourseIds)) {
            return;
        }
        delete(new LambdaQueryWrapperX<ClockInInfoDO>()
                .in(ClockInInfoDO::getClassCourseId, classCourseIds));
    }

    /**
     * 根据班级id和日期列表删除住宿考勤记录
     * @param classId 班级id
     * @param localDates 日期列表
     */
    default void deleteAccommodationByClassIdAndDateList(Long classId, List<LocalDate> localDates){
        if (CollUtil.isEmpty(localDates)) {
            return;
        }
        delete(new LambdaQueryWrapperX<ClockInInfoDO>()
                .eq(ClockInInfoDO::getClassId, classId)
                .eq(ClockInInfoDO::getType, AttendanceTypeEnum.ACCOMMODATION_ATTENDANCE.getStatus())
                .in(ClockInInfoDO::getDate, localDates));
    }

    /**
     * 根据日期列表删除住宿考勤记录
     * @param localDates 日期列表
     */
    default void deleteAccommodationByDateList(List<LocalDate> localDates){
        if (CollUtil.isEmpty(localDates)) {
            return;
        }
        delete(new LambdaQueryWrapperX<ClockInInfoDO>()
                .eq(ClockInInfoDO::getType, AttendanceTypeEnum.ACCOMMODATION_ATTENDANCE.getStatus())
                .in(ClockInInfoDO::getDate, localDates));
    }

    /**
     * 根据班级id删除指定日期列表的某些餐点的就餐考勤记录
     * @param classId 班级id
     * @param localDates 日期列表
     * @param deleteMealPeriodList 要删除的餐点列表
     */
    default void deleteMealByClassIdAndDateListAndMealPeriodList(Long classId,
                                                                 List<LocalDate> localDates,
                                                                 List<Integer> deleteMealPeriodList){
        if (CollUtil.isEmpty(localDates) || CollUtil.isEmpty(deleteMealPeriodList)) {
            return;
        }
        delete(new LambdaQueryWrapperX<ClockInInfoDO>()
                .eq(ClockInInfoDO::getClassId, classId)
                .eq(ClockInInfoDO::getType, AttendanceTypeEnum.MEAL_ATTENDANCE.getStatus())
                .in(ClockInInfoDO::getDate, localDates)
                .in(ClockInInfoDO::getMealPeriod, deleteMealPeriodList));
    }

    /**
     * 删除指定日期列表的某些餐点的就餐考勤记录
     * @param localDates 日期列表
     * @param deleteMealPeriodList 要删除的餐点列表
     */
    default void deleteMealByDateListAndMealPeriodList(List<LocalDate> localDates,
                                                                 List<Integer> deleteMealPeriodList){
        if (CollUtil.isEmpty(localDates) || CollUtil.isEmpty(deleteMealPeriodList)) {
            return;
        }
        delete(new LambdaQueryWrapperX<ClockInInfoDO>()
                .eq(ClockInInfoDO::getType, AttendanceTypeEnum.MEAL_ATTENDANCE.getStatus())
                .in(ClockInInfoDO::getDate, localDates)
                .in(ClockInInfoDO::getMealPeriod, deleteMealPeriodList));
    }

    /**
     * 获取班级学员考勤详情分页
     *
     * @param page   分页参数
     * @param pageVO 查询条件
     * @return 班级学员考勤详情列表
     */
    List<ClassAttendanceDetailRespVO> getClassAttendanceDetailsPage(IPage<ClassAttendanceDetailRespVO> page,
            @Param("pageVO") ClassAttendanceDetailPageReqVO pageVO);
}
