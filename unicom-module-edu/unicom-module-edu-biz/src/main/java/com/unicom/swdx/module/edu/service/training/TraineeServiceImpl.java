package com.unicom.swdx.module.edu.service.training;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.crypt.sm4.SM4Util;
import com.unicom.swdx.framework.common.util.desensitize.DesensitizeUtils;
import com.unicom.swdx.framework.common.util.object.PageUtils;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.todoitems.dto.ECContentDTO;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.edu.controller.admin.todoitems.dto.ECContentDTO;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.*;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO;
import com.unicom.swdx.module.edu.convert.trainee.TraineeConvert;
import com.unicom.swdx.module.edu.convert.trainee.TraineeInfoExportConvert;
import com.unicom.swdx.module.edu.dal.dataobject.cadreInformation.CadreInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationdetail.EvaluationDetailDO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse.EvaluationResponseDO;
import com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.evaluationdetail.EvaluationDetailMapper;
import com.unicom.swdx.module.edu.dal.mysql.evaluationresponse.EvaluationResponseMapper;
import com.unicom.swdx.module.edu.dal.mysql.signupunit.SignUpUnitMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.dal.redis.edu.TraineeLoginTypeRedisDAO;
import com.unicom.swdx.module.edu.enums.classmanagement.SemesterEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeDictTypeEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.enums.userrole.JwUserRoleTypeEnum;
import com.unicom.swdx.module.edu.service.cadreInformation.CadreInformationService;
import com.unicom.swdx.module.edu.service.classcommen.CommenService;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementServiceImpl;
import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoService;
import com.unicom.swdx.module.edu.service.electivetraineeselection.ElectiveTraineeSelectionService;
import com.unicom.swdx.module.edu.service.rollcallrecord.RollcallRecordService;
import com.unicom.swdx.module.edu.service.signupunit.SignUpUnitService;
import com.unicom.swdx.module.edu.service.signupunit.SignUpUnitServiceImpl;
import com.unicom.swdx.module.edu.service.traineeleave.TraineeLeaveService;
import com.unicom.swdx.module.edu.utils.IdCard.CheckIdCard;
import com.unicom.swdx.module.edu.utils.yezhong.YezhongResult;
import com.unicom.swdx.module.system.api.businesscenter.BusinessCenterApi;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import com.unicom.swdx.module.system.api.permission.PermissionApi;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;


/**
 * 用户信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TraineeServiceImpl extends ServiceImpl<TraineeMapper, TraineeDO> implements TraineeService {

    @Value("${unicom.mid-base-uri}")
    private String MID_BASE_URI;

    private final String ADD_BATCH_TRAINEE_URI = "/admin-api/system/user/createBatchTrainee";

    private final String ADD_TRAINEE_URI = "/admin-api/system/user/createTrainee";

    private final String EDIT_TRAINEE_URI = "/admin-api/system/user/updateTrainee";

    private final String DEL_TRAINEE_URI = "/admin-api/system/user/removeTrainee";

    private final String SYNC_TRAINEES_URI = "/admin-api/system/syncTrainees";

    @Value("${wx.miniapp.envVersion:trial}")
    private String XCX_ENV_VERSION;

    private static final String URL_QRCODE = "https://api.weixin.qq.com/wxa/getwxacodeunlimit";

    private final String REDISKEY_ACCESSTOKEN = "wxxcx:access_token";

    private final String URL_STABLE_ACCESSTOKEN = "https://api.weixin.qq.com/cgi-bin/stable_token";

    private static final String JOB_LEVEL_DICT_TYPE = "stu_rank";

    private static final String EDU_CLASS_ATTRIBUTE_TYPE = "edu_class_attribute";

    private static final String POLITICAL_IDENTITY = "political_identity";

    private static final String EDUCATIONAL_LEVEL = "stu-educational";

    private static final String TRAINEE_STATUS = "trainee_status";

    private static final String NATION = "nation";



    private static final String ASC = "0";

    private static final String DESC = "1";

    // 定义手机号的正则表达式
    private static final String PHONE_REGEX = "^1[3-9]\\d{9}$";

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private SignUpUnitMapper signUpUnitMapper;

    @Resource
    private BusinessCenterApi businessCenterApi;

    @Resource
    private AdminUserApi adminUserApi;
    @Autowired
    private TenantApi tenantApi;

    @Resource
    private TraineeDictConvertService traineeDictConvertService;

    @Resource
    @Lazy
    private ClockInInfoService clockInInfoService;

    @Resource
    private EvaluationResponseMapper evaluationResponseMapper;

    @Resource
    private EvaluationDetailMapper evaluationDetailMapper;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;

    @Resource
    private CadreInformationService cadreInformationService;
    @Autowired
    private SignUpUnitService signUpUnitService;
    @Autowired
    private SignUpUnitServiceImpl signUpUnitServiceImpl;

    @Autowired
    @Lazy
    private ClassManagementServiceImpl classManagementServiceImpl;

    @Resource
    DictDataApi dictDataApi;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private CommenService commenService;

    @Resource
    private TraineeLoginTypeRedisDAO traineeLoginTypeRedisDAO;

    @Resource
    @Lazy
    private TraineeLeaveService traineeLeaveService;

    @Resource
    @Lazy
    private ElectiveTraineeSelectionService electiveTraineeSelectionService;

    @Resource
    private RollcallRecordService rollcallRecordService;

    /**
     * 时间格式
     */
    private static final String DATE = "yyyy-MM-dd HH:mm:ss";

    /**
     * 单位为模版（父级单位）
     */
    private static final Integer TEMPLATE = 1;

    @Resource(name = "taskScheduler")
    private AsyncListenableTaskExecutor taskExecutor;

    private static final  String desensitizePublicKeyStr = "04d89d95d0129f8b22a27979fd2c5d1fad305e1ec9ebbd2f6ff26f5bfdf00e5493574e8b93a2a16d4f924efd5133b6e7474aa694ec6314e39c34ff89c22d7b3768";

    private static final String desensitizePrivateKeyStr = "5174bc2473f86d5cda2ddad6a2d9b705848dd65b6fa13ff0923b1d2b55f09d30";

    private static final SM2 sm2Desensitize = SmUtil.sm2(desensitizePrivateKeyStr,desensitizePublicKeyStr);

    private static String desensitizeEncrypt(String content) {
        return sm2Desensitize.encryptBase64(content, KeyType.PublicKey);
    }

    private static String desensitizeDecrypt(String content) {
        try {
            return sm2Desensitize.decryptStr(content, KeyType.PrivateKey);
        }catch (Exception e){
            log.info(content + "解密失败");
            return content;
        }
    }


    @Override
    public Page<RegistrationPageRespVO> getPage(RegistrationInfoReqVO reqVO) {
        Page<RegistrationPageRespVO> page = traineeMapper.getRegistrationPage(MyBatisUtils.buildPage(reqVO), reqVO);
//        page.getRecords().forEach(item ->{
//            item.setActualPeopleNumber(traineeMapper.getActualPeopleNumber(item.getId(),0));
//            item.setReportNumber(traineeMapper.getActualPeopleNumber(item.getId(),1));
//        });
        return page;
    }

    @Override
    public List<RegistrationDetailExcelVO> getRegistrationInfoList(RegistrationInfoReqVO reqVO) {
        List<RegistrationDetailVO> list = traineeMapper.getRegistrationInfo(reqVO);

        List<RegistrationDetailExcelVO> list1 = new ArrayList<>();

        //system_dict_data 没有tenant_id这个字段
//        Map<Long, Map<String, String>> campusMap = traineeDictConvertService.getDictDateMapByIdandTenantId("edu_classroom_campus" , SecurityFrameworkUtils.getTenantId());
        Map<Long, Map<String, String>> campusMap = traineeDictConvertService.getDictDateMapById("edu_classroom_campus" );
        list.forEach(item -> {
//            item.setActualPeopleNumber(traineeMapper.getActualPeopleNumber(item.getId(),0));
//            item.setReportNumber(traineeMapper.getActualPeopleNumber(item.getId(),1));
            RegistrationDetailExcelVO vo = new RegistrationDetailExcelVO();
            vo.setReportNumber(item.getReportNumber());

            if (item.getCampus() != null && campusMap != null && campusMap.containsKey(Long.valueOf(item.getCampus())) && campusMap.get(Long.valueOf(item.getCampus())) != null) {
                vo.setCampus(campusMap.get(Long.valueOf(item.getCampus())).get("label"));
            } else {
                vo.setCampus(null);
            }
            vo.setActualPeopleNumber(item.getActualPeopleNumber());
            vo.setClassName(item.getClassName());
            vo.setPeopleNumber(item.getPeopleNumber());
            vo.setCompletionTime(item.getCompletionTime());
            vo.setClassNameCode(item.getClassNameCode());
            vo.setClassOpenTime(item.getClassOpenTime());
            list1.add(vo);
        });


        return list1;
    }

    @Override
    public Page<RegistrationInfoRespVO> getPageByClassId(RegistrationInfoReqVO reqVO) {
        Page<RegistrationInfoRespVO> page = traineeMapper.getRegistrationInfoPage(MyBatisUtils.buildPage(reqVO), reqVO);
        List<RegistrationInfoRespVO> records = page.getRecords();
        ClassManagementDO classDO = classManagementMapper.selectById(reqVO.getClassId());
        for (RegistrationInfoRespVO vo : records) {
            vo.setClassStatus(this.getClassStatus(vo.getClassId()));
            vo.setClassName(classDO.getClassName());
            
            if (vo.getIsRestrict() == 1){
                vo.setNotRegistered(0);
            }else {
                if (vo.getCapacity() != null) {
                    vo.setNotRegistered(vo.getCapacity() - vo.getActualPeopleNumber());
                } else {
                    vo.setNotRegistered(0);
                }
            }
        }
        page.setRecords(records);
        return page;
    }

    @Override
    public Workbook exportHighRiskIndustryStatisticsTable(RegistrationInfoReqVO reqVO, List<Map<String, Object>> list, HttpServletResponse response) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("报名详情");

        XSSFRow titleRow = sheet.createRow(0);
        // 设置样式以及字体样式
        XSSFCellStyle headerStyle = createHeadCellStyle(workbook);
        XSSFCellStyle contentStyle = createContentCellStyle(workbook);

        // 创建一个Map对象
        Map<String, String> fieldMap = new HashMap<>();
        // 添加字段和描述
        fieldMap.put("classnamecode", "班次编码");
        fieldMap.put("classname", "班次名称");
        fieldMap.put("classopentime", "开班时间");
        fieldMap.put("completiontime", "结业时间");
        fieldMap.put("campus", "校区");
        fieldMap.put("peoplenumber", "预计人数");
        fieldMap.put("actualpeoplenumber", "实际报名人数");
        fieldMap.put("reportnumber", "已报道人数");

        List<String> headers = reqVO.getHeaders()
                .stream().map(String::toLowerCase)
                .collect(Collectors.toList());
        if (!headers.isEmpty()) {
            for (int i = 0; i < headers.size(); i++) {
                titleRow.createCell(i).setCellValue(fieldMap.get(headers.get(i)));
                titleRow.getCell(i).setCellStyle(headerStyle);
            }
        } else {
            // todo 否则不能查找
            return workbook;
        }

        // 内容
        int rowNum = 1;
        if (list != null && !list.isEmpty()) {
            // 遍历map
            for (Map<String, Object> entry : list) {
                XSSFRow row = sheet.createRow(rowNum++);
                for (int i = 0; i < headers.size(); i++) {
                    row.createCell(i).setCellValue(entry.get(headers.get(i)).toString());
                    row.getCell(i).setCellStyle(contentStyle);
                }
            }

            // 设置自动列宽
            for (int i = 1; i < titleRow.getLastCellNum(); i++) {
                sheet.autoSizeColumn(i);
            }
        }
        return workbook;
    }

    @Override
    public Page<UnitRegistrationPageRespVO> getPageByUnitId(UnitRegistrationPageReqVO reqVO) {
        return traineeMapper.getPageByUnitId(MyBatisUtils.buildPage(reqVO), reqVO);
    }

    @Override
    public Page<UnitRegistrationPageRespVO> pageTraineeInfo(TraineeInfoReqVO reqVO) {
        Page<UnitRegistrationPageRespVO> page = traineeMapper.pageTraineeInfo(MyBatisUtils.buildPage(reqVO), reqVO);
        List<UnitRegistrationPageRespVO> records = page.getRecords();

        for (int i = 0; i < records.size(); i++) {

            UnitRegistrationPageRespVO vo = records.get(i);
            String phone = vo.getPhone();
            vo.setNumber(phone.substring(3, 7));
            vo.setIndex((int) (page.getCurrent() - 1) * page.getSize() + i + 1);
            String rawPhone = vo.getPhone();
            String rawCardNo = vo.getCardNo();
            String rawPosition = vo.getPosition();
            if (reqVO.getIsPlatform() == null || !reqVO.getIsPlatform()) {
                if (rawPhone != null) {
                    String desensitizedPhone = DesensitizedUtil.mobilePhone(rawPhone);
                    String encryptedPhone = desensitizeEncrypt(desensitizedPhone);
                    vo.setPhone(encryptedPhone);
                }

                if (rawCardNo != null) {
                    String desensitizedCardNo = DesensitizedUtil.idCardNum(vo.getCardNo(), 5, 2);
                    String encryptedCardNo = desensitizeEncrypt(desensitizedCardNo);
                    vo.setCardNo(encryptedCardNo);
                }


                if (rawPosition != null) {
                    String desensitizedPosition = DesensitizeUtils.sliderDesensitize(rawPosition, 1, 0);
                    String encryptedPosition = desensitizeEncrypt(desensitizedPosition);
                    vo.setPosition(encryptedPosition);
                }
            } else if (reqVO.getIsPlatform()) {
                if (rawPhone != null) {
                    String encryptedPhone = desensitizeEncrypt(rawPhone);
                    vo.setPhone(encryptedPhone);
                }

                if (rawCardNo != null) {
                    String encryptedCardNo = desensitizeEncrypt(rawCardNo);
                    vo.setCardNo(encryptedCardNo);
                }


                if (rawPosition != null) {
                    String encryptedPosition = desensitizeEncrypt(rawPosition);
                    vo.setPosition(encryptedPosition);
                }
            }

        }
        page.setRecords(records);
        return page;
    }

    private Integer getClassStatus(Long classId) {
        return traineeMapper.getClassStatus(classId);
    }

    @Override
    public Long addTrainee(AddTraineeInfoReqVO reqVO) {

        String position = reqVO.getPosition();

        String phone = reqVO.getPhone();

        String cardNo = reqVO.getCardNo();

        if (position != null && position != "") {
            String decryptedPosition = desensitizeDecrypt(position);
            reqVO.setPosition(decryptedPosition);
        }

        if (phone != null) {
            String decryptedPhone = desensitizeDecrypt(phone);
            reqVO.setPhone(decryptedPhone);
        }

        if (cardNo != null) {
            String decryptedCardNo = desensitizeDecrypt(cardNo);
            reqVO.setCardNo(decryptedCardNo);
        }

        // 校验存在 根据身份证校验同一班级下是否有重复
        checkUnique(reqVO);

        ClassManagementDO classDO = classManagementMapper.selectById(reqVO.getClassId());

        //判断班级当前是否已结业，若结业则不能报名
        checkIfClassCompleted(classDO);

        // 判断班级是否处于报名中
//        checkIsSigning(reqVO.getClassId());

        // 判断该班级是否还有名额
//        Long count = traineeMapper.getActualPeopleNumber(reqVO.getClassId(),3);
//        if (count>=classDO.getPeopleNumber()){
//            throw exception(TRAINEE_CLASS_NO_QUOTA);
//        }

        // 判断该单位是否还有名额
        SignUpUnitDO signUpUnitDO = signUpUnitMapper.selectById(reqVO.getUnitId());
//        Long unitCount = traineeMapper.getActualPeopleNumber(reqVO.getUnitId(),2);
//        if (signUpUnitDO.getIsRestrict() == 0 && (signUpUnitDO.getCapacity() == null || unitCount >= signUpUnitDO.getCapacity())){
//            throw exception(TRAINEE_UNIT_NO_QUOTA);
//        }

        TraineeDO traineeDO = TraineeConvert.INSTANCE.convert(reqVO);


        if(ObjectUtil.isNotEmpty(traineeDO)){
            traineeDO.setClassName(classDO.getClassName());
        }

        if (Objects.nonNull(signUpUnitDO)) {
            traineeDO.setUnitName(signUpUnitDO.getUnitName());

            if (signUpUnitDO.getUnitClassification() != null) {
                traineeDO.setUnitClassification(Long.valueOf(signUpUnitDO.getUnitClassification()));
            }
        }


        traineeDO.setStatus(TraineeStatusEnum.REGISTERED.getStatus());

        // system_users表新增用户用于单点登录
        Long userId = adminUserApi.createUser(traineeDO.getPhone(), traineeDO.getName(), classDO.getTenantId()).getCheckedData();
        traineeDO.setUserId(userId);
        traineeMapper.insert(traineeDO);

        // 同步业中
        if (traineeDO.getTenantId() == null) {
            traineeDO.setTenantId(SecurityFrameworkUtils.getTenantId().intValue());
        }
        traineeDO.setAsync(true);

        String yzUserId = sendTraineeToMid(JSONUtil.toJsonStr(traineeDO), ADD_TRAINEE_URI);
        adminUserApi.updateSystemId(userId, yzUserId);


        if (Objects.nonNull(signUpUnitDO)) {
            // 判断干部信息中是否存在这个人，不存在则插入
            CadreInformationDO cadreInformationDO = cadreInformationService.queryByCadreIdCard(traineeDO.getCardNo(), signUpUnitDO.getParentId(), traineeDO.getTenantId());

            if (cadreInformationDO == null) {
                // 插入干部信息
                reqVO.setUnitId(signUpUnitDO.getParentId());
                reqVO.setFlag(true);
                cadreInformationService.addCadreInformation(reqVO);
            }
        }


        return traineeDO.getId();
    }

    /**
     * 判断班级是否已经结业
     * @param classDO 班级DO
     */
    private void checkIfClassCompleted(ClassManagementDO classDO) {
        LocalDate completionDate = classDO.getCompletionTime().toLocalDate();

        if (LocalDate.now().isAfter(completionDate)){
            throw exception(CLASS_COMPLETED);
        }
    }

    private void checkIsSigning(Long classId) {
        Integer count = traineeMapper.checkIsSigning(classId);
        if (count == 0) {
            throw exception(TRAINEE_CLASS_NOT_SIGNING);
        }
    }

    @Override
    public Page<ReportPageRespVO> reportPage(ReportPageReqVO reqVO) {
        if (Objects.isNull(reqVO.getReportStatus())) {
            Page<ReportPageRespVO> page = traineeMapper.reportPage(MyBatisUtils.buildPage(reqVO), reqVO);
            List<ReportPageRespVO> records = page.getRecords();
            for (int i = 0; i < records.size(); i++) {
                ReportPageRespVO vo = records.get(i);
                vo.setIndex((page.getCurrent() - 1) * page.getSize() + i + 1);
            }
            page.setRecords(records);
            return page;
        } else {
            if (StrUtil.isNotBlank(reqVO.getReportDateBeg())) {
                reqVO.setReportDateEnd(reqVO.getReportDateBeg() + " 23:59:59");
                reqVO.setReportDateBeg(reqVO.getReportDateBeg() + " 00:00:00");
            }
            Page<ReportPageRespVO> page = traineeMapper.reportInfoPageByStatus(MyBatisUtils.buildPage(reqVO), reqVO);
            List<ReportPageRespVO> records = page.getRecords();
            for (int i = 0; i < records.size(); i++) {
                ReportPageRespVO vo = records.get(i);
                vo.setIndex((page.getCurrent() - 1) * page.getSize() + i + 1);
                String rawPhone = vo.getPhone();
                String rawCardNo = vo.getCardNo();
                String rawPosition = vo.getPosition();

                if (rawPhone != null) {
                    String desensitizedPhone = DesensitizedUtil.mobilePhone(rawPhone);
                    String encryptedPhone = desensitizeEncrypt(desensitizedPhone);
                    vo.setPhone(encryptedPhone);
                }

                if (rawCardNo != null) {
                    String desensitizedCardNo = DesensitizedUtil.idCardNum(vo.getCardNo(), 5, 2);
                    String encryptedCardNo = desensitizeEncrypt(desensitizedCardNo);
                    vo.setCardNo(encryptedCardNo);
                }


                if (rawPosition != null) {
                    String desensitizedPosition = DesensitizeUtils.sliderDesensitize(rawPosition, 1, 0);
                    String encryptedPosition = desensitizeEncrypt(desensitizedPosition);
                    vo.setPosition(encryptedPosition);
                }

            }
            page.setRecords(records);
            return page;
        }

    }

    @Override
    public List<RegistrationInfoExcelVO> getUnitRegistrationList(RegistrationInfoReqVO reqVO) {

        return traineeMapper.getRegistrationInfoList(reqVO);
    }

    @Override
    public List<RegistrationExcelVO> getTraineeInfoList(TraineeInfoReqVO reqVO) {
        List<UnitRegistrationPageRespVO> traineeInfoList = traineeMapper.getTraineeInfoList(reqVO);

        List<RegistrationExcelVO> list = TraineeInfoExportConvert.INSTANCE.convertList(traineeInfoList);

        Map<Long, Map<String, String>> educationalLevel = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.EDUCATIONAL_LEVEL.getType());
        Map<Long, Map<String, String>> jobLevelMap = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.JOB_LEVEL.getType());
        Map<Long, Map<String, String>> politicalIdentityMap = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.POLITICAL_IDENTITY.getType());
        Map<String, Map<String, String>> traineeStatusMap = traineeDictConvertService.getDictDateMapById1(TraineeDictTypeEnum.TRAINEE_STATUS.getType());


        for (int i = 0; i < traineeInfoList.size(); i++) {
            UnitRegistrationPageRespVO tmpVO = traineeInfoList.get(i);
            RegistrationExcelVO excelVO = list.get(i);

            if (tmpVO.getEducationalLevel() != null && educationalLevel != null && educationalLevel.containsKey(Long.valueOf(tmpVO.getEducationalLevel())) && educationalLevel.get(Long.valueOf(tmpVO.getEducationalLevel())) != null) {
                excelVO.setEducationalLevel(educationalLevel.get(Long.valueOf(tmpVO.getEducationalLevel())).get("label"));
            } else {
                excelVO.setEducationalLevel(null);
            }

            if (tmpVO.getJobLevel() != null && jobLevelMap != null && jobLevelMap.containsKey(Long.valueOf(tmpVO.getJobLevel())) && jobLevelMap.get(Long.valueOf(tmpVO.getJobLevel())) != null) {
                excelVO.setJobLevel(jobLevelMap.get(Long.valueOf(tmpVO.getJobLevel())).get("label"));
            } else {
                excelVO.setJobLevel(null);
            }

            if (tmpVO.getPoliticalIdentity() != null && politicalIdentityMap != null && politicalIdentityMap.containsKey(Long.valueOf(tmpVO.getPoliticalIdentity())) && politicalIdentityMap.get(Long.valueOf(tmpVO.getPoliticalIdentity())) != null) {
                excelVO.setPoliticalIdentity(politicalIdentityMap.get(Long.valueOf(tmpVO.getPoliticalIdentity())).get("label"));
            } else {
                excelVO.setPoliticalIdentity(null);
            }


            if (tmpVO.getStatus() != null && traineeStatusMap != null && traineeStatusMap.containsKey(tmpVO.getStatus().toString()) && traineeStatusMap.get(tmpVO.getStatus().toString()) != null) {
                excelVO.setStatus(traineeStatusMap.get(tmpVO.getStatus().toString()).get("label"));
            } else {
                excelVO.setStatus(null);
            }


        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long editTrainee(EditTraineeInfoReqVO reqVO) {
        // 检查是否是同步请求，避免循环同步
        Boolean syncFlag = reqVO.getSyncFlag();

        String position = reqVO.getPosition();
        String phone = reqVO.getPhone();
        String cardNo = reqVO.getCardNo();

        if (position != null && position != "") {
            String decryptedPosition = desensitizeDecrypt(position);
            reqVO.setPosition(decryptedPosition);
        }

        if (phone != null) {
            String decryptedPhone = desensitizeDecrypt(phone);
            reqVO.setPhone(decryptedPhone);
        }

        if (cardNo != null) {
            String decryptedCardNo = desensitizeDecrypt(cardNo);
            reqVO.setCardNo(decryptedCardNo);
        }

        // 校验存在
        checkExist(Collections.singletonList(reqVO.getId()));

        // 校验是否可以编辑
        checkUnique(reqVO);

        SignUpUnitDO signUpUnitDO = signUpUnitMapper.selectById(reqVO.getUnitId());
//        if (signUpUnitDO!=null){
//            reqVO.setUnitName(signUpUnitDO.getUnitName());
//            reqVO.setUnitClassification(Long.valueOf(signUpUnitDO.getUnitClassification()));
//        }

        if (Objects.nonNull(signUpUnitDO)) {
            reqVO.setUnitName(signUpUnitDO.getUnitName());

            if (signUpUnitDO.getUnitClassification() != null) {
                reqVO.setUnitClassification(Long.valueOf(signUpUnitDO.getUnitClassification()));
            }
        }

        // 编辑
        traineeMapper.editTrainee(reqVO);

        TraineeDO traineeDO = TraineeConvert.INSTANCE.convert1(reqVO);

        Long tenantId = SecurityFrameworkUtils.getTenantId();
        if (tenantId != null) {
            traineeDO.setTenantId(Integer.parseInt(tenantId.toString()));
        }

        String returnResultBody = sendTraineeToMidSync(JSONUtil.toJsonStr(traineeDO), EDIT_TRAINEE_URI);
        // 请求数据 如果业中该学员用户信息修改失败 则异常回滚
        Long traineeSystemId = JSON.parseObject(returnResultBody, new TypeReference<CommonResult<Long>>() {}).getCheckedData();
        log.info("修改trainee信息到中台成功！对应业中userId: {}", traineeSystemId);

        // system_users表新增用户用于单点登录
//        Long userId = adminUserApi.createUser(traineeDO.getPhone()).getCheckedData();

        // system_users表新增用户用于单点登录
        TraineeDO trainee = baseMapper.selectById(reqVO.getId());
        adminUserApi.editUser(traineeDO.getPhone(), trainee.getUserId(), trainee.getName()).getCheckedData();

        // 只有在非同步请求时才同步回干部系统
        if (syncFlag == null || !syncFlag) {
            if (signUpUnitDO != null) {
                // 判断干部信息中是否存在这个人，不存在则插入
                CadreInformationDO cadreInformationDO = cadreInformationService
                        .queryByCadreIdCard(traineeDO.getCardNo(), signUpUnitDO.getParentId(), traineeDO.getTenantId());
                if (cadreInformationDO == null) {
                    // 插入干部信息
                    reqVO.setUnitId(signUpUnitDO.getParentId());
                    AddTraineeInfoReqVO addTraineeInfoReqVO = TraineeConvert.INSTANCE.convert2(reqVO);
                    addTraineeInfoReqVO.setId(null);
                    addTraineeInfoReqVO.setFlag(true);
                    cadreInformationService.addCadreInformation(addTraineeInfoReqVO);
                } else {
                    // 同步更新干部信息
                    syncToCadreInformationSystem(reqVO, cadreInformationDO);
                }
            }
        } else {
            log.info("跳过同步到干部调训系统，因为当前是同步请求，学员ID: {}", reqVO.getId());
        }

        return reqVO.getId();
    }

    /**
     * 同步学员信息到干部调训系统
     * 当教务管理系统或学员管理系统更新学员信息时，同步更新到干部调训系统
     *
     * @param reqVO              学员信息请求对象
     * @param cadreInformationDO 干部信息对象
     */
    private void syncToCadreInformationSystem(EditTraineeInfoReqVO reqVO, CadreInformationDO cadreInformationDO) {
        // 检查是否是同步请求，避免循环同步
        Boolean syncFlag = reqVO.getSyncFlag();
        if (syncFlag != null && syncFlag) {
            log.info("跳过同步到干部调训系统，因为当前是同步请求，学员ID: {}", reqVO.getId());
            return;
        }

        try {
            log.info("开始同步学员信息到干部调训系统，学员ID: {}, 干部ID: {}", reqVO.getId(), cadreInformationDO.getId());

            // 创建干部信息更新请求
            EditTraineeInfoReqVO cadreUpdateReqVO = new EditTraineeInfoReqVO();

            // 设置干部ID
            cadreUpdateReqVO.setId(cadreInformationDO.getId());

            // 复制基本信息
            cadreUpdateReqVO.setName(reqVO.getName());
            cadreUpdateReqVO.setSex(reqVO.getSex());
            cadreUpdateReqVO.setCardNo(reqVO.getCardNo());
            cadreUpdateReqVO.setPhone(reqVO.getPhone());
//            cadreUpdateReqVO.setCardNo(cardNo);
//            cadreUpdateReqVO.setPhone(phone);
            cadreUpdateReqVO.setEducationalLevel(reqVO.getEducationalLevel());
            cadreUpdateReqVO.setBirthday(reqVO.getBirthday());
            cadreUpdateReqVO.setEthnic(reqVO.getEthnic());
            cadreUpdateReqVO.setPosition(reqVO.getPosition());
//            cadreUpdateReqVO.setPosition(position);
            cadreUpdateReqVO.setJobLevel(reqVO.getJobLevel());
            cadreUpdateReqVO.setGraduationSchool(reqVO.getGraduationSchool());
            cadreUpdateReqVO.setPoliticalIdentity(reqVO.getPoliticalIdentity());
            cadreUpdateReqVO.setRemark(reqVO.getRemark());
            cadreUpdateReqVO.setPhoto(reqVO.getPhoto());

            // 保留干部系统的单位信息
            cadreUpdateReqVO.setUnitId(cadreInformationDO.getUnitId());

            // 从SignUpUnitDO获取单位名称
            SignUpUnitDO unitDO = signUpUnitMapper.selectById(cadreInformationDO.getUnitId());
            if (unitDO != null) {
                cadreUpdateReqVO.setUnitName(unitDO.getUnitName());
                if (unitDO.getUnitClassification() != null) {
                    cadreUpdateReqVO.setUnitClassification(Long.valueOf(unitDO.getUnitClassification()));
                }
            }

            // 保留租户ID
            cadreUpdateReqVO.setTenantId(cadreInformationDO.getTenantId().intValue());

            // 设置同步标记，避免循环同步
            cadreUpdateReqVO.setSyncFlag(true);

            // 使用干部信息服务进行更新
            cadreInformationService.editCadreInformation(cadreUpdateReqVO);
            log.info("同步更新干部信息成功，学员ID: {}, 干部ID: {}", reqVO.getId(), cadreInformationDO.getId());
        } catch (Exception e) {
            // 只记录错误，不影响主流程
            log.error("同步学员信息到干部调训系统失败，学员ID: {}, 干部ID: {}, 错误: {}",
                    reqVO.getId(), cadreInformationDO.getId(), e.getMessage(), e);
        }
    }

    @Override
    public Boolean deleteTrainee(Long id) {
        // todo 校验存在
        List<Long> ids = Collections.singletonList(id);
        checkExist(ids);


        List<Long> needdeleteda = new ArrayList<>();  // 只删除学员账号
        // 删除教务学员信息
        List<Long> needdeleted = removeJWUser(ids);  //直接删除业中的登录账号
        Map<String, Object> param = new HashMap<>();

        if(CollectionUtil.isEmpty(needdeleted)){
            needdeleteda.add(id);
            param.put("idsone", needdeleteda);
        }else{
            param.put("ids", ids);
        }

        /**
         *   List<String> traineeIds = param.get("ids");
         *
         *         if(CollectionUtil.isNotEmpty(traineeIds)){
         *             userService.removeTrainee(traineeIds);
         *             this.remove(new LambdaQueryWrapperX<TraineeUserDO>().in(TraineeUserDO::getTraineeId,traineeIds));
         *         }
         *
         *         List<String> traineeIdsone = param.get("idsone"); //这个数组只删除学员账号
         *
         *         if(CollectionUtil.isNotEmpty(traineeIdsone)){
         *             this.remove(new LambdaQueryWrapperX<TraineeUserDO>().in(TraineeUserDO::getTraineeId,traineeIds));
         *         }
         */


        // 执行删除
        traineeMapper.deleteBatchIds(ids);

        try {
            // 删除后同步删除问卷
            evaluationResponseMapper.delete(EvaluationResponseDO::getStudentId, id);
            evaluationDetailMapper.delete(EvaluationDetailDO::getStudentId, id);

            //删除对应考勤
            clockInInfoService.deleteClockInInfoByTraineeId(id);

            //删除对应请假数据
            traineeLeaveService.deleteLeaveInfoByTraineeId(id);

            //删除对应选修课数据
            electiveTraineeSelectionService.deleteElectiveInfoByTraineeId(id);

            //删除点名签到数据
            rollcallRecordService.deleteRollCallRecordByTraineeId(id);

            sendTraineeToMid(JSONUtil.toJsonStr(param), DEL_TRAINEE_URI);
        } catch (Exception e) {
            log.info("同步删除失败：{}",e.getMessage());
        }

        return true;
    }


    @Override
    public TraineeDO getTrainById(Long id) {
        TraineeDO trainee = traineeMapper.selectById(id);
        String position = trainee.getPosition();

        String phone = trainee.getPhone();

        String cardNo = trainee.getCardNo();

        if (position != null) {
            String encryptedPosition = desensitizeEncrypt(position);
            trainee.setPosition(encryptedPosition);
        }

        if (phone != null) {
            String encryptedPhone = desensitizeEncrypt(phone);
            trainee.setPhone(encryptedPhone);
        }

        if (cardNo != null) {
            String encryptedCardNo = desensitizeEncrypt(cardNo);
            trainee.setCardNo(encryptedCardNo);
        }
        return trainee;
    }

    @Override
    public Boolean unshackleTrainee(List<Long> classIds) {
        // todo 校验班级是否存在

        // todo 解绑学员
        log.info("正在解绑学员...");
        List<Long> ids = traineeMapper.getTraineeIds(classIds);

        if (!ids.isEmpty()) {
            log.info("解绑成功...");

            // 删除教务学员信息
            removeJWUser(ids);
            Map<String, Object> param = new HashMap<>();
            param.put("ids", ids);
            sendTraineeToMid(JSONUtil.toJsonStr(param), DEL_TRAINEE_URI);

            traineeMapper.deleteBatchIds(ids);
            return true;
        } else {
            log.info("解绑失败...");
            return false;
        }

    }

    @Override
    public List<ReportPageRespVO> getreportList(ReportPageReqVO reqVO) {
        if (Objects.isNull(reqVO.getReportStatus())) {
            return traineeMapper.getreportList(reqVO);
        } else {
            if (StrUtil.isNotBlank(reqVO.getReportDateBeg())) {
                reqVO.setReportDateEnd(reqVO.getReportDateBeg() + " 23:59:59");
                reqVO.setReportDateBeg(reqVO.getReportDateBeg() + " 00:00:00");
            }
            return traineeMapper.getReportInfoListByStatus(reqVO);
        }
    }

    private void checkExist(List<Long> ids) {
        List<TraineeDO> traineeDOS = traineeMapper.selectList(Wrappers.<TraineeDO>lambdaQuery().in(TraineeDO::getId, ids));
        if (traineeDOS.size() != ids.size()) {
            throw exception(TRAINEE_NOT_EXISTS);
        }
    }

//    private void checkUnique(TraineeBaseVO reqVO) {
//
//        //校验身份证是否重复
//        List<TraineeDO> traineeDOList = traineeMapper.getTraineeByCardNoAndClassId(reqVO);
//
//        if (CollUtil.isNotEmpty(traineeDOList)) {
//            Long currentClassId = reqVO.getClassId();
//            List<Long> classIds = traineeDOList.stream()
//                    .map(TraineeDO::getClassId)
//                    .distinct()
//                    .collect(Collectors.toList());
//
//            if (CollUtil.isNotEmpty(classIds)) {
//                List<ClassManagementDO> existingClasses = classManagementMapper.selectBatchIds(classIds);
//                ClassManagementDO currentClass = classManagementMapper.selectById(currentClassId);
//                LocalDateTime currentStart = currentClass.getClassOpenTime();
//                LocalDateTime currentEnd = currentClass.getCompletionTime();
//
//                for (ClassManagementDO existingClass : existingClasses) {
//                    LocalDateTime existingStart = existingClass.getClassOpenTime();
//                    LocalDateTime existingEnd = existingClass.getCompletionTime();
//
//                    // 严格判断时间段是否相交（包括端点相等）
//                    boolean isOverlap =
//                            (currentStart.isBefore(existingEnd) || currentStart.isEqual(existingEnd)) &&
//                                    (existingStart.isBefore(currentEnd) || existingStart.isEqual(currentEnd));
//
//                    if (isOverlap) {
//                        throw exception(TRAINEE_NOT_UNIQUE, currentClass.getClassName());
//                    }
//                }
//            }
//            // 其他逻辑...
//        }
//
//
////        if (CollUtil.isNotEmpty(traineeDOList)) {
////            List<Long> classIds = traineeDOList.stream().map(TraineeDO::getClassId).distinct().collect(Collectors.toList());
////            List<ClassManagementDO> list = classManagementMapper.selectBatchIds(classIds);
////
////            ClassManagementDO classDO = classManagementMapper.selectById(reqVO.getClassId());
////
////            //判断classDO和list中的班级开班时间是否重叠
////
////
////
////            throw exception(TRAINEE_NOT_UNIQUE, classDO.getClassName());
////        }
//
//        //校验手机号是否重复
//        List<TraineeDO> traineeDOList1 = traineeMapper.getTraineeByPhoneAndClassId(reqVO);
//
//        if (CollUtil.isNotEmpty(traineeDOList1)) {
//            Long currentClassId = reqVO.getClassId();
//            List<Long> classIds = traineeDOList1.stream()
//                    .map(TraineeDO::getClassId)
//                    .distinct()
//                    .collect(Collectors.toList());
//
//            if (CollUtil.isNotEmpty(classIds)) {
//                List<ClassManagementDO> existingClasses = classManagementMapper.selectBatchIds(classIds);
//                ClassManagementDO currentClass = classManagementMapper.selectById(currentClassId);
//                LocalDateTime currentStart = currentClass.getClassOpenTime();
//                LocalDateTime currentEnd = currentClass.getCompletionTime();
//
//                for (ClassManagementDO existingClass : existingClasses) {
//                    LocalDateTime existingStart = existingClass.getClassOpenTime();
//                    LocalDateTime existingEnd = existingClass.getCompletionTime();
//
//                    // 严格判断时间段是否相交（包括端点相等）
//                    boolean isOverlap =
//                            (currentStart.isBefore(existingEnd) || currentStart.isEqual(existingEnd)) &&
//                                    (existingStart.isBefore(currentEnd) || existingStart.isEqual(currentEnd));
//
//                    if (isOverlap) {
//                        throw exception(TRAINEE_PHONE_NOT_UNIQUE);
//                    }
//                }
//            }
//            // 其他逻辑...
//        }
//
////        if (traineeDOList1 != null) {
////            throw exception(TRAINEE_PHONE_NOT_UNIQUE);
////        }
//    }




    //-------------------------------------------//
//    private void checkUnique(TraineeBaseVO reqVO) {
//        Long currentClassId = reqVO.getClassId();
//        ClassManagementDO currentClass = classManagementMapper.selectById(currentClassId);
//
//        // 校验身份证是否重复
//        validateUnique(reqVO,
//                () -> traineeMapper.getTraineeByCardNoAndClassId(reqVO),
//                () -> exception(TRAINEE_NOT_UNIQUE, currentClass.getClassName()),
//                currentClass);
//
//        // 校验手机号是否重复
//        validateUnique(reqVO,
//                () -> traineeMapper.getTraineeByPhoneAndClassId(reqVO),
//                () -> exception(TRAINEE_PHONE_NOT_UNIQUE),
//                currentClass);
//    }
//
//    /**
//     * 通用校验方法（严格按照用户的异常格式抛出）
//     * @param reqVO         请求参数
//     * @param queryFunction 查询方法（lambda 形式）
//     * @param exceptionFunc 异常抛出方法（lambda 形式，保证格式正确）
//     * @param currentClass  当前班级信息
//     */
//    private void validateUnique(TraineeBaseVO reqVO,
//                                Supplier<List<TraineeDO>> queryFunction,
//                                Supplier<RuntimeException> exceptionFunc,
//                                ClassManagementDO currentClass) {
//
//        List<TraineeDO> traineeDOList = queryFunction.get();
//
//        if (CollUtil.isEmpty(traineeDOList)) {
//            return;
//        }
//
//        List<Long> classIds = traineeDOList.stream()
//                .map(TraineeDO::getClassId)
//                .distinct()
//                .collect(Collectors.toList());
//
//        if (CollUtil.isEmpty(classIds)) {
//            return;
//        }
//
//        List<ClassManagementDO> existingClasses = classManagementMapper.selectBatchIds(classIds);
//        LocalDateTime currentStart = currentClass.getClassOpenTime();
//        LocalDateTime currentEnd = currentClass.getCompletionTime();
//
//        boolean isOverlap = existingClasses.stream().anyMatch(existingClass -> {
//            LocalDateTime existingStart = existingClass.getClassOpenTime();
//            LocalDateTime existingEnd = existingClass.getCompletionTime();
//            return (currentStart.isBefore(existingEnd) || currentStart.isEqual(existingEnd)) &&
//                    (existingStart.isBefore(currentEnd) || existingStart.isEqual(currentEnd));
//        });
//
//        if (isOverlap) {
//            throw exceptionFunc.get(); // 严格按照你的格式抛出异常
//        }
//    }

    private void checkUnique(TraineeBaseVO reqVO) {
        // 校验身份证是否重复
        checkDuplicate(reqVO, true);
        // 校验手机号是否重复
        checkDuplicate(reqVO, false);
    }

    private void checkDuplicate(TraineeBaseVO reqVO, boolean checkCardNo) {
        List<TraineeDO> traineeDOList = checkCardNo
                ? traineeMapper.getTraineeByCardNoAndClassId(reqVO)
                : traineeMapper.getTraineeByPhoneAndClassId(reqVO);

        if (CollUtil.isEmpty(traineeDOList)) {
            return;
        }

        Long currentClassId = reqVO.getClassId();
        List<Long> classIds = traineeDOList.stream()
                .map(TraineeDO::getClassId)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(classIds)) {
            List<ClassManagementDO> existingClasses = classManagementMapper.selectBatchIds(classIds);
            ClassManagementDO currentClass = classManagementMapper.selectById(currentClassId);

            LocalDateTime currentStart = currentClass.getReportingTime();
            LocalDateTime currentEnd = currentClass.getCompletionTime();

            for (ClassManagementDO existingClass : existingClasses) {
                LocalDateTime existingStart = existingClass.getReportingTime();
                LocalDateTime existingEnd = existingClass.getCompletionTime();

                // 严格判断时间段是否相交（包括端点相等）
                boolean isOverlap =
                        (currentStart.isBefore(existingEnd) || currentStart.isEqual(existingEnd)) &&
                                (existingStart.isBefore(currentEnd) || existingStart.isEqual(currentEnd));

                if (isOverlap) {
                    if (checkCardNo) {
                        throw exception(TRAINEE_NOT_UNIQUE, existingClass.getClassName());
                    } else {
                        throw exception(TRAINEE_PHONE_NOT_UNIQUE);
                    }
                }
            }
        }
    }


    private String checkImportUnique(TraineeBaseVO reqVO, List<String> idCardList) {
        return checkImportDuplicate(reqVO, idCardList, true);
    }

    private String checkImportPhoneUnique(TraineeBaseVO reqVO, List<String> phoneList) {
        return checkImportDuplicate(reqVO, phoneList, false);
    }

    private String checkImportDuplicate(TraineeBaseVO reqVO, List<String> existingList, boolean checkCardNo) {

        // 检查是否在导入列表中重复
        if (existingList.contains(checkCardNo ? reqVO.getCardNo() : reqVO.getPhone())) {
            if (checkCardNo) {
                ClassManagementDO classDO = classManagementMapper.selectById(reqVO.getClassId());
                return "已报名" + classDO.getClassName() + "班次，请移步该班报到";
            } else {
                return "手机号重复";
            }
        }

        List<TraineeDO> traineeDOList = checkCardNo
                ? traineeMapper.getTraineeByCardNoAndClassId(reqVO)
                : traineeMapper.getTraineeByPhoneAndClassId(reqVO);

        if (CollUtil.isNotEmpty(traineeDOList)) {
            Long currentClassId = reqVO.getClassId();
            List<Long> classIds = traineeDOList.stream()
                    .map(TraineeDO::getClassId)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(classIds)) {
                List<ClassManagementDO> existingClasses = classManagementMapper.selectBatchIds(classIds);
                ClassManagementDO currentClass = classManagementMapper.selectById(currentClassId);

                LocalDateTime currentStart = currentClass.getReportingTime();
                LocalDateTime currentEnd = currentClass.getCompletionTime();

                for (ClassManagementDO existingClass : existingClasses) {
                    LocalDateTime existingStart = existingClass.getReportingTime();
                    LocalDateTime existingEnd = existingClass.getCompletionTime();

                    // 严格判断时间段是否相交（包括端点相等）
                    boolean isOverlap =
                            (currentStart.isBefore(existingEnd) || currentStart.isEqual(existingEnd)) &&
                                    (existingStart.isBefore(currentEnd) || existingStart.isEqual(currentEnd));

                    if (isOverlap) {
                        return checkCardNo
                                ? "已报名" + existingClass.getClassName() + "班次，请移步该班报到"
                                : "手机号重复";
                    }
                }
            }
        }

        return "";
    }



//    private String checkImportUnique(TraineeBaseVO reqVO, List<String> idCardList) {
//        List<TraineeDO> list = traineeMapper.getTraineeByCardNoAndClassId(reqVO);
//
//        if (CollUtil.isNotEmpty(list)) {
//            Long currentClassId = reqVO.getClassId();
//            List<Long> classIds = list.stream()
//                    .map(TraineeDO::getClassId)
//                    .distinct()
//                    .collect(Collectors.toList());
//
//            if (CollUtil.isNotEmpty(classIds)) {
//                List<ClassManagementDO> existingClasses = classManagementMapper.selectBatchIds(classIds);
//                ClassManagementDO currentClass = classManagementMapper.selectById(currentClassId);
//                LocalDateTime currentStart = currentClass.getReportingTime();
//                LocalDateTime currentEnd = currentClass.getCompletionTime();
//
//                for (ClassManagementDO existingClass : existingClasses) {
//                    LocalDateTime existingStart = existingClass.getReportingTime();
//                    LocalDateTime existingEnd = existingClass.getCompletionTime();
//
//                    // 严格判断时间段是否相交（包括端点相等）
//                    boolean isOverlap =
//                            (currentStart.isBefore(existingEnd) || currentStart.isEqual(existingEnd)) &&
//                                    (existingStart.isBefore(currentEnd) || existingStart.isEqual(currentEnd));
//
//                    if (isOverlap) {
//                        return "已报名" + existingClass.getClassName() + "班次，请移步该班报到";
//                    }
//                }
//            }
//        }
//        if (idCardList.contains(reqVO.getCardNo())) {
//            ClassManagementDO classDO = classManagementMapper.selectById(reqVO.getClassId());
//            return "已报名" + classDO.getClassName() + "班次，请移步该班报到";
//        }
//
////        TraineeDO traineeDO = traineeMapper.getTraineeByCardNoAndClassId(reqVO);
////        if (traineeDO != null) {
////            ClassManagementDO classDO = classManagementMapper.selectById(traineeDO.getClassId());
////
////            return "已报名" + classDO.getClassName() + "班次，请移步该班报到";
////        } else if (idCardList.contains(reqVO.getCardNo())) {
////            ClassManagementDO classDO = classManagementMapper.selectById(reqVO.getClassId());
////
////            return "已报名" + classDO.getClassName() + "班次，请移步该班报到";
////
////        } else {
////            return "";
////        }
//
//        return "";
//    }
//
//    private String checkImportPhoneUnique(TraineeBaseVO reqVO, List<String> phoneList) {
//        List<TraineeDO> list = traineeMapper.getTraineeByPhoneAndClassId(reqVO);
//
//        if (CollUtil.isNotEmpty(list)) {
//            Long currentClassId = reqVO.getClassId();
//            List<Long> classIds = list.stream()
//                    .map(TraineeDO::getClassId)
//                    .distinct()
//                    .collect(Collectors.toList());
//
//            if (CollUtil.isNotEmpty(classIds)) {
//                List<ClassManagementDO> existingClasses = classManagementMapper.selectBatchIds(classIds);
//                ClassManagementDO currentClass = classManagementMapper.selectById(currentClassId);
//                LocalDateTime currentStart = currentClass.getReportingTime();
//                LocalDateTime currentEnd = currentClass.getCompletionTime();
//
//                for (ClassManagementDO existingClass : existingClasses) {
//                    LocalDateTime existingStart = existingClass.getReportingTime();
//                    LocalDateTime existingEnd = existingClass.getCompletionTime();
//
//                    // 严格判断时间段是否相交（包括端点相等）
//                    boolean isOverlap =
//                            (currentStart.isBefore(existingEnd) || currentStart.isEqual(existingEnd)) &&
//                                    (existingStart.isBefore(currentEnd) || existingStart.isEqual(currentEnd));
//
//                    if (isOverlap) {
//                        return "手机号重复";
//                    }
//                }
//            }
//        }
//
//        if (phoneList.contains(reqVO.getPhone())) {
//            return "手机号重复";
//        }
//
////        if (traineeDO != null) {
////            return "手机号重复";
////        } else if (phoneList.contains(reqVO.getCardNo())) {
////            return "手机号重复";
////        } else {
////            return "";
////        }
//
//        return "";
//    }


    /**
     * 创建表头样式
     *
     * @param wb
     * @return
     */
    private static XSSFCellStyle createHeadCellStyle(XSSFWorkbook wb) {
        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setWrapText(true);// 设置自动换行
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());// 背景颜色
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.index);
        cellStyle.setBorderBottom(BorderStyle.THIN); // 下边框
        cellStyle.setBorderLeft(BorderStyle.THIN); // 左边框
        cellStyle.setBorderRight(BorderStyle.THIN); // 右边框
        cellStyle.setBorderTop(BorderStyle.THIN); // 上边框


        XSSFFont headerFont = (XSSFFont) wb.createFont(); // 创建字体样式
        headerFont.setBold(true); // 字体加粗
        headerFont.setFontName("黑体"); // 设置字体类型
        headerFont.setFontHeightInPoints((short) 12); // 设置字体大小
        cellStyle.setFont(headerFont); // 为标题样式设置字体样式

        return cellStyle;
    }

    /**
     * 创建内容样式
     *
     * @param wb
     * @return
     */
    private static XSSFCellStyle createContentCellStyle(XSSFWorkbook wb) {
        XSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);// 水平居中
        cellStyle.setWrapText(true);// 设置自动换行
        cellStyle.setBorderBottom(BorderStyle.THIN); // 下边框
        cellStyle.setBorderLeft(BorderStyle.THIN); // 左边框
        cellStyle.setBorderRight(BorderStyle.THIN); // 右边框
        cellStyle.setBorderTop(BorderStyle.THIN); // 上边框

        // 生成12号字体
        XSSFFont font = wb.createFont();
        font.setColor((short) 8);
        font.setFontHeightInPoints((short) 12);
        cellStyle.setFont(font);

        return cellStyle;
    }

    /**
     * 同步请求业中
     * @param traineeJsonStr 学员信息
     * @param uri  业中接口地址
     * @return 返回消息
     */
    public String sendTraineeToMidSync(String traineeJsonStr, String uri) {
        log.info("开始向业中传递学员信息");
//        String token = businessCenterApi.getTokenOfBusinessCenter(YezhongUtil.getTokenFromRequestHead()).getCheckedData();
        HttpRequest request = HttpUtil.createPost(MID_BASE_URI + uri)
//                .header("Authorization","Bearer "+token)
                .body(traineeJsonStr);
        HttpResponse response = null;
        String errorMsg = "";
        try {
            response = request.execute();
            log.info(response.body());
        } catch (Exception e) {
            errorMsg = e.getMessage();
            log.info("sendTraineeToMidWrong:" + e.getMessage());
        }
        if (StrUtil.isNotBlank(errorMsg)) {
            return errorMsg + "-" + traineeJsonStr;
        }
        return response.body();
    }

    /**
     * 调用业中接口发送学员增删改信息到业中
     *
     * @param traineeJsonStr 学员信息
     * @param uri            业中接口地址
     */
    @Async
    public String sendTraineeToMid(String traineeJsonStr, String uri) {
        log.info("开始向业中传递学员信息");
//        String token = businessCenterApi.getTokenOfBusinessCenter(YezhongUtil.getTokenFromRequestHead()).getCheckedData();
        HttpRequest request = HttpUtil.createPost(MID_BASE_URI + uri)
//                .header("Authorization","Bearer "+token)
                .body(traineeJsonStr);
        HttpResponse response = null;
        String errorMsg = "";
        try {
            response = request.execute();
            log.info(response.body());
        } catch (Exception e) {
            errorMsg = e.getMessage();
            log.info("sendTraineeToMidWrong:" + e.getMessage());
        }
        if (StrUtil.isNotBlank(errorMsg)) {
            return errorMsg + "-" + traineeJsonStr;
        }
        String result = response.body();
        YezhongResult res = JSONUtil.toBean(result, YezhongResult.class);
        return res.getData();
    }

    @Override
    public void sendAllTraineeInfo(String url) {
        List<TraineeDO> list = traineeMapper.selectList();
        String resp = HttpUtil.post(url + SYNC_TRAINEES_URI, JSONUtil.toJsonStr(list));
        log.info("全量发送学员数据完毕-------" + resp);
    }

    @Override
    public List<UnitRegistrationPageRespVO> getAllTraineeInfo(TraineeInfoReqVO reqVO) {
        return traineeMapper.getAllTraineeInfo(reqVO);
    }

    @Override
    public Boolean checkInByIds(List<Long> ids) {
        List<TraineeDO> traineeDOList = new ArrayList<>();
        ids.forEach(id -> {
            TraineeDO traineeDO = new TraineeDO();
            traineeDO.setId(id);
            traineeDO.setStatus(TraineeStatusEnum.REPORTED.getStatus());
            traineeDO.setReportTime(LocalDateTime.now());
            traineeDOList.add(traineeDO);
        });
        this.updateBatchById(traineeDOList);

        try {
            // 刷新签到表,立即提交任务，不等待结果
            CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                    .exceptionally(e -> {
                        log.error(e.getMessage(), e);
                        return null;
                    });
        } catch (Exception e) {
            e.printStackTrace();
        }


        return true;
    }

    @Override
    @Transactional
    public Boolean traineeOrder(TraineeOrderReqVO reqVO) {

        List<Long> idList = new ArrayList<>();
        // 根据reqVO获取到两个学员的id
        Long id = reqVO.getId();
        idList.add(id);
        idList.add(reqVO.getChangeId());

        // 校验存在
        List<TraineeDO> traineeList = traineeMapper.selectList(new LambdaQueryWrapperX<TraineeDO>()
                .in(TraineeDO::getId, idList));

        if (idList.size() != traineeList.size()) {
            throw exception(TRAINEE_NOT_EXISTS);
        }

        // 交换这两个的顺序
        Integer groupSort = traineeList.get(0).getGroupSort();
        traineeList.get(0).setGroupSort(traineeList.get(1).getGroupSort());
        traineeList.get(1).setGroupSort(groupSort);

        this.updateBatchById(traineeList);
        try {
            this.setGroupOrder(traineeList.get(0).getGroupId());
        } catch (Exception e) {
            log.info("排序失败：{}",e.getMessage());
        }

        return true;
    }

    @Override
    @Transactional
    public Boolean batchSetGroup(BatchSetGroupReqVO reqVO) {

        if (reqVO.getIds().isEmpty()) {
            return false;
        }
        // 查出所有学员
        List<TraineeDO> traineeList = traineeMapper.selectList(new LambdaQueryWrapperX<TraineeDO>()
                .in(TraineeDO::getId, reqVO.getIds()));

        AtomicInteger sort = new AtomicInteger(traineeMapper.getMaxGroupSort(reqVO.getGroupId()));

        traineeList.forEach(traineeDO -> {
            traineeDO.setGroupId(reqVO.getGroupId());
            int currentSort = sort.getAndIncrement();
            traineeDO.setGroupSort(currentSort + 1); // 如果需要 sort+1，则这样设置
            // 注意：这里实际上设置的是 sort 的原始值加1后的结果，因为 getAndIncrement 会返回增加前的值
        });

        return this.updateBatchById(traineeList);

    }

    @Override
    public Boolean setGroupOrCommittee(SetGroupCommitteeReqVO reqVO) {

        if (reqVO.getClassCommitteeId() == null) {
            reqVO.setClassCommitteeId(0L);
        }

        if (reqVO.getGroupId() == null) {
            reqVO.setGroupId(0L);
        }

        TraineeDO traineeDO = traineeMapper.selectById(reqVO.getId());

        if (!Objects.equals(traineeDO.getGroupId(), reqVO.getGroupId())) {
            traineeDO.setGroupId(reqVO.getGroupId());
            // 获取最大排序
            Integer sort = traineeMapper.getMaxGroupSort(reqVO.getGroupId());
            traineeDO.setGroupSort(sort + 1);
        }

        if ((!Objects.equals(traineeDO.getClassCommitteeId(), reqVO.getClassCommitteeId()))) {
            traineeDO.setClassCommitteeId(reqVO.getClassCommitteeId());
        }

        return this.updateById(traineeDO);

    }

    /**
     * 检查学员权限并禁止访问
     */
    @Override
    public void checkTrainPermissionAndAccessDenied(HttpServletRequest request, Long userId) {

        // 判断是否是管理员
        if (isAdmin(request, adminUserApi.getUser(userId).getData().getSystemId())) {
            return;
        }

        // 判断老师身份允许访问
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectByUserId(userId);
        if (Objects.isNull(teacherInformationDO)) {
            throw exception(ACCESS_DENIED);
        }
        // 取消判断学员身份并禁止访问
//        TraineeDO traineeDO = traineeMapper.selectByUserId(userId);
//        if (Objects.nonNull(traineeDO)){
//            throw exception(ACCESS_DENIED);
//        }
    }

    /**
     * 学员小程序首页获取学员信息
     *
     * @param phone 学员手机号
     * @return 学员类型和所在班级
     */
    @Override
    public Map<String, Object> getTraineeType(String phone) {
        TraineeDO traineeDO = traineeMapper.selectOne(new LambdaQueryWrapperX<TraineeDO>()
                .eq(TraineeDO::getPhone, phone)
                .orderByDesc(TraineeDO::getCreateTime)
                .last("limit 1"));
        Map<String, Object> map = new HashMap<>();
        map.put("traineeType", getTraineeStatus(traineeDO));
        if (Objects.isNull(traineeDO)) {
            map.put("classId", null);
            map.put("className", null);
        } else {
            map.put("classId", traineeDO.getClassId());
            ClassManagementDO classDO = classManagementMapper.selectById(traineeDO.getClassId());
            map.put("className", Objects.isNull(classDO) ? null : classDO.getClassName());
        }
        return map;
    }

    @Override
    public Map<String, Object> getTraineeTypebyTraineeId(String traineeId) {
        TraineeDO traineeDO = traineeMapper.selectById(Long.valueOf(traineeId));
        Map<String, Object> map = new HashMap<>();
        map.put("traineeType", getTraineeStatus(traineeDO));
        if (Objects.isNull(traineeDO)) {
            map.put("classId", null);
            map.put("className", null);
            log.info("学员小程序获取学员信息-{}学员不存在！", traineeId);
        } else {
            log.info("学员小程序获取学员信息-学员信息获取成功！{}", traineeDO);
            map.put("classId", traineeDO.getClassId());
            ClassManagementDO classDO = classManagementMapper.selectById(traineeDO.getClassId());
            map.put("className", Objects.isNull(classDO) ? null : classDO.getClassName());

            Long userId = traineeDO.getUserId();
            if (Objects.isNull(userId)) {
                log.info("学员小程序获取学员信息-学员{}用户不存在！", traineeId);
            } else {
                try{
                    // 设置users表employee_id为当前的登录的学员
                    traineeMapper.setLoginTrainee(userId, traineeId);
                    // 缓存登录学员身份
                    traineeLoginTypeRedisDAO.set(userId, traineeDO);
                }catch (Exception e){
                    log.info("学员小程序获取学员信息-设置users表employee_id当前登录学员{}失败！", traineeId,e);
                }
            }

        }



        return map;
    }

    /**
     * 获取某个班级 总人数、报名已确认、报名未确认人数信息
     *
     * @param classId 班级id
     * @return ECContentDTO
     */
    @Override
    public ECContentDTO getECContent(Long classId) {
        // 获取班级已报名、已报到、已结业的所有学员
        List<TraineeDO> list = traineeMapper.selectListByClassId(classId);
        return getECContentByList(list);
    }

    /**
     * 获取未确认人数、已确认人数、总人数
     *
     * @param list 待统计的学员集合
     * @return 未确认人数、已确认人数、总人数
     */
    public static ECContentDTO getECContentByList(List<TraineeDO> list) {
        // 获取所有已报名状态学员
        List<TraineeDO> unconfirmedList = list.stream()
                .filter(item -> TraineeStatusEnum.REGISTERED.getStatus().equals(item.getStatus()))
                .collect(Collectors.toList());
        // 总班级人数
        int totalClassSize = list.size();
        // 已确认人数
        Integer confirmedNumber = totalClassSize - unconfirmedList.size();
        return new ECContentDTO(unconfirmedList.size(), confirmedNumber, totalClassSize);
    }

    /**
     * 获取学员类型
     *
     * @param traineeDO 学员信息
     * @return 学员类型
     */
    private Integer getTraineeStatus(@Nullable TraineeDO traineeDO) {
        /***
         * 1-------已报名，未报到-弹窗联系班主任
         * 2-------正常访问
         * 3-------未报名/结业超过7天/退学-学员不存在
         */
        if (Objects.isNull(traineeDO)) {
            return 3;
        }
        if (Objects.equals(traineeDO.getStatus(), TraineeStatusEnum.REGISTERED.getStatus())) {
            return 1;
        }
        if (Objects.equals(traineeDO.getStatus(), TraineeStatusEnum.REPORTED.getStatus())) {
            return 2;
        }
        if (Objects.equals(traineeDO.getStatus(), TraineeStatusEnum.DROPPED_OUT.getStatus())) {
            return 3;
        }
        if (Objects.equals(traineeDO.getStatus(), TraineeStatusEnum.GRADUATED.getStatus())
                && Objects.nonNull(traineeDO.getGraduateDate())) {
            long between = ChronoUnit.DAYS.between(traineeDO.getGraduateDate(), LocalDate.now());
            return between > 7 ? 3 : 2;
        }
        // 其他状态表示数据有误
        return 3;
    }

    @Override
    public TraineeByCardNoVO getTrainByCardNo(String cardNo,Long currentClassId) {
        // 获取正在开班中的班级
        List<Long> classIdList = baseMapper.getClassIdSameTime(1L);
        if (CollUtil.isEmpty(classIdList)) {
            return null;
        }
        // 加密身份证进行数据匹配
        cardNo = SM4Util.encrypt(cardNo);
        List<TraineeDO> traineeDOList = this.lambdaQuery()
                .eq(TraineeDO::getCardNo, cardNo)
                .in(TraineeDO::getClassId, classIdList)
                .list();
        if (traineeDOList.isEmpty()) {
            return null;
        }

        List<TraineeDO> traineeList = new ArrayList<>();
        if (CollUtil.isNotEmpty(traineeDOList)) {
            List<Long> classIds = traineeDOList.stream()
                    .map(TraineeDO::getClassId)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(classIds)) {
                List<ClassManagementDO> existingClasses = classManagementMapper.selectBatchIds(classIds);
                ClassManagementDO currentClass = classManagementMapper.selectById(currentClassId);
                LocalDateTime currentStart = currentClass.getReportingTime();
                LocalDateTime currentEnd = currentClass.getCompletionTime();

                // 收集所有时间重叠的班级ID
                List<Long> overlappingClassIds = new ArrayList<>();
                for (ClassManagementDO existingClass : existingClasses) {
                    LocalDateTime existingStart = existingClass.getReportingTime();
                    LocalDateTime existingEnd = existingClass.getCompletionTime();

                    boolean isOverlap = (currentStart.isBefore(existingEnd) || currentStart.isEqual(existingEnd)) &&
                            (existingStart.isBefore(currentEnd) || existingStart.isEqual(currentEnd));
                    if (isOverlap) {
                        overlappingClassIds.add(existingClass.getId());
                    }
                }

                // 找到第一个匹配的学员并返回
                if (CollUtil.isNotEmpty(overlappingClassIds)) {
                    for (TraineeDO trainee : traineeDOList) {
                        if (overlappingClassIds.contains(trainee.getClassId())) {
                            traineeList.add(trainee);
                            break;
                        }
                    }
                }
            }
            // 其他逻辑...
        }

        if (traineeList.isEmpty()){
            return null;
        }

        TraineeDO traineeDO = traineeList.get(0);

        // 加密处理
        String position = traineeDO.getPosition();
        String phone = traineeDO.getPhone();
        String idCardNo = traineeDO.getCardNo();

        if (position != null) {
            String encryptedPosition = desensitizeEncrypt(position);
            traineeDO.setPosition(encryptedPosition);
        }

        if (phone != null) {
            String encryptedPhone = desensitizeEncrypt(phone);
            traineeDO.setPhone(encryptedPhone);
        }

        if (idCardNo != null) {
            String encryptedCardNo = desensitizeEncrypt(idCardNo);
            traineeDO.setCardNo(encryptedCardNo);
        }

        TraineeByCardNoVO respVO = TraineeConvert.INSTANCE.convertToTraineeByCardNoVO(traineeDO);
        try {
            ClassManagementDO classManagementDO = classManagementMapper.selectById(respVO.getClassId());
            if (classManagementDO != null) {
                respVO.setClassName(classManagementDO.getClassName());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return respVO;
    }

    @Override
    @Transactional
    public TraineeImportRespVO importInfo(List<TraineeInfoImportStrExcelVO> list, Long classId) {

        int num;
        if ("示例：1（示例请勿删除）".equals(list.get(0).getIndex())) {
            list.remove(0);
            num = 3;
        } else {
            num = 2;
        }

        List<TraineeDO> importList = TraineeConvert.INSTANCE.convertImportList(list);

        if (CollUtil.isEmpty(importList)){
            return TraineeImportRespVO.builder()
                    .count(0)
                    .tag(1)
                    .build();
        }

        // 用于存储错误信息
        List<String> errorMessages = new ArrayList<>();

        // 用于存储校验通过和校验失败的数据
        List<TraineeDO> rightData = new ArrayList<>();

        // 用于存储表格中重复身份证信息
        List<String> idCardList = new ArrayList<>();
        List<String> phoneList = new ArrayList<>();

        // 获取民族
        Map<String, Map<String, Long>> nationMap = baseMapper.getNation();


        Map<String, Map<String, Integer>> unitMap = baseMapper.getUnitByClassId(classId);
        Map<String, Map<String, Integer>> normalizedUnitMap = new HashMap<>();
        unitMap.forEach((key, value) -> {
            if (key != null) {
                normalizedUnitMap.put(key.trim(), value); // 去除 key 的换行符和空格
            }
        });

        Map<String, Map<String, Integer>> unitClassificationMap = baseMapper.getUnitClassificationByClassId(classId);
        Map<String, Map<String, Integer>> normalizedUnitClassificationMap = new HashMap<>();
        unitClassificationMap.forEach((key, value) -> {
            if (key != null) {
                normalizedUnitClassificationMap.put(key.trim(), value); // 去除 key 的换行符和空格
            }
        });


        Map<String, Map<String, Long>> educationalLevel = traineeDictConvertService.getDictDateMap(TraineeDictTypeEnum.EDUCATIONAL_LEVEL.getType());
        Map<String, Map<String, Long>> personRank = traineeDictConvertService.getDictDateMap(TraineeDictTypeEnum.JOB_LEVEL.getType());
        Map<String, Map<String, Long>> politicalIdentity = traineeDictConvertService.getDictDateMap(TraineeDictTypeEnum.POLITICAL_IDENTITY.getType());

//        List<String> sexList = new ArrayList<>();
//        sexList.add("男");
//        sexList.add("女");
        ClassManagementDO classDO = classManagementMapper.selectById(classId);
        // 判断该班级是否还有名额
//        Long count = traineeMapper.getActualPeopleNumber(classId,3);


        // 判断该单位是否还有名额
//        Map<String,Map<String,Integer>> unitCountMap = traineeMapper.getUnitCountByClassId(classId);


//        Map<String,Long> map = new HashMap<>();
        for (int i = 0; i < importList.size(); i++) {
            TraineeInfoImportStrExcelVO traineeDO = list.get(i);

            List<String> currentErrors = new ArrayList<>(); // 当前数据的错误信息列表

            // 学员名字
            if (StringUtils.isNotBlank(traineeDO.getName())) {
                // 姓名只包含中英文
//                boolean containsOnlyChineseAndEnglish = traineeDO.getName().matches("^[\\u4e00-\\u9fa5a-zA-Z]+$");
//                if (!containsOnlyChineseAndEnglish) {
//                    currentErrors.add("第 " + (i + num) + " 行: 学员名字只能包含中文和英文");
//                }
//
//                if (traineeDO.getName().length() > 50) {
//                    currentErrors.add("第 " + (i + num) + " 行: 学员姓名长度超出限制");
//                }

            } else {
                currentErrors.add("第 " + (i + num) + " 行: 学员名字不能为空");
            }

            if (StringUtils.isNotBlank(traineeDO.getPosition())) {

                if (traineeDO.getPosition().length() > 200) {
                    currentErrors.add("第 " + (i + num) + " 行: 职务长度超出限制");
                }

            }

            if (StringUtils.isNotBlank(traineeDO.getEducationalLevelName())) {
                if (educationalLevel.get(traineeDO.getEducationalLevelName()) == null){
                    currentErrors.add("第 " + (i + num) + " 行: 文化程度数据不正确");
                }
            }

//            if (StringUtils.isNotBlank(traineeDO.getSex())) {
//                if (!sexList.contains(traineeDO.getSex())){
//                    currentErrors.add("第 " + (i + 3) + " 行: 性别数据不正确");
//                }
//            }

            if (StringUtils.isNotBlank(traineeDO.getPoliticalIdentityName())) {
                if (politicalIdentity.get(traineeDO.getPoliticalIdentityName()) == null){
                    currentErrors.add("第 " + (i + num) + " 行: 政治面貌数据不正确");
                }
            }

            if (StringUtils.isNotBlank(traineeDO.getEthnicName())) {
                if (nationMap.get(traineeDO.getEthnicName()) == null){
                    currentErrors.add("第 " + (i + num) + " 行: 干部民族数据不正确");
                }
            }

            if (StringUtils.isNotBlank(traineeDO.getJobLevelName())) {
                if (personRank.get(traineeDO.getJobLevelName()) == null){
                    currentErrors.add("第 " + (i + num) + " 行: 职级数据不正确");
                }
            }

            if (StringUtils.isNotBlank(traineeDO.getUnitName())) {
                if (normalizedUnitMap.get(traineeDO.getUnitName()) == null){
                    currentErrors.add("第 " + (i + num) + " 行: 学员单位数据不正确");
                }
            }


            // 身份证校验
            if (StringUtils.isNotBlank(traineeDO.getCardNo())) {
                TraineeBaseVO req = new TraineeBaseVO();
                req.setCardNo(traineeDO.getCardNo());
                req.setClassId(classId);

                if (!CheckIdCard.isValidBirthDate(traineeDO.getCardNo())) {
                    currentErrors.add("第 " + (i + num) + " 行: 学员身份证格式不对");
                } else {
                    String birthDate = CheckIdCard.getFormattedBirthDate(traineeDO.getCardNo());
                    traineeDO.setBirth(birthDate);

                    String sex = CheckIdCard.getGenderByIdCard(traineeDO.getCardNo());
                    traineeDO.setSex(sex);

                    //身份证判重
                    String s = checkImportUnique(req, idCardList);
                    if (StringUtils.isNotBlank(s)) {
                        currentErrors.add("第 " + (i + num) + " 行: 学员" + s);
                    }
                }
            } else {
                currentErrors.add("第 " + (i + num) + " 行: 学员身份证不能为空");
            }

            // 手机号
            if (StringUtils.isNotBlank(traineeDO.getPhone())) {
                boolean validPhoneNumber = isValidPhoneNumber(traineeDO.getPhone());
                if (!validPhoneNumber) {
                    currentErrors.add("第 " + (i + num) + " 行: 学员手机号格式不对");
                }
                TraineeBaseVO req = new TraineeBaseVO();
                req.setPhone(traineeDO.getPhone());
                req.setClassId(classId);
                //手机号判重
                String s = checkImportPhoneUnique(req, phoneList);
                if (StringUtils.isNotBlank(s)) {
                    currentErrors.add("第 " + (i + num) + " 行: 学员" + s);
                }
            } else {
                currentErrors.add("第 " + (i + num) + " 行: 学员手机号不能为空");
            }


            if (!currentErrors.isEmpty()) {
                // 如果当前行有错误，添加到错误信息列表
                errorMessages.addAll(currentErrors);
            } else {
                TraineeDO item = importList.get(i);

                if (StrUtil.isNotBlank(traineeDO.getEducationalLevelName())){
                    item.setEducationalLevel(educationalLevel.get(traineeDO.getEducationalLevelName()).get("id").intValue());
                }


                if (StrUtil.isNotBlank(traineeDO.getJobLevelName())){
                    item.setJobLevel(personRank.get(traineeDO.getJobLevelName()).get("id").intValue());
                }


                if (StrUtil.isNotBlank(traineeDO.getPoliticalIdentityName())){
                    item.setPoliticalIdentity(politicalIdentity.get(traineeDO.getPoliticalIdentityName()).get("id").intValue());
                }

                if (StrUtil.isNotBlank(traineeDO.getEthnicName())){
                    Long ethnic = nationMap.get(traineeDO.getEthnicName()).get("id");
                    item.setEthnic(ethnic.intValue());
                }


                item.setStatus(TraineeStatusEnum.REGISTERED.getStatus());

                item.setCardNo(traineeDO.getCardNo());
                // 时间字符串转LocalDate yyyy/MM/hh
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/M/d");
                item.setBirthday(LocalDate.parse(traineeDO.getBirth(), formatter));

                item.setClassId(classId);

                item.setTenantId(SecurityFrameworkUtils.getTenantId().intValue());

                if (StringUtils.isNotBlank(traineeDO.getUnitName())) {
                    Integer unitId = normalizedUnitMap.get(traineeDO.getUnitName()).get("id");
                    Integer unitClassification = normalizedUnitClassificationMap.get(traineeDO.getUnitName()).get("id");
                    item.setUnitId(Long.valueOf(unitId));

                    if (Objects.nonNull(unitClassification)) {
                        item.setUnitClassification(Long.valueOf(unitClassification));
                    }
                }

                if (StringUtils.isNotBlank(traineeDO.getSex())){
                    item.setSex(traineeDO.getSex());
                }

                // 处理最近参训日期
                if (StringUtils.isNotBlank(traineeDO.getLastTrainingTime())) {
                    try {
                        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        item.setLastTrainingTime(LocalDate.parse(traineeDO.getLastTrainingTime(), dateFormatter));
                    } catch (Exception e) {
                        currentErrors.add("第 " + (i + num) + " 行: 最近参训日期格式不正确，请使用yyyy-MM-dd格式");
                    }
                }

                // 处理最近培训班次
                if (StringUtils.isNotBlank(traineeDO.getLastTrainingClass())) {
                    item.setLastTrainingClass(traineeDO.getLastTrainingClass());
                }

                // 处理最近参加任职资格考试日期
                if (StringUtils.isNotBlank(traineeDO.getLatestQualExamDate())) {
                    try {
                        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        item.setLatestQualExamDate(LocalDate.parse(traineeDO.getLatestQualExamDate(), dateFormatter));
                    } catch (Exception e) {
                        currentErrors.add("第 " + (i + num) + " 行: 最近参加任职资格考试日期格式不正确，请使用yyyy-MM-dd格式");
                    }
                }

                // system_users表新增用户用于单点登录
                Long userId = adminUserApi.createUser(traineeDO.getPhone(), traineeDO.getName(), classDO.getTenantId()).getCheckedData();
                item.setUserId(userId);

                idCardList.add(item.getCardNo());
                phoneList.add(item.getPhone());
                rightData.add(item);

            }

        }

        // 处理校验通过的数据：保存到数据库
        if (CollUtil.isNotEmpty(rightData)) {

            synchronizeCadreInformation(rightData);

            this.saveBatch(rightData);


            //异步通知业中 ，加快导入速度
            taskExecutor.submit(() -> {
                String returnIds = sendTraineeBatchToMid(rightData);
                log.info("批量导入学员业中返回："+returnIds);
                adminUserApi.updateSystemIdBatchById(returnIds);
            });



            if (rightData.size() == importList.size()) {
                return TraineeImportRespVO.builder()
                        .count(rightData.size())
                        .tag(1)
                        .build();
            } else {
                return TraineeImportRespVO.builder()
                        .errorMessages(errorMessages)
                        .count(rightData.size())
                        .tag(2)
                        .build();

            }
        } else {
            return TraineeImportRespVO.builder()
                    .errorMessages(errorMessages)
                    .count(rightData.size())
                    .tag(3)
                    .build();
        }

    }

    @Override
    public List<TraineeDO> getAllTraineeByClassIds(List<Long> classIdList) {
        return baseMapper.getAllTraineeByClassIds(classIdList);
    }

    @Override
    public List<TraineeDO> selectTraineeByClassCommitteeId(Long id) {
        return baseMapper.selectList(new LambdaQueryWrapper<TraineeDO>().eq(TraineeDO::getClassCommitteeId, id));
    }

    @Override
    public Boolean selectTraineeByUserId(HttpServletRequest request, Long id) {


        // 判断是否是管理员
        if (isAdmin(request, adminUserApi.getUser(id).getData().getSystemId())) {
            return true;
        }

        // 判断老师身份允许访问
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectByUserId(id);
        if (teacherInformationDO == null) {
            return false;
        }else {
            return true;
        }

//        //判断学员身份并禁止访问
//        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(TraineeDO::getUserId, id);
//        wrapper.last("limit 1");
//        TraineeDO traineeDO = baseMapper.selectOne(wrapper);
//        if (traineeDO == null) {
//            return true;
//        } else {
//            return false;
//        }

    }

    private boolean isAdmin(HttpServletRequest request, String id) {

//        if (StrUtil.isBlank(id)) {
//            return false;
//        }
//
//        // 判断是否是租户管理员
//        Long id1 = Long.valueOf(id);
//        if (tenantApi.tenantAdmin(id1).getCheckedData()) {
//            return true;
//        }
//
//        // 超管
//        List<Long> superadmin = businessCenterApi.getAdminInfo(UserRoleTypeEnum.SUPER_ADMIN.getType(), getTokenFromRequestHead(request)).getCheckedData();
//
//        //教务管理员
//        List<Long> jiaowus = businessCenterApi.getAdminInfo(UserRoleTypeEnum.JIAO_WU.getType(),getTokenFromRequestHead(request)).getCheckedData();
//
//        // 培训管理员
//        List<Long> peixuns = businessCenterApi.getAdminInfo(UserRoleTypeEnum.PEI_XUN.getType(), getTokenFromRequestHead(request)).getCheckedData();
//
//        // 班主任
//        List<Long> banzhurens = businessCenterApi.getAdminInfo(UserRoleTypeEnum.BAN_ZHU_REN.getType(), getTokenFromRequestHead(request)).getCheckedData();
//
//        List<Long> ids = new ArrayList<>(superadmin);
//        ids.addAll(jiaowus);
//        ids.addAll(peixuns);
//        ids.addAll(banzhurens);
//
//        if (ids.contains(Long.valueOf(id))) {
//            return true;
//        }

        return true;
    }

    @Override
    public List<Long> removeJWUser(List<Long> idList) {
        // 删除教务关联的user
        List<TraineeDO> traineeList = this.listByIds(idList);
        List<Long> list = traineeList.stream().map(TraineeDO::getUserId).collect(Collectors.toList());

        // 检查TraineeDO 这个学院是不是多次报名 多个身份，
        /**
         * createUserJW
         *  AdminUserDO user = getUserByMobileTenant(mobile , tennantid );
         *         if (Objects.nonNull(user)) {
         *             user.setTenantId(tennantid);
         *             user.setStatus(CommonStatusEnum.ENABLE.getStatus());
         *             userMapper.updateById(user);
         *             return user.getId();
         *         }
         */

        //创建学员 如果这个人租户下手机号存在 则直接绑定了学员 ，所有删除的时候 应该先判断是不是报了多个学员*，只有删除最后一个账号，才删除该账号

        // 检查TraineeDO 这个学院是不是多次报名 多个身份

        List<Pair<String, Integer>> traineeIdAndTenantIdPairs = traineeList.stream()
                .map(trainee -> Pair.of(trainee.getPhone(), trainee.getTenantId()))
                .collect(Collectors.toList());

        List<TraineeDO> traineeListall = traineeMapper.selectBatchByPhoneAndTenantId(traineeIdAndTenantIdPairs);


        Map<Long, List<TraineeDO>> userGroupedByUserId = traineeListall.stream()
                .collect(Collectors.groupingBy(TraineeDO::getUserId));

        // 遍历分组，判断是否仅剩最后一个账号需要删除
        List<Long> removableUserIds = new ArrayList<>();
        for (Map.Entry<Long, List<TraineeDO>> entry : userGroupedByUserId.entrySet()) {
            Long userId = entry.getKey();
            List<TraineeDO> userTraineeRecords = entry.getValue();

            // 如果当前用户只有一个学员记录，则标记为可以删除
            if (userTraineeRecords.size() == 1) {
                removableUserIds.add(userId);
            }
        }

        // 更新 `list` 为只包含只绑定了一个学员的用户ID
        list = list.stream().filter(removableUserIds::contains).collect(Collectors.toList());

        // 获取待删除的userIdList的身份
        Map<Long, List<Integer>> userRoleMap = commenService.getJwUserRoleByUserIdList(list);
        // 最终需要删除的userIdList
        List<Long> toBeDeletedUserIds = new ArrayList<>();
        list.forEach(o -> {
            List<Integer> roles = userRoleMap.getOrDefault(o, Collections.emptyList());
            // 如果该用户除了学员身份外，没有其他身份了则可删除
            if (CollUtil.isEmpty(roles) ||
                    (roles.size() == 1 && roles.contains(JwUserRoleTypeEnum.TRAINEE.getRoleType()))) {
                toBeDeletedUserIds.add(o);
            }
        });

        if (CollUtil.isEmpty(toBeDeletedUserIds)) {
            return toBeDeletedUserIds;
        }
        try {
            log.info("开始删除教务的学员账号");
            String ids = toBeDeletedUserIds.stream().map(Object::toString).collect(Collectors.joining(","));
            Boolean flag = adminUserApi.deleteUser(ids).getCheckedData();
            if (flag) {
                log.info("教务学员账号删除成功");
            } else {
                log.info("教务学员删除失败");
            }
        } catch (Exception e) {
            log.info("教务学员删除异常：{}", e.getMessage());
        }
        return toBeDeletedUserIds;
    }

    @Override
    public TraineeDO getTraineeByUserId(Long userId) {
        return traineeMapper.selectByUserId(userId);
    }

    @Override
    public Long getUserBySystemId(String id) {
        return traineeMapper.getUserBySystemId(id);
    }

    @Override
    public void updateUserSystemId() {

        List<Long> userIds = adminUserApi.getErrorUserIdList().getCheckedData();

        if (CollUtil.isEmpty(userIds)) {
            return;
        }

        List<TraineeDO> traineeList = traineeMapper.getTraineeByUserIdList(userIds);

        if (CollUtil.isEmpty(traineeList)) {
            return;
        }

        log.info("批量发送学员到业中");
        String returnIds = sendTraineeBatchToMid(traineeList);
        log.info("批量导入学员业中返回：" + returnIds);

        adminUserApi.updateSystemIdBatchById(returnIds);

    }

    // 校验手机号的方法
    private boolean isValidPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return false;
        }

        Pattern pattern = Pattern.compile(PHONE_REGEX);
        Matcher matcher = pattern.matcher(phoneNumber);
        return matcher.matches();
    }


    @Override
    public String sendTraineeBatchToMid(List<TraineeDO> list) {
//        String token = businessCenterApi.getTokenOfBusinessCenter(YezhongUtil.getTokenFromRequestHead()).getCheckedData();
//        log.info("业中token"+token);
        HttpRequest request = HttpUtil.createPost(MID_BASE_URI + ADD_BATCH_TRAINEE_URI)
//                .header("Authorization","Bearer "+token)
                .body(JSONUtil.toJsonStr(list));
        HttpResponse response = null;
        log.info("开始向业中发送批量导入学员");
        try {
            response = request.execute();
            log.info(response.body());
        } catch (Exception e) {
            log.info("sendBatchTraineeToMidWrong" + e.getMessage());
        }
        String result = response.body();
        YezhongResult res = JSONUtil.toBean(result, YezhongResult.class);
        return res.getData();
    }

    @Override
    public Page<TraineeInfoPageRespVO> getTraineeInfoPage(TraineeInfoPageReqVO reqVO) {

        Page<TraineeInfoPageRespVO> page = traineeMapper.getTraineeInfoPage(MyBatisUtils.buildPage(reqVO), reqVO);

        AtomicInteger start = new AtomicInteger(PageUtils.getStart(reqVO));
        page.getRecords().forEach(item -> {
            item.setIndex(start.getAndIncrement() + 1);
            item.setPhone(DesensitizedUtil.mobilePhone(item.getPhone()));
        });

        return page;
    }

    @Override
    public List<ExportTraineeInfoExcelVO> exportTraineeInfo(TraineeInfoPageReqVO reqVO) {

        List<ExportTraineeInfoExcelVO> list = traineeMapper.getTraineeReportInfoList(reqVO);


        handleDictValue(list);

        return list;
    }

    @Override
    public PageResult<TraineeGroupRespVO> getTrainByGroup(TraineeGroupReqVO reqVO) {
        IPage<TraineeGroupRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<TraineeGroupRespVO> pageResult = traineeMapper.getTraineeGroupPage(page, reqVO);
        for (int i = 0; i < pageResult.size(); i++) {


            TraineeGroupRespVO vo = pageResult.get(i);

            if (i!=0){
                vo.setPreId(pageResult.get(i-1).getId());
            }
            if (i!=pageResult.size()-1){
                vo.setNextId(pageResult.get(i+1).getId());
            }
            vo.setIndex((page.getCurrent() - 1) * page.getSize() + i + 1);
        }
        return new PageResult<>(pageResult, page.getTotal());
    }

    /**
     * 同步干部信息
     *
     * @param list 培训人员数据
     */
    @Async
    public void synchronizeCadreInformation(List<TraineeDO> list) {

        if (CollUtil.isEmpty(list)) {
            log.info("待同步的培训人员数据为空");
            return;
        }

        // 1. 获取需要同步的人员列表
        List<TraineeDO> rightData = list.stream().filter(item -> item.getUnitId() != null).collect(Collectors.toList());
        List<TraineeDO> traineesToSync = getTraineesToSync(rightData);
        if (CollUtil.isEmpty(traineesToSync)) {
            log.info("没有需要同步的新干部信息");
            return;
        }

        // 2. 获取并处理单位信息
        List<TraineeDO> processedTrainees = processTraineesWithParentUnits(traineesToSync);
        if (CollUtil.isEmpty(processedTrainees)) {
            log.warn("未找到有效的单位父级信息");
            return;
        }

        // 3. 转换并保存干部信息
        saveCadreInformation(processedTrainees);
    }

    /**
     * 获取需要同步的人员列表
     */
    private List<TraineeDO> getTraineesToSync(List<TraineeDO> rightData) {
        if (CollUtil.isEmpty(rightData)) {
            log.info("待同步的培训人员数据为空");
            return Collections.emptyList();
        }
        Set<String> existingCardNos = cadreInformationService.getCadreInfoByTrainees(rightData)
                .stream()
                .map(CadreInformationDO::getCardNo)
                .collect(Collectors.toSet()); // 使用Set提高查找效率

        return rightData.stream()
                .filter(trainee -> !existingCardNos.contains(trainee.getCardNo()))
                .collect(Collectors.toList());
    }

    /**
     * 处理培训人员的上级单位信息
     */
    private List<TraineeDO> processTraineesWithParentUnits(List<TraineeDO> trainees) {
        // 获取所有单位ID
        Set<Long> unitIds = trainees.stream()
                .map(TraineeDO::getUnitId)
                .collect(Collectors.toSet());

        // 获取单位父级信息映射
        Map<Integer, Long> parentUnitMap = getParentUnitMap(unitIds);
        if (parentUnitMap.isEmpty()) {
            log.warn("未查询到单位父级信息映射关系");
        }

        return trainees.stream()
                .map(trainee -> createTraineeWithParentUnit(trainee, parentUnitMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取单位父级信息映射
     */
    private Map<Integer, Long> getParentUnitMap(Set<Long> unitIds) {
        if (CollUtil.isEmpty(unitIds)) {
            log.warn("单位ID列表为空");
            return Collections.emptyMap();
        }

        List<SignUpUnitDO> unitList = signUpUnitMapper.selectBatchIds(unitIds);
        if (CollUtil.isEmpty(unitList)) {
            log.warn("未查询到单位信息，单位ID列表：{}", unitIds);
        }

        return unitList.stream()
                .filter(unit -> unit.getParentId() != null)
                .collect(Collectors.toMap(
                        SignUpUnitDO::getId,
                        SignUpUnitDO::getParentId,
                        (existing, replacement) -> existing,
                        HashMap::new
                ));
    }

    /**
     * 创建带有父级单位信息的培训人员对象
     */
    private TraineeDO createTraineeWithParentUnit(TraineeDO source, Map<Integer, Long> parentUnitMap) {
        Long parentId = parentUnitMap.get(source.getUnitId().intValue());
        if (parentId == null) {
            log.debug("未找到培训人员的单位父级信息，身份证号：{}，单位ID：{}",
                    source.getCardNo(), source.getUnitId());
            return null;
        }

        TraineeDO trainee = new TraineeDO();
        BeanUtils.copyProperties(source, trainee);
        trainee.setUnitId(parentId);
        return trainee;
    }

    /**
     * 保存干部信息
     */
    private void saveCadreInformation(List<TraineeDO> processedTrainees) {
        try {
            List<CadreInformationDO> cadreList = TraineeConvert.INSTANCE.convertCadreList(processedTrainees);
            cadreInformationService.saveBatch(cadreList);
            log.info("成功同步{}条干部信息记录", cadreList.size());
        } catch (Exception e) {
            log.error("保存干部信息失败，错误信息：", e);
            throw exception(CADRE_INFO_SYNC_FAIL);
        }
    }


    private void handleDictValue(List<ExportTraineeInfoExcelVO> list) {

        Map<Long, Map<String, String>> politicalIdentityMap = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.POLITICAL_IDENTITY.getType());
        Map<Long, Map<String, String>> personRank = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.JOB_LEVEL.getType());
//        Map<Long, Map<String, String>> statusMap = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.TRAINEE_STATUS.getType());

        Map<String, Map<String, String>> statusMap = traineeDictConvertService.getDictDateMapById1(TraineeDictTypeEnum.TRAINEE_STATUS.getType());

        for (ExportTraineeInfoExcelVO excelVO : list) {
            // 处理 jobLevel
            Optional.ofNullable(excelVO.getJobLevel())
                    .map(Long::valueOf)
                    .map(personRank::get)
                    .map(map -> map.get("label"))
                    .ifPresent(excelVO::setJobLevel);

            // 处理 politicalIdentity
            Optional.ofNullable(excelVO.getPoliticalIdentity())
                    .map(Long::valueOf)
                    .map(politicalIdentityMap::get)
                    .map(map -> map.get("label"))
                    .ifPresent(excelVO::setPoliticalIdentity);

            // 处理 status
            Optional.ofNullable(excelVO.getStatus())
                    .map(statusMap::get)
                    .map(map -> map.get("label"))
                    .ifPresent(excelVO::setStatus);
        }

    }

    // 修正脏数据
    @Async
    public void setGroupOrder(Long groupId) {
        // 重新排序
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeDO::getGroupId, groupId);
        wrapper.orderByAsc(TraineeDO::getGroupSort);
        List<TraineeDO> list = traineeMapper.selectList(wrapper);
        for (int i = 0; i < list.size(); i++) {
            TraineeDO traineeDO = list.get(i);
            traineeDO.setGroupSort(i + 1);
        }
        this.updateBatchById(list);

    }

    @Override
    public Page<TraineeStatPageRespVO> getTraineeStat(@Valid TraineeInfoStatReqVO reqVO) {
        List<TraineeStatPageRespVO> filteredList = getTraineeStatList(reqVO);
        // 手动分页
        int pageIndex = reqVO.getPageNo();
        int pageSize = reqVO.getPageSize();
        int fromIndex = (pageIndex - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, filteredList.size());
        List<TraineeStatPageRespVO> pageContent = filteredList.subList(fromIndex, toIndex);
        int totalRecords = filteredList.size();
        Page<TraineeStatPageRespVO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        page.setRecords(pageContent);
        page.setTotal(totalRecords);
        return page;
    }

    @Override
    public Page<TraineeStatByUnitPageRespVO> getTraineeStatByUnit(@Valid TraineeInfoStatByUnitReqVO reqVO) {
        List<TraineeStatByUnitPageRespVO> filteredList = getTraineeStatListByUnit(reqVO);
        // 手动分页
        int pageIndex = reqVO.getPageNo();
        int pageSize = reqVO.getPageSize();
        int fromIndex = (pageIndex - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, filteredList.size());
        List<TraineeStatByUnitPageRespVO> pageContent = filteredList.subList(fromIndex, toIndex);
        //加密敏感数据
        encryptData(pageContent);
        int totalRecords = filteredList.size();
        Page<TraineeStatByUnitPageRespVO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        page.setRecords(pageContent);
        page.setTotal(totalRecords);
        return page;
    }

    @Override
    public Boolean batchDeleteTrainee(TraineeDeleteReqVO reqVO) {

        //如果classId不为空，则删除班级所有学员
        if (reqVO.getClassId() != null){
            List<TraineeDO> traineeList = traineeMapper.getAllTraineeByClassIds(Collections.singletonList(reqVO.getClassId()));
            List<Long> traineeIds = traineeList.stream().map(TraineeDO::getId).collect(Collectors.toList());
            reqVO.setTraineeIds(traineeIds);
        }

        List<Long> ids = reqVO.getTraineeIds();

        if (CollUtil.isEmpty(ids)){
            return false;
        }

        List<Long> needdeleteda = new ArrayList<>();  // 只删除学员账号
        // 删除教务学员信息
        List<Long> needdeleted = removeJWUser(ids);  //直接删除业中的登录账号
        Map<String, Object> param = new HashMap<>();

        if(CollectionUtil.isEmpty(needdeleted)){
            needdeleteda.addAll(ids);
            param.put("idsone", needdeleteda);
        }else{
            param.put("ids", ids);
        }


        // 执行删除
        traineeMapper.deleteBatchIds(ids);

        try {
            // 删除后同步删除问卷
            evaluationResponseMapper.deleteBatchIds(ids);
            evaluationDetailMapper.deleteBatchIds(ids);

            //删除对应考勤
            clockInInfoService.deleteClockInInfoByTraineeIds(ids);

            //删除对应请假数据
            traineeLeaveService.deleteLeaveInfoByTraineeIds(ids);

            //删除对应选修课数据
            electiveTraineeSelectionService.deleteElectiveInfoByTraineeIds(ids);

            //删除点名签到数据
            rollcallRecordService.deleteRollCallRecordByTraineeIds(ids);

            sendTraineeToMid(JSONUtil.toJsonStr(param), DEL_TRAINEE_URI);
        } catch (Exception e) {
            log.info("同步删除失败：{}",e.getMessage());
        }

        return true;
    }

    @Override
    @Async
    public void deleteByUnitIds(List<Integer> deleteUnitIds) {
        //根据单位id获取所有学员id
        List<TraineeDO> traineeList = traineeMapper.getByUnitIds(deleteUnitIds);

        if (CollUtil.isEmpty(traineeList)){
            return;
        }

        TraineeDeleteReqVO reqVO = new TraineeDeleteReqVO();
        reqVO.setTraineeIds(traineeList.stream().map(TraineeDO::getId).collect(Collectors.toList()));
        batchDeleteTrainee(reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTraineeUnitInfo(List<Integer> unitIds, SignUpUnitDO updateObj) {

        if (CollUtil.isEmpty(unitIds)) {
            return;
        }
        List<TraineeDO> list = traineeMapper.getByUnitIds(unitIds);

        list.forEach(item -> {
            item.setUnitName(updateObj.getUnitName());
            item.setUnitClassification(Long.valueOf(updateObj.getUnitClassification()));
        });

        updateBatchById(list);
    }

    /**
     * 加密敏感数据
     * @param pageContent 加密对象
     */
    private static void encryptData(List<TraineeStatByUnitPageRespVO> pageContent) {
        // 1、创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        // 2、多线程计数器
        CountDownLatch latch = new CountDownLatch(pageContent.size());
        pageContent.forEach(vo -> {
            executorService.submit(() -> {
                try {
                    // 手机号脱敏
                    if(StringUtils.isNotEmpty(vo.getPhone())){
                        vo.setPhone(desensitizeEncrypt(DesensitizeUtils.mobileDesensitize(vo.getPhone())));
                    }
                    //职务脱敏并加密
                    if(StringUtils.isNotEmpty(vo.getPosition())){
                        vo.setPosition(desensitizeEncrypt(DesensitizeUtils.sliderDesensitize(vo.getPosition(), 1, 0)));
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }finally {
                    latch.countDown();
                }
            });
        });
        // 3. 主线程等待所有任务完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        // 4. 关闭线程池
        executorService.shutdown();
    }


    /**
     * 获取学员统计列表
     * 根据请求参数获取学员信息并进行筛选和排序
     *
     * @param reqVO 学员信息统计请求对象，包含筛选条件
     * @return 学员统计列表
     */
    public List<TraineeStatPageRespVO> getTraineeStatList(@Valid TraineeInfoStatReqVO reqVO) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        List<SignUpUnitDO> unitList = signUpUnitServiceImpl.lambdaQuery().eq(SignUpUnitDO::getUserId, loginUserId).eq(SignUpUnitDO::getTemplate,TEMPLATE).list();
        Integer unitId;
        if (unitList.isEmpty()) {
            throw exception(SIGN_UP_UNIT_LEADER_NOT_EXISTS);
        }
        unitId = unitList.get(0).getId();
        List<SignUpUnitDO> unitByParentIdList = signUpUnitServiceImpl.lambdaQuery().eq(SignUpUnitDO::getParentId, unitId).list();
        List<Integer> unitIdList = unitByParentIdList.stream().map(SignUpUnitDO::getId).collect(Collectors.toList());
        // 学员-是否按照报名时间createTime排序
        LambdaQueryWrapper<TraineeDO> traineeWrapper = new LambdaQueryWrapper<>();
        traineeWrapper.in(TraineeDO::getUnitId, unitIdList);
        if (StringUtils.isEmpty(reqVO.getOrderByReportTime()) || DESC.equals(reqVO.getOrderByReportTime())) {
            traineeWrapper.orderByDesc(TraineeDO::getCreateTime);
        } else if (ASC.equals(reqVO.getOrderByReportTime())) {
            traineeWrapper.orderByAsc(TraineeDO::getCreateTime);
        }
        List<TraineeDO> traineeList = new ArrayList<>();
        if (!unitIdList.isEmpty()) {
            traineeList = list(traineeWrapper);
        }
        // 班级
        List<Long> classIdList = traineeList.stream().map(TraineeDO::getClassId).collect(Collectors.toList());
        List<ClassManagementDO> classList = !classIdList.isEmpty() ? classManagementServiceImpl.lambdaQuery().in(ClassManagementDO::getId, classIdList).list() : new ArrayList<>();
        Map<Long, ClassManagementDO> classMap = classList.stream()
                .collect(Collectors.toMap(ClassManagementDO::getId, classManagementDO -> classManagementDO));
        // 获取职级字典
        List<String> dictTypes = new ArrayList<>();
        dictTypes.add(JOB_LEVEL_DICT_TYPE);
        List<DictDataRespDTO> dictDataList = dictDataApi.getByDictTypes(dictTypes).getCheckedData();
        Map<Long, DictDataRespDTO> dictDataMap = dictDataList.stream()
                .collect(Collectors.toMap(DictDataRespDTO::getId, d -> d));

        List<TraineeStatPageRespVO> respVOList = new ArrayList<>();
        for (TraineeDO traineeDO : traineeList) {
            TraineeStatPageRespVO traineeStatPageRespVO = new TraineeStatPageRespVO();
            // 学员信息
            BeanUtils.copyProperties(traineeDO, traineeStatPageRespVO);
            traineeStatPageRespVO.setCreateTime(traineeDO.getCreateTime());
            traineeStatPageRespVO.setBirthDay(CheckIdCard.getFormattedBirthDateByIdCard(traineeDO.getCardNo()));
            // 班级信息
            ClassManagementDO classManagementDO = classMap.get(traineeDO.getClassId());
            if (classManagementDO != null) {
                traineeStatPageRespVO.setClassName(classManagementDO.getClassName());
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                traineeStatPageRespVO.setClassOpenTime(classManagementDO.getClassOpenTime().format(dateTimeFormatter));
                traineeStatPageRespVO.setCompletionTime(classManagementDO.getCompletionTime().format(dateTimeFormatter));
                traineeStatPageRespVO.setSemester(classManagementDO.getSemester().toString());
                traineeStatPageRespVO.setYear(classManagementDO.getYear());
                // 学期枚举补充
                if (SemesterEnum.FIRST_SEMESTER.getCode().equals(classManagementDO.getSemester())) {
                    traineeStatPageRespVO.setSemester(SemesterEnum.FIRST_SEMESTER.getDesc());
                } else if (SemesterEnum.SECOND_SEMESTER.getCode().equals(classManagementDO.getSemester())) {
                    traineeStatPageRespVO.setSemester(SemesterEnum.SECOND_SEMESTER.getDesc());
                }
            }
            // 职级
            if (traineeDO.getJobLevel() != null) {
                DictDataRespDTO dictDataRespDTO = dictDataMap.get(Long.valueOf(traineeDO.getJobLevel()));
                if (dictDataRespDTO != null) {
                    traineeStatPageRespVO.setJobLevel(dictDataRespDTO.getLabel());
                }
            }

            // 装箱
            respVOList.add(traineeStatPageRespVO);
        }
        // 结果筛选
        List<TraineeStatPageRespVO> filteredList = respVOList.stream()
                .filter(vo -> (reqVO.getYear() == null || vo.getYear().equals(reqVO.getYear())))
                //todo 判定逻辑优化
                .filter(vo -> (StringUtils.isEmpty(reqVO.getClassName()) || (StringUtils.isNotEmpty(vo.getClassName()) && vo.getClassName().contains(reqVO.getClassName())) || StringUtils.isEmpty(vo.getClassName())))
                .filter(vo -> (StringUtils.isEmpty(reqVO.getNameOrPhone()) || (StringUtils.isNotEmpty(vo.getName()) && vo.getName().contains(reqVO.getNameOrPhone())) || (StringUtils.isNotEmpty(vo.getPhone()) && vo.getPhone().contains(reqVO.getNameOrPhone()))))
                .collect(Collectors.toList());

        filteredList.forEach(vo -> {
            // 手机号脱敏
            vo.setPhone(DesensitizeUtils.mobileDesensitize(vo.getPhone()));
        });

        // 班级名称排序
        if (ASC.equals(reqVO.getOrderByClassName())) {
            filteredList.sort(Comparator.comparing(TraineeStatPageRespVO::getClassName));
        } else if (DESC.equals(reqVO.getOrderByClassName())) {
            filteredList.sort(Comparator.comparing(TraineeStatPageRespVO::getClassName).reversed());
        }
        return filteredList;
    }


    /**
     * 根据租户id缓存学员数据-过期时间1小时
     */
    public static Cache<Long, List<TraineeDO>> traineeListCache=
            CacheBuilder.newBuilder()
                    // 初始容量
                    .initialCapacity(1024)
                    // 设定最大容量
                    .maximumSize(1024*10)
                    // 设定写入过期时间
                    .expireAfterWrite(1L, TimeUnit.HOURS)
                    // 设置最大并发写操作线程数
                    .concurrencyLevel(8)
                    .build();

    /**
     * 根据租户id缓存班级数据-过期时间1小时
     */
    public static Cache<Long, List<ClassManagementDO>> classManagementListCache=
            CacheBuilder.newBuilder()
                    // 初始容量
                    .initialCapacity(1024)
                    // 设定最大容量
                    .maximumSize(1024*10)
                    // 设定写入过期时间
                    .expireAfterWrite(1L, TimeUnit.HOURS)
                    // 设置最大并发写操作线程数
                    .concurrencyLevel(8)
                    .build();

    /**
     * 根据租户id缓存单位数据-过期时间1小时
     */
    public static Cache<Long, List<SignUpUnitDO>> unitListCache=
            CacheBuilder.newBuilder()
                    // 初始容量
                    .initialCapacity(1024)
                    // 设定最大容量
                    .maximumSize(1024*10)
                    // 设定写入过期时间
                    .expireAfterWrite(1L, TimeUnit.HOURS)
                    // 设置最大并发写操作线程数
                    .concurrencyLevel(8)
                    .build();

    /**
     * 获取学员统计列表
     * 根据请求参数获取学员信息并进行筛选和排序
     *
     * @param reqVO 学员信息统计请求对象，包含筛选条件
     * @return 学员统计列表
     */
    public List<TraineeStatByUnitPageRespVO> getTraineeStatListByUnit(@Valid TraineeInfoStatByUnitReqVO reqVO) {
        Long tenantId = SecurityFrameworkUtils.getTenantId();
        //学员
        LambdaQueryWrapper<TraineeDO> traineeWrapper = new LambdaQueryWrapper<>();
        List<TraineeDO> traineeList = new ArrayList<>();
        try {
            if (tenantId != null) {
                traineeList = traineeListCache.get(tenantId,()-> list(traineeWrapper));
            }
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        // 班级
        List<Long> classIdList = traineeList.stream().map(TraineeDO::getClassId).collect(Collectors.toList());
        List<ClassManagementDO> classList = new ArrayList<>();
        try {
            if (tenantId != null) {
                classList = classManagementListCache.get(tenantId,()-> classManagementServiceImpl.list());
            }
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        classList = classList.stream().filter(vo -> classIdList.contains(vo.getId())).collect(Collectors.toList());
        Map<Long, ClassManagementDO> classMap = classList.stream()
                .collect(Collectors.toMap(ClassManagementDO::getId, classManagementDO -> classManagementDO));
        //单位
        List<SignUpUnitDO> unitList = new ArrayList<>();
        try {
            if (tenantId != null) {
                unitList = unitListCache.get(tenantId,()-> signUpUnitServiceImpl.list());
            }
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        Map<Integer, SignUpUnitDO> unitMap = unitList.stream()
                .collect(Collectors.toMap(SignUpUnitDO::getId, unitDO -> unitDO));
        // 获取职级字典
        List<String> dictTypes = new ArrayList<>();
        dictTypes.add(JOB_LEVEL_DICT_TYPE);
        dictTypes.add(EDU_CLASS_ATTRIBUTE_TYPE);
        dictTypes.add(POLITICAL_IDENTITY);
        dictTypes.add(EDUCATIONAL_LEVEL);
        dictTypes.add(NATION);
        dictTypes.add(TRAINEE_STATUS);
        List<DictDataRespDTO> dictDataList = dictDataApi.getByDictTypes(dictTypes).getCheckedData();
        Map<Long, DictDataRespDTO> dictDataMap = dictDataList.stream()
                .collect(Collectors.toMap(DictDataRespDTO::getId, d -> d));
        List<TraineeStatByUnitPageRespVO> respVOList = new CopyOnWriteArrayList<>();
        //绑定相关信息
        matchInformation(traineeList, classMap, dictDataMap, respVOList,unitMap);
        // 结果筛选
        List<TraineeStatByUnitPageRespVO> filteredList = respVOList.stream()
                .filter(vo -> (reqVO.getYear() == null || vo.getYear().equals(reqVO.getYear())))
                .filter(vo -> (StringUtils.isEmpty(reqVO.getClassName()) || (StringUtils.isNotEmpty(vo.getClassName()) && vo.getClassName().contains(reqVO.getClassName()))))
                .filter(vo -> (StringUtils.isEmpty(reqVO.getUnitName()) || (StringUtils.isNotEmpty(vo.getUnitName()) && vo.getUnitName().contains(reqVO.getUnitName()))))
                .filter(vo -> (StringUtils.isEmpty(reqVO.getSex()) || (StringUtils.isNotEmpty(vo.getSex()) && vo.getSex().equals(reqVO.getSex()))))
                .filter(vo -> (reqVO.getShortClass() == null || (vo.getShortClass() != null && vo.getShortClass().equals(reqVO.getShortClass()))))
                .filter(vo -> (reqVO.getCampus() == null || (vo.getCampus() != null && vo.getCampus().equals(reqVO.getCampus()))))
                //默认情况：年份倒序排序，同年度下按单位名称排序。同一报名单位默认按报名时间倒序排列。
                .sorted(Comparator
                        .comparing(TraineeStatByUnitPageRespVO::getYear, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(TraineeStatByUnitPageRespVO::getUnitName, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(TraineeStatByUnitPageRespVO::getCreateTime, Comparator.nullsLast(Comparator.naturalOrder()))
                        .reversed()).collect(Collectors.toList());
        //进行排序
        sort(reqVO, filteredList);
        return filteredList;
    }

    /**
     * 排序
     * @param reqVO 请求对象
     * @param filteredList 筛选对象
     */
    private static void sort(TraineeInfoStatByUnitReqVO reqVO, List<TraineeStatByUnitPageRespVO> filteredList) {
        // 班级名称排序
        if (ASC.equals(reqVO.getOrderByClassName())) {
            filteredList.sort(Comparator.comparing(TraineeStatByUnitPageRespVO::getClassName, Comparator.nullsLast(Comparator.naturalOrder())));
        } else if (DESC.equals(reqVO.getOrderByClassName())) {
            filteredList.sort(Comparator.comparing(TraineeStatByUnitPageRespVO::getClassName, Comparator.nullsLast(Comparator.reverseOrder())));
        }

        // 单位名称排序
        if (ASC.equals(reqVO.getOrderByUnitName())) {
            filteredList.sort(Comparator.comparing(TraineeStatByUnitPageRespVO::getUnitName, Comparator.nullsLast(Comparator.naturalOrder())));
        } else if (DESC.equals(reqVO.getOrderByUnitName())) {
            filteredList.sort(Comparator.comparing(TraineeStatByUnitPageRespVO::getUnitName, Comparator.nullsLast(Comparator.reverseOrder())));
        }

        // 年份排序
        if (ASC.equals(reqVO.getOrderByYear())) {
            filteredList.sort(Comparator.comparing(TraineeStatByUnitPageRespVO::getYear, Comparator.nullsLast(Comparator.naturalOrder())));
        } else if (DESC.equals(reqVO.getOrderByYear())) {
            filteredList.sort(Comparator.comparing(TraineeStatByUnitPageRespVO::getYear, Comparator.nullsLast(Comparator.reverseOrder())));
        }

        // 开班时间排序
        if (ASC.equals(reqVO.getOrderByClassOpenTime())) {
            filteredList.sort(Comparator.comparing(TraineeStatByUnitPageRespVO::getClassOpenTime, Comparator.nullsLast(Comparator.naturalOrder())));
        } else if (DESC.equals(reqVO.getOrderByClassOpenTime())) {
            filteredList.sort(Comparator.comparing(TraineeStatByUnitPageRespVO::getClassOpenTime, Comparator.nullsLast(Comparator.reverseOrder())));
        }

        // 结业时间排序
        if (ASC.equals(reqVO.getOrderByCompletionTime())) {
            filteredList.sort(Comparator.comparing(TraineeStatByUnitPageRespVO::getCompletionTime, Comparator.nullsLast(Comparator.naturalOrder())));
        } else if (DESC.equals(reqVO.getOrderByCompletionTime())) {
            filteredList.sort(Comparator.comparing(TraineeStatByUnitPageRespVO::getCompletionTime, Comparator.nullsLast(Comparator.reverseOrder())));
        }

        //根据当前的排序整体顺序或降序,不用根据任何字段，仅将当前的数组正序或者倒序就可以了
        if (DESC.equals(reqVO.getOrderByNow())) {
            Collections.reverse(filteredList);
        }
    }

    /**
     * 绑定相关信息
     * @param traineeList 培训人员列表
     * @param classMap 班级信息
     * @param dictDataMap 字典数据
     * @param respVOList 请求信息
     */
    private static void matchInformation(List<TraineeDO> traineeList, Map<Long, ClassManagementDO> classMap, Map<Long, DictDataRespDTO> dictDataMap, List<TraineeStatByUnitPageRespVO> respVOList,Map<Integer, SignUpUnitDO> unitMap) {
        // 1、创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        // 2、多线程计数器
        CountDownLatch latch = new CountDownLatch(traineeList.size());
        for (TraineeDO traineeDO : traineeList) {
            executorService.submit(() -> {
                try {
                    TraineeStatByUnitPageRespVO traineeStatPageRespVO = new TraineeStatByUnitPageRespVO();
                    // 学员信息
                    BeanUtils.copyProperties(traineeDO, traineeStatPageRespVO);
                    // 班级信息
                    ClassManagementDO classManagementDO = classMap.get(traineeDO.getClassId());
                    if (classManagementDO != null) {
                        traineeStatPageRespVO.setClassName(classManagementDO.getClassName());
                        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        traineeStatPageRespVO.setClassOpenTime(classManagementDO.getClassOpenTime().format(dateTimeFormatter));
                        traineeStatPageRespVO.setCompletionTime(classManagementDO.getCompletionTime().format(dateTimeFormatter));
                        traineeStatPageRespVO.setSemester(classManagementDO.getSemester().toString());
                        traineeStatPageRespVO.setYear(classManagementDO.getYear());
                        //判断是否委托班
                        if (classManagementDO.getClassAttribute() != null) {
                            DictDataRespDTO dictDataRespDTO = dictDataMap.get(Long.valueOf(classManagementDO.getClassAttribute()));
                            //todo 魔法值常量化、枚举化
                            traineeStatPageRespVO.setShortClass(dictDataRespDTO != null && "委托班".equals(dictDataRespDTO.getLabel()));
                        }
                        traineeStatPageRespVO.setCampus(classManagementDO.getCampus());
                        // 学期枚举补充
                        if (SemesterEnum.FIRST_SEMESTER.getCode().equals(classManagementDO.getSemester())) {
                            traineeStatPageRespVO.setSemester(SemesterEnum.FIRST_SEMESTER.getDesc());
                        } else if (SemesterEnum.SECOND_SEMESTER.getCode().equals(classManagementDO.getSemester())) {
                            traineeStatPageRespVO.setSemester(SemesterEnum.SECOND_SEMESTER.getDesc());
                        }
                    }
                    //单位信息
                    if (traineeDO.getUnitId() != null) {
                        SignUpUnitDO signUpUnitDO = unitMap.get(traineeDO.getUnitId().intValue());
                        if (signUpUnitDO != null) {
                            traineeStatPageRespVO.setUnitName(signUpUnitDO.getUnitName());
                        }
                    }
                    traineeStatPageRespVO.setCreateTime(traineeDO.getCreateTime());
                    traineeStatPageRespVO.setBirthDay(CheckIdCard.getFormattedBirthDateByIdCard(traineeDO.getCardNo()));
                    // 职级
                    if (traineeDO.getJobLevel() != null) {
                        DictDataRespDTO dictDataRespDTO = dictDataMap.get(Long.valueOf(traineeDO.getJobLevel()));
                        if (dictDataRespDTO != null) {
                            traineeStatPageRespVO.setJobLevel(dictDataRespDTO.getLabel());
                        }
                    }
                    //政治面貌
                    if (traineeDO.getPoliticalIdentity() != null) {
                        DictDataRespDTO dictDataRespDTO = dictDataMap.get(Long.valueOf(traineeDO.getPoliticalIdentity()));
                        if (dictDataRespDTO != null) {
                            traineeStatPageRespVO.setPoliticalIdentity(dictDataRespDTO.getLabel());
                        }
                    }
                    //文化程度
                    if (traineeDO.getEducationalLevel() != null) {
                        DictDataRespDTO dictDataRespDTO = dictDataMap.get(Long.valueOf(traineeDO.getEducationalLevel()));
                        if (dictDataRespDTO != null) {
                            traineeStatPageRespVO.setEducationalLevel(dictDataRespDTO.getLabel());
                        }
                    }
                    //种族
                    if (traineeDO.getEthnic() != null) {
                        DictDataRespDTO dictDataRespDTO = dictDataMap.get(Long.valueOf(traineeDO.getEthnic()));
                        if (dictDataRespDTO != null) {
                            traineeStatPageRespVO.setEthnic(dictDataRespDTO.getLabel());
                        }
                    }
                    //状态
                    if (traineeDO.getStatus() != null) {
                            traineeStatPageRespVO.setStatus(TraineeStatusEnum.getDescByStatus(traineeDO.getStatus()));
                    }
                    //根据出生年月设置年龄
                    if (traineeDO.getBirthday() != null) {
                        traineeStatPageRespVO.setAge(ageOfNow(traineeDO.getBirthday()).toString());
                    }
                    //是否委托班填充其他值
                    if(traineeStatPageRespVO.getShortClass() == null) {
                        traineeStatPageRespVO.setShortClass(false);
                    }
                    // 装箱
                    synchronized (respVOList) {
                        respVOList.add(traineeStatPageRespVO);
                    }
                } catch (Exception e) {
                    log.error("*************************************************************************************", e);
                }finally {
                    latch.countDown();
                }
            });
        }
        // 3. 主线程等待所有任务完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        // 4. 关闭线程池
        executorService.shutdown();
    }


    public static Integer ageOfNow(LocalDate birthday) {
        if (birthday == null) {
            throw new IllegalArgumentException("Birthday cannot be null");
        }

        LocalDate today = LocalDate.now();
        if (birthday.isAfter(today)) {
            throw new IllegalArgumentException("Birthday cannot be in the future");
        }

        Period period = Period.between(birthday, today);

        return period.getYears();
    }

    /**
     * 导出学员统计信息
     *
     * @param response HTTP响应对象，用于输出导出的Excel文件
     * @param reqVO    学员信息统计请求对象，包含查询条件和导出属性列表
     */
    @Override
    public void traineeStatExport(HttpServletResponse response, @Valid TraineeInfoStatReqVO reqVO) {
        List<TraineeStatPageRespVO> records = getTraineeStatList(reqVO);
        List<TraineeStatExportVO> traineeStatPageRespVOList = TraineeConvert.INSTANCE.convertToExportList(records);
        // 设置学期和职级
        // 获取职级字典
        List<String> dictTypes = new ArrayList<>();
        dictTypes.add(JOB_LEVEL_DICT_TYPE);
        List<DictDataRespDTO> dictDataList = dictDataApi.getByDictTypes(dictTypes).getCheckedData();
        Map<Long, DictDataRespDTO> dictDataMap = dictDataList.stream()
                .collect(Collectors.toMap(DictDataRespDTO::getId, d -> d));
        traineeStatPageRespVOList.forEach(vo -> {
            try {
                // 学期
                if (SemesterEnum.FIRST_SEMESTER.getCode().toString().equals(vo.getSemester())) {
                    vo.setSemester(SemesterEnum.FIRST_SEMESTER.getDesc());
                } else if (SemesterEnum.SECOND_SEMESTER.getCode().toString().equals(vo.getSemester())) {
                    vo.setSemester(SemesterEnum.SECOND_SEMESTER.getDesc());
                }
                // 职级
                DictDataRespDTO dictDataRespDTO = dictDataMap.get(Long.valueOf(vo.getJobLevel()));
                if (dictDataRespDTO != null) {
                    vo.setJobLevel(dictDataRespDTO.getLabel());
                }
                // 手机号脱敏
                vo.setPhone(DesensitizeUtils.mobileDesensitize(vo.getPhone()));
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        });
        try {
            // 动态生成列头
            List<String> columnsToExport = reqVO.getExportPropertyNameList();
            columnsToExport = columnsToExport.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            exportToExcel(response, "参训统计.xlsx", "参训统计", columnsToExport, traineeStatPageRespVOList);
            ExcelUtils.write(response, "参训统计.xlsx", "参训统计", TraineeStatExportVO.class, traineeStatPageRespVOList);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 导出单位学员统计信息
     *
     * @param response HTTP响应对象，用于输出导出的Excel文件
     * @param reqVO    学员信息统计请求对象，包含查询条件和导出属性列表
     */
    @Override
    public void traineeStatByUnitExport(HttpServletResponse response, @Valid TraineeInfoStatByUnitReqVO reqVO) {
        List<TraineeStatByUnitPageRespVO> records = getTraineeStatListByUnit(reqVO);
        List<TraineeStatByUnitExportVO> traineeStatPageRespVOList = TraineeConvert.INSTANCE.convertToByUnitExportList(records);
        // 设置学期
        traineeStatPageRespVOList.forEach(vo -> {
            try {
                // 学期
                if (SemesterEnum.FIRST_SEMESTER.getCode().toString().equals(vo.getSemester())) {
                    vo.setSemester(SemesterEnum.FIRST_SEMESTER.getDesc());
                } else if (SemesterEnum.SECOND_SEMESTER.getCode().toString().equals(vo.getSemester())) {
                    vo.setSemester(SemesterEnum.SECOND_SEMESTER.getDesc());
                }
                // 手机号解密
                if (StringUtils.isNotEmpty(vo.getPhone())) {
                    vo.setPhone(desensitizeDecrypt(vo.getPhone()));
                }
                //职务解密
                if (StringUtils.isNotEmpty(vo.getPosition())) {
                    vo.setPosition(desensitizeDecrypt(vo.getPosition()));
                }
                //是否短班补充
                if("true".equals(vo.getShortClass())){
                    vo.setShortClass("是");
                }else{
                    vo.setShortClass("否");
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        });
        try {
            // 动态生成列头
            List<String> columnsToExport = reqVO.getExportPropertyNameList();
            columnsToExport = columnsToExport.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            exportToByUnitExcel(response, "单位报名统计.xlsx", "单位报名统计", columnsToExport, traineeStatPageRespVOList);
            ExcelUtils.write(response, "单位报名统计.xlsx", "单位报名统计", TraineeStatByUnitExportVO.class, traineeStatPageRespVOList);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 将数据导出到Excel文件
     *
     * @param response Servlet响应对象，用于输出Excel文件
     * @param filename 导出文件的名称
     * @param sheetName Excel工作表的名称
     * @param columnsToExport 要导出的列名列表
     * @param data 要导出的数据列表
     * @throws IOException 当写入响应流时可能抛出的异常
     */
    private void exportToExcel(HttpServletResponse response, String filename, String sheetName,
                               List<String> columnsToExport, List<TraineeStatExportVO> data) throws IOException {

        // 创建工作簿和工作表
        // 使用 HSSF 导出 Excel 97-2003 格式，或使用 XSSF 导出 .xlsx 格式
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet(sheetName);

        // 创建表头
        HSSFRow headerRow = sheet.createRow(0);
        int columnIndex = 0;

        // 动态生成表头
        for (String columnName : columnsToExport) {
            HSSFCell cell = headerRow.createCell(columnIndex++);
            // 获取列的显示名称
            cell.setCellValue(getColumnDisplayName(columnName));
        }

        // 填充数据
        for (int i = 0; i < data.size(); i++) {
            // 数据从第2行开始
            HSSFRow row = sheet.createRow(i + 1);
            TraineeStatExportVO vo = data.get(i);

            columnIndex = 0;
            for (String columnName : columnsToExport) {
                HSSFCell cell = row.createCell(columnIndex++);
                // 根据列名设置对应的值
                setCellValue(cell, columnName, vo);
            }
        }

        // 设置响应头
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");

        // 写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * 将数据导出到Excel文件
     *
     * @param response Servlet响应对象，用于输出Excel文件
     * @param filename 导出文件的名称
     * @param sheetName Excel工作表的名称
     * @param columnsToExport 要导出的列名列表
     * @param data 要导出的数据列表
     * @throws IOException 当写入响应流时可能抛出的异常
     */
    private void exportToByUnitExcel(HttpServletResponse response, String filename, String sheetName,
                               List<String> columnsToExport, List<TraineeStatByUnitExportVO> data) throws IOException {

        // 创建工作簿和工作表
        // 使用 HSSF 导出 Excel 97-2003 格式，或使用 XSSF 导出 .xlsx 格式
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet(sheetName);

        // 创建表头
        HSSFRow headerRow = sheet.createRow(0);
        int columnIndex = 0;

        // 动态生成表头
        for (String columnName : columnsToExport) {
            HSSFCell cell = headerRow.createCell(columnIndex++);
            // 获取列的显示名称
            cell.setCellValue(getColumnDisplayName(columnName));
        }

        // 填充数据
        for (int i = 0; i < data.size(); i++) {
            // 数据从第2行开始
            HSSFRow row = sheet.createRow(i + 1);
            TraineeStatByUnitExportVO vo = data.get(i);

            columnIndex = 0;
            for (String columnName : columnsToExport) {
                HSSFCell cell = row.createCell(columnIndex++);
                // 根据列名设置对应的值
                setCellValueByUnit(cell, columnName, vo);
            }
        }

        // 设置响应头
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");

        // 写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    private String getColumnDisplayName(String columnName) {
        switch (columnName) {
            case "className":
                return "班次名称";
            case "year":
                return "年度";
            case "semester":
                return "学期";
            case "classOpenTime":
                return "开班日期";
            case "completionTime":
                return "结业日期";
            case "name":
                return "姓名";
            case "sex":
                return "性别";
            case "createTime":
                return "报名时间";
            case "birthDay":
                return "出生日期";
            case "phone":
                return "手机号";
            case "jobLevel":
                return "职级";
            case "position":
                return "职务";
            case "shortClass":
                return "是否短班";
            case "unitName":
                return "单位名称";
            case "age":
                return "年龄";
            case "politicalIdentity":
                return "政治面貌";
            case "educationalLevel":
                return "文化程度";
            case "ethnic":
                return "民族";
            case "status":
                return "学员状态";
            default:
                // 如果找不到对应的列名，则返回原始列名
                return columnName;
        }
    }

    private void setCellValue(HSSFCell cell, String columnName, TraineeStatExportVO vo) {
        // 使用反射设置相应列的值
        try {
            switch (columnName) {
                case "className":
                    cell.setCellValue(vo.getClassName());
                    break;
                case "year":
                    cell.setCellValue(vo.getYear());
                    break;
                case "semester":
                    cell.setCellValue(vo.getSemester());
                    break;
                case "classOpenTime":
                    cell.setCellValue(vo.getClassOpenTime());
                    break;
                case "completionTime":
                    cell.setCellValue(vo.getCompletionTime());
                    break;
                case "name":
                    cell.setCellValue(vo.getName());
                    break;
                case "sex":
                    cell.setCellValue(vo.getSex());
                    break;
                case "createTime":
                    cell.setCellValue(vo.getCreateTime() != null ? vo.getCreateTime().format(DateTimeFormatter.ofPattern(DATE)) : "");
                    break;
                case "birthDay":
                    cell.setCellValue(vo.getBirthDay());
                    break;
                case "phone":
                    cell.setCellValue(vo.getPhone());
                    break;
                case "jobLevel":
                    cell.setCellValue(vo.getJobLevel());
                    break;
                case "position":
                    cell.setCellValue(vo.getPosition());
                    break;
                default:
                    cell.setCellValue("未定义");
                    break;
            }
        } catch (Exception e) {
            cell.setCellValue("错误");
        }
    }

    private void setCellValueByUnit(HSSFCell cell, String columnName, TraineeStatByUnitExportVO vo) {
        // 使用反射设置相应列的值
        try {
            switch (columnName) {
                case "className":
                    cell.setCellValue(vo.getClassName());
                    break;
                case "year":
                    cell.setCellValue(vo.getYear());
                    break;
                case "semester":
                    cell.setCellValue(vo.getSemester());
                    break;
                case "classOpenTime":
                    cell.setCellValue(vo.getClassOpenTime());
                    break;
                case "completionTime":
                    cell.setCellValue(vo.getCompletionTime());
                    break;
                case "name":
                    cell.setCellValue(vo.getName());
                    break;
                case "sex":
                    cell.setCellValue(vo.getSex());
                    break;
                case "birthDay":
                    cell.setCellValue(vo.getBirthDay());
                    break;
                case "phone":
                    cell.setCellValue(vo.getPhone());
                    break;
                case "jobLevel":
                    cell.setCellValue(vo.getJobLevel());
                    break;
                case "position":
                    cell.setCellValue(vo.getPosition());
                    break;
                case "shortClass":
                    cell.setCellValue(vo.getShortClass());
                    break;
                case "unitName":
                    cell.setCellValue(vo.getUnitName());
                    break;
                case "politicalIdentity":
                    cell.setCellValue(vo.getPoliticalIdentity());
                    break;
                case "educationalLevel":
                    cell.setCellValue(vo.getEducationalLevel());
                    break;
                case "ethnic":
                    cell.setCellValue(vo.getEthnic());
                    break;
                case "status":
                    cell.setCellValue(vo.getStatus());
                    break;
                case "age":
                    cell.setCellValue(vo.getAge());
                    break;
                default:
                    cell.setCellValue("未定义");
                    break;
            }
        } catch (Exception e) {
            cell.setCellValue("错误");
        }
    }

    private String getTokenFromRequestHead(HttpServletRequest request) {
        // 从请求头中获取token
        String token = request.getHeader("Authorization");
        // 如果token以"Bearer "开头，则提取实际的token值
        if (token != null && token.startsWith("Bearer ")) {
            // 去除"Bearer "部分
            token = token.substring(7);
        }
        log.info("获得请求头Authorization:{}", token);
        return token;
    }

    /**
     * 根据id获取学员详细信息，返回TraineeBaseVO
     * 
     * @param id 学员id
     * @return 学员详细信息
     */
    @Override
    public TraineeBaseVO getTraineeDetailById(Long id) {
        // 获取学员信息
        TraineeDO trainee = getTrainById(id);

        // 使用转换器进行对象转换，确保所有字段都被正确映射
        TraineeBaseVO vo = TraineeConvert.INSTANCE.convertToTraineeBaseVO(trainee);

        return vo;
    }
}
