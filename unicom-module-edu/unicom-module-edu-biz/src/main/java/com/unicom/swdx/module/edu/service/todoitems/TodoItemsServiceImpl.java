package com.unicom.swdx.module.edu.service.todoitems;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.todoitems.dto.ECContentDTO;
import com.unicom.swdx.module.edu.controller.admin.todoitems.dto.TraineeLeaveContentDTO;
import com.unicom.swdx.module.edu.controller.admin.todoitems.vo.TodoItemsReqVO;
import com.unicom.swdx.module.edu.controller.admin.todoitems.vo.TodoItemsRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveCreateReqVO;
import com.unicom.swdx.module.edu.convert.todoitems.TodoItemsConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.todoitems.TodoItemsDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.todoitems.TodoItemsMapper;
import com.unicom.swdx.module.edu.enums.teachertodoitems.TeacherTodoStatusEnum;
import com.unicom.swdx.module.edu.enums.teachertodoitems.TeacherTodoTypeEnum;
import com.unicom.swdx.module.edu.enums.traineeleave.LeaveTaskStatus;
import com.unicom.swdx.module.edu.enums.traineeleave.TraineeLeaveStatus;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.TEACHER_TODO_ITEMS_CLASS_INFO_NOT_EXISTS;

/**
 * 班主任待办事项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TodoItemsServiceImpl implements TodoItemsService {

    @Resource
    private TodoItemsMapper teacherTodoItemsMapper;

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private TraineeService traineeService;

    public static final Integer HOME_MAX_LENGTH_LIMIT = 3;


    /**
     * 获得班主任待办事项列表
     *
     * @param reqVO 请求参数
     * @return 班主任待办事项列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TodoItemsRespVO> getTeacherTodoItemsList(TodoItemsReqVO reqVO) {
        // 校验班级是否存在
        ClassManagementDO classManagementDO = classManagementMapper.selectById(reqVO.getClassId());
        if (Objects.isNull(classManagementDO)) {
            throw exception(TEACHER_TODO_ITEMS_CLASS_INFO_NOT_EXISTS);
        }
        // 检测和刷新学员报名确认待办事项
        refreshEnrollmentConfirmationByClass(classManagementDO);
        return teacherTodoItemsMapper.selectListByReq(reqVO);
    }

    /**
     * 获得班主任首页待办事项列表
     *
     * @param classId 班级ID
     * @return 班主任首页待办事项列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TodoItemsRespVO> getTeacherTodoItemsHomeList(Long classId) {
        // 校验班级是否存在
        ClassManagementDO classManagementDO = classManagementMapper.selectById(classId);
        if (Objects.isNull(classManagementDO)) {
            throw exception(TEACHER_TODO_ITEMS_CLASS_INFO_NOT_EXISTS);
        }
        // 检测和刷新报名确认待办事项
        refreshEnrollmentConfirmationByClass(classManagementDO);
        // 获取当前所有未办的待办
        List<TodoItemsDO> teacherTodoItemsDOList = teacherTodoItemsMapper.selectListByStatusAndClassId(TeacherTodoStatusEnum.UNDO.getStatus(),
                classId);
        // 获取为报名确认的待办事项
        List<TodoItemsDO> enrollmentConfirmationList = teacherTodoItemsDOList.stream()
                .filter(item -> TeacherTodoTypeEnum.ENROLLMENT_CONFIRMATION.getType().equals(item.getType()))
                .collect(Collectors.toList());
        // 获取为请假申请的待办事项
        List<TodoItemsDO> leaveApplicationList = teacherTodoItemsDOList.stream()
                .filter(item -> TeacherTodoTypeEnum.LEAVE_APPLY.getType().equals(item.getType()))
                .collect(Collectors.toList());
        if (!enrollmentConfirmationList.isEmpty()) {
            leaveApplicationList.add(0, enrollmentConfirmationList.get(0));
        }
        // 返回的结果列表 最多包含一个报名确认的待办事项， 整个列表长度限制为HOME_MAX_LENGTH_LIMIT
        if (leaveApplicationList.size() > HOME_MAX_LENGTH_LIMIT) {
            leaveApplicationList = leaveApplicationList.subList(0, HOME_MAX_LENGTH_LIMIT);
        }
        return TodoItemsConvert.INSTANCE.convertList(leaveApplicationList);
    }

    /**
     * 刷新某个班级学员确认报道数据 -计算和更新学员报名确认待办事项
     *
     * @param classId 班级ID
     */
    @Override
    public void refreshEnrollmentConfirmationByClassId(Long classId) {
        ClassManagementDO classManagementDO = classManagementMapper.selectById(classId);
        refreshEnrollmentConfirmationByClass(classManagementDO);
    }

    /**
     * 刷新某个班级学员确认报道数据  -计算和更新学员报名确认待办事项
     *
     * @param classManagementDO 班级信息
     */
    @Override
    public void refreshEnrollmentConfirmationByClass(ClassManagementDO classManagementDO) {
        log.info("[报名确认待办事项] 开始更新班级'{}'", classManagementDO);
        // 获取报名开始时间
        LocalDateTime registrationStartTime = classManagementDO.getRegistrationStartTime()
                .withHour(0).withMinute(0).withSecond(0);
        log.info("[报名确认待办事项-班级(ID = {})] 班级报名开始时间:'{}'", classManagementDO.getId(), registrationStartTime);
        LocalDateTime registration18Before = registrationStartTime.plusHours(18);

        // 如果当前时间在报名开始时间当天18:00之前 则不计算待办
        if (LocalDateTime.now().isBefore(registration18Before)) {
            log.info("[报名确认待办事项-班级(ID = {})] 报名开始时间当天18:00之前({})，不更新学员报名确认待办事项", classManagementDO.getId(), registration18Before);
            return;
        }

        // 不判断结业时间 - 结业后学员无已报名状态 会自动刷新待办

        // 获取当前班级学员确认报道信息
        ECContentDTO ecContent = traineeService.getECContent(Long.valueOf(classManagementDO.getId()));
        log.info("[报名确认待办事项-班级(ID = {})] 学员报名确认信息{}", classManagementDO.getId(), ecContent);
        // 获取当前确认的状态
        Integer status = ecContent.getUnconfirmedNumber() > 0 ? TeacherTodoStatusEnum.UNDO.getStatus() : TeacherTodoStatusEnum.DONE.getStatus();

        // 查找当前该班级是否有未办的确认报道待办(维护一个班级只有一个确认报道待办)
        TodoItemsDO teacherTodoItemsDO = teacherTodoItemsMapper.selectOneByClassIdAndTypeAndStatus(classManagementDO.getId(),
                TeacherTodoTypeEnum.ENROLLMENT_CONFIRMATION.getType(), TeacherTodoStatusEnum.UNDO.getStatus());

        if (Objects.isNull(teacherTodoItemsDO)) {
            if (TeacherTodoStatusEnum.DONE.getStatus().equals(status)) {
                // 未办待办事项不存在且当前学员报名都已确认 则直接跳过
                log.info("[报名确认待办事项-班级(ID = {})] 学员报名都已确认，跳过", classManagementDO.getId());
                return;
            }
            teacherTodoItemsDO = new TodoItemsDO();
            teacherTodoItemsDO.setClassId(Long.valueOf(classManagementDO.getId()));
            teacherTodoItemsDO.setType(TeacherTodoTypeEnum.ENROLLMENT_CONFIRMATION.getType());
            log.info("[报名确认待办事项-班级(ID = {})] 未办待办事项不存在，新增未办待办事项！", classManagementDO.getId());
        } else {
            log.info("[报名确认待办事项-班级(ID = {})] 已存在未办的学员报名确认待办事项信息{}", classManagementDO.getId(), teacherTodoItemsDO);
        }

        // 设置当前的待办状态
        teacherTodoItemsDO.setStatus(status);
        // 设置当前的待办内容
        teacherTodoItemsDO.setContent(ecContent.toJsonString());

        if (Objects.isNull(teacherTodoItemsDO.getId())) {
            // 新增未办待办
            teacherTodoItemsMapper.insert(teacherTodoItemsDO);
            log.info("[报名确认待办事项-班级(ID = {})] 新增未办待办事项成功！'{}'", classManagementDO.getId(), teacherTodoItemsDO);
        } else {
            // 更新待办
            teacherTodoItemsMapper.updateById(teacherTodoItemsDO);
            log.info("[报名确认待办事项-班级(ID = {})] 更新未办待办事项成功！'{}'", classManagementDO.getId(), teacherTodoItemsDO);
        }
    }

    /**
     * 插入请假代办信息
     *
     * @param reqVO 请假信息
     */
    @Override
    public void addTraineeLeaveTodoItem(TraineeLeaveCreateReqVO reqVO) {
        ClassManagementDO classManagementDO = classManagementMapper.selectById(reqVO.getClassId());

        if (Objects.isNull(classManagementDO)) {
            throw exception(TEACHER_TODO_ITEMS_CLASS_INFO_NOT_EXISTS);
        }

        log.info("[新增请假申请代办] 班级'{}' 开始新增代办！",classManagementDO.getClassName());

        TodoItemsDO itemsDO = new TodoItemsDO();
        itemsDO.setClassId(reqVO.getClassId());
        itemsDO.setStatus(TeacherTodoStatusEnum.UNDO.getStatus());
        itemsDO.setType(TeacherTodoTypeEnum.LEAVE_APPLY.getType());
        itemsDO.setLeaveId(reqVO.getId());

        TraineeLeaveContentDTO content = new TraineeLeaveContentDTO();
        content.setLeaveTitle(reqVO.getTitle());
        content.setLeaveDay(reqVO.getDays());
        content.setLeaveType(reqVO.getLeaveType().toString());
        content.setLeaveStatus(TraineeLeaveStatus.WAIT.getCode());

        itemsDO.setContent(content.toJsonString());

        log.info("[新增请假申请代办] 班级'{}' 正在新增代办，代办内容：'{}'", classManagementDO.getClassName(),content);

        teacherTodoItemsMapper.insert(itemsDO);

        log.info("[新增请假申请代办] 班级'{}'新增代办成功！",classManagementDO.getClassName());

    }

    /**
     * 更新请假代办信息
     *
     * @param leaveId 学员请假id
     */
    @Override
    public void updateTraineeLeaveTodoItem(Long leaveId, Integer leaveStatus) {

        // 根据请假id 获取对应请假代办
        TodoItemsDO itemsDO = teacherTodoItemsMapper.getByLeaveId(leaveId);
        if (Objects.isNull(itemsDO)) {
            return;
        }

        if(Objects.equals(leaveStatus, TraineeLeaveStatus.CANCEL.getCode())){
            // 删除待办
            teacherTodoItemsMapper.deleteById(itemsDO.getId());
        }else {
            // 更新代办
            itemsDO.setStatus(TeacherTodoStatusEnum.DONE.getStatus());
            TraineeLeaveContentDTO content = JSONUtil.toBean(itemsDO.getContent(), TraineeLeaveContentDTO.class);
            content.setLeaveStatus(leaveStatus);
            itemsDO.setContent(content.toJsonString());
            teacherTodoItemsMapper.updateById(itemsDO);
        }
    }

    @Override
    public void removeTodoItemsByLeaveIds(List<Long> leaveIds) {
        if (leaveIds.isEmpty()) {
            return;
        }
        LambdaQueryWrapperX<TodoItemsDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.in(TodoItemsDO::getLeaveId, leaveIds);

        List<TodoItemsDO> todoItemsDOList = teacherTodoItemsMapper.selectList(wrapperX);
        if (todoItemsDOList.isEmpty()) {
            return;
        }

        List<Long> ids = todoItemsDOList.stream().map(TodoItemsDO::getId).collect(Collectors.toList());
        teacherTodoItemsMapper.deleteBatchIds(ids);
    }
}
