package com.unicom.swdx.module.edu.dal.dataobject.questionnairemanagement;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 评估问卷管理 DO
 *
 * <AUTHOR>
 */
@TableName("pg_questionnaire_management")
@KeySequence("pg_questionnaire_management_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionnaireManagementDO extends TenantBaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 问卷标题
     */
    private String title;
    /**
     * 问卷副标题
     */
    private String subtitle;
    /**
     * 是否默认问卷 (0: 否, 1: 是)
     */
    private String isDefault;
    /**
     * 状态 0未发布1已发布 2 已结束
     */
    private String status;
    /**
     * 创建部门
     */
    private Long createDept;
    /**
     * 创建人
     */
    // private Long creator;
    /**
     * 备注
     */
    private String remark;
    /**
     * 专题库教学形式
     */
    private String topicEducateForm;
    /**
     * 最低分
     */
    private Integer lowscore;
    /**
     * 启用最低分
     */
    private Boolean lowscoreTag;
    /**
     * 最低字数
     */
    private Integer lowword;
    /**
     * 启用最低字数
     */
    private Boolean lowwordTag;
    /**
     * 启用时效限制
     */
    private Boolean timeTag;
    /**
     * 失效天数
     */
    private Integer timeLimit;
    /**
     * 启用问卷有效期撤回时限
     */
    private Boolean revocableTag;
    /**
     * 撤回时限天数（支持小数）
     */
    private Float revocableTimeLimit;

    private Boolean builtIn;

}
