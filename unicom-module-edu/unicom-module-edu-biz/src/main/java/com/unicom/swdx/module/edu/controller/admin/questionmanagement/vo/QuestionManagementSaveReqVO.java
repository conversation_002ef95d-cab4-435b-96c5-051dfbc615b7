package com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo;

import com.unicom.swdx.module.edu.controller.admin.options.vo.OptionsSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 题目管理新增/修改 Request VO")
@Data
public class QuestionManagementSaveReqVO {

    @Schema(description = "主键ID",  example = "9775")
    private Long id;

    @Schema(description = "题干")
    private String stem;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "题目类型(字典)", example = "1")
    private String questionType;

    @Schema(description = "分数")
    private BigDecimal score;

    @Schema(description = "所属类别", example = "10861")
    private Long categoryId;

    @Schema(description = "描述", example = "随便")
    private String description;

    @Schema(description = "创建部门")
    private Long createDept;

    @Schema(description = "创建人")
    private Long creator;

    @Schema(description = "一票否决说明题1是0不是")
    private String oneBallotVetoResult;

    @Schema(description = "是否被添加逻辑(0没有 ，1 有 )")
    private Short isLogic;

    @Schema(description = "选项")
    private List<OptionsSaveReqVO> options;

}
