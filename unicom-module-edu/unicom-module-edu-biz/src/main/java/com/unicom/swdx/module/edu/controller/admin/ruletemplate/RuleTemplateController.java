package com.unicom.swdx.module.edu.controller.admin.ruletemplate;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.*;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateDO;
import com.unicom.swdx.module.edu.convert.ruletemplate.RuleTemplateConvert;
import com.unicom.swdx.module.edu.service.ruletemplate.RuleTemplateService;

@Api(tags = "管理后台 - 考勤规则模版")
@RestController
@RequestMapping("/edu/rule-template")
@Validated
public class RuleTemplateController {

    @Resource
    private RuleTemplateService ruleTemplateService;

    @PostMapping("/create")
    @ApiOperation("创建考勤规则")
    @PreAuthorize("@ss.hasPermission('edu:rule-template:create')")
    public CommonResult<Long> createRuleTemplate(@Valid @RequestBody RuleTemplateCreateReqVO createReqVO) {
        return success(ruleTemplateService.createRuleTemplate(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("编辑考勤规则")
    @PreAuthorize("@ss.hasPermission('edu:rule-template:update')")
    public CommonResult<Boolean> updateRuleTemplate(@Valid @RequestBody RuleTemplateUpdateReqVO updateReqVO) {
        ruleTemplateService.updateRuleTemplate(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除考勤规则")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:rule-template:delete')")
    public CommonResult<Boolean> deleteRuleTemplate(@RequestParam("id") Integer id) {
        ruleTemplateService.deleteRuleTemplate(Long.valueOf(id));
        return success(true);
    }

    @PostMapping("/delete-batch")
    @ApiOperation("批量删除考勤规则")
    @PreAuthorize("@ss.hasPermission('edu:rule-template:delete')")
    public CommonResult<Boolean> deleteRuleTemplateBatch(@Valid @RequestBody RuleBatchDeleteVO ruleBatchDeleteVO) {
        ruleTemplateService.deleteRuleTemplateBatch(ruleBatchDeleteVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得单个考勤规则")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:rule-template:query')")
    public RuleTemplateRespVO getRuleTemplate(@RequestParam("id") Long id) {
        return ruleTemplateService.getRuleTemplate(id);
    }

    @GetMapping("/list")
    @ApiOperation("获得考勤规则")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:rule-template:query')")
    public CommonResult<List<RuleTemplateRespVO>> getRuleTemplateList(@RequestParam("ids") Collection<Integer> ids) {
        List<RuleTemplateDO> list = ruleTemplateService.getRuleTemplateList(ids);
        return success(RuleTemplateConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("考勤规则分页列表")
    @PreAuthorize("@ss.hasPermission('edu:rule-template:query')")
    public CommonResult<PageResult<RuleTemplateRespVO>> getRuleTemplatePage(@Valid RuleTemplatePageReqVO pageVO) {
        PageResult<RuleTemplateDO> pageResult = ruleTemplateService.getRuleTemplatePage(pageVO);
        return success(RuleTemplateConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @ApiOperation("导出考勤规则模版 Excel")
    @PreAuthorize("@ss.hasPermission('edu:rule-template:export')")
    @OperateLog(type = EXPORT)
    public void exportRuleTemplateExcel(@Valid RuleTemplateExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<RuleTemplateDO> list = ruleTemplateService.getRuleTemplateList(exportReqVO);
        // 导出 Excel
        List<RuleTemplateExcelVO> datas = RuleTemplateConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "考勤规则模版.xls", "数据", RuleTemplateExcelVO.class, datas);
    }

    @GetMapping("/list-all")
    @ApiOperation("根据考勤类型获取列表")
    @PreAuthorize("@ss.hasPermission('edu:rule-template:query')")
    public CommonResult<List<RuleTemplateVO>> getRuleTemplateList(@Valid RuleTemplateListVO listVO) {
        List<RuleTemplateVO> list = ruleTemplateService.getRuleTemplateListByType(listVO);
        return success(list);
    }

}
