package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;


@Data
public class CourseChangeUpdateReqVO  {

    @ApiModelProperty(value = "唯一标识", required = true)
    @NotNull(message = "唯一标识不能为空")
    private Long id;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id", required = true)
    private Long courseId;

    /**
     * 教室id
     */
    @ApiModelProperty(value = "教室id", required = true)
    private Long classroomId;

    /**
     * 是否合班授课
     */
    @ApiModelProperty(value = "是否合班授课", required = true)
    private Boolean isMerge;

    /**
     * 是否为部门授课，默认为教师授课
     */
    @ApiModelProperty(value = "是否为部门授课，默认为教师授课")
    private Boolean department;

    /**
     * 授课者字符串，id之间逗号间隔
     */
    @ApiModelProperty(value = "授课者字符串，id之间逗号间隔")
    private String teacherIdString;

    /**
     * 部门授课时的部门Id
     */
    @ApiModelProperty(value = "部门授课时的部门Id")
    private Long deptId;

    /**
     * 是否党政领导讲课，0不是，1是
     */
    @ApiModelProperty(value = "是否党政领导讲课，0不是，1是")
    private Boolean isLeaderLecture;
}
