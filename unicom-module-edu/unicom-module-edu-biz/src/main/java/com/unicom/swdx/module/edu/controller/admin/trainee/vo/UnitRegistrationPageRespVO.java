package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class UnitRegistrationPageRespVO {
    private String id;
    @ApiModelProperty(value = "序号")
    private Long index;
    @ApiModelProperty(value = "学员姓名")
    private String name;
    @ApiModelProperty(value = "学员性别")
    private String sex;
    @ApiModelProperty(value = "学员身份证号")
    private String cardNo;
    @ApiModelProperty(value = "学员手机号")
    private String phone;
    @ApiModelProperty(value = "单位名称")
    private String unitName;
    @ApiModelProperty(value = "文化程度")
    private Integer educationalLevel;
    @ApiModelProperty(value = "职务")
    private String position;
    @ApiModelProperty(value = "职级")
    private Integer jobLevel;
    @ApiModelProperty(value = "政治面貌")
    private Integer politicalIdentity;
    @ApiModelProperty(value = "学员状态")
    private Integer status;
    @ApiModelProperty("报名时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate createTime;

    private Integer classStatus;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty("班级名称")
    private String className;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate updateTime;

    @ApiModelProperty("状态更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime statusUpdateTime;

    private String dropoutReason;

    private String number;
}
