package com.unicom.swdx.module.edu.dal.mysql.leavereport;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.LeaveReportQueryReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

@Mapper
public interface LeaveReportMapper extends BaseMapper<LeaveReportDO> {
    Long countByClassId(@Param("classId") Long classId);

    default PageResult<LeaveReportDO> selectPageQuery(LeaveReportQueryReqVO reqVO) {
        // 构建基础查询条件
        LambdaQueryWrapper<LeaveReportDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LeaveReportDO::getDeleted, false);

        // 按离校报备名称筛选
        if (StringUtils.hasText(reqVO.getName())) {
            queryWrapper.like(LeaveReportDO::getName, reqVO.getName());
        }

        // 按状态筛选
        if (reqVO.getStatus() != null) {
            LocalDateTime now = LocalDateTime.now();
            if (reqVO.getStatus() == 0) { // 过期状态
                queryWrapper.lt(LeaveReportDO::getEndTime, now);
            } else if (reqVO.getStatus() == 1) { // 进行中状态
                queryWrapper.ge(LeaveReportDO::getEndTime, now);
            }
        }

        // 执行分页查询
        Page<LeaveReportDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<LeaveReportDO> resultPage = this.selectPage(page, queryWrapper);

        // 返回结果
        return new PageResult<>(resultPage.getRecords(), resultPage.getTotal());
    }
}
