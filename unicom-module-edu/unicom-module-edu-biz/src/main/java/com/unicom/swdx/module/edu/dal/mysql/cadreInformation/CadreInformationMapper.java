package com.unicom.swdx.module.edu.dal.mysql.cadreInformation;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.common.util.crypt.sm4.SM4Util;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.*;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.TraineeBaseVO;
import com.unicom.swdx.module.edu.dal.dataobject.cadreInformation.CadreInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CadreInformationMapper extends BaseMapperX<CadreInformationDO> {
    default List<CadreInformationDO> getTraineeByCardNo(TraineeBaseVO reqVO) {

        // 对 cardNo 进行加密
        String encryptedCardNo = SM4Util.encrypt(reqVO.getCardNo());
        LambdaQueryWrapper<CadreInformationDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CadreInformationDO::getCardNo, encryptedCardNo);

        wrapper.eq(CadreInformationDO::getUnitId,reqVO.getUnitId());

        // 编辑时需要去除本身
        wrapper.ne(Objects.nonNull(reqVO.getId()), CadreInformationDO::getId, reqVO.getId());

        return selectList(wrapper);
    }

    @TenantIgnore
    default CadreInformationDO selectByCard(String cadreIdCard,Long unitId, Long tenantId) {
        return selectOne(new LambdaQueryWrapper<CadreInformationDO>()
                .eq(CadreInformationDO::getCardNo, cadreIdCard)
                .eq(CadreInformationDO::getUnitId, unitId)
                .eq(CadreInformationDO::getTenantId,tenantId)
                .last("limit 1"));
    }

    Page<PageCadreInformationRespVO> getPage(@Param("objectPage") Page<PageCadreInformationReqVO> objectPage, @Param("reqVO") PageCadreInformationReqVO reqVO);

    /**
     * 根据单位id查询干部信息
     * @param unitId 单位id
     * @return 干部信息列表
     */
    default List<CadreInformationDO> selectListByUnitId(Long unitId) {

        return selectList(new LambdaQueryWrapper<CadreInformationDO>().eq(CadreInformationDO::getUnitId,unitId));
    }


    List<ExportCadreInfoExcelVO> getList(@Param("reqVO") ExportCadreInformationReqVO reqVO);

    Page<CadreInfoDetailRespVO> selectInfoDetailPage(@Param("objectPage") Page<CadreInfoDetailReqVO> objectPage, @Param("reqVO") CadreInfoDetailReqVO reqVO);

    List<ExportCadreInfoDetailExcelVO> selectInfoDetailList(@Param("reqVO") CadreInfoDetailReqVO reqVO);


    default List<CadreInformationDO> getTraineeByCardNoList(Long unitId, List<String> deleteCardList) {
        if (deleteCardList.isEmpty()) {
            return new ArrayList<>();
        }
        // 对 cardNo 进行加密
        List<String> encryptedCardNoList = SM4Util.encryptList(deleteCardList);

        LambdaQueryWrapper<CadreInformationDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CadreInformationDO::getCardNo, encryptedCardNoList);
        wrapper.eq(CadreInformationDO::getUnitId,unitId);
        return selectList(wrapper);
    }

    Page<TraineeInfoPageRespVO> traineeInfoByUnitId(@Param("objectPage") Page<TraineeInfoPageReqVO> objectPage, @Param("reqVO") TraineeInfoPageReqVO reqVO, @Param("classIds") List<Long> classIds);

    List<CadreInformationDO> getCadreInfoByTrainees(@Param("list") List<TraineeDO> list);
}
