package com.unicom.swdx.module.edu.controller.admin.signupunit.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class SignUnitExcelVO {

    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "单位名称")
    @ExcelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "用户名")
    @ExcelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "单位联系人")
    @ExcelProperty(value = "单位联系人")
    private String unitChargePeople;


    @ApiModelProperty(value = "联系电话")
    @ExcelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "单位分类")
    @ExcelProperty(value = "单位分类")
    private String unitClassificationName;

    @ApiModelProperty(value = "状态")
    @ExcelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "排序")
    @ExcelProperty(value = "排序")
    private String sort;

}
