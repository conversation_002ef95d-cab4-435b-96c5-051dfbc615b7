package com.unicom.swdx.module.edu.dal.dataobject.evaluationdetail;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 评估详情 DO
 *
 * <AUTHOR>
 */
@TableName("pg_evaluation_detail")
@KeySequence("pg_evaluation_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationDetailDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 问卷id
     */
    private Long questionnaireId;
    /**
     * 问题id
     */
    private Long questionId;
    /**
     * 问题类型1打分2单选3简答
     */
    private String questionType;
    /**
     * 评卷人id
     */
    private Long studentId;
    /**
     * 打分题得分
     */
    private Long score;
    /**
     * 选择题选项
     */
    private Long optionId;
    /**
     * 简单题内容
     */
    private String content;
    /**
     * 课程id
     */
    private Long classCourseId;

    /**
     * 课程id
     */
    private Long courseId;

}