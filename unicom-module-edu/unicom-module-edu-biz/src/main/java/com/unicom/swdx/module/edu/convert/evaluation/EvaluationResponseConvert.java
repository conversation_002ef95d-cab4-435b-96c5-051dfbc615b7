package com.unicom.swdx.module.edu.convert.evaluation;

import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.TeacherEvaluationResponseVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationPageRespVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationStatsExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.*;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse.EvaluationResponseDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface EvaluationResponseConvert {

    EvaluationResponseConvert INSTANCE = Mappers.getMapper(EvaluationResponseConvert.class);

    List<MyEvaluationExcelVO> convertList(List<MyEvaluationPageRespVO> respVOList);

    List<TeacherEvaluationExcelVO> convertList02(List<TeacherEvaluationPageRespVO> respVOList);

    List<DeptEvaluationExcelVO> convertList03(List<DeptEvaluationPageRespVO> list);

    List<ClassEvaluationStatsExcelVO> convertList04(List<ClassEvaluationStatsPageRespVO> list);

    List<TraineeEvaluationDetailExcelVO> convertList05(List<TraineeEvaluationDetailPageRespVO> list);

    List<TraineeEvaluatedAndUnEvaluatedDetailExcelVO> convertList06(List<TraineeEvaluatedAndUnEvaluatedDetailPageRespVO> list);

    List<TeacherEvaluationResponseVO> convertList07(List<EvaluationResponseDO> list);

    List<DeptEvaluationStatsExcelVO> convertList08(List<DeptEvaluationPageRespVO> list);

    List<MyEvaluationDetailExcelVO> convertList09(List<TeacherEvaluationPageRespVO> respVOList);

    List<TeacherEvaluationPageRespVO> convertList10(List<MyEvaluationPageRespVO> list);

    List<ClassEvaluationDetailExcelVO> convertList11(List<TeacherEvaluationExcelVO> exportTeacherEvaluationExcel);
}
