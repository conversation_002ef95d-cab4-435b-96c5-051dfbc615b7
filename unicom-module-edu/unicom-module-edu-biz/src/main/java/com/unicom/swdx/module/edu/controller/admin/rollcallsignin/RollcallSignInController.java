package com.unicom.swdx.module.edu.controller.admin.rollcallsignin;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo.*;
import com.unicom.swdx.module.edu.service.rollcallsignin.RollcallSignInService;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 大课考勤、点名签到信息")
@RestController
@RequestMapping("/edu/rollcall-sign-in")
@Validated
public class RollcallSignInController {

    @Resource
    private RollcallSignInService rollcallSignInService;

    @Resource
    private TraineeService traineeService;

    @PostMapping("/createRollCallSignIn")
    @ApiOperation("班主任-移动端-发起点名签到")
    @PreAuthorize("@ss.hasPermission('edu:rollcall-sign-in:create')")
    public CommonResult<Long> createRollCallSignIn(HttpServletRequest request, @Valid @RequestBody RollcallSignInCreateReqVO createReqVO) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        return success(rollcallSignInService.createRollCallSignIn(createReqVO));
    }

    @PostMapping("/listRollCallSignIn")
    @ApiOperation("获得点名签到信息列表")
    @PreAuthorize("@ss.hasPermission('edu:rollcall-sign-in:query')")
    public CommonResult<List<RollcallSignInRespVO>> getRollcallSignInList(HttpServletRequest request,@Valid @RequestBody RollcallSignInListReqVO reqVO) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        List<RollcallSignInRespVO> list = rollcallSignInService.getRollcallSignInList(reqVO);
        return success(list);
    }

    @PostMapping("/createLectureAttendance")
    @ApiOperation("班主任-移动端-发起大课考勤")
    @PreAuthorize("@ss.hasPermission('edu:rollcall-sign-in:create')")
    public CommonResult<Long> createLectureAttendance(HttpServletRequest request,@Valid @RequestBody RollcallSignInCreateReqVO createReqVO) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        return success(rollcallSignInService.createLectureAttendance(createReqVO));
    }

    @PostMapping("/withdrawAttendance")
    @ApiOperation("班主任-移动端-撤回大课考勤、点名签到")
    @PreAuthorize("@ss.hasPermission('edu:rollcall-sign-in:update')")
    @ApiImplicitParam(name = "attendanceId", value = "大课考勤、点名签到id", required = true, example = "1", dataTypeClass = Long.class)
    public CommonResult<Boolean> withdrawLectureAttendance(HttpServletRequest request,
                                                        @RequestParam("attendanceId") Long attendanceId) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request, userId);
        rollcallSignInService.withdrawAttendance(attendanceId);
        return success(true);
    }

    @PostMapping("/listLectureAttendance")
    @ApiOperation("获得大课考勤信息列表")
    @PreAuthorize("@ss.hasPermission('edu:rollcall-sign-in:query')")
    public CommonResult<List<RollcallSignInRespVO>> getLectureAttendanceList(HttpServletRequest request,@Valid @RequestBody RollcallSignInListReqVO reqVO) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        List<RollcallSignInRespVO> list = rollcallSignInService.getLectureAttendanceList(reqVO);
        return success(list);
    }

    @PostMapping("/endLectureAttendance")
    @ApiOperation("结束大课考勤、点名签到")
    @ApiImplicitParam(name = "id", value = "大课考勤、点名签到id", required = true, example = "1", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:rollcall-sign-in:update')")
    public CommonResult<Integer> endLectureAttendance(HttpServletRequest request,@RequestParam("id") Long id) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        Integer res = rollcallSignInService.endLectureAttendance(id);
        return success(res);
    }

    @PostMapping("/updateAddress")
    @ApiOperation("更新未结束的大课考勤、点名签到打卡地址")
    @PreAuthorize("@ss.hasPermission('edu:rollcall-sign-in:update')")
    public CommonResult<Boolean> updateAddress(HttpServletRequest request,@RequestBody RollcallSignInUpdateAddressReqVO updateReqVO) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        rollcallSignInService.updateAddress(updateReqVO);
        return success(true);
    }

}
