package com.unicom.swdx.module.edu.service.rollcallsignin;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassRuleClockingInVO;
import com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.dto.RollcallCommonLocationsAddDTO;
import com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo.*;
import com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo.RuleTemplateRespVO;
import com.unicom.swdx.module.edu.convert.rollcallsignin.RollcallSignInConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallrecord.RollcallRecordDO;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallsignin.RollcallSignInDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.dal.mysql.rollcallrecord.RollcallRecordMapper;
import com.unicom.swdx.module.edu.dal.mysql.rollcallsignin.RollcallSignInMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.dal.mysql.traineeleave.TraineeLeaveMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.attendance.AttendanceStatusEnum;
import com.unicom.swdx.module.edu.enums.attendance.AttendanceTypeEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.ClockStatusEnum;
import com.unicom.swdx.module.edu.enums.rollcallsignin.SignInStatusEnum;
import com.unicom.swdx.module.edu.enums.rollcallsignin.SignInTypeEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.enums.traineeleave.TraineeLeaveStatus;
import com.unicom.swdx.module.edu.service.evaluationdetail.EvaluationDetailService;
import com.unicom.swdx.module.edu.service.rollcallcommonlocations.RollcallCommonLocationsService;
import com.unicom.swdx.module.edu.service.ruletemplate.RuleTemplateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 大课考勤、点名签到信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RollcallSignInServiceImpl extends ServiceImpl<RollcallSignInMapper, RollcallSignInDO> implements RollcallSignInService {

    @Resource
    private RollcallSignInMapper rollcallSignInMapper;

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private ClassCourseMapper classCourseMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private RollcallRecordMapper rollcallRecordMapper;

    @Resource
    private ClockInInfoMapper clockInInfoMapper;

    @Resource
    private TraineeLeaveMapper traineeLeaveMapper;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;

    @Resource
    private RollcallCommonLocationsService rollcallCommonLocationsService;

    @Resource
    private RuleTemplateService ruleTemplateService;

    @Resource
    private EvaluationDetailService evaluationDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRollCallSignIn(RollcallSignInCreateReqVO createReqVO) {
        // 根据用户获取教师信息
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectByUserId(getLoginUserId());
        if (Objects.isNull(teacherInformationDO)) {
            throw exception(TEACHER_NOT_EXISTS);
        }

        // 设置创建类型为点名签到
        createReqVO.setType(SignInTypeEnum.ROLL_CALL_SIGN_IN.getStatus());
        // 校验字段
        validateRollcallSignInFields(createReqVO);
        // 校验班级存在
        validateClassIdExist(createReqVO.getClassId());
        // 校验当前班级是否存在正在进行中的点名签到
        RollcallSignInDO currentRollcallSignIn = rollcallSignInMapper.selectOneByClassIdAndTypeAndCurrentTime(createReqVO.getClassId(),
                SignInTypeEnum.ROLL_CALL_SIGN_IN.getStatus(), LocalDateTime.now());
        if (Objects.nonNull(currentRollcallSignIn)) {
            throw exception(ROLLCALL_SIGN_IN_CLASS_HAS_CURRENT_SIGN_IN);
        }
        // 插入点名签到信息
        RollcallSignInDO rollCallSignInDO = RollcallSignInConvert.INSTANCE.convert(createReqVO);
        rollcallSignInMapper.insert(rollCallSignInDO);

        // 查询班级所有(已报到)学员
        List<Integer> traineeStatusList = Collections.singletonList(TraineeStatusEnum.REPORTED.getStatus());
        List<TraineeDO> traineeDOList = traineeMapper.selectListByClassIdAndStatus(createReqVO.getClassId(),
                traineeStatusList);
        // 初始化所有学员签到状态为未签到
        List<RollcallRecordDO> rollcallRecordDOList = traineeDOList.stream().map(item -> {
            RollcallRecordDO rollcallRecordDO = new RollcallRecordDO();
            rollcallRecordDO.setRollcallId(rollCallSignInDO.getId());
            rollcallRecordDO.setTraineeId(item.getId());
            rollcallRecordDO.setStatus(AttendanceStatusEnum.NOT_ARRIVE.getStatus());
            return rollcallRecordDO;
        }).collect(Collectors.toList());

        if (!rollcallRecordDOList.isEmpty()) {
            rollcallRecordMapper.insertBatch(rollcallRecordDOList);
        }
        // 添加历史地点
        RollcallCommonLocationsAddDTO addDTO = RollcallSignInConvert.INSTANCE.convert2(rollCallSignInDO);
        addDTO.setTeacherId(teacherInformationDO.getId());
        addDTO.setType(SignInTypeEnum.ROLL_CALL_SIGN_IN.getStatus());
        rollcallCommonLocationsService.addOne(addDTO);
        // 返回
        return rollCallSignInDO.getId();
    }

    /**
     * 发送大课考勤签到
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLectureAttendance(RollcallSignInCreateReqVO createReqVO) {
        // 根据用户获取教师信息
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectByUserId(getLoginUserId());
        if (Objects.isNull(teacherInformationDO)) {
            throw exception(TEACHER_NOT_EXISTS);
        }
        // 设置创建类型为大课考勤
        createReqVO.setType(SignInTypeEnum.LECTURE_ATTENDANCE.getStatus());
        // 校验字段
        validateRollcallSignInFields(createReqVO);
        // 校验班级存在
        validateClassIdExist(createReqVO.getClassId());
        // 校验课表存在
        ClassCourseDO classCourseDO = valiateClassCourseIdExist(createReqVO.getClassCourseId());

        LocalDateTime now = LocalDateTime.now();
        // 查询该班级是否有这堂课的其他正在进行、未开始的大课考勤
        List<RollcallSignInDO> rollcallSignInDOList = rollcallSignInMapper.selectListByClassIdAndClassCourseId(createReqVO.getClassCourseId(),
                createReqVO.getClassId(), now);
        if (!rollcallSignInDOList.isEmpty()) {
            throw exception(ROLLCALL_SIGN_IN_CLASS_HAS_CURRENT_LECTURE_ATTENDANCE);
        }

        // 查询该班级的其他与该次发起打卡时间段有重叠，且正在进行中的大课考勤
        List<RollcallSignInDO> overlapList = rollcallSignInMapper.selectListByCheckOverlap(createReqVO.getClassId(),
                createReqVO.getCheckStartTime(), createReqVO.getCheckEndTime(), now);
        if (!overlapList.isEmpty()) {
            List<String> overlapDateTimeList = overlapList.stream().map(item -> "【"+DateUtils.format(item.getCheckStartTime(), FORMAT_HOUR_MINUTE) +
                    "-" + DateUtils.format(item.getCheckEndTime(), FORMAT_HOUR_MINUTE) + "】").collect(Collectors.toList());
            throw exception(ROLLCALL_SIGN_IN_CLASS_IS_OVERLAP, String.join("\n", overlapDateTimeList));
        }

        // 插入大课考勤信息
        RollcallSignInDO rollcallSignInDO = RollcallSignInConvert.INSTANCE.convert(createReqVO);
        rollcallSignInMapper.insert(rollcallSignInDO);
        // 查询班级所有(已报到)学员
        List<Integer> traineeStatusList = Collections.singletonList(TraineeStatusEnum.REPORTED.getStatus());
        List<TraineeDO> traineeDOList = traineeMapper.selectListByClassIdAndStatus(createReqVO.getClassId(),
                traineeStatusList);

        // 获取该节课的到课考勤时间信息
        RuleTemplateRespVO courseRuleTemplate = ruleTemplateService.getAttendanceCheckRuleTemplateByClassId(createReqVO.getClassId());
        LocalDateTime checkStartTime = classCourseDO.getBeginTime().minusMinutes(courseRuleTemplate
                .getBeforeClassTime().longValue());
        LocalDateTime checkEndTime = classCourseDO.getBeginTime().plusMinutes(courseRuleTemplate
                .getAfterClassTime().longValue());

        // 获取请假通过、课程考勤开始时间在请假时间端内的学员请假记录
        List<Integer> leaveStatusList = Collections.singletonList(TraineeLeaveStatus.OK.getCode());
        List<Long> traineeIdList = traineeDOList.stream().map(TraineeDO::getId).collect(Collectors.toList());
        List<TraineeLeaveDO> traineeLeaveDOList = traineeLeaveMapper.selectListByTraineeIdListAndStartTime(traineeIdList,
                checkStartTime, leaveStatusList);
        // traineeId->请假记录字典
        Map<Long, TraineeLeaveDO> traineeIdToLeaveDOMap = traineeLeaveDOList.stream()
                .collect(Collectors.toMap(TraineeLeaveDO::getTraineeId, o -> o, (v1, v2) -> v2));
        // 考勤日期为上课日期
        LocalDate date = DateUtils.parseLocalDate(classCourseDO.getDate(), DateUtils.FORMAT_YEAR_MONTH_DAY);
        // 初始化所有学员签到状态为未签到
        List<ClockInInfoDO> clockInInfoDOList = traineeDOList.stream().map(item -> {
            ClockInInfoDO clockInInfoDO = new ClockInInfoDO();
            clockInInfoDO.setClassId(createReqVO.getClassId());
            clockInInfoDO.setTraineeId(item.getId());
            clockInInfoDO.setClassCourseId(createReqVO.getClassCourseId());
            // 到课考勤
            clockInInfoDO.setType(AttendanceTypeEnum.CLASS_ATTENDANCE.getStatus());
            TraineeLeaveDO traineeLeaveDO = traineeIdToLeaveDOMap.get(item.getId());
            // 打卡状态
            if (Objects.nonNull(traineeLeaveDO)) {
                clockInInfoDO.setTraineeStatus(AttendanceStatusEnum.LEAVE.getStatus());
                clockInInfoDO.setLeaveType(traineeLeaveDO.getLeaveType());
            } else {
                clockInInfoDO.setTraineeStatus(AttendanceStatusEnum.NOT_ARRIVE.getStatus());
            }
            // 考勤日期
            clockInInfoDO.setDate(date);
            // 请假时间判断冗余时间段字段为到课考勤时间段
            clockInInfoDO.setCheckBeginTime(checkStartTime);
            clockInInfoDO.setCheckEndTime(checkEndTime);
            return clockInInfoDO;
        }).collect(Collectors.toList());

        // 查询该班级的课程已经存在的考勤记录 -- 插入考勤记录时不改变原有考勤状态
        List<ClockInInfoDO> existsClockInInfoDOList = clockInInfoMapper.selectListByClassIdAndClassCourseId(createReqVO.getClassId(),
                createReqVO.getClassCourseId());
        List<ClockInInfoDO> insertClockInInfoDOList = new ArrayList<>();
        if (!existsClockInInfoDOList.isEmpty()) {
            // 剔除已存在的学员考勤记录
            insertClockInInfoDOList = clockInInfoDOList.stream().filter(o ->
                    existsClockInInfoDOList.stream().noneMatch(e ->
                            e.getTraineeId().equals(o.getTraineeId())
                        && e.getClassCourseId().equals(o.getClassCourseId())
                        && e.getClassId().equals(o.getClassId())
                        && e.getType().equals(o.getType()))
            ).collect(Collectors.toList());
        }else {
            insertClockInInfoDOList = clockInInfoDOList;
        }

        if (!clockInInfoDOList.isEmpty()) {
            clockInInfoMapper.insertBatch(insertClockInInfoDOList);
        }
        // 添加历史地点
        RollcallCommonLocationsAddDTO addDTO = RollcallSignInConvert.INSTANCE.convert2(rollcallSignInDO);
        addDTO.setTeacherId(teacherInformationDO.getId());
        addDTO.setType(SignInTypeEnum.LECTURE_ATTENDANCE.getStatus());
        rollcallCommonLocationsService.addOne(addDTO);
        return rollcallSignInDO.getId();
    }

    /**
     * 更新未结束的大课考勤、点名签到打卡地址
     *
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateAddress(RollcallSignInUpdateAddressReqVO updateReqVO) {
        // 根据用户获取教师信息
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectByUserId(getLoginUserId());
        if (Objects.isNull(teacherInformationDO)) {
            throw exception(TEACHER_NOT_EXISTS);
        }
        RollcallSignInDO rollcallSignInDO = rollcallSignInMapper.selectById(updateReqVO.getId());
        if (Objects.isNull(rollcallSignInDO)) {
            throw exception(ROLLCALL_SIGN_IN_NOT_EXISTS);
        }
        LocalDateTime localDateTime = LocalDateTime.now();
        // 考勤已结束
        if (Boolean.TRUE.equals(rollcallSignInDO.getEnded()) ||
                localDateTime.isAfter(rollcallSignInDO.getCheckEndTime())) {
            throw exception(ROLLCALL_SIGN_IN_ENDED);
        }
        rollcallSignInDO.setLatitude(updateReqVO.getLatitude());
        rollcallSignInDO.setLongitude(updateReqVO.getLongitude());
        rollcallSignInDO.setAddress(updateReqVO.getAddress());
        rollcallSignInDO.setRadius(updateReqVO.getRadius());
        rollcallSignInDO.setUpdateTime(localDateTime);
        rollcallSignInMapper.updateById(rollcallSignInDO);
        // 添加历史地点
        RollcallCommonLocationsAddDTO addDTO = RollcallSignInConvert.INSTANCE.convert2(rollcallSignInDO);
        addDTO.setTeacherId(teacherInformationDO.getId());
        addDTO.setType(rollcallSignInDO.getType());
        rollcallCommonLocationsService.addOne(addDTO);
    }

    /**
     * 校验课表是否存在
     *
     * @param classCourseId 课表编号
     */
    public ClassCourseDO valiateClassCourseIdExist(Long classCourseId) {
        ClassCourseDO classCourseDO = classCourseMapper.selectById(classCourseId);
        if (Objects.isNull(classCourseDO)) {
            throw exception(ROLLCALL_LECTURE_ATTENDANCE_CLASS_COURSE_NOT_EXISTS);
        }
        return classCourseDO;
    }

    /**
     * 获取点名签到列表
     *
     * @param reqVO 请求查询参数
     * @return 点名签到列表
     */
    @Override
    public List<RollcallSignInRespVO> getRollcallSignInList(RollcallSignInListReqVO reqVO) {
        // 点名签到列表查询
        return rollcallSignInMapper.selectRollcallSignInListByReqVO(reqVO,
                LocalDateTime.now());
    }

    /**
     * 获取大课考勤列表
     *
     * @param reqVO 请求查询参数
     * @return 大课考勤列表
     */
    @Override
    public List<RollcallSignInRespVO> getLectureAttendanceList(RollcallSignInListReqVO reqVO) {
        List<RollcallSignInRespVO> list = rollcallSignInMapper.selectLectureAttendanceListByReqVO(reqVO,
                LocalDateTime.now());

        List<RollcallSignInRespVO> signInRespVOS = list.stream().filter(item -> Objects.nonNull(item.getCourseId()))
                .collect(Collectors.toList());
        signInRespVOS.forEach(item -> {
            // 设置上课时间段
            item.setClassStartTimeStr(DateUtils.format(item.getClassStartTime(), DateUtils.FORMAT_HOUR_MINUTE));
            item.setClassEndTimeStr(DateUtils.format(item.getClassEndTime(), DateUtils.FORMAT_HOUR_MINUTE));
        });
        return list;
    }

    /**
     * 结束大课考勤、点名签到
     *
     * @param id 大课考勤、点名签到编号
     * @return 结束结果
     */
    @Override
    public Integer endLectureAttendance(Long id) {
        RollcallSignInDO rollcallSignInDO = validateRollcallSignInExists(id);
        rollcallSignInDO.setEnded(true);
        return rollcallSignInMapper.updateById(rollcallSignInDO);
    }

    /**
     * 撤回一个大课考勤或者是点名签到
     *
     * @param attendanceId 大课考勤或者是点名签到id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawAttendance(Long attendanceId) {
        RollcallSignInDO rollcallSignInDO = validateRollcallSignInExists(attendanceId);
        // 撤回逻辑  手动结束+标记撤回
        rollcallSignInDO.setEnded(true);
        rollcallSignInDO.setStatus(SignInStatusEnum.WITHDRAW.getStatus());
        rollcallSignInMapper.updateById(rollcallSignInDO);
        // 如果是大课考勤 学员考勤记录重置为未签到 + 撤回已下发的问卷
        if (SignInTypeEnum.LECTURE_ATTENDANCE.getStatus().equals(rollcallSignInDO.getType())){
            // 学员大课考勤签到时会记录大课考勤id 且 请假的学员打不上卡
            // 1.根据大课考勤id查询通过大课考勤签到的考勤记录 都重置为未签到
            List<ClockInInfoDO> clockInInfoDOList = clockInInfoMapper.selectListByLargeAttendanceId(attendanceId);
            List<ClockInInfoDO> updateClockInInfoDOList = new ArrayList<>();
            for (ClockInInfoDO clockInInfoDO : clockInInfoDOList) {
                ClockInInfoDO updateClockInInfoDO = new ClockInInfoDO();
                updateClockInInfoDO.setId(clockInInfoDO.getId());
                updateClockInInfoDO.setTraineeStatus(ClockStatusEnum.NOT_DONE.getCode());
                // 重置大课考勤标记
                updateClockInInfoDO.setLargeAttendanceId(null);
                updateClockInInfoDOList.add(updateClockInInfoDO);
            }
            if (!updateClockInInfoDOList.isEmpty()){
                clockInInfoMapper.updateBatch(updateClockInInfoDOList);
            }

            // 2.撤回学员对应课程的问卷
            // 组装<学员id, 排课id>键值对列表 用于批量删除
            List<Pair<Long, Long>> traineeIdAndClassCourseIdPairs = clockInInfoDOList.stream()
                    .map(o -> Pair.of(o.getTraineeId(), o.getClassCourseId()))
                    .collect(Collectors.toList());
            evaluationDetailService.batchRevokeQuestionnaireAnyHandle(traineeIdAndClassCourseIdPairs);
        }
    }

    private RollcallSignInDO validateRollcallSignInExists(Long id) {
        RollcallSignInDO rollcallSignInDO = rollcallSignInMapper.selectById(id);
        if (Objects.isNull(rollcallSignInDO)) {
            throw exception(ROLLCALL_SIGN_IN_NOT_EXISTS);
        }
        rollcallSignInDO.setUpdateTime(null);
        rollcallSignInDO.setUpdater(null);
        return rollcallSignInDO;
    }

    /**
     * 校验班级是否存在
     *
     * @param classId 班级编号
     */
    private void validateClassIdExist(Long classId) {
        ClassManagementDO classManagementDO = classManagementMapper.selectById(classId);
        if (Objects.isNull(classManagementDO)) {
            throw exception(ROLLCALL_SIGN_IN_CLASS_NOT_EXISTS);
        }
    }

    /**
     * 校验请求参数字段
     *
     * @param baseVO 请求信息
     */
    private void validateRollcallSignInFields(RollcallSignInBaseVO baseVO) {
        if (SignInTypeEnum.ROLL_CALL_SIGN_IN.getStatus().equals(baseVO.getType())) {
            // 点名签到校验
            if (StringUtils.isBlank(baseVO.getTitle())) {
                throw exception(ROLLCALL_SIGN_IN_TITLE_IS_BLANK);
            }
        } else if (SignInTypeEnum.LECTURE_ATTENDANCE.getStatus().equals(baseVO.getType()) && (Objects.isNull(baseVO.getClassCourseId()))) {
            throw exception(ROLLCALL_SIGN_IN_CLASS_COURSE_ID_IS_NULL);

        }
        // 打卡开始时间不能大于结束时间
        if (baseVO.getCheckStartTime().isAfter(baseVO.getCheckEndTime())) {
            throw exception(ROLLCALL_SIGN_IN_CHECK_START_TIME_IS_AFTER_END_TIME);
        }
    }

}
