package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - 我的评估-评分详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MyEvaluationDetailPageReqVO extends PageParam {

    @ApiModelProperty(value = "排课ID", example = "1")
    private Long classCourseId;

    @ApiModelProperty(value = "课程ID", example = "1")
    private Long courseId;

    @ApiModelProperty(value = "学员姓名 （用于学员评估详情）")
    private String traineeName;
}