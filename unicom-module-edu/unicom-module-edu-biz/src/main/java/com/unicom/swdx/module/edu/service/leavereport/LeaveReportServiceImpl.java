package com.unicom.swdx.module.edu.service.leavereport;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoRespVO;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.*;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxGroupMsg;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgSendReqVO;
import com.unicom.swdx.module.edu.convert.leavereport.LeaveReportConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDO;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDetailDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.leavereport.LeaveReportDetailMapper;
import com.unicom.swdx.module.edu.dal.mysql.leavereport.LeaveReportMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.classmanage.ClassManageStatus;
import com.unicom.swdx.module.edu.enums.xcxmsg.XcxMsgSendType;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementService;
import com.unicom.swdx.module.edu.service.teacherinformation.TeacherInformationService;
import com.unicom.swdx.module.edu.service.xcxMsg.XcxMsgService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.boot.autoconfigure.klock.model.LockType;
import org.springframework.boot.autoconfigure.klock.model.ReleaseTimeoutStrategy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;

import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import com.unicom.swdx.framework.common.pojo.PageResult;

@Service
@Validated
public class LeaveReportServiceImpl implements LeaveReportService{

    public static final String REMARK = "点击查看离校报备";
    public static final String CONTENT = "学员你好，温馨提醒您离校报备未填写";
    private static final String PAGE_URI_PREFIX = "pages/stu/departureReport/add";

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private LeaveReportMapper leaveReportMapper;

    @Resource
    private LeaveReportDetailMapper leaveReportDetailMapper;

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private ClassManagementService classManagementService;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private TeacherInformationService teacherInformationService;

    @Resource
    private XcxMsgService xcxMsgService;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public Long createLeaveReport(LeaveReportCreateReqVO createReqVO) {


        Long loginUserId = getLoginUserId();
        Long classId = createReqVO.getClassId();

        // 3. 分布式锁，加锁
        String lockKey = "lock:createLeaveReport:" + loginUserId;
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        try {




            LeaveReportDO leaveReportDO = LeaveReportConvert.INSTANCE.convert(createReqVO);

            Long teacherId = teacherInformationService.getTeacherId(loginUserId);

            leaveReportDO.setTeacherId(teacherId);
            Long count = leaveReportMapper.countByClassId(classId) + 1L;

            leaveReportDO.setName("第" + count + "次离校申请");

            leaveReportMapper.insert(leaveReportDO);
            return leaveReportDO.getId();
        } finally {
            lock.unlock();
        }
    }

    @Override
    public TeacherLeaveReportLeaveVO getLeaveReportDetail(Long leaveId, Integer fillStatus) {
        TeacherLeaveReportLeaveVO result = new TeacherLeaveReportLeaveVO();
        LeaveReportDO leaveReportDO = leaveReportMapper.selectById(leaveId);
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeDO::getClassId, leaveReportDO.getClassId());
        wrapper.eq(TraineeDO::getDeleted, false);
        List<TraineeDO> traineeList = traineeMapper.selectList(wrapper);

        LambdaQueryWrapper<LeaveReportDetailDO> leaveWrapper = new LambdaQueryWrapper<>();
        leaveWrapper.eq(LeaveReportDetailDO::getLeaveId, leaveId);
        leaveWrapper.eq(LeaveReportDetailDO::getDeleted, false);
        leaveWrapper.eq(LeaveReportDetailDO::getLeaveStatus, true);
        List<LeaveReportDetailDO> leave = leaveReportDetailMapper.selectList(leaveWrapper);

        LambdaQueryWrapper<LeaveReportDetailDO> stayWrapper = new LambdaQueryWrapper<>();
        stayWrapper.eq(LeaveReportDetailDO::getLeaveId, leaveId);
        stayWrapper.eq(LeaveReportDetailDO::getDeleted, false);
        stayWrapper.eq(LeaveReportDetailDO::getLeaveStatus, false);
        List<LeaveReportDetailDO> stay = leaveReportDetailMapper.selectList(stayWrapper);

        result.setStay(stay.size());
        result.setLeave(leave.size());
        result.setUnfilled(traineeList.size() - stay.size() - leave.size());

        List<Long> leaveIdList = leave.stream().map(LeaveReportDetailDO::getStudentId).collect(Collectors.toList());
        List<Long> stayIdList = stay.stream().map(LeaveReportDetailDO::getStudentId).collect(Collectors.toList());
        // fillStatus : 0 未填报，1离校，2不离校
        if (fillStatus == 0) {
            traineeList = traineeList.stream().filter(trainee -> !leaveIdList.contains(trainee.getId())).collect(Collectors.toList());
            traineeList = traineeList.stream().filter(trainee -> !stayIdList.contains(trainee.getId())).collect(Collectors.toList());
        } else if (fillStatus == 1) {
            traineeList = traineeList.stream().filter(trainee -> leaveIdList.contains(trainee.getId())).collect(Collectors.toList());
        } else if (fillStatus == 2) {
            traineeList = traineeList.stream().filter(trainee -> stayIdList.contains(trainee.getId())).collect(Collectors.toList());
        }

        result.setTrainee(traineeList);

        return result;
    }

    @Override
    public TeacherLeaveReportLeavePageVO getLeaveReportDetailPage(Long leaveId, Integer fillStatus, Integer pageNo, Integer pageSize) {
        TeacherLeaveReportLeavePageVO result = new TeacherLeaveReportLeavePageVO();

        LeaveReportDO leaveReportDO = leaveReportMapper.selectById(leaveId);
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeDO::getClassId, leaveReportDO.getClassId());
        wrapper.eq(TraineeDO::getDeleted, false);

        // 获取符合条件的总记录数（total）
        Long total = traineeMapper.selectCount(wrapper);

        result.setTotal(total);

        int offset = (pageNo - 1) * pageSize;
        // 使用 MyBatis 提供的方式来手动加上 LIMIT 和 OFFSET
        wrapper.last("LIMIT " + offset + ", " + pageSize); // 对应 SQL 的 LIMIT

        List<TraineeDO> traineeList = traineeMapper.selectList(wrapper);







        LambdaQueryWrapper<LeaveReportDetailDO> leaveWrapper = new LambdaQueryWrapper<>();
        leaveWrapper.eq(LeaveReportDetailDO::getLeaveId, leaveId);
        leaveWrapper.eq(LeaveReportDetailDO::getDeleted, false);
        leaveWrapper.eq(LeaveReportDetailDO::getLeaveStatus, true);
        List<LeaveReportDetailDO> leave = leaveReportDetailMapper.selectList(leaveWrapper);

        LambdaQueryWrapper<LeaveReportDetailDO> stayWrapper = new LambdaQueryWrapper<>();
        stayWrapper.eq(LeaveReportDetailDO::getLeaveId, leaveId);
        stayWrapper.eq(LeaveReportDetailDO::getDeleted, false);
        stayWrapper.eq(LeaveReportDetailDO::getLeaveStatus, false);
        List<LeaveReportDetailDO> stay = leaveReportDetailMapper.selectList(stayWrapper);

        result.setStay(stay.size());
        result.setLeave(leave.size());
        result.setUnfilled(traineeList.size() - stay.size() - leave.size());

        List<Long> leaveIdList = leave.stream().map(LeaveReportDetailDO::getStudentId).collect(Collectors.toList());
        List<Long> stayIdList = stay.stream().map(LeaveReportDetailDO::getStudentId).collect(Collectors.toList());
        // fillStatus : 0 未填报，1离校，2不离校
        if (fillStatus == 0) {
            traineeList = traineeList.stream().filter(trainee -> !leaveIdList.contains(trainee.getId())).collect(Collectors.toList());
            traineeList = traineeList.stream().filter(trainee -> !stayIdList.contains(trainee.getId())).collect(Collectors.toList());
        } else if (fillStatus == 1) {
            traineeList = traineeList.stream().filter(trainee -> leaveIdList.contains(trainee.getId())).collect(Collectors.toList());
        } else if (fillStatus == 2) {
            traineeList = traineeList.stream().filter(trainee -> stayIdList.contains(trainee.getId())).collect(Collectors.toList());
        }


        List<LeaveReportPageDetailVO> detailVOList = new ArrayList<>();

        traineeList.forEach(it->{

            LeaveReportPageDetailVO item = new LeaveReportPageDetailVO();


            item.setTraineeDO(it);
            item.setLeaveReportDetailDO(leave.stream().filter(detail -> detail.getStudentId().equals(it.getId())).findFirst().orElse(null));
            detailVOList.add(item);


        });



        result.setTrainee(detailVOList);

        return result;
    }

    @Override
    public List<LeaveReportRespVO> getLeaveReportList(Long classId) {
        LambdaQueryWrapper<LeaveReportDO> leaveWrapper = new LambdaQueryWrapper<>();
        leaveWrapper.eq(LeaveReportDO::getClassId, classId);
        leaveWrapper.eq(LeaveReportDO::getDeleted, false);

        List<LeaveReportDO> leaveReportList = leaveReportMapper.selectList(leaveWrapper);
        List<LeaveReportRespVO> result = LeaveReportConvert.INSTANCE.convertList(leaveReportList);

        // 初始化必要的字段，防止空指针异常
        if (result != null) {
            result.forEach(item -> {
                if (item != null) {
                    // 确保基本字段不为null
                    if (item.getLeaveCount() == null)
                        item.setLeaveCount(0L);
                    if (item.getStayCount() == null)
                        item.setStayCount(0L);
                    if (item.getUnfilled() == null)
                        item.setUnfilled(0L);
                    if (item.getStatus() == null)
                        item.setStatus(0); // 默认为过期状态
                }
            });
        } else {
            return Collections.emptyList();
        }

        Long classCount = traineeMapper.selectCount(TraineeDO::getClassId, classId);

        LambdaQueryWrapper<LeaveReportDetailDO> leaveStudentWrapper = new LambdaQueryWrapper<>();
        leaveStudentWrapper.eq(LeaveReportDetailDO::getDeleted, false);
        leaveStudentWrapper.eq(LeaveReportDetailDO::getLeaveStatus, true);
        List<LeaveReportDetailDO> leave = leaveReportDetailMapper.selectList(leaveStudentWrapper);

        LambdaQueryWrapper<LeaveReportDetailDO> stayWrapper = new LambdaQueryWrapper<>();
        stayWrapper.eq(LeaveReportDetailDO::getDeleted, false);
        stayWrapper.eq(LeaveReportDetailDO::getLeaveStatus, false);
        List<LeaveReportDetailDO> stay = leaveReportDetailMapper.selectList(stayWrapper);

        LocalDateTime now = LocalDateTime.now();

        result.forEach(leaveReport -> {
            if (leaveReport == null) {
                return;
            }

            Long leaveCount = 0L;
            Long stayCount = 0L;

            if (!leave.isEmpty() && leaveReport.getId() != null) {
                leaveCount = leave
                        .stream()
                        .filter(leaveReportDetail -> leaveReportDetail != null &&
                                leaveReportDetail.getLeaveId() != null &&
                                Objects.equals(leaveReportDetail.getLeaveId(), leaveReport.getId()))
                        .count();
            }

            if (!stay.isEmpty() && leaveReport.getId() != null) {
                stayCount = stay
                        .stream()
                        .filter(leaveReportDetail -> leaveReportDetail != null &&
                                leaveReportDetail.getLeaveId() != null &&
                                Objects.equals(leaveReportDetail.getLeaveId(), leaveReport.getId()))
                        .count();
            }

            if (leaveReport.getEndTime() != null) {
                LocalDateTime endTime = leaveReport.getEndTime();
                if (endTime.isBefore(now)) {
                    leaveReport.setStatus(0);
                } else {
                    leaveReport.setStatus(1);
                }
            } else {
                leaveReport.setStatus(0); // 默认为过期状态
            }

            leaveReport.setUnfilled(classCount - leaveCount - stayCount);
            leaveReport.setLeaveCount(leaveCount);
            leaveReport.setStayCount(stayCount);
        });

        return result;
    }

    @Override
    public List<LeaveReportRespVO> getLeaveReportList(LeaveReportQueryReqVO queryReqVO) {
        // 1. 构建基础查询条件
        LambdaQueryWrapper<LeaveReportDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LeaveReportDO::getDeleted, false);

        // 2. 添加筛选条件
        // 2.1 按离校报备名称筛选
        if (StringUtils.hasText(queryReqVO.getName())) {
            queryWrapper.like(LeaveReportDO::getName, queryReqVO.getName());
        }

        // 2.2 按状态筛选
        if (queryReqVO.getStatus() != null) {
            LocalDateTime now = LocalDateTime.now();
            if (queryReqVO.getStatus() == 0) { // 过期状态
                queryWrapper.lt(LeaveReportDO::getEndTime, now);
            } else if (queryReqVO.getStatus() == 1) { // 进行中状态
                queryWrapper.ge(LeaveReportDO::getEndTime, now);
            }
        }

        // 2.3 查询所有符合条件的离校报备
        List<LeaveReportDO> leaveReportList = leaveReportMapper.selectList(queryWrapper);
        List<LeaveReportRespVO> result = LeaveReportConvert.INSTANCE.convertList(leaveReportList);

        // 初始化必要的字段，防止空指针异常
        if (result != null) {
            result.forEach(item -> {
                if (item != null) {
                    // 确保基本字段不为null
                    if (item.getLeaveCount() == null)
                        item.setLeaveCount(0L);
                    if (item.getStayCount() == null)
                        item.setStayCount(0L);
                    if (item.getUnfilled() == null)
                        item.setUnfilled(0L);
                    if (item.getStatus() == null)
                        item.setStatus(0); // 默认为过期状态
                }
            });
        }

        if (result == null || result.isEmpty()) {
            return result != null ? result : Collections.emptyList();
        }

        // 3. 查询班级信息，用于按班级名称和班主任筛选
        List<Long> classIds = result.stream()
                .map(LeaveReportRespVO::getClassId)
                .distinct()
                .collect(Collectors.toList());

        LambdaQueryWrapper<ClassManagementDO> classWrapper = new LambdaQueryWrapper<>();
        classWrapper.in(ClassManagementDO::getId, classIds);
        // 3.1 按校区筛选
        if (queryReqVO.getCampus() != null) {
            classWrapper.eq(ClassManagementDO::getCampus, queryReqVO.getCampus());
        }
        List<ClassManagementDO> classes = classManagementMapper.selectList(classWrapper);

        // 3.2 填充班主任姓名
        fillClassTeacherNames(classes);

        // 4. 应用班级名称、班主任和班级状态筛选
        boolean hasClassNameFilter = StringUtils.hasText(queryReqVO.getClassName());
        boolean hasTeacherNameFilter = StringUtils.hasText(queryReqVO.getTeacherName());
        boolean hasCampusFilter = queryReqVO.getCampus() != null;
        boolean hasClassStatusFilter = queryReqVO.getClassStatus() != null;

        if (hasClassNameFilter || hasTeacherNameFilter || hasCampusFilter || hasClassStatusFilter) {
            // 4.1 过滤符合条件的班级ID
            List<Long> filteredClassIds = classes.stream()
                    .filter(classDO -> {
                        boolean matchesClassName = !hasClassNameFilter ||
                                (classDO.getClassName() != null &&
                                        classDO.getClassName().contains(queryReqVO.getClassName()));

                        boolean matchesTeacherName = !hasTeacherNameFilter ||
                                (classDO.getClassTeacherLeadName() != null &&
                                        classDO.getClassTeacherLeadName().contains(queryReqVO.getTeacherName()));

                        // 校区过滤在前面的SQL中已经处理，这里不需要再次过滤

                        // 班级状态筛选
                        boolean matchesClassStatus = !hasClassStatusFilter ||
                                matchesClassStatus(classDO, queryReqVO.getClassStatus());

                        return matchesClassName && matchesTeacherName && matchesClassStatus;
                    })
                    .map(ClassManagementDO::getId)
                    .collect(Collectors.toList());

            // 4.2 过滤结果
            if (filteredClassIds.isEmpty()) {
                return Collections.emptyList();
            }

            result = result.stream()
                    .filter(report -> filteredClassIds.contains(report.getClassId()))
                    .collect(Collectors.toList());
        }

        // 5. 应用放假时间筛选
        if (queryReqVO.getHolidayDate() != null) {
            result = result.stream()
                    .filter(report -> {
                        LocalDate startDate = report.getStartTime().toLocalDate();
                        LocalDate endDate = report.getEndTime().toLocalDate();
                        return (queryReqVO.getHolidayDate().isEqual(startDate) ||
                                queryReqVO.getHolidayDate().isEqual(endDate) ||
                                (queryReqVO.getHolidayDate().isAfter(startDate) &&
                                        queryReqVO.getHolidayDate().isBefore(endDate)));
                    })
                    .collect(Collectors.toList());
        }

        // 设置班级名称
        setClassNames(result, classes);

        // 6. 计算统计数据
        processLeaveReportStatistics(result);

        // 7. 按创建时间倒序排序
        result.sort((a, b) -> {
            if (a.getCreateTime() == null && b.getCreateTime() == null) {
                return 0;
            } else if (a.getCreateTime() == null) {
                return 1;
            } else if (b.getCreateTime() == null) {
                return -1;
            }
            return b.getCreateTime().compareTo(a.getCreateTime());
        });

        return result;
    }

    /**
     * 填充班级的班主任姓名
     */
    private void fillClassTeacherNames(List<ClassManagementDO> classes) {
        if (classes == null || classes.isEmpty()) {
            return;
        }

        for (ClassManagementDO classDO : classes) {
            // 班主任可能为空
            String classTeacherLeadName = null;
            if (classDO.getClassTeacherLead() != null) {
                // 根据班主任ID查询班主任姓名，如果找不到则返回null
                classTeacherLeadName = classManagementMapper.getClassTeacherLeadInfo(classDO.getClassTeacherLead());
            }
            // 设置班主任姓名（可能为null）
            classDO.setClassTeacherLeadName(classTeacherLeadName);
        }
    }

    /**
     * 为离校报备列表设置班级名称
     */
    private void setClassNames(List<LeaveReportRespVO> result, List<ClassManagementDO> classes) {
        if (classes == null || classes.isEmpty()) {
            return; // 没有班级数据，直接返回
        }

        // 创建班级ID到班级名称的映射
        Map<Long, String> classIdToNameMap = new HashMap<>();
        // 创建班级ID到班主任姓名的映射
        Map<Long, String> classIdToTeacherMap = new HashMap<>();

        // 安全地填充映射
        for (ClassManagementDO classDO : classes) {
            Long id = classDO.getId();
            if (id != null) {
                classIdToNameMap.put(id, classDO.getClassName());
                classIdToTeacherMap.put(id, classDO.getClassTeacherLeadName());
            }
        }

        // 设置班级名称和班主任
        result.forEach(report -> {
            if (report.getClassId() != null) {
                report.setClassname(classIdToNameMap.get(report.getClassId()));
                report.setClassTeacher(classIdToTeacherMap.get(report.getClassId()));
            }
        });
    }

    @Override
    public PageResult<LeaveReportRespVO> getLeaveReportPage(LeaveReportQueryReqVO queryReqVO) {
        // 1. 先获取所有符合条件的数据（重用getLeaveReportList的完整逻辑）
        List<LeaveReportRespVO> allResults = getLeaveReportList(queryReqVO);

        // 2. 如果没有数据，直接返回空的分页结果
        if (allResults.isEmpty()) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }

        // 3. 手动分页处理
        int pageNo = queryReqVO.getPageNo() != null ? queryReqVO.getPageNo() : 1;
        int pageSize = queryReqVO.getPageSize() != null ? queryReqVO.getPageSize() : 10;

        // 计算分页参数
        int total = allResults.size();
        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        // 4. 获取当前页的数据
        List<LeaveReportRespVO> pageData;
        if (startIndex >= total) {
            // 如果起始索引超出总数，返回空列表
            pageData = Collections.emptyList();
        } else {
            pageData = allResults.subList(startIndex, endIndex);
        }

        // 5. 设置序号（基于分页的全局序号）
        for (int i = 0; i < pageData.size(); i++) {
            pageData.get(i).setSerialNumber(startIndex + i + 1);
        }

        // 6. 返回分页结果
        return new PageResult<>(pageData, (long) total);
    }

    /**
     * 处理离校报备的统计信息
     */
    private void processLeaveReportStatistics(List<LeaveReportRespVO> result) {
        if (result == null || result.isEmpty()) {
            return;
        }

        // 查询所有离校和不离校的明细
        LambdaQueryWrapper<LeaveReportDetailDO> leaveStudentWrapper = new LambdaQueryWrapper<>();
        leaveStudentWrapper.eq(LeaveReportDetailDO::getDeleted, false);
        leaveStudentWrapper.eq(LeaveReportDetailDO::getLeaveStatus, true);
        List<LeaveReportDetailDO> leave = leaveReportDetailMapper.selectList(leaveStudentWrapper);

        LambdaQueryWrapper<LeaveReportDetailDO> stayWrapper = new LambdaQueryWrapper<>();
        stayWrapper.eq(LeaveReportDetailDO::getDeleted, false);
        stayWrapper.eq(LeaveReportDetailDO::getLeaveStatus, false);
        List<LeaveReportDetailDO> stay = leaveReportDetailMapper.selectList(stayWrapper);

        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 处理每条离校报备，计算相关统计数据
        result.forEach(leaveReport -> {
            if (leaveReport == null) {
                return; // 跳过null对象
            }

            // 获取当前离校报备的班级总人数
            Long classCount = 0L;
            if (leaveReport.getClassId() != null) {
                classCount = traineeMapper.selectCount(TraineeDO::getClassId, leaveReport.getClassId());
            }

            // 计算离校人数
            Long leaveCount = 0L;
            if (!leave.isEmpty() && leaveReport.getId() != null) {
                leaveCount = leave
                        .stream()
                        .filter(leaveReportDetail -> leaveReportDetail != null &&
                                leaveReportDetail.getLeaveId() != null &&
                                leaveReport.getId() != null &&
                                Objects.equals(leaveReportDetail.getLeaveId(), leaveReport.getId()))
                        .count();
            }

            // 计算不离校人数
            Long stayCount = 0L;
            if (!stay.isEmpty() && leaveReport.getId() != null) {
                stayCount = stay
                        .stream()
                        .filter(leaveReportDetail -> leaveReportDetail != null &&
                                leaveReportDetail.getLeaveId() != null &&
                                leaveReport.getId() != null &&
                                Objects.equals(leaveReportDetail.getLeaveId(), leaveReport.getId()))
                        .count();
            }

            // 设置状态：过期(0)或进行中(1)
            if (leaveReport.getEndTime() != null) {
                LocalDateTime endTime = leaveReport.getEndTime();
                if (endTime.isBefore(now)) {
                    leaveReport.setStatus(0);
                } else {
                    leaveReport.setStatus(1);
                }
            } else {
                // 如果结束时间为空，默认设置为已过期
                leaveReport.setStatus(0);
            }

            // 设置统计数据
            leaveReport.setUnfilled(classCount - leaveCount - stayCount);
            leaveReport.setLeaveCount(leaveCount);
            leaveReport.setStayCount(stayCount);

            // 设置放假时间，格式为：yyyy-MM-dd ~ yyyy-MM-dd
            if (leaveReport.getStartTime() != null && leaveReport.getEndTime() != null) {
                String startDate = leaveReport.getStartTime().format(dateFormatter);
                String endDate = leaveReport.getEndTime().format(dateFormatter);
                leaveReport.setVacationTime(startDate + " ~ " + endDate);
            }
        });
    }

    @Override
    public LeaveReportTimeVO getLeaveReportTime(Long leaveId) {
        LeaveReportDO leaveReportDO = leaveReportMapper.selectById(leaveId);
        LeaveReportTimeVO leaveReportTimeVO = new LeaveReportTimeVO().setStartTime(leaveReportDO.getStartTime())
                .setEndTime(leaveReportDO.getEndTime());
        Long loginUserId = getLoginUserId();
        Long studentId = traineeMapper.getTraineeId(loginUserId, leaveReportDO.getClassId());
        LambdaQueryWrapper<LeaveReportDetailDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LeaveReportDetailDO::getLeaveId, leaveId);
        wrapper.eq(LeaveReportDetailDO::getStudentId, studentId);
        List<LeaveReportDetailDO> leaveReportDetail = leaveReportDetailMapper.selectList(wrapper);
        if (!leaveReportDetail.isEmpty()) {
            leaveReportTimeVO.setDetailId(leaveReportDetail.get(0).getId());
        }
        return leaveReportTimeVO;
    }

    @Override
    public Boolean remind(LeaveReportRemindReqVO remindReqVO) {

        xcxMsgService.sendBatchXcxMsg(new XcxMsgSendReqVO(
                remindReqVO.getStudentIdList(),
                XcxMsgSendType.LEAVE_REPORT.getCode(),
                null, null,
                "leaveId_" + remindReqVO.getLeaveId().toString()));
        return true;
    }

    @Override
    public void deleteLeaveReport(Long id) {




        // 先删除关联的离校报备明细记录
        LambdaQueryWrapper<LeaveReportDetailDO> Wrapper = new LambdaQueryWrapper<>();
        Wrapper.eq(LeaveReportDetailDO::getId, id);

        LeaveReportDetailDO  leaveReportDetailDO = leaveReportDetailMapper.selectById( id);


        if(leaveReportDetailDO==null){
            return;
        }


        // 3. 分布式锁，加锁
        String lockKey = "lock:createLeaveReport:" + leaveReportDetailDO.getCreator();
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        try {


            LambdaQueryWrapper<LeaveReportDetailDO> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(LeaveReportDetailDO::getLeaveId, id);
            leaveReportDetailMapper.delete(detailWrapper);

            // 再删除离校报备主记录
            leaveReportMapper.deleteById(id);

        } finally {
            lock.unlock();
        }


    }

    @Override
    public List<ClassInfoRespVO> getOpeningClasses(Integer campus) {
        // 获取当前租户下所有"开班中"的班级
        // ClassManageStatus.OPENING.getCode() = 2 表示"开班中"状态
        List<ClassInfoRespVO> classes = classManagementService.getClassPageByStatus(ClassManageStatus.OPENING.getCode(), null, null);

        // 如果指定了校区，则进行校区筛选
        if (campus != null) {
            classes = classes.stream()
                    .filter(classInfo -> campus.equals(classInfo.getCampus()))
                    .collect(Collectors.toList());
        }

        return classes;
    }

    @Override
    public List<LeaveReportRespVO> getAllLeaveReportList() {
        // 重用新增的方法，传入空的查询条件
        List<LeaveReportRespVO> result = getLeaveReportList(new LeaveReportQueryReqVO());
        return result != null ? result : Collections.emptyList();
    }

    @Override
    public List<ClassInfoRespVO> getAllClassesWithFilter(String className) {
        // 先尝试获取开班中的班级
        List<ClassInfoRespVO> classes = classManagementService.getClassPageByStatus(
                ClassManageStatus.OPENING.getCode(), null, null);

        // 如果没有开班中的班级且没有指定班级名称，则查询最近一个已结束的班级
        if (classes.isEmpty() && !StringUtils.hasText(className)) {
            classes = classManagementService.getClassPageByStatus(
                    ClassManageStatus.CLASS_END.getCode(), null, null);
            // 如果有多个已结束班级，只取一个（模拟"最近的一个"）
            if (!classes.isEmpty()) {
                classes = classes.subList(0, 1);
            }
        }

        return classes;
    }

    @Override
    public List<LeaveReportExcelVO> getExportAllLeaveReportExcel(List<Long> ids) {
        // 1. 获取所有离校申请数据
        List<LeaveReportRespVO> allResults = getAllLeaveReportList();

        // 2. 如果指定了ids，则只导出指定的记录
        if (ids != null && !ids.isEmpty()) {
            allResults = allResults.stream()
                    .filter(item -> ids.contains(item.getId()))
                    .collect(Collectors.toList());
        }

        // 3. 转换为Excel VO并设置序号
        List<LeaveReportExcelVO> excelList = new ArrayList<>();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        for (int i = 0; i < allResults.size(); i++) {
            LeaveReportRespVO respVO = allResults.get(i);
            LeaveReportExcelVO excelVO = new LeaveReportExcelVO();

            // 设置序号（从1开始）
            excelVO.setSerialNumber(i + 1);
            excelVO.setName(respVO.getName());

            // 格式化放假时间（开始时间 - 结束时间）
            String formattedStartTime = respVO.getStartTime() != null ? respVO.getStartTime().format(dateFormatter)
                    : "";
            String formattedEndTime = respVO.getEndTime() != null ? respVO.getEndTime().format(dateFormatter) : "";
            excelVO.setHolidayTime(formattedStartTime + " - " + formattedEndTime);

            excelVO.setClassname(respVO.getClassname());
            excelVO.setClassTeacher(respVO.getClassTeacher());

            // 计算状态
            String status = "";
            if (0 == respVO.getStatus()) {
                status = "已结束";
            } else if (1 == respVO.getStatus()) {
                status = "进行中";
            }
            excelVO.setStatus(status);

            // 保存创建时间作为发布时间
            excelVO.setCreateTime(respVO.getCreateTime());

            // 保存原始字段供内部使用
            excelVO.setStartTime(respVO.getStartTime());
            excelVO.setEndTime(respVO.getEndTime());

            // 计算总人数、已离校人数、未离校人数和离校率
            Long totalStudents = (respVO.getLeaveCount() != null ? respVO.getLeaveCount() : 0L) +
                    (respVO.getStayCount() != null ? respVO.getStayCount() : 0L) +
                    (respVO.getUnfilled() != null ? respVO.getUnfilled() : 0L);
            Long leftStudents = respVO.getLeaveCount() != null ? respVO.getLeaveCount() : 0L;
            Long notLeftStudents = (respVO.getStayCount() != null ? respVO.getStayCount() : 0L) +
                    (respVO.getUnfilled() != null ? respVO.getUnfilled() : 0L);

            excelVO.setTotalStudents(totalStudents.intValue());
            excelVO.setLeftStudents(leftStudents.intValue());
            excelVO.setNotLeftStudents(notLeftStudents.intValue());

            // 计算离校率
            String leaveRate = "0%";
            if (totalStudents > 0) {
                double rate = (double) leftStudents / totalStudents * 100;
                leaveRate = String.format("%.1f%%", rate);
            }
            excelVO.setLeaveRate(leaveRate);

            excelList.add(excelVO);
        }

        return excelList;
    }

    /**
     * 获取离校报备状态
     *
     * @param respVO 离校报备VO
     * @return 状态字符串
     */
    private String getLeaveReportStatus(LeaveReportRespVO respVO) {
        // 总学员人数
        Long totalStudents = (respVO.getLeaveCount() != null ? respVO.getLeaveCount() : 0L) +
                (respVO.getStayCount() != null ? respVO.getStayCount() : 0L) +
                (respVO.getUnfilled() != null ? respVO.getUnfilled() : 0L);

        // 未填写人数
        Long unfilled = respVO.getUnfilled() != null ? respVO.getUnfilled() : 0L;

        // 判断状态
        if (unfilled == 0 && totalStudents > 0) {
            return "已完成";
        } else if (unfilled > 0 && unfilled < totalStudents) {
            return "进行中";
        } else {
            return "未开始";
        }
    }

    /**
     * 判断班级是否匹配指定的班级状态
     * @param classDO 班级信息
     * @param classStatus 要匹配的班级状态：2-开班中，3-已结业，4-未开始
     * @return 是否匹配
     */
    private boolean matchesClassStatus(ClassManagementDO classDO, Integer classStatus) {
        if (classStatus == null || classDO == null) {
            return true;
        }

        // 只处理已发布的班级
        if (classDO.getPublish() != 1) {
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime classOpenTime = classDO.getClassOpenTime();
        LocalDateTime completionTime = classDO.getCompletionTime();

        if (classOpenTime == null || completionTime == null) {
            return false;
        }

        switch (classStatus) {
            case 2: // 开班中
                return now.isAfter(classOpenTime) && now.isBefore(completionTime);
            case 3: // 已结业
                return now.isAfter(completionTime);
            case 4: // 未开始
                return now.isBefore(classOpenTime);
            default:
                return true;
        }
    }
}
