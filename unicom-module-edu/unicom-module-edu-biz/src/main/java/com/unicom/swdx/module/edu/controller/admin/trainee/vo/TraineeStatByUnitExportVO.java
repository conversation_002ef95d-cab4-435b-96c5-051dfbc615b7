package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: zhouhk
 * @Date: 2024/12/20
 */

@Data
@ApiModel(value = "报名详情分页返回VO")
public class TraineeStatByUnitExportVO {

    @ExcelProperty(value = "年份")
    private String year;

    @ExcelProperty(value = "单位名称")
    private String unitName;

    @ExcelProperty(value = "班次名称")
    private String className;

    @ExcelProperty(value = "学期，1-上学期，2-下学期")
    private String semester;

    @ExcelProperty(value = "开班日期")
    private String classOpenTime;

    @ExcelProperty(value = "结业日期")
    private String completionTime;

    @ExcelProperty(value = "是否短班")
    private String shortClass;

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "年龄")
    private String age;

    @ExcelProperty(value = "出生年月")
    private String birthDay;

    @ExcelProperty(value = "性别")
    private String sex;

    @ExcelProperty(value = "手机号码")
    private String phone;

    @ExcelProperty(value = "职级")
    private String jobLevel;

    @ExcelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "政治面貌")
    private String politicalIdentity;

    @ApiModelProperty(value = "文化程度")
    private String educationalLevel;

    @ApiModelProperty(value = "民族")
    private String ethnic;

    @ApiModelProperty(value = "学员状态")
    private String status;




}
