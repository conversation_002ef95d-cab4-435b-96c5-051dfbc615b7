package com.unicom.swdx.module.edu.controller.admin.evaluationdetail;

import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.EvaluationResponseSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.MyEvaluationPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionnaireManagementRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.courses.CoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.questionnairemanagement.QuestionnaireManagementMapper;
import com.unicom.swdx.module.edu.service.classcommen.CommenService;
import com.unicom.swdx.module.edu.service.evaluationresponse.EvaluationResponseService;
import com.unicom.swdx.module.edu.service.questionnairedetail.QuestionnaireDetailService;
import com.unicom.swdx.module.edu.service.questionnairemanagement.QuestionnaireManagementService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationdetail.EvaluationDetailDO;
import com.unicom.swdx.module.edu.service.evaluationdetail.EvaluationDetailService;

@Tag(name = "管理后台 - 评估详情")
@RestController
@RequestMapping("/edu/evaluation-detail")
@Validated
public class EvaluationDetailController {

    @Resource
    private EvaluationDetailService evaluationDetailService;

    @Resource
    private EvaluationResponseService evaluationResponseService;

    @Resource
    private ClassCourseMapper classCourseMapper;

    @Resource
    private CoursesMapper  coursesMapper;

    @Resource
    private ClassManagementMapper  classManagementMapper;

    @Resource
    private QuestionnaireManagementService questionnaireManagementService;

    @Resource
    private CommenService commenService;

    @PostMapping("/create")
    @Operation(summary = "创建评估详情")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:create')")
    public CommonResult<Long> createEvaluationDetail(@Valid @RequestBody EvaluationDetailSaveReqVO createReqVO) {
        return success(evaluationDetailService.createEvaluationDetail(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新评估详情")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:update')")
    public CommonResult<Boolean> updateEvaluationDetail(@Valid @RequestBody EvaluationDetailSaveReqVO updateReqVO) {
        evaluationDetailService.updateEvaluationDetail(updateReqVO);
        return success(true);
    }

    @PostMapping("/modify")
    @Operation(summary = "学员评估调整")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:update')")
    public CommonResult<Boolean> modifyEvaluationDetail(@Valid @RequestBody EvaluationModifyVO updateReqVO) {
        evaluationDetailService.modifyEvaluationDetail(updateReqVO);
        return success(true);
    }

    @GetMapping("/getComment")
    @Operation(summary = "获取意见详情")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:get')")
    public CommonResult<PageResult<CommentRespVO>> getComment(@Valid CommentPageReqVO reqVO) {
        PageResult<CommentRespVO> result = evaluationDetailService.getComment(reqVO);
        return success(result);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除评估详情")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:delete')")
    public CommonResult<Boolean> deleteEvaluationDetail(@RequestParam("id") Long id) {
        evaluationDetailService.deleteEvaluationDetail(id);
        return success(true);
    }

    @PostMapping("/deleteByClassCourseId")
    @Operation(summary = "删除评课数据")
    @Parameter(name = "classCourseId", description = "课程id", required = true)
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:delete')")
    public CommonResult<Boolean> deleteByClassCourseId(@RequestParam("classCourseIds") List<Long> classCourseIds) {
        evaluationResponseService.deleteByClassCourseId(classCourseIds);
        evaluationDetailService.deleteByClassCourseId(classCourseIds);
        return success(true);
    }

    @GetMapping("/getEvaluationList")
    @Operation(summary = "获得评估列表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:query')")
    public CommonResult<List<EvaluationRespVO>> getEvaluationDetail(@RequestParam(value = "handle", required = false) Boolean handle,
                                                                    @RequestParam(value = "courseName", required = false) String courseName,
                                                                    @RequestParam(value = "userId", required = false) Long userId,
                                                                    @RequestParam(value = "studentId", required = false) Long studentId) {
        List<EvaluationRespVO> evaluationList = evaluationDetailService.getEvaluationList(handle, courseName, userId, studentId);
        return success(evaluationList);
    }

    @GetMapping("/getStudentUnhandled")
    @Operation(summary = "获得评估列表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:query')")
    public CommonResult<Long> countStudentUnhandled(@RequestParam(value = "userId", required = false) Long userId,
                                                    @RequestParam(value = "studentId", required = false) Long studentId) {
        Long unhandled = evaluationDetailService.countStudentUnhandled(userId, studentId);
        return success(unhandled);
    }

    @PostMapping("/submit")
    @Operation(summary = "提交问卷")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:update')")
    public CommonResult<Boolean> submitEvaluation(@Valid @RequestBody EvaluationSubmitVO submitVO) {
        evaluationDetailService.submitEvaluation(submitVO);
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得评估详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:query')")
    public CommonResult<EvaluationDetailRespVO> getEvaluationDetail(@RequestParam("id") Long id) {
        EvaluationDetailDO evaluationDetail = evaluationDetailService.getEvaluationDetail(id);
        return success(BeanUtils.toBean(evaluationDetail, EvaluationDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得评估详情分页")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:query')")
    public CommonResult<PageResult<EvaluationDetailRespVO>> getEvaluationDetailPage(@Valid EvaluationDetailPageReqVO pageReqVO) {
        PageResult<EvaluationDetailDO> pageResult = evaluationDetailService.getEvaluationDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EvaluationDetailRespVO.class));
    }

    @PostMapping("/summaryPage")
    @Operation(summary = "获得学员评价汇总分页")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:query')")
    public CommonResult<PageResult<EvaluationSummaryPageRespVO>> getEvaluationSummaryPage(@Valid @RequestBody EvaluationSummaryPageReqVO pageReqVO) {
        return success(evaluationDetailService.getEvaluationSummaryPage(pageReqVO));
    }

    @PostMapping("/exportSummaryExcel")
    @Operation(summary = "导出评估详情 Excel")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:export')")
    public void exportEvaluationSummaryExcel(@Valid @RequestBody EvaluationSummaryPageReqVO pageReqVO,
                                            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EvaluationSummaryPageRespVO> list = evaluationDetailService.getEvaluationSummaryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "评估详情.xls", "数据", EvaluationSummaryPageRespVO.class,
                BeanUtils.toBean(list, EvaluationSummaryPageRespVO.class), pageReqVO.getIncludeColumnIndexes());
    }

    @GetMapping("/pageRecord")
    @Operation(summary = "获得学员评估记录分页")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:query')")
    public CommonResult<PageResult<EvaluationDetailRespVO>> getEvaluationRecordPage(@Valid EvaluationDetailPageReqVO pageReqVO) {
        PageResult<EvaluationDetailDO> pageResult = evaluationDetailService.getEvaluationDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EvaluationDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出评估详情 Excel")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:export')")
    public void exportEvaluationDetailExcel(@Valid EvaluationDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EvaluationDetailDO> list = evaluationDetailService.getEvaluationDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "评估详情.xls", "数据", EvaluationDetailRespVO.class,
                        BeanUtils.toBean(list, EvaluationDetailRespVO.class));
    }

    @PostMapping("/distribute")
    @Operation(summary = "手动下发问卷")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:update')")
    public CommonResult<Boolean> distributeEvaluation(@Valid @RequestBody EvaluationDistributeVO distributeVO) {
        evaluationDetailService.distributeEvaluation(distributeVO);
        return success(true);
    }

    @PostMapping("/revokeEvaluated")
    @Operation(summary = "撤回已评问卷")
    @PreAuthorize("@ss.hasPermission('edu:evaluation-detail:update')")
    public CommonResult<Boolean> revokeEvaluated(@Valid @RequestBody EvaluationRevokeReqVO reqVO) {
        boolean result = evaluationResponseService.revokeEvaluated(reqVO);
        return success(result);
    }

    // 每10分钟查询一次决定是否下发问卷
    // 0 */10 8-18 * * MON-FRI
    @XxlJob("distributeQuestionnaire")
    @TenantIgnore
    public void distributeQuestionnaire() throws Exception {
        // 查询近半个小时内没有下发问卷且已经结束的课程
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime time = now.minus(1, ChronoUnit.DAYS);
        List<ClassCourseDO> endCourses = classCourseMapper.getEndedCourse(time, now);
        endCourses.addAll(classCourseMapper.getDepartmentCourse(time, now));

        if(endCourses != null && !endCourses.isEmpty()) {
            // 下发问卷
            endCourses.forEach(endCourse -> {
                endCourse.setIsDistributed(true);
                Long classId = endCourse.getClassId();
                ClassManagementDO classManagementDO = classManagementMapper.selectById(classId);
                // 获取对应问卷信息
                try {
                    Long questionnaireId = questionnaireManagementService.getByEducateForm(endCourse.getEducateFormId(), endCourse.getTenantId());
                    if (questionnaireId == null) {
                        questionnaireId = questionnaireManagementService.getDefaultQuestionnaireId(endCourse.getTenantId());
                    }
                    QuestionnaireManagementRespVO questionnaire = questionnaireManagementService.getQuestionnaireManagement(questionnaireId,null,null);
                    List<QuestionManagementRespVO> questions = questionnaire.getQuestions();

                    // 获取课程对应学员信息
                    List<TraineeDO> trainee = new ArrayList<>();
                    if (classManagementDO.getEvaluate().equals(2)) { //不是考勤评课的情况
                        trainee = commenService.getUsersByClasscourseid(endCourse.getId(), endCourse.getClassId());
                    } else {
                        trainee = commenService.getCheckedUserByClassCourseId(endCourse.getId(), endCourse.getClassId());
                    }
                    List<Long> traineeIdList =  evaluationResponseService.getExistEvaluationTrainee(endCourse.getId());
                    trainee = trainee.stream().filter(t -> !traineeIdList.contains(t.getId())).collect(Collectors.toList());
                    if (!trainee.isEmpty()) {
                        trainee.forEach(traineeDO -> {
                            EvaluationResponseSaveReqVO responseSaveReqVO = new EvaluationResponseSaveReqVO();
                            responseSaveReqVO.setClassCourseId(endCourse.getId());
                            responseSaveReqVO.setQuestionnaireId(questionnaire.getId());
                            responseSaveReqVO.setStudentId(traineeDO.getId());
                            responseSaveReqVO.setIssuer(endCourse.getTeacherIdString());
                            responseSaveReqVO.setTeacherId(endCourse.getTeacherIdString());
                            responseSaveReqVO.setHandle(false);
                            responseSaveReqVO.setTenantId(endCourse.getTenantId());
                            responseSaveReqVO.setDepartment(endCourse.getDepartment());
                            if (Boolean.TRUE.equals(questionnaire.getTimeTag())) {
                                LocalDateTime expireTime = now.plusDays(questionnaire.getTimeLimit());
                                responseSaveReqVO.setExpireTime(expireTime);
                            }

                            // 下发问卷
                            evaluationResponseService.createEvaluationResponse(responseSaveReqVO);

                            classCourseMapper.updateById(endCourse);

                            questions.forEach(question -> {
                                EvaluationDetailSaveReqVO detailSaveReqVO = new EvaluationDetailSaveReqVO();
                                detailSaveReqVO.setQuestionnaireId(questionnaire.getId());
                                detailSaveReqVO.setQuestionId(question.getId());
                                detailSaveReqVO.setQuestionType(question.getQuestionType());
                                detailSaveReqVO.setStudentId(traineeDO.getId());
                                detailSaveReqVO.setClassCourseId(endCourse.getId());
                                detailSaveReqVO.setTenantId(endCourse.getTenantId());

                                // 记录每个学员的选项
                                evaluationDetailService.createEvaluationDetail(detailSaveReqVO);
                            });

                        });
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            // 下发完成后更新下发状态为已下发
            // classCourseMapper.updateBatch(endCourses);
        }
    }

}
