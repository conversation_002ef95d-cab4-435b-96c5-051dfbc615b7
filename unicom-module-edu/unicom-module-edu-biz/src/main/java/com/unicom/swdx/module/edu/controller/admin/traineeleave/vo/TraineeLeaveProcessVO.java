package com.unicom.swdx.module.edu.controller.admin.traineeleave.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@ApiModel(value = "学员请假审批过程VO")
public class TraineeLeaveProcessVO {

    @ApiModelProperty(value = "请假记录ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long leaveId;

    @ApiModelProperty(value = "节点用户姓名")
    private String userName;

    @ApiModelProperty(value = "审批节点名称")
    private String taskName;

    @ApiModelProperty(value = "审批节点状态，包括：0未审批-1已通过-2已拒绝")
    private Integer taskStatus;

    @ApiModelProperty(value = "审批意见，班主任同意时不填意见，拒绝必须填意见，转办人同意/不同意都可填可不填意见")
    private String taskComment;

    @ApiModelProperty(value = "节点用户类型，包括：学员-班主任-分管领导")
    private String userType;

    @ApiModelProperty(value = "节点操作时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "修改意见")
    private String modifyReason;

}
