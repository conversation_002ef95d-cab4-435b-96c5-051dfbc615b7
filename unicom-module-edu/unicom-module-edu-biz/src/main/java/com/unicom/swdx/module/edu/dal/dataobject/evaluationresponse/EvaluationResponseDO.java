package com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 课程评价记录 DO
 *
 * <AUTHOR>
 */
@TableName("pg_evaluation_response")
@KeySequence("pg_evaluation_response_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationResponseDO extends TenantBaseDO {

    /**
     * 评估结果主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 评估问卷id
     */
    private Long questionnaireId;
    /**
     * 评卷人id
     */
    private Long studentId;
    /**
     * 发卷人id
     */
    private String issuer;
    /**
     * 发卷人部门id
     */
    private Long deptId;
    /**
     * 班级教师id
     */
    private String teacherId;
    /**
     * 课程id
     */
    private Long classCourseId;
    /**
     * 总分
     */
    private BigDecimal score;
    /**
     * 评分等级
     */
    private String grade;
    /**
     * 是否评卷 （0 否， 1是）
     */
    private Boolean handle;
    /**
     * 免评价类型（0 必须评价 ，1 请假了不用评）
     */
    private Boolean remarktype;

    /**
     * 是否部门授课
     */
    private Boolean department;

    /**
     * 问卷过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 问卷提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 问卷撤回截止时间
     */
    private LocalDateTime revocableTime;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 最低分理由说明
     */
    private String lowScoreReason;

}
