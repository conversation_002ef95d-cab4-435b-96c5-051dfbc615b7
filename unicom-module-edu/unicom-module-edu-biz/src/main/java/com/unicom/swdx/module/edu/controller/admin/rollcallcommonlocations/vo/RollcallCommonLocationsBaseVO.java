package com.unicom.swdx.module.edu.controller.admin.rollcallcommonlocations.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 大课考勤、点名签到常用地点 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class RollcallCommonLocationsBaseVO {

    @ApiModelProperty(value = "教师id", example = "1")
    private Long teacherId;

    @ApiModelProperty(value = "0-大课考勤 1-点名签到", example = "1")
    private Integer type;

    @ApiModelProperty(value = "打卡位置的纬度", required = true, example = "10.2552")
    @NotNull(message = "打卡位置的纬度不能为空")
    private String latitude;

    @ApiModelProperty(value = "打卡位置的经度", required = true, example = "20.25552")
    @NotNull(message = "打卡位置的经度不能为空")
    private String longitude;

    @ApiModelProperty(value = "打卡位置的地址", example = "中国")
    @NotNull(message = "打卡位置的地址不能为空")
    private String address;

    @ApiModelProperty(value = "打卡范围半径", required = true, example = "50.50")
    @NotNull(message = "打卡范围半径不能为空")
    private BigDecimal radius;

}
