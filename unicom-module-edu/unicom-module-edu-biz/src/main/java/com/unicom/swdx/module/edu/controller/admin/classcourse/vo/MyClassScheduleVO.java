package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoRespVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学生小程序
 * 我的课表
 * 返回参数类
*/
@Data
public class MyClassScheduleVO {

    @ApiModelProperty(value = "课程类型(1-专题课、2-选修课、3-教学活动)")
    private Long coursesType;

    @ApiModelProperty(value = "任课老师")
    private String teacherName;

    @ApiModelProperty(value = "上课地点（教室名称）")
    private String className;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "0-上午，1-下午，2晚上")
    private String period;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "课程id")
    private Long courseId;

    @ApiModelProperty(value = "班级课程表Id")
    private Long classCourseId;

    @ApiModelProperty(value = "是否开启到课考勤")
    private Boolean isCheck;

    @ApiModelProperty(value = "是否为部门授课，默认为教师授课")
    private Boolean department;

    @ApiModelProperty(value = "授课者字符串，id之间逗号间隔")
    private String teacherIdString;

    @ApiModelProperty(value = "课程人数")
    private Long traineeNum;

    @ApiModelProperty(value = "是否党政领导讲课，0不是，1是")
    private Boolean isLeaderLecture;

    @ApiModelProperty(value = "是否合班授课")
    private Boolean isMerge;

    @ApiModelProperty(value = "是否显示合班授课标签 true - 是，false - 否")
    private Boolean isShowMergeTag;

    @ApiModelProperty(value = "合班授课班级列表")
    private List<ClassInfoRespVO> mergedClass;
}
