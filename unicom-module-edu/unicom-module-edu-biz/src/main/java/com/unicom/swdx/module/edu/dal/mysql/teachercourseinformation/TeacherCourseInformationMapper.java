package com.unicom.swdx.module.edu.dal.mysql.teachercourseinformation;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.teachercourseinformation.vo.TeacherCourseInformationExportReqVO;
import com.unicom.swdx.module.edu.controller.admin.teachercourseinformation.vo.TeacherCourseInformationPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationSimpleRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.teachercourseinformation.TeacherCourseInformationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 师资-任课信息中间 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TeacherCourseInformationMapper extends BaseMapperX<TeacherCourseInformationDO> {

    default PageResult<TeacherCourseInformationDO> selectPage(TeacherCourseInformationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TeacherCourseInformationDO>()
                .eqIfPresent(TeacherCourseInformationDO::getCoursesId, reqVO.getCoursesId())
                .eqIfPresent(TeacherCourseInformationDO::getTeacherId, reqVO.getTeacherId())
                .eqIfPresent(TeacherCourseInformationDO::getDeptId, reqVO.getDeptId())
                .orderByDesc(TeacherCourseInformationDO::getId));
    }

    default List<TeacherCourseInformationDO> selectList(TeacherCourseInformationExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TeacherCourseInformationDO>()
                .eqIfPresent(TeacherCourseInformationDO::getCoursesId, reqVO.getCoursesId())
                .eqIfPresent(TeacherCourseInformationDO::getTeacherId, reqVO.getTeacherId())
                .eqIfPresent(TeacherCourseInformationDO::getDeptId, reqVO.getDeptId())
                .orderByDesc(TeacherCourseInformationDO::getId));
    }

    default void deleteByCoursesId(Long coursesId) {
        delete(new LambdaQueryWrapperX<TeacherCourseInformationDO>()
                .eq(TeacherCourseInformationDO::getCoursesId, coursesId));
    }

    default void deleteByCoursesIdList(List<Long> coursesIdList) {
        delete(new LambdaQueryWrapperX<TeacherCourseInformationDO>()
                .in(TeacherCourseInformationDO::getCoursesId, coursesIdList));
    }

    /**
     * 根据课程ID列表查询对应的教师课程信息列表
     *
     * @param coursesIds 课程ID列表
     * @return 教师课程信息列表
     */
    default List<TeacherCourseInformationDO> selectListByCoursesIds(List<Long> coursesIds) {
        if (Objects.isNull(coursesIds) || coursesIds.isEmpty()) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapperX<TeacherCourseInformationDO>()
                .in(TeacherCourseInformationDO::getCoursesId, coursesIds));
    }

    default List<TeacherCourseInformationDO> selectListByCoursesId(Long coursesId) {
        return selectList(new LambdaQueryWrapperX<TeacherCourseInformationDO>()
                .eq(TeacherCourseInformationDO::getCoursesId, coursesId));
    }

    List<TeacherInformationSimpleRespVO> getSimpleListByCourseId(Long coursesId);

    default void deleteByTeacherId(Long teacherId) {
        delete(new LambdaQueryWrapperX<TeacherCourseInformationDO>()
                .eq(TeacherCourseInformationDO::getTeacherId, teacherId));
    }

    default List<TeacherCourseInformationDO> selectListByTeacherId(Long teacherId) {
        return selectList(new LambdaQueryWrapperX<TeacherCourseInformationDO>()
                .eq(TeacherCourseInformationDO::getTeacherId, teacherId));
    }

    /**
     * 根据课程类型查询 教师关联关系
     *
     * @param coursesType 课程类型
     * @return List<TeacherCourseInformationDO>
     */
    List<TeacherCourseInformationDO> selectListByCourseType(@Param("coursesType") Integer coursesType);
}
