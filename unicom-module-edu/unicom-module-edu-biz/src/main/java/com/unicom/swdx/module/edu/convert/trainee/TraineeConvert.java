package com.unicom.swdx.module.edu.convert.trainee;

import com.unicom.swdx.module.edu.controller.admin.trainee.vo.*;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.cadreInformation.CadreInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * EduClassroomLibrary Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TraineeConvert {

    TraineeConvert INSTANCE = Mappers.getMapper(TraineeConvert.class);

    TraineeDO convert(AddTraineeInfoReqVO bean);


    TraineeDO convert1(EditTraineeInfoReqVO reqVO);

    List<ReportInfoExcelVO> convertList(List<ReportPageRespVO> list);

    List<ReportInfoDetailExcelVO> convertList1(List<ReportPageRespVO> list);

    List<AllTraineeExcelVO> covertTrainList(List<UnitRegistrationPageRespVO> allTraineeInfo);

    List<AppTraineeGroupRespVO> convertToAppTraineeGroupRespVOList(List<TraineeDO> traineeDOList);

    List<TraineeDO> convertGroupList(List<SetGroupCommitteeReqVO> list);

    TraineeDO convertGroup(SetGroupCommitteeReqVO reqVO);

    @Mapping(target = "lastTrainingTime", source = "lastTrainingTime")
    @Mapping(target = "lastTrainingClass", source = "lastTrainingClass")
    @Mapping(target = "latestQualExamDate", source = "latestQualExamDate")
    @Mapping(target = "employmentTime", source = "employmentTime")
    @Mapping(target = "className", source = "className")
    TraineeByCardNoVO convertToTraineeByCardNoVO(TraineeDO traineeDO);

    /**
     * 将TraineeDO转换为TraineeBaseVO
     * 确保映射所有字段，特别是lastTrainingTime、lastTrainingClass和latestQualExamDate
     */
    @Mapping(target = "lastTrainingTime", source = "lastTrainingTime")
    @Mapping(target = "lastTrainingClass", source = "lastTrainingClass")
    @Mapping(target = "latestQualExamDate", source = "latestQualExamDate")
    @Mapping(target = "employmentTime", source = "employmentTime")
    TraineeBaseVO convertToTraineeBaseVO(TraineeDO traineeDO);

    // 确保字段映射包含最近参训日期和最近参加任职资格考试日期
    @Mapping(target = "lastTrainingTime", expression = "java(org.apache.commons.lang3.StringUtils.isNotBlank(source.getLastTrainingTime()) ? java.time.LocalDate.parse(source.getLastTrainingTime(), java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd\")) : null)")
    @Mapping(target = "lastTrainingClass", source = "lastTrainingClass")
    @Mapping(target = "latestQualExamDate", expression = "java(org.apache.commons.lang3.StringUtils.isNotBlank(source.getLatestQualExamDate()) ? java.time.LocalDate.parse(source.getLatestQualExamDate(), java.time.format.DateTimeFormatter.ofPattern(\"yyyy-MM-dd\")) : null)")
    TraineeDO convertImport(TraineeInfoImportStrExcelVO source);

    @IterableMapping(elementTargetType = TraineeDO.class)
    List<TraineeDO> convertImportList(List<TraineeInfoImportStrExcelVO> list);

    List<RegistrationUnitExcelVO> convertUnitList(List<RegistrationExcelVO> list);

    List<RegistrationTrainExcelVO> convertTrainList(List<RegistrationExcelVO> list);

    List<RegistrationReportExcelVO> convertReportList(List<RegistrationExcelVO> list);

    List<StudentStatusQueryExcelVO> convertQueryList(List<RegistrationExcelVO> list);

    AddTraineeInfoReqVO convert2(EditTraineeInfoReqVO reqVO);

    List<TraineeStatExportVO> convertToExportList(List<TraineeStatPageRespVO> records);

    CadreInformationDO convertCadre(TraineeDO traineeDO);

    List<CadreInformationDO> convertCadreList(List<TraineeDO> list);

    List<TraineeStatByUnitExportVO> convertToByUnitExportList(List<TraineeStatByUnitPageRespVO> records);
}
