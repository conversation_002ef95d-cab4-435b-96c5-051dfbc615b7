package com.unicom.swdx.module.edu.service.signupunit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.desensitize.DesensitizeUtils;
import com.unicom.swdx.framework.common.util.validation.ValidationUtils;
import com.unicom.swdx.module.edu.controller.admin.signupunit.vo.*;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.TraineeImportRespVO;
import com.unicom.swdx.module.edu.convert.signupunit.SignUpUnitConvert;
import com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO;
import com.unicom.swdx.module.edu.dal.mysql.signupunit.SignUpUnitMapper;
import com.unicom.swdx.module.edu.enums.signunit.UnitStatusEnum;
import com.unicom.swdx.module.edu.enums.signunit.UnitTemplateEnum;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.CreateAdminForUnitBatchDTO;
import com.unicom.swdx.module.system.api.user.dto.CreateAdminForUnitBatchResultDTO;
import com.unicom.swdx.module.system.api.user.dto.CreateAdminForUnitDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;


/**
 * EduSignUpUnit Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SignUpUnitServiceImpl extends ServiceImpl<SignUpUnitMapper, SignUpUnitDO> implements SignUpUnitService {

    @Resource
    private SignUpUnitMapper signUpUnitMapper;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    @Lazy
    private TraineeService traineeService;

    @Resource
    @Lazy
    private SignUpUnitService self;

    private static final  String desensitizePublicKeyStr = "04d89d95d0129f8b22a27979fd2c5d1fad305e1ec9ebbd2f6ff26f5bfdf00e5493574e8b93a2a16d4f924efd5133b6e7474aa694ec6314e39c34ff89c22d7b3768";

    private static final String desensitizePrivateKeyStr = "5174bc2473f86d5cda2ddad6a2d9b705848dd65b6fa13ff0923b1d2b55f09d30";

    private static final SM2 sm2Desensitize = SmUtil.sm2(desensitizePrivateKeyStr,desensitizePublicKeyStr);

    private static String desensitizeEncrypt(String content) {
        return sm2Desensitize.encryptBase64(content, KeyType.PublicKey);
    }

    private static String desensitizeDecrypt(String content) {
        return sm2Desensitize.decryptStr(content, KeyType.PrivateKey);
    }

    @Override
    public Integer createSignUpUnit(SignUpUnitCreateReqVO createReqVO) {


        String phone = createReqVO.getPhone();
        if (phone != null && phone != "") {
            String decryptedPhone = desensitizeDecrypt((phone));
            createReqVO.setPhone(decryptedPhone);
        }
        String officePhone = createReqVO.getOfficePhone();
        if (officePhone != null && officePhone != "") {
            String decryptedOfficePhone = desensitizeDecrypt((officePhone));
            createReqVO.setOfficePhone(decryptedOfficePhone);
        }

        // 单位名称唯一性校验
        validateShortName(createReqVO.getUnitName(),null);
        // 单位管理员用户名校验
        validateUsername(createReqVO.getUsername());
        // 单位管理员手机号校验
        validatePhoneNumber(createReqVO.getPhone());

        // 查询是否和其他单位手机号重复
        Integer count = signUpUnitMapper.countByPhone(createReqVO.getPhone());
        if (count != 0) {
            throw exception(SIGN_UP_UNIT_PHONE_DUPLICATE);
        }

        // 查询账号是否重复
        List<SignUpUnitDO> byUsrename = signUpUnitMapper.selectByUsernameFromTemplate(createReqVO.getUsername());
        if (!byUsrename.isEmpty()) {
            throw exception(SIGN_UP_UNIT_USERNAME_DUPLICATE);
        }

        // 创建第二个 SignUpUnitDO 对象
        SignUpUnitDO signUpUnit = SignUpUnitConvert.INSTANCE.convert(createReqVO);
        // 设置模板数据
        signUpUnit.setTemplate(1); // 模板数据
        signUpUnit.setClassId(0L); // 模板数据 classId
        signUpUnit.setIsRestrict(1);

        // 创建管理员用户  会校验用户名重复
        Long userId = adminUserApi.createAdminForUnit(createReqVO.getUsername(),
                createReqVO.getPhone(),
                createReqVO.getUsername()).getCheckedData();

        signUpUnit.setUserId(userId);
        // 插入第二条数据
        signUpUnitMapper.insert(signUpUnit);

        // 返回第一个对象的 ID
        return signUpUnit.getId();
    }

    private void validatePhoneNumber(String phone) {

        if (StringUtils.isBlank(phone)) {
            throw exception(SIGN_UP_UNIT_PHONE_IS_BLANK);
        }

        if (!ValidationUtils.isMobile(phone)) {
            throw exception(SIGN_UP_UNIT_PHONE_FORMAT_ERROR);
        }


    }

    private void validateUsername(String username) {
        if (StringUtils.isBlank(username)) {
            throw exception(SIGN_UP_UNIT_USERNAME_IS_BLANK);
        }
        // 仅支持字母、数字，长度限制20个字符
        if (username.length() > 20 || !validateUsernameFormat(username)) {
            throw exception(SIGN_UP_UNIT_USRENAME_FORMAT_ERROR);
        }
    }

    public static boolean validateUsernameFormat(String str) {
        // 定义正则表达式模式，匹配仅包含字母和数字，且长度不超过 20 个字符
        String pattern = "^[a-zA-Z0-9]{1,20}$";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(str);
        return m.matches();
    }

    @Override
    public void updateSignUpUnit(SignUpUnitUpdateReqVO updateReqVO) {

        String phone = updateReqVO.getPhone();
        if (phone != null && phone != "") {
            String decryptedPhone = desensitizeDecrypt((phone));
            updateReqVO.setPhone(decryptedPhone);
        }
        String officePhone = updateReqVO.getOfficePhone();
        if (officePhone != null && officePhone != "") {
            String decryptedOfficePhone = desensitizeDecrypt((officePhone));
            updateReqVO.setOfficePhone(decryptedOfficePhone);
        }

        // 校验存在
        SignUpUnitDO signUpUnitDO = this.validateSignUpUnitExists(updateReqVO.getId());

        // 单位名称唯一性校验
        validateShortName(updateReqVO.getUnitName(),signUpUnitDO.getId());
        // 屏蔽用户名更新
        updateReqVO.setUsername(null);
        // 更新
        SignUpUnitDO updateObj = SignUpUnitConvert.INSTANCE.convert(updateReqVO);

        Long userId = signUpUnitDO.getUserId();
        if (Objects.isNull(userId)) {
            log.info("更新单位账号管理: id={}，单位用户为空，新增绑定手机号{}用户信息",
                    updateReqVO.getId(), updateReqVO.getPhone());
            // 如果userId为空，更新的时候就会新增这个手机号的用户
            validatePhoneNumber(updateReqVO.getPhone());
            // 查询是否和其他单位手机号重复
            Integer count = signUpUnitMapper.countByPhoneAndUnitId(updateReqVO.getPhone(),signUpUnitDO.getId());
            if (count != 0) {
                throw exception(SIGN_UP_UNIT_PHONE_DUPLICATE);
            }
            String username = updateReqVO.getPhone();
            // 用户名重复
            int i = 1;
            // 查询账号是否重复
            List<SignUpUnitDO> byUsrename = signUpUnitMapper.selectByUsernameFromTemplate(username);
            while (!byUsrename.isEmpty()) {
                username = updateReqVO.getPhone() + '_' + i;
                byUsrename = signUpUnitMapper.selectByUsernameFromTemplate(username);
                i++;
                if (i > 20) {
                    throw exception(SIGN_UP_UNIT_USERNAME_DUPLICATE);
                }
            }
            updateObj.setUsername(username);
            updateObj.setPhone(updateReqVO.getPhone());
            // 创建管理员用户
            Long createUserId = adminUserApi.createAdminForUnit(updateObj.getUsername(),
                    updateObj.getPhone(),
                    updateObj.getUsername()).getCheckedData();
            updateObj.setUserId(createUserId);
            log.info("调训单位管理员更新时新增绑定用户：username = {}, userId = {}", updateObj.getUsername(),
                    updateObj.getUserId());
        } else {
            // 手机号修改联动用户
            if (StringUtils.isNotBlank(updateReqVO.getPhone()) &&
                    !updateReqVO.getPhone().equals(signUpUnitDO.getPhone())) {
                validatePhoneNumber(updateReqVO.getPhone());
                // 查询是否和其他单位手机号重复
                Integer count = signUpUnitMapper.countByPhoneAndUnitId(updateReqVO.getPhone(),signUpUnitDO.getId());
                if (count != 0) {
                    throw exception(SIGN_UP_UNIT_PHONE_DUPLICATE);
                }
                userId = adminUserApi.updateAdminMobileForUnit(signUpUnitDO.getUserId(),
                        updateReqVO.getPhone()).getCheckedData();
                log.info("调训单位管理员：{}，手机号修改{}->{} userId:{}", signUpUnitDO.getUsername(),
                        signUpUnitDO.getPhone(),
                        updateReqVO.getPhone(),
                        userId);
            }
            updateObj.setUserId(userId);
        }

        signUpUnitMapper.updateById(updateObj);

        //异步更新班级和学员单位信息
        try {
            self.updateClassAndTraineeUnitInfo(updateObj);
        } catch (Exception e) {
            log.error("更新班级和学员单位信息失败：{}", e.getMessage());
        }
    }

    @Async
    @Override
    public void updateClassAndTraineeUnitInfo(SignUpUnitDO updateObj) {

        // 更新班级单位信息
        List<Integer> unitIds = updateClassUnitInfo(updateObj);

        // 更新学员单位信息
        traineeService.updateTraineeUnitInfo(unitIds,updateObj);
        log.info("更新班级和学员单位信息成功");

    }

    private List<Integer> updateClassUnitInfo(SignUpUnitDO updateObj) {
        //根据parentId查出对应的单位
        List<SignUpUnitDO> list = signUpUnitMapper.selectByParentId(Collections.singletonList(updateObj.getId()));

        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        list.forEach(item -> {
            item.setUnitName(updateObj.getUnitName());
            item.setPhone(updateObj.getPhone());
            item.setUnitClassification(updateObj.getUnitClassification());
        });

        updateBatchById(list);

        return list.stream()
                .map(SignUpUnitDO::getId)
                .collect(Collectors.toList());
    }

    @Override
    public void updateProfileUnit(ProfileUnitUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateSignUpUnitExists(updateReqVO.getId());

        this.validatePhoneDuplicate(updateReqVO);

        SignUpUnitDO updateObj = SignUpUnitConvert.INSTANCE.convert(updateReqVO);
        Long userId = adminUserApi.updateAdminMobileForUnit(getLoginUserId(), updateReqVO.getPhone()).getCheckedData();
        updateObj.setUserId(userId);
        signUpUnitMapper.updateById(updateObj);

    }

    @Override
    public void resetUserPassword(Long id) {
        SignUpUnitDO signUpUnitDO = signUpUnitMapper.selectById(id);
        if (signUpUnitDO == null) {
            throw exception(SIGN_UP_UNIT_NOT_EXISTS);
        }

        Long userId = signUpUnitDO.getUserId();

        adminUserApi.resetUserPassword(userId);

    }

    @Override
    public List<String> getPhoneList() {
        return signUpUnitMapper.getPhoneList();
    }

    private void validatePhoneDuplicate(ProfileUnitUpdateReqVO updateReqVO) {
        SignUpUnitDO signUpUnitDO = signUpUnitMapper.selectById(updateReqVO.getId());
        if (!Objects.equals(signUpUnitDO.getPhone(), updateReqVO.getPhone())) {
            // 查询是否和其他单位手机号重复
            Integer count = signUpUnitMapper.countByPhone(updateReqVO.getPhone());
            if (count != 0) {
                throw exception(SIGN_UP_UNIT_PHONE_DUPLICATE);
            }
        }
    }

    @Override
    public void deleteSignUpUnit(Integer id) {
        // 校验存在
        this.validateSignUpUnitExists(id);
        // if (Objects.nonNull(signUpUnitDO.getUserId())){
        //     // 删除管理员用户
        //     adminUserApi.deleteByIds(Collections.singletonList(signUpUnitDO.getUserId()))
        //             .getCheckedData();
        // }
        // 删除
        signUpUnitMapper.deleteById(id);
    }


    /**
     * 批量删除
     *
     * @param signUpUnitDeleteVO
     * @return EduSignUpUnit
     */
    @Override
    public void deleteSignUpUnitBatch(SignUpUnitDeleteVO signUpUnitDeleteVO) {

        String ids = signUpUnitDeleteVO.getIds();

        // 将 IDs 字符串按逗号分割成数组
        String[] idArray = ids.split(",");
        List<Long> userIdList = new ArrayList<>();
        try {
            for (String idStr : idArray) {

                // 转化成 int 类型
                int id = Integer.parseInt(idStr);

                // 校验存在
                SignUpUnitDO signUpUnitDO = this.validateSignUpUnitExists(id);
                // 删除
                signUpUnitMapper.deleteById(id);
                if (Objects.nonNull(signUpUnitDO.getUserId())) {
                    userIdList.add(signUpUnitDO.getUserId());
                }
            }
        } catch (Exception e) {
            log.info("批量删除单位存在失败：{}", e.getMessage());
        }
        // adminUserApi.deleteByIds(userIdList)
        //         .getCheckedData();
    }

    private SignUpUnitDO validateSignUpUnitExists(Integer id) {
        SignUpUnitDO signUpUnitDO = signUpUnitMapper.selectById(id);
        if (signUpUnitDO == null) {
            throw exception(SIGN_UP_UNIT_NOT_EXISTS);
        }
        return signUpUnitDO;
    }

    @Override
    public SignUpUnitDO getSignUpUnit(Integer id) {
        SignUpUnitDO signUpUnit =  signUpUnitMapper.selectById(id);
        String phone = signUpUnit.getPhone();
        if (phone != null) {
            String encryptedPhone = desensitizeEncrypt((phone));
            signUpUnit.setPhone(encryptedPhone);
        }
        String officePhone = signUpUnit.getOfficePhone();
        if (officePhone != null) {
            String encryptedOfficePhone = desensitizeEncrypt((officePhone));
            signUpUnit.setOfficePhone(encryptedOfficePhone);
        }
        return signUpUnit;
    }

    @Override
    public PageResult<SignUpUnitRespVO> getSignUpUnitPage(SignUpUnitPageReqVO pageReqVO) {
        PageResult<SignUpUnitDO> pageResult = signUpUnitMapper.selectPage(pageReqVO);
        PageResult<SignUpUnitRespVO> result = SignUpUnitConvert.INSTANCE.convertPage(pageResult);

        Integer current = pageReqVO.getPageNo();
        Integer pageSize = pageReqVO.getPageSize();

        List<SignUpUnitRespVO> list = result.getList();

        List<Integer> ids = list.stream().map(SignUpUnitRespVO::getId).collect(Collectors.toList());

        List<SignUpUnitDO> signUnitList = signUpUnitMapper.selectByParentId(ids);

        Map<Long, Boolean> map = signUnitList.stream()
                .collect(Collectors.toMap(
                        SignUpUnitDO::getParentId,
                        unit -> true,
                        (existingValue, newValue) -> existingValue
                ));

        int i = 0;
        for (SignUpUnitRespVO unit : list) {
            unit.setIndex((current - 1) * pageSize + i + 1);
            i++;
            String rawPhone = unit.getPhone();
            if (rawPhone != null) {
                String phone = DesensitizeUtils.mobileDesensitize(rawPhone);
                unit.setPhone(desensitizeEncrypt(phone));
            }

            String rawOfficePhone = unit.getOfficePhone();
            if (rawOfficePhone != null) {
                String officePhone = DesensitizeUtils.mobileDesensitize(rawOfficePhone);
                unit.setOfficePhone(desensitizeEncrypt(officePhone));
            }
            unit.setDeleteFlag(map.getOrDefault(Long.valueOf(unit.getId()), false));
        }

        result.setList(list);

        return result;
    }

    /**
     * 更新限制状态
     *
     * @param id       , restrict
     * @param restrict
     * @return
     */
    @Override
    public Integer updateSignUpUnitRestrict(Integer id, Integer restrict) {
        return signUpUnitMapper.updateSignUpUnitRestrictById(id, restrict);
    }

    @Override
    public List<Map<String, String>> getByClassId(Integer classId) {
        List<SignUpUnitDO> list = signUpUnitMapper.getByClassId(classId);
        // 处理成 map
        List<Map<String, String>> maps = new ArrayList<>();

        for (SignUpUnitDO unit : list) {
            Map<String, String> map = new HashMap<>();
            map.put("value", unit.getId().toString());
            map.put("label", unit.getUnitName());
            maps.add(map);
        }
        return maps;
    }

    @Override
    public TraineeImportRespVO importInfo(List<SignUnitImportTemplateExcelVO> list) {

        List<SignUpUnitDO> importList = SignUpUnitConvert.INSTANCE.convertImportList(list);

        // 用于存储错误信息
        List<String> errorMessages = new ArrayList<>();

        // 用于存储校验通过和校验失败的数据
        List<SignUpUnitDO> rightData = new ArrayList<>();
        // 用于存储校验通过的数据的索引
        List<Integer> rightDataIndex = new ArrayList<>();

        // 查出模板中所有单位名称
        Set<String> nameSet = baseMapper.getAllUnitName();

        // 该租户所有单位
        List<SignUpUnitDO> allSignUpUnitDOList = signUpUnitMapper.getAllTemplateList();
        // 所有已存在的账号
        Set<String> allUsernameList = allSignUpUnitDOList.stream()
                .map(SignUpUnitDO::getUsername)
                .collect(Collectors.toSet());
        // 所有已存在的手机号
        Set<String> allPhoneList = allSignUpUnitDOList.stream()
                .map(SignUpUnitDO::getPhone).collect(Collectors.toSet());

        Map<String, Map<String, Long>> unitClassification = signUpUnitMapper.getSignUnitClassification();

        for (int i = 0; i < list.size(); i++) {

            List<String> currentErrors = new ArrayList<>(); // 当前数据的错误信息列表

            SignUnitImportTemplateExcelVO vo = list.get(i);
            SignUpUnitDO signUpUnit = importList.get(i);

            // 单位管理员用户名不能为空
            if (StringUtils.isBlank(vo.getUsername())) {
                currentErrors.add("第 " + (i + 2) + " 行: 单位管理员用户名不能为空");
            } else {
                try {
                    validateUsername(vo.getUsername());
                    if (allUsernameList.contains(vo.getUsername())) {
                        currentErrors.add("第 " + (i + 2) + " 行: 单位管理员用户名重复");
                    }
                } catch (Exception e) {
                    currentErrors.add("第 " + (i + 2) + " 行: " + e.getMessage());
                }

            }

            // 单位名称
            if (StringUtils.isNotBlank(vo.getUnitName())) {

                if (nameSet.contains(vo.getUnitName())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 单位名称重复");
                }

//                if (vo.getUnitName().length() > 50) {
//                    currentErrors.add("第 " + (i + 2) + " 行: 单位名称超出长度");
//                }
                // 姓名只包含中英文
//                boolean containsOnlyChineseAndEnglish = vo.getUnitName().matches("^[\\u4e00-\\u9fa5a-zA-Z]+$");
//                if (!containsOnlyChineseAndEnglish) {
//                    currentErrors.add("第 " + (i + 2) + " 行: 单位名称只能包含中文和英文");
//                }

            } else {
                currentErrors.add("第 " + (i + 2) + " 行: 单位名称不能为空");
            }

            // 手机号不能为空
            if (StringUtils.isBlank(vo.getPhone())) {
                currentErrors.add("第 " + (i + 2) + " 行: 负责人手机号不能为空");
            } else {
                if (!isValidPhoneNumber(vo.getPhone())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 负责人手机号格式不对");
                }
                if (allPhoneList.contains(vo.getPhone())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 负责人手机号重复");
                }
            }

            if (StrUtil.isBlank(vo.getUnitClassificationName())) {
                currentErrors.add("第 " + (i + 2) + " 行: 单位分类不能为空");
            } else {
                if (unitClassification.get(vo.getUnitClassificationName()) == null) {
                    currentErrors.add("第 " + (i + 2) + " 行: 单位分类数据不正确");
                }
            }

            if (StrUtil.isBlank(vo.getStatus())) {
                currentErrors.add("第 " + (i + 2) + " 行: 是否禁用不能为空");
            } else {
                if (!Objects.equals(vo.getStatus(), UnitStatusEnum.ENABLE.getStatus().toString())
                        && !Objects.equals(vo.getStatus(), UnitStatusEnum.FORBIDDEN.getStatus().toString())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 是否禁用数据不正确");
                }
            }


            if (CollUtil.isNotEmpty(currentErrors)) {
                errorMessages.addAll(currentErrors);
            } else {
                signUpUnit.setTemplate(1);
                signUpUnit.setClassId(0L);
                signUpUnit.setUnitClassification(unitClassification.get(vo.getUnitClassificationName()).get("id").intValue());
                signUpUnit.setStatus(Integer.parseInt(vo.getStatus()));
                signUpUnit.setIsRestrict(1);
                signUpUnit.setCapacity(100);
                signUpUnit.setSort(0);
                rightData.add(signUpUnit);
                rightDataIndex.add(i);
                nameSet.add(vo.getUnitName());
            }

        }

        // 处理校验通过的数据：保存到数据库
        if (CollUtil.isNotEmpty(rightData)) {
            //将rightData倒序，后面的先新增
            Collections.reverse(rightData);
            List<SignUpUnitDO> insertList = new ArrayList<>();
            // 组装CreateAdminForUnitBatchDTO 用于批量新增
            List<CreateAdminForUnitDTO> createUserInfos = rightData.stream().map(o -> {
                CreateAdminForUnitDTO createAdminForUnitBatchDTO = new CreateAdminForUnitDTO();
                createAdminForUnitBatchDTO.setUsername(o.getUsername());
                createAdminForUnitBatchDTO.setMobile(o.getPhone());
                createAdminForUnitBatchDTO.setNickname(o.getUsername());
                return createAdminForUnitBatchDTO;
            }).collect(Collectors.toList());

            CreateAdminForUnitBatchDTO dtos = new CreateAdminForUnitBatchDTO();
            dtos.setUserInfoList(createUserInfos);

            // 批量新增用户
            List<CreateAdminForUnitBatchResultDTO> insertResult = adminUserApi.batchCreateAdminForUnit(dtos)
                    .getCheckedData();
            Map<String, CreateAdminForUnitBatchResultDTO> usernameToResult = insertResult.stream()
                    .collect(Collectors.toMap(CreateAdminForUnitBatchResultDTO::getUsername, o -> o, (v1, v2) -> v1));

            for (int j = 0; j < rightData.size(); j++) {
                SignUpUnitDO signUpUnitDO = rightData.get(j);
                CreateAdminForUnitBatchResultDTO result = usernameToResult.get(signUpUnitDO.getUsername());
                if (Objects.nonNull(result)) {
                    // 如果新增用户失败
                    if (Objects.nonNull(result.getErrorInfoList())
                            && !result.getErrorInfoList().isEmpty()) {
                        for (String errorInfo : result.getErrorInfoList()) {
                            errorMessages.add("第 " + (rightDataIndex.get(j) + 2) + " 行: " + errorInfo);
                        }
                    }
                    if (Objects.nonNull(result.getId())) {
                        signUpUnitDO.setUserId(result.getId());
                        insertList.add(signUpUnitDO);
                    }
                }
            }
            if (CollUtil.isNotEmpty(insertList)) {
                baseMapper.insertBatch(insertList);
                if (insertList.size() == importList.size()) {
                    return TraineeImportRespVO.builder()
                            .count(insertList.size())
                            .tag(1)
                            .build();
                } else {
                    return TraineeImportRespVO.builder()
                            .errorMessages(errorMessages)
                            .count(insertList.size())
                            .tag(2)
                            .build();
                }
            } else {
                return TraineeImportRespVO.builder()
                        .errorMessages(errorMessages)
                        .count(insertList.size())
                        .tag(3)
                        .build();
            }

        } else {
            return TraineeImportRespVO.builder()
                    .errorMessages(errorMessages)
                    .count(rightData.size())
                    .tag(3)
                    .build();
        }

    }

    @Override
    public List<SignUnitExcelVO> getSignUpUnitList(SignUpUnitPageReqVO reqVO) {

        List<SignUpUnitDO> unitList = signUpUnitMapper.selectExportUnitList(reqVO);

        List<SignUnitExcelVO> list = SignUpUnitConvert.INSTANCE.convertExportList(unitList);

        Map<Long, String> map = getDictMap(Collections.singletonList("edu_class_unitType"));

        int i = 0;
        for (SignUnitExcelVO unit : list) {
            SignUpUnitDO signUpUnitDO = unitList.get(i);
            unit.setUnitClassificationName(map.getOrDefault(Long.valueOf(signUpUnitDO.getUnitClassification()), ""));
            unit.setStatus(UnitStatusEnum.getDescByStatus(signUpUnitDO.getStatus()));
            i++;
        }

        return list;
    }

    @Override
    public Map<Long, String> getDictMap(List<String> dictTypes) {
        // 获取课程分类 教学形式 业中同步过来的字典数据
        List<DictDataRespDTO> dictData = dictDataApi.getByDictTypes(dictTypes).getCheckedData();
        if (CollUtil.isNotEmpty(dictData)) {
            return dictData.stream().collect(Collectors.toMap(DictDataRespDTO::getId, DictDataRespDTO::getLabel, (d1, d2) -> d1));
        }
        return Collections.emptyMap();
    }

    @Override
    public List<SignUpUnitRespVO> getSignUpUnitListByClassId(Long classId) {
        SignUpUnitPageReqVO reqVO = new SignUpUnitPageReqVO();
        reqVO.setClassId(classId);

        List<SignUpUnitDO> list = baseMapper.selectUnitList(reqVO);

        return SignUpUnitConvert.INSTANCE.convertList(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignCapacity(SignUnitAssignCapacityReqVO reqVO) {

        List<SignUnitAssignCapacityReqVO.AssignCapacityVO> list = reqVO.getList();


        List<SignUnitAssignCapacityReqVO.AssignCapacityVO> addList = list.stream()
                .filter(item -> Objects.equals(item.getTemplate(), UnitTemplateEnum.TEMPLATE.getStatus()))
                .collect(Collectors.toList());

        List<SignUnitAssignCapacityReqVO.AssignCapacityVO> updateList = list.stream()
                .filter(item -> Objects.equals(item.getTemplate(), UnitTemplateEnum.NOT_TEMPLATE.getStatus()))
                .collect(Collectors.toList());


        // 根据classId查询该班已经存在的单位，和updateList进行比较，筛选出已经删除的单位
        List<SignUpUnitRespVO> unitList = this.getSignUpUnitListByClassId(reqVO.getClassId());


        // 执行新增操作
        if (CollUtil.isNotEmpty(addList)) {

            Map<String, Integer> map = baseMapper.getSignUpUnitDOList().stream().collect(Collectors.toMap(SignUpUnitDO::getUnitName, SignUpUnitDO::getId, (k1, k2) -> k1));
            List<SignUpUnitDO> insertList = SignUpUnitConvert.INSTANCE.convertList1(addList);

            List<SignUpUnitDO> newList = new ArrayList<>();
            for (SignUpUnitDO item : insertList) {
                item.setClassId(reqVO.getClassId());
                item.setId(null);
                item.setStatus(UnitStatusEnum.ENABLE.getStatus());
                item.setTemplate(UnitTemplateEnum.NOT_TEMPLATE.getStatus());

                item.setParentId(Long.valueOf(map.get(item.getUnitName())));
                newList.add(item);
            }
            this.saveBatch(newList);


        } else {
            // 执行更新操作
            Set<Integer> oldUnitIds = unitList.stream().map(SignUpUnitRespVO::getId).collect(Collectors.toSet());

            List<SignUpUnitDO> newUpdateList = SignUpUnitConvert.INSTANCE.convertList2(updateList);

            Set<Integer> newUnitIds = newUpdateList.stream().map(SignUpUnitDO::getId).collect(Collectors.toSet());


            // 取出在oldUnitIds中但是不在newUnitIds中的id集合

            Set<Integer> deleteUnitIds = new HashSet<>(oldUnitIds);
            deleteUnitIds.removeAll(newUnitIds);

            // 如果存在需要删除的单位，则先删除
            if (!deleteUnitIds.isEmpty()) {
                this.removeBatchByIds(deleteUnitIds);
            }

            this.updateBatchById(newUpdateList);
        }


        return true;
    }

    /**
     * 根据单位管理员userId查询调训单位
     *
     * @param userId 单位管理员用户id
     * @return 调训单位信息
     */
    @Override
    public SignUpUnitDO getByUserId(Long userId) {
        return signUpUnitMapper.getByUserId(userId);
    }

    @Override
    public void updateSignUpUnitSort(Integer id, Integer sort) {
        // 校验存在
        this.validateSignUpUnitExists(id);

        // 更新排序号
        SignUpUnitDO updateObj = new SignUpUnitDO();
        updateObj.setId(id);
        updateObj.setSort(sort);
        signUpUnitMapper.updateById(updateObj);
    }

    private void validateShortName(String UnitName, Integer unitId) {

        SignUpUnitDO UnitNameObj = signUpUnitMapper.selectByUnitName(UnitName, unitId);
        if (UnitNameObj != null) {
            throw exception(SIGN_UP_UNIT_UNItNAME_EXISTS);
        }
    }

//    private void (Integer id, String UnitName) {
//        List<SignUpUnitDO> unitNameObjList = signUpUnitMapper.selectByUnitName(UnitName);
//
//        if (unitNameObjList.isEmpty()) {
//            return; // 若没有找到任何单位名，直接返回
//        }
//
//        // 如果 id 为空，说明不用比较是否为相同 id 的客户端
//        if (id == null) {
//            throw exception(SIGN_UP_UNIT_UNItNAME_EXISTS);
//        }
//
//        // 检查是否存在不同 id 的对象
//        for (SignUpUnitDO unit : unitNameObjList) {
//            if (!unit.getId().equals(id)) {
//                throw exception(SIGN_UP_UNIT_UNItNAME_EXISTS);
//            }
//        }
//    }


    public static boolean isValidPhoneNumber(String phoneNumber) {
        String regex = "^1[3-9]\\d{9}$";
        return phoneNumber.matches(regex);
    }
}
