package com.unicom.swdx.module.edu.service.teacherinformation;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassTimeTableReqVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.ElectiveReleaseClassTimeTeacherReqVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.*;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.businesscenter.TeachingHoursForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.businesscenter.TeachingHoursForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.utils.excel.ExcelImportResultRespVO;
import com.unicom.swdx.module.system.api.businesscenter.dto.PersonnalRespDTO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

public interface TeacherInformationService extends IService<TeacherInformationDO> {
    Long createTeacherInformation(TeacherInformationCreateReqVO createReqVO);

    void updateTeacherInformation(TeacherInformationUpdateReqVO updateReqVO, Boolean isEncrypted);

    void deleteTeacherInformation(Long id);

    TeacherInformationDO getTeacherInformation(Long id);

    List<TeacherInformationDO> getTeacherInformationList(Collection<Long> ids);

    PageResult<TeacherInformationRespVO> getTeacherInformationPage(TeacherInformationPageReqVO pageVO);

    List<TeacherInformationExcelVO> getTeacherInformationExportList(TeacherInformationPageReqVO exportVO);

    List<CourseInformationRespVO> getCourseInformationByTeacher(Long id);

    TeacherSyncResultRespVO syncTeacher(HttpServletRequest request , Long tenantid);



    TeacherSyncResultRespVO convertTeacherAndInsert(Long tenantId, List<PersonnalRespDTO> personnals);

    /**
     * 选修课管理-根据选修课发布上课时间段获取空闲下拉教师数据
     * @param reqVO 上课时间段
     * @return 教师列表
     */
    List<TeacherInformationSimpleRespVO> listForElectiveRelease(ElectiveReleaseClassTimeTeacherReqVO reqVO);

    Long getTeacherId(Long userId);
    /**
     * 获取全校教师信息列表
     * @return 教师列表
     */
    List<TeacherInformationRespVO> getTeacherInformationListAll();


    void comparisonTeacherUsers(Long tenantId);

    void deleteTeacherBySystemId(Long systemId);

    /**
     * 根据系统用户id获取教师id
     * @param systemId 系统用户id
     * @return 教师id
     */
    Long getTeacherBySystemId(Long systemId);

    /**
     * 教学培训-教师个人-仪表盘
     * @param userAppletBaseVO 开始时间+结束时间
     * @return 教学总工时+平均分+课程统计
     */
    UserAppletDashboardVO getDashboard(UserAppletBaseVO userAppletBaseVO);

    /**
     * 业中首页-仪表盘-教学课时
     * @param reqVO 请求参数
     * @return 教学课时
     */
    TeachingHoursForBusinessCenterRespVO getTeachingHours(TeachingHoursForBusinessCenterReqVO reqVO);

    /**
     * 教学培训-教师个人-教学方式
     * @param reqVO 课程类型+开始时间+结束时间
     * @return 教学方式
     */
    List<UserAppletTeachingMethodsVO> getTeachingMethods(UserAppletBaseVO reqVO);

    /**
     * 教学培训-课程情况
     * @param userAppletBaseVO 开始时间+结束时间
     * @return 课程情况
     */
    UserAppletReultVO getCourseSituation(UserAppletCourseSituationReqVO userAppletBaseVO);

    /**
     * 导出校外师资导入模板
     * @param request 请求
     * @param response 响应
     */
    void exportImportTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 外校师资导入
     * @param request 请求
     * @param list 导入数据
     * @return 导入结果
     */
    ExcelImportResultRespVO importTeacherInformation(HttpServletRequest request, List<TeacherInformationImportResultExcelVO> list);

    /**
     * 获取教师授课记录
     * 
     * @param reqVO 请求参数
     * @return 授课记录分页结果
     */
    PageResult<TeachingRecordRespVO> getTeachingRecordPage(TeachingRecordReqVO reqVO);

    /**
     * 获取教师授课记录Excel导出数据
     * 
     * @param reqVO 请求参数
     * @return 授课记录列表
     */
    List<TeachingRecordExcelVO> getTeachingRecordExportList(TeachingRecordReqVO reqVO);

}
