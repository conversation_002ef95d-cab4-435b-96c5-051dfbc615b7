package com.unicom.swdx.module.edu.dal.dataobject.classmanagement;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import com.unicom.swdx.module.edu.service.classcommen.CommenService;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * EduClassManagement DO
 *
 * <AUTHOR>
 */
@TableName("edu_class_management")
@KeySequence("edu_class_management_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassManagementDO extends TenantBaseDO {

    /**
     * 主键id,自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 班次编码
     */
    private String classNameCode;
    /**
     * 班次名称
     */
    private String className;
    /**
     * 办班类型 字典表中的id
     */
    private Integer classTypeDictId;
    /**
     * 班级属性
     */
    private Integer classAttribute;
    /**
     * 年度
     */
    private Integer year;
    /**
     * 学期，1-上学期，2-下学期
     */
    private Integer semester;
    /**
     * 学制
     */

    private Integer learningSystem;
    /**
     * 学制单位，1-天，2-周，3-月，默认为1
     */
    private Integer learningSystemUnit;
    /**
     * 培训对象
     */
    private String trainingObject;
    /**
     * 预计人数
     */
    private Integer peopleNumber;
    /**
     * 轮次
     */
    private String turn;
    /**
     * 校区
     */
    private Integer campus;
    /**
     * 报道时间
     */
    private LocalDateTime reportingTime;
    /**
     * 开班时间
     */
    private LocalDateTime classOpenTime;
    /**
     * 结业时间
     */
    private LocalDateTime completionTime;
    /**
     * 报名开始时间
     */
    private LocalDateTime registrationStartTime;
    /**
     * 缴费报道，1-是，2-否
     */
    private Integer paymentReport;
    /**
     * 考勤评课，1-是，2-否
     */
    private Integer evaluate;
    /**
     * 附件
     */
    private String accessory;
    /**
     * 排序号
     */
    private Integer sort;
    /**
     * 备注
     */
    private String remark;
    /**
     * 1-发布中，2-待发布
     */
    private Integer publish;
    /**
     * 报名结束时间
     */
    private LocalDateTime registrationEndTime;
    /**
     * 班主任
     */
    private Long classTeacherLead;
    /**
     * 辅导老师
     */
    private String coachTeacher;
    /**
     * 班级状态
     */
    @TableField(exist = false)
    private String classStatus;
    /**
     * 班级对应学员的人数
     */
    @TableField(exist = false)
    private Integer classPeopleCount;
    /**
     * 班主任姓名
     */
    @TableField(exist = false)
    private String classTeacherLeadName;
    /**
     * 辅导老师
     */
    @TableField(exist = false)
    private String coachTeacherName;

    /**
     * 到课考勤，0-开，1关
     */
    private Integer attendanceCheck;

    /**
     * 就餐考勤，0-开，1关
     */
    private Integer mealAttendance;

    /**
     * 住宿考勤，0-开，1关
     */
    private Integer checkIn;

    /**
     * 考勤状态 0-开启，1-关闭
     */
    @TableField(exist = false)
    private Integer attendanceStatus;

    /**
     * 到课考勤规则id
     */
    @TableField(exist = false)
    private Long attendanceCheckId;

    /**
     * 就餐考勤规则id
     */
    @TableField(exist = false)
    private Long mealAttendanceId;

    /**
     * 住宿考勤规则id
     */
    @TableField(exist = false)
    private Long checkInId;

    private String idCode;

    /**
     * 结业考核编码
     */
    private String id_code;

    /**
     * 班次来源
     */
    private Integer classSource;


    /**
     * 结业考核编码名称
     */
    @TableField(exist = false)
    private String idCodeName;
    public void checkTeacher(CommenService commenService){
        //去调用业中创建老师权限
    }

}
