package com.unicom.swdx.module.edu.controller.admin.leavereport.vo;

import lombok.Data;

import java.time.LocalDateTime;
@Data
public class LeaveReportRespVO {
    private Long id;
    /**
     * 序号
     */
    private Integer serialNumber;
    /**
     * 离校报备名称
     */
    private String name;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 班级id
     */
    private Long classId;
    /**
     * 班级名称
     */
    private String classname;
    /**
     * 填报状态
     */
    private Integer status;
    /**
     * 教师id
     */
    private Long teacherId;
    /**
     * 未填报人数
     */
    private Long unfilled;
    /**
     * 离校人数
     */
    private Long leaveCount;
    /**
     * 不离校人数
     */
    private Long stayCount;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 放假时间
     */
    private String vacationTime;
    /**
     * 班主任
     */
    private String classTeacher;
}
