package com.unicom.swdx.module.edu.controller.admin.leavereport.vo;

import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@ApiModel("班主任离校报备查询 Response VO")
@Data
public class TeacherLeaveReportLeaveVO {

    private List<TraineeDO> trainee;

    private Integer unfilled;

    private Integer stay;

    private Integer leave;

    private Long total;
}
