package com.unicom.swdx.module.edu.dal.dataobject.courses;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDate;

/**
 * 课程库 DO
 *
 * <AUTHOR>
 */
@TableName("edu_courses")
@KeySequence("edu_courses_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CoursesDO extends TenantBaseDO {

    /**
     * 课程主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 课程类型(1-专题课、2-选修课、3-教学活动)
     */
    private Integer coursesType;
    /**
     * 课程名称
     */
    private String name;
    /**
     * 别名
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String shortName;
    /**
     * 课程分类字典ID
     */
    private Long themeId;
    /**
     * 教学形式字典ID
     */
    private Long educateFormId;
    /**
     * 教学方式字典ID
     */
    private Long teachingMethodId;
    /**
     * 管理部门字典ID(人事系统部门)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long managementDeptId;
    /**
     * 状态，0启用，1封库
     */
    private Integer status;
    /**
     * 开发时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDate date;
    /**
     * 教学活动库活动类型字典ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long activityType;
    /**
     * 系统内部门
     */
    private Long deptId;
//    /**
//     * 是否党政领导讲课，0不是，1是
//     */
//    private Boolean isLeaderLecture;

}
