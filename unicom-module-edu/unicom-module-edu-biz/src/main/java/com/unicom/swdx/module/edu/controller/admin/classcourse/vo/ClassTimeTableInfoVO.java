package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoRespVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ClassTimeTableInfoVO {

    @ApiModelProperty(value = "唯一标识", required = true)
    private Long id;
    private Long courseId;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;
    //早中晚（0，1，2）
    private String period;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "教师")
    private String teacherName;

    @ApiModelProperty(value = "教室")
    private String classroom;

    @ApiModelProperty(value = "是否合班授课")
    private Boolean isMerge;

    @ApiModelProperty(value = "是否显示合班授课标签 true - 是，false - 否")
    private Boolean isShowMergeTag;

    private String date;

    private Integer coursesType;
    private Long educateFormId;
    private Integer activityType;

    private String courseTypeName;


    private Integer classroomId;

    @ApiModelProperty(value = "常用教室")
    private Long classroomName;

    @ApiModelProperty(value = "班级考勤状态")
    private Boolean isCheck;

    @ApiModelProperty(value = "授课者字符串，id之间逗号间隔")
    private String teacherIdString;

    @ApiModelProperty(value = "是否为部门授课，默认为教师授课")
    private Boolean department;

    @ApiModelProperty(value = "是否党政领导讲课，0不是，1是")
    private Boolean isLeaderLecture;



    @ApiModelProperty(value = "是否调课")
    private Boolean isChange;

}
