package com.unicom.swdx.module.edu.dal.mysql.todoitems;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.todoitems.vo.TodoItemsReqVO;
import com.unicom.swdx.module.edu.controller.admin.todoitems.vo.TodoItemsRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.todoitems.TodoItemsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 班主任待办事项 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TodoItemsMapper extends BaseMapperX<TodoItemsDO> {


    List<TodoItemsRespVO> selectListByReq(@Param("reqVO") TodoItemsReqVO reqVO);

    default List<TodoItemsDO> selectListByStatusAndClassId(Integer status, Long classId) {
        return selectList(new LambdaQueryWrapperX<TodoItemsDO>()
                .eq(TodoItemsDO::getStatus, status)
                .eq(TodoItemsDO::getClassId, classId)
                .orderByDesc(TodoItemsDO::getCreateTime));
    }

    default TodoItemsDO selectOneByClassIdAndTypeAndStatus(Long classId, Integer type, Integer status){
        return selectOne(new LambdaQueryWrapperX<TodoItemsDO>()
                .eq(TodoItemsDO::getClassId, classId)
                .eq(TodoItemsDO::getType, type)
                .eq(TodoItemsDO::getStatus, status)
                .orderByDesc(TodoItemsDO::getCreateTime)
                .last("limit 1"));
    }

    default TodoItemsDO getByLeaveId(Long leaveId) {
        LambdaQueryWrapperX<TodoItemsDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(TodoItemsDO::getLeaveId, leaveId);
        wrapperX.eq(TodoItemsDO::getDeleted, false);
        wrapperX.orderByDesc(TodoItemsDO::getId);
        wrapperX.last("limit 1");
        return selectOne(wrapperX);
    }
}
