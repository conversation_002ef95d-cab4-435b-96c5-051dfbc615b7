package com.unicom.swdx.module.edu.controller.admin.questionnairemanagement;

import com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo.QuestionCategoryManagementListReqVO;
import com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo.QuestionCategoryManagementRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.questioncategorymanagement.QuestionCategoryManagementDO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairemanagement.QuestionnaireManagementDO;
import com.unicom.swdx.module.edu.service.questionnairemanagement.QuestionnaireManagementService;

@Tag(name = "管理后台 - 评估问卷管理")
@RestController
@RequestMapping("/edu/questionnaire-management")
@Validated
public class QuestionnaireManagementController {

    @Resource
    private QuestionnaireManagementService questionnaireManagementService;

    @PostMapping("/create")
    @Operation(summary = "创建评估问卷管理")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:create')")
    public CommonResult<Long> createQuestionnaireManagement(@Valid @RequestBody QuestionnaireManagementSaveReqVO createReqVO) {
        return success(questionnaireManagementService.createQuestionnaireManagement(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新评估问卷管理")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:update')")
    public CommonResult<Boolean> updateQuestionnaireManagement(@Valid @RequestBody QuestionnaireManagementSaveReqVO updateReqVO) {
        questionnaireManagementService.updateQuestionnaireManagement(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除评估问卷管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:delete')")
    public CommonResult<Boolean> deleteQuestionnaireManagement(@RequestParam("id") Long id) {
        questionnaireManagementService.deleteQuestionnaireManagement(id);
        return success(true);
    }

    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除评估问卷管理")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:delete')")
    public CommonResult<Boolean> deleteQuestionnaireManagement(@RequestParam("ids") List<Long> ids) {
        questionnaireManagementService.batchDeleteQuestionnaireManagement(ids);
        return success(true);
    }

    @GetMapping("/getCollectingEducateForm")
    @Operation(summary = "获取正在收集中的教学形式")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:delete')")
    public CommonResult<List<Long>> getCollectingEducateForm() {
       return success(questionnaireManagementService.getCollectingEducateForm());
    }

    @GetMapping("/get")
    @Operation(summary = "获得评估问卷管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Parameter(name = "userId", description = "用户ID", required = false)
    @Parameter(name = "classCourseId", description = "课程ID", required = false)
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:query')")
    public CommonResult<QuestionnaireManagementRespVO> getQuestionnaireManagement(@RequestParam("id") Long id,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "classCourseId", required = false) Long classCourseId) {
        QuestionnaireManagementRespVO questionnaireManagement = questionnaireManagementService
                .getQuestionnaireManagement(id, userId, classCourseId);
        return success(questionnaireManagement);
    }

    @GetMapping("/getRateResult")
    @Operation(summary = "获得已评的问卷")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:query')")
    public CommonResult<QuestionnaireManagementRespVO> getRatedQuestionnaire(@RequestParam("questionnaireId") Long questionnaireId,
                                                                             @RequestParam("classCourseId") Long classCourseId,
                                                                             @RequestParam(value = "userId", required = false) Long userId) {
        QuestionnaireManagementRespVO questionnaireManagement = questionnaireManagementService.getRatedQuestionnaireManagement(questionnaireId,userId, classCourseId);
        return success(questionnaireManagement);
    }

    @GetMapping("/questionList")
    @Operation(summary = "获得题库列表")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<List<QuestionRespVO>> getQuestionCategoryManagementList(@Valid QuestionListReqVO listReqVO) {
        List<QuestionRespVO> list = questionnaireManagementService.getQuestionList(listReqVO);
        return success(list);
    }

    @GetMapping("/page")
    @Operation(summary = "获得评估问卷管理分页")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:query')")
    public CommonResult<PageResult<QuestionnaireManagementRespVO>> getQuestionnaireManagementPage(@Valid QuestionnaireManagementPageReqVO pageReqVO) {
        PageResult<QuestionnaireManagementRespVO> pageResult = questionnaireManagementService.getQuestionnaireManagementPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出评估问卷管理 Excel")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:export')")

    public void exportQuestionnaireManagementExcel(@Valid QuestionnaireManagementPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<QuestionnaireManagementRespVO> list = questionnaireManagementService.getQuestionnaireManagementPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "评估问卷管理.xls", "数据", QuestionnaireManagementRespVO.class,
                        BeanUtils.toBean(list, QuestionnaireManagementRespVO.class));
    }

}
