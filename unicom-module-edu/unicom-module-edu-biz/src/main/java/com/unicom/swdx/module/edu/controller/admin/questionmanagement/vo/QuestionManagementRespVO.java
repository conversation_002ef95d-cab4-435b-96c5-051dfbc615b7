package com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo;

import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.QuestionLogicRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 题目管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionManagementRespVO {

    @Schema(description = "主键ID",  example = "9775")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "题干")
    @ExcelProperty("题干")
    private String stem;

    @Schema(description = "标题")
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "题目类型(字典)", example = "1")
    @ExcelProperty("题目类型(字典)")
    private String questionType;

    @Schema(description = "分数")
    @ExcelProperty("分数")
    private BigDecimal score;

    @Schema(description = "所属类别", example = "10861")
    @ExcelProperty("所属类别")
    private Long categoryId;

    @Schema(description = "描述", example = "随便")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "创建部门")
    @ExcelProperty("创建部门")
    private Long createDept;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private Long creator;

    @Schema(description = "一票否决说明题1是0不是")
    @ExcelProperty("一票否决说明题1是0不是")
    private String oneBallotVetoResult;

    @Schema(description = "是否被添加逻辑(0没有 ，1 有 )")
    @ExcelProperty("是否被添加逻辑(0没有 ，1 有 )")
    private Short isLogic;

    private List<OptionsDO> options;

    @ApiModelProperty(value = "序号")
    private Long serialNumber;

    @Schema(description = "一票否决说明题1是0不是")
    private Boolean oneBallotVeto;

    @Schema(description = "0不是1是必答题")
    private Boolean required;

    @Schema(description = "一票否决对应的选项id")
    private Long optionId;

    @Schema(description = "和问卷的关联主键ID",  example = "9775")
    private Long detailId;

    @Schema(description = "评估项关联的逻辑")
    private List<QuestionLogicRespVO> questionLogic;

    @Schema(description = "评估项被关联的逻辑")
    private List<QuestionLogicRespVO> logicQuestion;

    private String rateScore;

    private String rateOptionId;

    private String rateContent;
}
