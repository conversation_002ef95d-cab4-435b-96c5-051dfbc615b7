package com.unicom.swdx.module.edu.dal.mysql.training;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.common.util.crypt.sm4.SM4Util;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoImportDictLableExcelVO;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.*;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO;
import com.unicom.swdx.module.edu.convert.trainee.TraineeConvert;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TraineeMapper extends BaseMapperX<TraineeDO> {
    Page<RegistrationPageRespVO> getRegistrationPage(Page<RegistrationInfoReqVO> objectPage, @Param("reqVO") RegistrationInfoReqVO reqVO);

    List<RegistrationDetailVO> getRegistrationInfo(@Param("reqVO") RegistrationInfoReqVO reqVO);

//    List<Map<String, Object>> getRegistrationInfo2(@Param("reqVO") RegistrationInfoReqVO reqVO);

    Page<RegistrationInfoRespVO> getRegistrationInfoPage(@Param("objectPage") Page<RegistrationInfoReqVO> objectPage, @Param("reqVO") RegistrationInfoReqVO reqVO);

    Page<UnitRegistrationPageRespVO> getPageByUnitId(@Param("objectPage") Page<Object> objectPage, @Param("reqVO") UnitRegistrationPageReqVO reqVO);

    Page<UnitRegistrationPageRespVO> pageTraineeInfo(@Param("objectPage") Page<Object> objectPage, @Param("reqVO") TraineeInfoReqVO reqVO);


    default List<TraineeDO> getTraineeByCardNoAndClassId(TraineeBaseVO reqVO) {
        // 查找同一时间开班的班级id
        List<Long> classIds = this.getClassIdSameTime(reqVO.getClassId());
        if (classIds == null || classIds.isEmpty()) {
            return null;
        }
        // 对 cardNo 进行加密
        String encryptedCardNo = SM4Util.encrypt(reqVO.getCardNo());
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeDO::getCardNo, encryptedCardNo);
        wrapper.in(TraineeDO::getClassId, classIds);

        //小程序没有登陆，需要手动传入tenantId
        if (reqVO.getTenantId() != null){
            wrapper.eq(TraineeDO::getTenantId, reqVO.getTenantId());
        }

        // 编辑时需要去除本身
        wrapper.ne(Objects.nonNull(reqVO.getId()), TraineeDO::getId, reqVO.getId());
//        wrapper.last("limit 1");
        List<TraineeDO> traineeDOList = selectList(wrapper);

        if (traineeDOList.isEmpty()){
            return Collections.emptyList();
        }

        return traineeDOList;
        //看是否存在开班时间重叠的班级
//        List<Long> traineeClassIds = traineeDOList.stream().map(TraineeDO::getClassId).distinct().collect(Collectors.toList());
//
//        return list.get(0);
    }

    default List<TraineeDO> getTraineeByTraineeList(List<TraineeDO> list,List<Long> classIds) {

        if (CollUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        // 查找同一时间开班的班级id
//        List<Long> classIds = this.getClassIdSameTime(list.get(0).getClassId());
        if (classIds == null || classIds.isEmpty()) {
            return null;
        }

        // 对 cardNo 进行加密
        List<String> encryptedCardNos = SM4Util.encryptList(list.stream().map(TraineeDO::getCardNo).collect(Collectors.toList()));
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TraineeDO::getCardNo, encryptedCardNos);
        wrapper.in(TraineeDO::getClassId, classIds);

        return selectList(wrapper);
    }

    default List<TraineeDO> getTraineeByTraineePhone(List<TraineeDO> list,List<Long> classIds) {

        if (CollUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        // 查找同一时间开班的班级id
//        List<Long> classIds = this.getClassIdSameTime(list.get(0).getClassId());
        if (classIds == null || classIds.isEmpty()) {
            return null;
        }

        // 对 cardNo 进行加密
        List<String> phones = list.stream().map(TraineeDO::getPhone).collect(Collectors.toList());
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TraineeDO::getPhone, phones);
        wrapper.in(TraineeDO::getClassId, classIds);

        return selectList(wrapper);
    }

    List<Long> getClassIdSameTime(@Param("classId") Long classId);

    Page<ReportPageRespVO> reportPage(@Param("objectPage") Page<Object> objectPage, @Param("reqVO") ReportPageReqVO reqVO);

    Page<ReportPageRespVO> reportInfoPageByStatus(@Param("objectPage") Page<Object> objectPage, @Param("reqVO") ReportPageReqVO reqVO);

    List<RegistrationInfoExcelVO> getRegistrationInfoList(@Param("reqVO") RegistrationInfoReqVO reqVO);

    List<UnitRegistrationPageRespVO> getTraineeInfoList(@Param("reqVO") TraineeInfoReqVO reqVO);

    default void editTrainee(EditTraineeInfoReqVO reqVO) {
        TraineeDO traineeDO = TraineeConvert.INSTANCE.convert1(reqVO);

        updateById(traineeDO);

        // 如果 unitId 为 null，使用 updateWrapper 置空 unitId、unitName、unitClassification
        if (traineeDO.getUnitId() == null) {
            UpdateWrapper<TraineeDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("unit_id", null) // 置空 unitId
                    .set("unit_name", null) // 置空 unitName
                    .set("unit_classification", null) // 置空 unitClassification
                    .eq("id", traineeDO.getId()); // 根据 ID 更新

            update(null, updateWrapper); // 传入 null 作为实体对象，表示只更新 updateWrapper 中指定的字段
        }
    }

    default Boolean unshackleTrainee(List<Long> classIds) {
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TraineeDO::getClassId, classIds);
        List<Long> traineeIds = this.selectList(wrapper).stream().map(TraineeDO::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(traineeIds)) {
            this.deleteBatchIds(traineeIds);
        }
        return true;
    }

    Integer getClassStatus(@Param("classId") Long classId);

    default Long getActualPeopleNumber(Long id, int status) {
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        if (status != 2) {
            wrapper.eq(TraineeDO::getClassId, id);
        } else {
            wrapper.eq(TraineeDO::getUnitId, id);
        }
        if (status == 1) {
            wrapper.in(TraineeDO::getStatus, 798);
        }
        if (status == 3) {
            wrapper.in(TraineeDO::getStatus, 797);
        }
        return selectCount(wrapper);
    }

    Integer checkIsSigning(@Param("classId") Long classId);

    List<ReportPageRespVO> getreportList(@Param("reqVO") ReportPageReqVO reqVO);

    List<ReportPageRespVO> getReportInfoListByStatus(@Param("reqVO") ReportPageReqVO reqVO);

    List<UnitRegistrationPageRespVO> getAllTraineeInfo(@Param("reqVO") TraineeInfoReqVO reqVO);

    /**
     * 获取班级已报名、已报到的所有学员
     *
     * @param classId 班级id
     * @return 所有学员信息
     */
    default List<TraineeDO> selectListByClassId(Long classId) {
        return selectList(new LambdaQueryWrapper<TraineeDO>()
                .eq(TraineeDO::getClassId, classId)
                .in(TraineeDO::getStatus, TraineeStatusEnum.REGISTERED.getStatus(),
                        TraineeStatusEnum.REPORTED.getStatus()));
    }

    /**
     * 获取班级某些状态的所有学员
     *
     * @param classId    班级id
     * @param statusList 学员状态
     * @return 所有学员信息
     */
    default List<TraineeDO> selectListByClassIdAndStatus(Long classId, List<Integer> statusList) {
        LambdaQueryWrapper<TraineeDO> queryWrapper = new LambdaQueryWrapper<TraineeDO>()
                .eq(TraineeDO::getClassId, classId);
        if (Objects.nonNull(statusList) && !statusList.isEmpty()) {
            queryWrapper.in(TraineeDO::getStatus, statusList);
        }
        queryWrapper.orderByDesc(TraineeDO::getCreateTime);
        return selectList(queryWrapper);
    }

    /**
     * 获取班级某些状态的所有学员和分组信息
     *
     * @param classId     班级id
     * @param traineeName 学员姓名
     * @param statusList  学员状态
     * @return 所有学员和分组信息
     */
    List<AppTraineeGroupRespVO> selectTraineeGroupListByClassIdAndStatus(@Param("classId") Long classId,
                                                                         @Param("traineeName") String traineeName,
                                                                         @Param("statusList") List<Integer> statusList);

    // /**
    //  * 根据userId 获取该用户最新的   学员信息
    //  *
    //  * @param userId
    //  * @return 学员信息
    //  */
    // default TraineeDO selectByUserId(Long userId) {
    //     return selectOne(new LambdaQueryWrapper<TraineeDO>()
    //             .eq(TraineeDO::getUserId, userId)
    //             .orderByDesc(TraineeDO::getCreateTime)
    //             .last("limit 1"));
    // }

    /**
     * 根据userId 获取该用户表中当前登录的学员信息（小程序学员切换班级）
     * 切换班级会将学员id存入users表 employee_id
     * @param userId 用户id
     * @return 学员信息
     */
    TraineeDO selectByUserId(@Param("userId") Long userId);

    default Integer getMaxGroupSort(Long groupId) {

        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(TraineeDO::getGroupId, groupId);
        wrapper.orderByDesc(TraineeDO::getSort);
        wrapper.last("limit 1");
        TraineeDO traineeDO = selectOne(wrapper);
        if (traineeDO == null) {
            return 0;
        }
        return traineeDO.getGroupSort();

    }

//    List<Map<Integer, String>> getDictData(@Param("dictType") String type);

    List<ClassInfoImportDictLableExcelVO> getDictTypeByDictLabel(@Param("type") int i);

    List<ClassInfoImportDictLableExcelVO> getUnit(@Param("classId") Long classId);

    default Integer getMaxSort(Long classId) {
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeDO::getClassId, classId);
        List<TraineeDO> list = this.selectList(wrapper);

        if (!list.isEmpty()) {
            return list.stream().map(TraineeDO::getGroupSort).max(Integer::compareTo).get();
        } else {
            return 0;
        }

    }

    @MapKey("label")
    Map<String, Map<String, Long>> getNation();

    @MapKey("unit_name")
    Map<String, Map<String, Integer>> getUnitByClassId(@Param("classId") Long classId);

    @MapKey("unit_name")
    Map<String, Map<String, Integer>> getUnitClassificationByClassId(@Param("classId") Long classId);

    default List<TraineeDO> getAllTraineeByClassIds(List<Long> classIdList) {
        if (CollUtil.isEmpty(classIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TraineeDO::getClassId, classIdList);

        return selectList(wrapper);
    }

    @MapKey("unit_name")
    Map<String, Map<String, Integer>> getUnitCountByClassId(@Param("classId") Long classId);

    default Long getUnitCountByUnitName(Long classId, String unitName) {
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeDO::getUnitName, unitName);
        wrapper.eq(TraineeDO::getClassId, classId);
        return selectCount(wrapper);
    }

    @MapKey("label")
    Map<String, Map<String, Long>> getDictDateMap(@Param("type") String type);

    @MapKey("id")
    Map<Long, Map<String, String>> getDictDateMapById(@Param("type") String type);

    @MapKey("id")
    Map<String, Map<String, String>> getDictDateMapById1(@Param("type") String type);



    @MapKey("id")
    Map<Long, Map<String, String>> getDictDateMapByIdandTenant(@Param("type") String type, @Param("tenantId") Long tenantId);

    @MapKey("id")
    Map<String, Map<String, String>> getDictDateMapByIdandTenant1(@Param("type") String type, @Param("tenantId") Long tenantId);


    default List<Long> getTraineeIds(List<Long> classIds) {

        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TraineeDO::getClassId, classIds);

        return this.selectList(wrapper).stream().map(TraineeDO::getId).collect(Collectors.toList());
    }

    Long getTraineeId(@Param("userId") Long userId, @Param("classId") Long classId);

    List<TraineeDO> getTraineeInfoByTraineeStatus(@Param("status") Integer status);

    List<TraineeDO> getCurrentGraduateTrainee(@Param("statusList") List<Integer> statusList);

    List<Long> selectUserSystemIdList(@Param("traineeIds") List<Long> traineeIds);

    default List<TraineeDO> selectByIdList(List<Long> idList) {
        return selectList(new LambdaQueryWrapper<TraineeDO>()
                .in(TraineeDO::getId, idList));
    }

    Long getUserBySystemId(String id);

    Long getSystemIdByUserId(Long id);

    // default TraineeDO selectTraineeByUserId(Long userId, Long studentId) {
    //     return selectOne(new LambdaQueryWrapperX<TraineeDO>()
    //             .eq(TraineeDO::getUserId, userId)
    //             .eqIfPresent(TraineeDO::getId, studentId)
    //             .orderByDesc(TraineeDO::getId)
    //             .last("limit 1"));
    // }

    /**
     * 根据userId 获取该用户表中当前登录的学员信息（小程序学员切换班级）
     *    切换班级会将学员id存入users表 employee_id
     * @param userId 用户id
     * @param studentId 学员id
     * @return 学员信息
     */
    TraineeDO selectTraineeByUserId(@Param("userId") Long userId,
                                    @Param("studentId") Long studentId);


    default List<TraineeDO> getTraineeByUserIdList(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TraineeDO::getUserId,userIds);
        return selectList(wrapper);
    }

    Page<TraineeInfoPageRespVO> getTraineeInfoPage(@Param("objectPage") Page<TraineeInfoPageReqVO> objectPage, @Param("reqVO") TraineeInfoPageReqVO reqVO);

    List<ExportTraineeInfoExcelVO> getTraineeReportInfoList(@Param("reqVO") TraineeInfoPageReqVO reqVO);

    List<TraineeDO> selectBatchByPhoneAndTenantId(@Param("list") List<Pair<String, Integer>> phoneandtenantidPairs);

    default List<TraineeDO> getTraineeByPhoneAndClassId(TraineeBaseVO reqVO) {
//        // 查找同一时间开班的班级id
//        List<Long> classIds = this.getClassIdSameTime(reqVO.getClassId());
//        if (classIds == null || classIds.isEmpty()) {
//            return null;
//        }
//        // 对 cardNo 进行加密
//        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(TraineeDO::getPhone, reqVO.getPhone());
//        wrapper.in(TraineeDO::getClassId, classIds);
//
//        // 编辑时需要去除本身
//        wrapper.ne(Objects.nonNull(reqVO.getId()), TraineeDO::getId, reqVO.getId());
//        wrapper.last("limit 1");
//
//        return selectOne(wrapper);

        // 查找同一时间开班的班级id
        List<Long> classIds = this.getClassIdSameTime(reqVO.getClassId());
        if (classIds == null || classIds.isEmpty()) {
            return null;
        }
        // 对 cardNo 进行加密
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeDO::getPhone, reqVO.getPhone());
        wrapper.in(TraineeDO::getClassId, classIds);

        //小程序没有登陆，需要手动传入tenantId
        if (reqVO.getTenantId() != null){
            wrapper.eq(TraineeDO::getTenantId, reqVO.getTenantId());
        }

        // 编辑时需要去除本身
        wrapper.ne(Objects.nonNull(reqVO.getId()), TraineeDO::getId, reqVO.getId());
//        wrapper.last("limit 1");
        List<TraineeDO> traineeDOList = selectList(wrapper);

        if (traineeDOList.isEmpty()){
            return Collections.emptyList();
        }

        return traineeDOList;
    }

    List<TraineeGroupRespVO> getTraineeGroupPage(IPage<TraineeGroupRespVO> page, @Param("reqVO") TraineeGroupReqVO reqVO);

    /**
     * 设置users表employee_id当前登录学员
     * @param userId 用户id
     * @param traineeId 学员id
     */
    void setLoginTrainee(@Param("userId") Long userId,
                         @Param("traineeId") String traineeId);

    /**
     * 根据单位id获取学员信息
     * @param unitIds 单位id列表
     * @return 学员列表
     */
    default List<TraineeDO> getByUnitIds(List<Integer> unitIds) {
        return selectList(new LambdaQueryWrapperX<TraineeDO>()
                .in(TraineeDO::getUnitId,unitIds));
    }

    /**
     * 根据身份证号查询学员信息
     *
     * @param cardNo 身份证号
     * @return 学员列表
     */
    default List<TraineeDO> selectByCardNo(String cardNo) {
        // 对身份证号进行加密
        String encryptedCardNo = SM4Util.encrypt(cardNo);
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeDO::getCardNo, encryptedCardNo);
        wrapper.eq(TraineeDO::getDeleted, 0); // 只查询未删除的记录
        return selectList(wrapper);
    }
}
