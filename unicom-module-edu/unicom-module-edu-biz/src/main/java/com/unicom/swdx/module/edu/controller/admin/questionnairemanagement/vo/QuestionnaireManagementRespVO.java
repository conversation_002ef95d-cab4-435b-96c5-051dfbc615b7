package com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo;

import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.QuestionLogicRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.QuestionLogicSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.QuestionnaireDetailRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.QuestionnaireDetailSaveReqVO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 评估问卷管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionnaireManagementRespVO {

    @Schema(description = "主键ID",  example = "5655")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "问卷标题")
    @ExcelProperty("问卷标题")
    private String title;

    @Schema(description = "问卷副标题")
    @ExcelProperty("问卷副标题")
    private String subtitle;

    @Schema(description = "是否默认问卷 (0: 否, 1: 是)")
    @ExcelProperty("是否默认问卷 (0: 否, 1: 是)")
    private String isDefault;

    @Schema(description = "状态 0未发布1已发布 2 已结束", example = "1")
    @ExcelProperty("状态 0未发布1已发布 2 已结束")
    private String status;

    @Schema(description = "创建部门")
    @ExcelProperty("创建部门")
    private Long createDept;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private Long creator;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "专题库教学形式")
    @ExcelProperty("专题库教学形式")
    private String topicEducateForm;

    @Schema(description = "最低分")
    @ExcelProperty("最低分")
    private Integer lowscore;

    @Schema(description = "启用最低分")
    @ExcelProperty("启用最低分")
    private Boolean lowscoreTag;

    @Schema(description = "最低字数")
    @ExcelProperty("最低字数")
    private Integer lowword;

    @Schema(description = "启用最低字数")
    @ExcelProperty("启用最低字数")
    private Boolean lowwordTag;

    @Schema(description = "启用时效限制")
    @ExcelProperty("启用时效限制")
    private Boolean timeTag;

    @Schema(description = "失效天数")
    @ExcelProperty("失效天数")
    private Integer timeLimit;

    @Schema(description = "启用问卷有效期撤回时限")
    @ExcelProperty("启用问卷有效期撤回时限")
    private Boolean revocableTag;

    @Schema(description = "撤回时限天数（支持小数）")
    @ExcelProperty("撤回时限天数（支持小数）")
    private Float revocableTimeLimit;

    @Schema(description = "评估项列表")
    private List<QuestionManagementRespVO> questions;

    @ApiModelProperty(value = "序号")
    private Long serialNumber;

    @Schema(description = "是否内置", example = "false")
    private Boolean builtIn;
}
