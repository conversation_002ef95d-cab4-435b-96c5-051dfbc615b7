package com.unicom.swdx.module.edu.service.rollcallsignin;

import com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 大课考勤、点名签到信息 Service 接口
 *
 * <AUTHOR>
 */
public interface RollcallSignInService {

    /**
     * 发起点名签到
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRollCallSignIn(@Valid RollcallSignInCreateReqVO createReqVO);

    /**
     * 发送大课考勤签到
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLectureAttendance(RollcallSignInCreateReqVO createReqVO);

    /**
     * 获取点名签到列表
     *
     * @param reqVO 请求查询参数
     * @return 点名签到列表
     */
    List<RollcallSignInRespVO> getRollcallSignInList(RollcallSignInListReqVO reqVO);

    /**
     * 获取大课考勤列表
     *
     * @param reqVO 请求查询参数
     * @return 大课考勤列表
     */
    List<RollcallSignInRespVO> getLectureAttendanceList(RollcallSignInListReqVO reqVO);

    /**
     * 结束大课考勤、点名签到
     * @param id 大课考勤、点名签到编号
     * @return 结束结果
     */
    Integer endLectureAttendance(Long id);

    ClassCourseDO valiateClassCourseIdExist(Long classcourseid);

    /**
     * 更新未结束的大课考勤、点名签到打卡地址
     * @param updateReqVO 更新信息
     */
    void updateAddress(RollcallSignInUpdateAddressReqVO updateReqVO);

    /**
     * 撤回一个大课考勤或者是点名签到
     * @param attendanceId 大课考勤或者是点名签到id
     */
    void withdrawAttendance(Long attendanceId);
}
