package com.unicom.swdx.module.edu.convert.classmanagement;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * EduClassManagement Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassManagementConvert {

    ClassManagementConvert INSTANCE = Mappers.getMapper(ClassManagementConvert.class);

    ClassManagementDO convert(ClassManagementCreateReqVO bean);

    ClassManagementDO convert(ClassManagementUpdateReqVO bean);

    ClassManagementRespVO convert(ClassManagementDO bean);

    List<ClassManagementRespVO> convertList(List<ClassManagementDO> list);

    PageResult<ClassManagementRespVO> convertPage(PageResult<ClassManagementDO> page);

    List<ClassManagementExcelVO> convertList02(List<ClassManagementDO> list);

    default List<ClassInfoRespVO> convertClassList(List<ClassManagementDO> list) {
        if ( list == null ) {
            return null;
        }
        List<ClassInfoRespVO> list1 = new ArrayList<>( list.size() );
        for ( ClassManagementDO classManagementDO : list ) {
            ClassInfoRespVO respVO = new ClassInfoRespVO();
            respVO.setId( Long.valueOf(classManagementDO.getId()) );
            respVO.setName( classManagementDO.getClassName() );
            respVO.setCampus( classManagementDO.getCampus() );
            list1.add( respVO );
        }
        return list1;
    }

    List<ClassManagementExportExcelVO> convertList1(List<ClassManagementExcelVO> list);

    List<ClassInfoImportStrExcelVO> convertList3(List<ClassInfoImportExcelVO> list1);
}
