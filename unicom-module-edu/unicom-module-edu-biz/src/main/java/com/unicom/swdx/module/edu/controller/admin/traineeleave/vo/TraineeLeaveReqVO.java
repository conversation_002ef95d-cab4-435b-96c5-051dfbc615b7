package com.unicom.swdx.module.edu.controller.admin.traineeleave.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
public class TraineeLeaveReqVO {
    @ApiModelProperty(value = "班主任id，培训管理页面不传，班主任管理页面必传")
    private Long classTeacherLeadId;

    @ApiModelProperty(value = "校区id-对应字典edu_classroom_campus")
    private Integer campus;

    @ApiModelProperty(value = "班级状态：2开班中，3已结业，4未开始")
    private Integer classStatus;

    @ApiModelProperty(value = "班次id")
    private Long classId;

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty(value = "提交时间-开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime applyTimeStart;

    @ApiModelProperty(value = "提交时间-截至日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime applyTimeEnd;

    @ApiModelProperty(value = "学员姓名")
    private String traineeName;

    @ApiModelProperty(value = "流程状态：2待审批，3审批中，4已通过，5已拒绝")
    private Integer status;

    @ApiModelProperty(value = "请假类别，对应字典")
    private Integer leaveType;

    @ApiModelProperty(value = "1-班次名称，2-请假开始时间，3-请假结束时间")
    private Integer tag;

    @ApiModelProperty(value = "1-升序，2-降序")
    private Integer seq;

}
