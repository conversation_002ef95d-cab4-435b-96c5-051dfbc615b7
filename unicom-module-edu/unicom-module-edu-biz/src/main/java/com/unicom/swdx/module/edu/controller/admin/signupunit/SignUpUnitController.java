package com.unicom.swdx.module.edu.controller.admin.signupunit;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.validation.ExcelValidator;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.signupunit.excelimporthandler.GetSignStatusWriteHandler;
import com.unicom.swdx.module.edu.controller.admin.signupunit.excelimporthandler.GetSignTypeWriteHandler;
import com.unicom.swdx.module.edu.controller.admin.signupunit.vo.*;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.TraineeImportRespVO;
import com.unicom.swdx.module.edu.convert.signupunit.SignUpUnitConvert;
import com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO;
import com.unicom.swdx.module.edu.dal.mysql.signupunit.SignUpUnitMapper;
import com.unicom.swdx.module.edu.service.signupunit.SignUpUnitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.Normalizer;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.unicom.swdx.framework.common.enums.ErrorCodeConstants.EXPORT_FAILED;
import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.IMPORT;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.UPDATE;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.CLASS_MANAGEMENT_HEAD_NAME_ERROR;


@Api(tags = "管理后台 - 名额分配")
@RestController
@RequestMapping("/edu/sign-up-unit")
@Validated
public class SignUpUnitController {

    @Resource
    private SignUpUnitService signUpUnitService;

    @Resource
    private SignUpUnitMapper signUpUnitMapper;
    @PostMapping("/create")
    @ApiOperation("创建EduSignUpUnit")
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:create')")
    public CommonResult<Integer> createSignUpUnit(@Valid @RequestBody SignUpUnitCreateReqVO createReqVO) {
        return success(signUpUnitService.createSignUpUnit(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新EduSignUpUnit")
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:update')")
    public CommonResult<Boolean> updateSignUpUnit(@Valid @RequestBody SignUpUnitUpdateReqVO updateReqVO) {
        signUpUnitService.updateSignUpUnit(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除EduSignUpUnit")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:delete')")
    public CommonResult<Boolean> deleteSignUpUnit(@RequestParam("id") Integer id) {
        signUpUnitService.deleteSignUpUnit(id);
        return success(true);
    }

    @PostMapping("/delete-batch")
    @ApiOperation("批量删除EduSignUpUnit")
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:delete')")
    public CommonResult<Boolean> deleteSignUpUnitBatch(@Valid @RequestBody SignUpUnitDeleteVO signUpUnitDeleteVO) {
        signUpUnitService.deleteSignUpUnitBatch(signUpUnitDeleteVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得EduSignUpUnit")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:query')")
    public CommonResult<SignUpUnitRespVO> getSignUpUnit(@RequestParam("id") Integer id) {
        SignUpUnitDO signUpUnit = signUpUnitService.getSignUpUnit(id);
        return success(SignUpUnitConvert.INSTANCE.convert(signUpUnit));
    }

    @GetMapping("/profile/get")
    @ApiOperation("获得个人中心Unit")
    @ApiImplicitParam(name = "unitId", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:query')")
    public CommonResult<SignUpUnitRespVO> getProfileUnit(@RequestParam(value = "unitId", required = false) Integer unitId) {
        SignUpUnitDO signUpUnit = new SignUpUnitDO();
        if (unitId == null) {
            signUpUnit = signUpUnitService.getByUserId(getLoginUserId());
        } else {
            signUpUnit = signUpUnitService.getSignUpUnit(unitId);
        }

        return success(SignUpUnitConvert.INSTANCE.convert(signUpUnit));
    }

    @PostMapping("/profile/update")
    @ApiOperation("更新个人中心")
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:update')")
    public CommonResult<Boolean> updateProfileUnit(@Valid @RequestBody ProfileUnitUpdateReqVO updateReqVO) {
        signUpUnitService.updateProfileUnit(updateReqVO);
        return success(true);
    }

    @PostMapping("/reset-password")
    @ApiOperation("重置密码")
    @PreAuthorize("@ss.hasPermission('system:user:update-password')")
    @OperateLog(type = UPDATE)
    public CommonResult<Boolean> resetUserPassword(@RequestParam("id") Long id) {
        signUpUnitService.resetUserPassword(id);
        return success(true);
    }


    @GetMapping("/page")
    @ApiOperation("获得EduSignUpUnit分页")
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:query')")
    public CommonResult<PageResult<SignUpUnitRespVO>> getSignUpUnitPage(@Valid SignUpUnitPageReqVO pageVO) {
        return success(signUpUnitService.getSignUpUnitPage(pageVO));
    }

    @PostMapping("/is-restrict")
    @ApiOperation("是否限制")
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:update')")
    public CommonResult<Boolean> updateSignUpUnitRestrict(@RequestParam("id") Integer id, @RequestParam("restrict") Integer restrict) {
        signUpUnitService.updateSignUpUnitRestrict(id, restrict);
        return success(true);
    }

    @GetMapping("/getByClassId")
    @ApiOperation("根据classId获得EduSignUpUnit")
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:query')")
    public CommonResult<List<Map<String,String>>> getByClassId(@RequestParam("classId") Integer classId) {
        List<Map<String,String>> maps = signUpUnitService.getByClassId(classId);
        return success(maps);
    }


    @GetMapping("/xcx-getByClassId")
    @ApiOperation("根据classId获得EduSignUpUnit")
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:query')")
    @TenantIgnore
    @PermitAll
    public CommonResult<List<Map<String,String>>> xcxGetByClassId(@RequestParam("classId") Integer classId) {
        List<Map<String,String>> maps = signUpUnitService.getByClassId(classId);
        return success(maps);
    }

    @GetMapping("/get-import-templates")
    @ApiOperation("获得导入单位信息模板")
    @TenantIgnore
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 输出
        String filename = "单位账号管理导入模板.xls";
        String sheetName = "单位信息列表";
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 输出 Excel
        try {
            EasyExcel.write(response.getOutputStream(), SignUnitImportTemplateExcelVO.class)
                    .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                    .registerWriteHandler(new GetSignTypeWriteHandler(signUpUnitMapper))
                    .registerWriteHandler(new GetSignStatusWriteHandler(signUpUnitMapper))
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 基于 column 长度，自动适配。最大 255 宽度
                    .sheet(sheetName).doWrite(Collections.emptyList());
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }


    @PostMapping("/import")
    @ApiOperation("导入单位信息")
    @OperateLog(type = IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)
    })
    public CommonResult<TraineeImportRespVO> importExcel(@RequestParam("file") MultipartFile file) {

        // 获取文件名并去除路径和扩展名
        String originalFileName = file.getOriginalFilename();

        if (originalFileName == null || originalFileName.isEmpty()) {
            throw new IllegalArgumentException("文件名为空");
        }

        // 获取文件名（不带路径和扩展名）
        String fileNameWithoutExtension = new File(originalFileName).getName().substring(0, originalFileName.lastIndexOf('.')).trim();

        // 标准化处理
        fileNameWithoutExtension = Normalizer.normalize(fileNameWithoutExtension, Normalizer.Form.NFC);


        List<SignUnitImportTemplateExcelVO> list = null;
        // 校验表头信息
        try{
            list = ExcelUtils.readFirstSheetAndCheckHead(file, SignUnitImportTemplateExcelVO.class);
        }catch (Exception e){
            throw exception(CLASS_MANAGEMENT_HEAD_NAME_ERROR);
        }

        // 验证 Excel 数据
        ExcelValidator.valid(list,1);

        // 调用服务层，导入学生信息
        return success(signUpUnitService.importInfo(list));
    }


    /**
     * 单位信息导出
     * @param reqVO
     * @param response
     * @throws IOException
     */
    @PostMapping("/export")
    @ApiOperation(value = "单位信息导出")
    @PreAuthorize("@ss.hasPermission('edu:trainee:export')")
    public void exportRegistrationInfo(@RequestBody SignUpUnitPageReqVO reqVO, HttpServletResponse response) throws IOException {
        List<SignUnitExcelVO> list = signUpUnitService.getSignUpUnitList(reqVO);

        ExcelUtils.writeByIncludeColumnIndexes(response, "单位信息.xls",
                "数据", SignUnitExcelVO.class,list, reqVO.getIncludeColumnIndexes());

    }


    @GetMapping("/list")
    @ApiOperation("获得EduSignUpUnit分页")
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:list')")
    public CommonResult<List<SignUpUnitRespVO>> getSignUpUnitList(@RequestParam("classId") Long classId) {
        return success(signUpUnitService.getSignUpUnitListByClassId(classId));
    }

    @PostMapping("/assign")
    @ApiOperation("获得EduSignUpUnit分页")
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:assign')")
    public CommonResult<Boolean> assignCapacity(@RequestBody SignUnitAssignCapacityReqVO reqVO) {
        return success(signUpUnitService.assignCapacity(reqVO));
    }

    @PostMapping("/update-sort")
    @ApiOperation("更新排序号")
    @PreAuthorize("@ss.hasPermission('edu:sign-up-unit:update')")
    public CommonResult<Boolean> updateSignUpUnitSort(@RequestParam("id") Integer id,
            @RequestParam("sort") Integer sort) {
        signUpUnitService.updateSignUpUnitSort(id, sort);
        return success(true);
    }

}
