package com.unicom.swdx.module.edu.service.signupunit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.signupunit.vo.*;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.TraineeImportRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO;
import org.springframework.scheduling.annotation.Async;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * EduSignUpUnit Service 接口
 *
 * <AUTHOR>
 */
public interface SignUpUnitService extends IService<SignUpUnitDO> {

    /**
     * 创建EduSignUpUnit
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createSignUpUnit(@Valid SignUpUnitCreateReqVO createReqVO);

    /**
     * 更新EduSignUpUnit
     *
     * @param updateReqVO 更新信息
     */
    void updateSignUpUnit(@Valid SignUpUnitUpdateReqVO updateReqVO);

    /**
     * 删除EduSignUpUnit
     *
     * @param id 编号
     */
    void deleteSignUpUnit(Integer id);

    /**
     * 获得EduSignUpUnit
     *
     * @param id 编号
     * @return EduSignUpUnit
     */
    SignUpUnitDO getSignUpUnit(Integer id);

    /**
     * 批量删除
     *
     * @param signUpUnitDeleteVO
     * @return EduSignUpUnit
     */
    void deleteSignUpUnitBatch(SignUpUnitDeleteVO signUpUnitDeleteVO);

    /**
     * 获得EduSignUpUnit分页
     *
     * @param pageReqVO 分页查询
     * @return EduSignUpUnit分页
     */
    PageResult<SignUpUnitRespVO> getSignUpUnitPage(SignUpUnitPageReqVO pageReqVO);
    /**
     * 更新限制状态
     *
     * @param id , restrict
     * @return
     */
    Integer updateSignUpUnitRestrict(Integer id, Integer restrict);


    List<Map<String,String>> getByClassId(Integer classId);

    TraineeImportRespVO importInfo(List<SignUnitImportTemplateExcelVO> list);

    List<SignUnitExcelVO> getSignUpUnitList(SignUpUnitPageReqVO reqVO);


    Map<Long,String> getDictMap(List<String> dictType);

    List<SignUpUnitRespVO> getSignUpUnitListByClassId(Long classId);

    Boolean assignCapacity(SignUnitAssignCapacityReqVO reqVO);

    /**
     * 根据单位管理员userId查询调训单位
     * @param userId 单位管理员用户id
     * @return 调训单位信息
     */
    SignUpUnitDO getByUserId(Long userId);

    @Async
    void updateClassAndTraineeUnitInfo(SignUpUnitDO updateObj);

    void updateProfileUnit(ProfileUnitUpdateReqVO updateReqVO);

    void resetUserPassword(Long id);

    List<String> getPhoneList();

    /**
     * 更新排序号
     *
     * @param id   编号
     * @param sort 排序号
     * @return 是否成功
     */
    void updateSignUpUnitSort(Integer id, Integer sort);
}
