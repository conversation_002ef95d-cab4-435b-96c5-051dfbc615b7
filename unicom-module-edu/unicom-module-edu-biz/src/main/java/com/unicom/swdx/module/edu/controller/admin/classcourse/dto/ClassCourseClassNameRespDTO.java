package com.unicom.swdx.module.edu.controller.admin.classcourse.dto;

import com.unicom.swdx.module.edu.enums.classcourse.CourseChangeTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel("管理后台 - 有班级名称字典的课程安排 Response VO")
@Data
public class ClassCourseClassNameRespDTO {

    /**
     * 唯一标识
     */
    private Long id;
    /**
     * 班级id
     */
    private Long classId;
    /**
     * 班级名称
     */
    private String className;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 教师id
     */
    private Long teacherId;
    /**
     * 教室id
     */
    private Long classroomId;
    /**
     * 是否暂存
     */
    private Boolean isTemporary;
    /**
     * 是否合班授课
     */
    private Boolean isMerge;
    /**
     * 是否调课
     */
    private Boolean isChange;
    /**
     * 调课时间
     */
    private LocalDateTime changeTime;
    /**
     * 调课类型 1调课2更换教室3更换上课时间4更换授课老师5取消课程
     * {@link CourseChangeTypeEnum}
     */
    private Integer changeType;
    /**
     * 是否由配置模版生成的初始数据，默认为false
     */
    private Boolean original;
    /**
     * 是否为部门授课，默认为教师授课
     */
    private Boolean department;
    /**
     * 授课者字符串，id之间逗号间隔，部门授课时直接存储部门名称
     */
    private String teacherIdString;
    /**
     * 教学计划id
     */
    private Long planId;
    /**
     * 日期
     */
    private String date;

    /**
     * 时间段（0上午，1下午，2晚上）
     */
    private String period;

    /**
     * 是否到课考勤
     */
    private Boolean isCheck;

    /**
     * 部门授课时的部门Id
     */
    private Long deptId;

    /**
     * 是否已下发问卷
     */
    private Boolean isDistributed;

    /**
     * 是否党政领导讲课，0不是，1是
     */
    private Boolean isLeaderLecture;

}
