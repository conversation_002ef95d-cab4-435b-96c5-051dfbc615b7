package com.unicom.swdx.module.edu.dal.dataobject.classcourse;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import com.unicom.swdx.module.edu.enums.classcourse.CourseChangeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 班级课程安排 DO
 *
 * <AUTHOR>
 */
@TableName("edu_class_course")
@KeySequence("edu_class_course_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassCourseDO extends TenantBaseDO {

    /**
     * 唯一标识
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 班级id
     */
    private Long classId;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 开始时间
     */
    private LocalDateTime beginTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 教师id
     */
    private Long teacherId;
    /**
     * 教室id
     */
    private Long classroomId;
    /**
     * 是否暂存
     */
    private Boolean isTemporary;
    /**
     * 是否合班授课
     */
    private Boolean isMerge;
    /**
     * 是否调课
     */
    private Boolean isChange;
    /**
     * 调课时间
     */
    private LocalDateTime changeTime;
    /**
     * 调课类型 1调课2更换教室3更换上课时间4更换授课老师5取消课程
     * {@link CourseChangeTypeEnum}
     */
    private Integer changeType;
    /**
     * 是否由配置模版生成的初始数据，默认为false
     */
    private Boolean original;
    /**
     * 是否为部门授课，默认为教师授课
     */
    private Boolean department;
    /**
     * 授课者字符串，id之间逗号间隔，部门授课时直接存储部门名称
     */
    private String teacherIdString;
    /**
     * 教学计划id
     */
    private Long planId;
    /**
     * 日期
     */
    private String date;

    /**
     * 时间段（0上午，1下午，2晚上）
     */
    private String period;

    /**
     * 是否到课考勤
     */
    private Boolean isCheck;

    /**
     * 部门授课时的部门Id
     */
    private Long deptId;

    /**
     * 冲突信息，去掉数据库相关字段，取消持久化
     */
    @TableField(exist = false)
    private String conflictInfo;

    /**
     * 教师冲突字段，同一第一教师最多只能排一个课程
     */
    @TableField(exist = false)
    private String teacherConflictInfo;

    /**
     * 教师名称
     */
    @TableField(exist = false)
    private String teacherName;

    /**
     * 教室名称
     */
    @TableField(exist = false)
    private String classroomName;

    /**
     * 课程名称
     */
    @TableField(exist = false)
    private String courseName;

    /**
     * 课程类型
     */
    @TableField(exist = false)
    private String courseType;

    /**
     * 课程类型Id
     */
    @TableField(exist = false)
    private String courseTypeId;


    /**
     * 教学形式字典ID
     */
    @TableField(exist = false)
    private Long educateFormId;

    /**
     * 教学形式字典名称
     */
    @TableField(exist = false)
    private String educateFormName;

    /**
     * 课程分类字典ID
     */
    @TableField(exist = false)
    private Long themeId;

    /**
     * 教学形式字典ID
     */
    @TableField(exist = false)
    private String themeName;

    /**
     * 教学活动库活动类型字典ID
     */
    @TableField(exist = false)
    private String activityType;

    /**
     * 开始时间结束时间数组拼接字符串，方便前端回显
     */
    @TableField(exist = false)
    private String timeRange;

    /**
     * 开始时间结束时间数组，方便前端回显
     */
    @TableField(exist = false)
    private List<LocalDateTime> localDateTimeList;

    /**
     * 常用教室id
     */
    @TableField(exist = false)
    private Long classroomIdAlways;

    /**
     * 常用教室名称
     */
    @TableField(exist = false)
    private String classroomIdAlwaysName;

    /**
     * 常用教室生效日期
     */
    @TableField(exist = false)
    private String effectiveDate;

    /**
     * 日期date对应周几
     */
    @TableField(exist = false)
    private String weekDay;

    /**
     *  是否显示合班授课标签
     */
    @TableField(exist = false)
    private Boolean isShowMergeTag;

    /**
     * 是否已下发问卷
     */
    private Boolean isDistributed;

    /**
     * 是否党政领导讲课，0不是，1是
     */
    private Boolean isLeaderLecture;

}
