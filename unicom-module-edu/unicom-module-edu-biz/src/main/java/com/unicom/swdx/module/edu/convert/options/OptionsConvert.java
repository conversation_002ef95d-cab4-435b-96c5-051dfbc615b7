package com.unicom.swdx.module.edu.convert.options;

import com.unicom.swdx.module.edu.controller.admin.options.vo.OptionsSaveReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface OptionsConvert {
    OptionsConvert Instance = Mappers.getMapper(OptionsConvert.class);

    List<OptionsDO> convertList(List<OptionsSaveReqVO> options);
}
