package com.unicom.swdx.module.edu.controller.admin.traineeleave;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementExcelVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementExportParamsVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.*;
import com.unicom.swdx.module.edu.service.traineeleave.TraineeLeaveService;
import com.unicom.swdx.module.edu.utils.word.DocUtils;
import com.unicom.swdx.module.infra.api.file.FileApi;
import com.unicom.swdx.module.infra.api.file.dto.FileUploadReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "教务学员- 学员请假")
@RestController
@RequestMapping("/edu/traineeLeave")
@Validated
public class TraineeLeaveController {

    @Resource
    private TraineeLeaveService leaveService;

    @Value("${app.file.download-url:http://116.162.221.162:90/admin-api/infra/file/1/get/1445bea740f9455f9e6aafc1a4c835cf1750232899432.docx}")
    private String fileDownloadUrl;
    @Resource
    private FileApi fileApi;

    //请假管理列表
    @GetMapping("/pageList")
    @ApiOperation(value = "请假管理-----分页")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:manage')")
    public CommonResult<PageResult<TraineeLeaveRespVO>> pageList(TraineeLeavePageReqVO reqVO) {
        return success(leaveService.pageList(reqVO));
    }

    //导出请假管理列表
    @GetMapping("/export")
    @ApiOperation(value = "请假管理-----导出")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:manage')")
    public void export(TraineeLeaveExportReqVO reqVO, HttpServletResponse response) throws IOException {
        List<TraineeLeaveExcelVO> list = leaveService.getLeaveExportList(reqVO);

        ExcelUtils.writeByIncludeColumnIndexes(response, "请假管理.xls",
                "数据", TraineeLeaveExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    //班主任首页感叹号
    @GetMapping("/leaveCount")
    @ApiOperation(value = "小程序-----班主任首页请假审批感叹号")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:manage')")
    public CommonResult<Integer> leaveCount(Long classId) {
        return success(leaveService.getLeaveCount(classId));
    }

    //班主任的小程序请假列表
    @GetMapping("/teacherList")
    @ApiOperation(value = "小程序-----班主任的小程序请假列表")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:manage')")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "classId", value = "班级id", required = true),
            @ApiImplicitParam(name = "status", value = "0-待审批 1-已通过 2-已拒绝 3-全部 ", required = true)
    })

    public CommonResult<List<TraineeLeaveRespVO>> teacherList(Long classId, Integer status) {
        return success(leaveService.teacherList(classId, status));
    }

    //我的请假列表
    @GetMapping("/myList")
    @ApiOperation(value = "小程序-----我的请假列表")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:create')")
    public CommonResult<List<TraineeLeaveRespVO>> myList(TraineeLeaveMyReqVO reqVO) {
        return success(leaveService.getMyList(reqVO));
    }

    //获取学员请假详情
    @GetMapping("/detail")
    @ApiOperation(value = "小程序-----获取学员请假详情")
//    @PreAuthorize("@ss.hasAnyPermissions('edu:trainee:leave:deal','edu:trainee:leave:create')")
    @PermitAll
    public CommonResult<TraineeLeaveRespVO> detail(Long leaveId) {
        return success(leaveService.getTraineeLeaveDetail(leaveId));
    }

    //获取学员请假审批流程记录
    @GetMapping("/processList")
    @ApiOperation(value = "小程序-----获取学员请假审批流程记录")
    @PreAuthorize("@ss.hasAnyPermissions('edu:trainee:leave:deal','edu:trainee:leave:create')")
    public CommonResult<List<TraineeLeaveProcessVO>> processList(Long leaveId) {
        return success(leaveService.getTraineeLeaveProcessList(leaveId));
    }

    @GetMapping("/getDraft")
    @ApiOperation(value = "请假申请-----如有默认打开草稿")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:create')")
    public CommonResult<TraineeLeaveRespVO> getDraft(Long classId) {
        return success(leaveService.getDraft(classId));
    }

    @GetMapping("/getNumber")
    @ApiOperation(value = "请假申请-----获取学员当天是第几次发起")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:create')")
    public CommonResult<Integer> getNumber() {
        return success(leaveService.getNumber());
    }

    @PostMapping("/save")
    @ApiOperation(value = "请假申请-----保存为草稿")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:create')")
    public CommonResult<Long> saveDraft(@RequestBody TraineeLeaveBaseVO reqVO) {
        return success(leaveService.saveDraft(reqVO));
    }

    /**
     * 学员发起请假
     * @param reqVO 入参
     * @return
     */
    @PostMapping("/create")
    @ApiOperation(value = "请假申请-----学员发起请假")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:create')")
    public CommonResult<Long> createLeave(@RequestBody TraineeLeaveCreateReqVO reqVO) {
        return success(leaveService.createLeave(reqVO));
    }

    //学员撤回
    @GetMapping("/cancel")
    @ApiOperation(value = "请假申请-----学员撤回请假")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:create')")
    public CommonResult<Boolean> cancelLeave(Long leaveId, String modifyReason) {
        return success(leaveService.cancelLeave(leaveId, modifyReason));
    }

    @PostMapping("/updateAccessory")
    @ApiOperation(value = "请假申请-----补充附件")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:create')")
    public CommonResult<Boolean> updateAccessory(@RequestBody TraineeLeaveCreateReqVO reqVO) {
        return success(leaveService.updateAccessory(reqVO));
    }

    //班主任修改
    @PostMapping("/modify")
    @ApiOperation(value = "请假审批-----班主任修改")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:deal')")
    @PermitAll
    public CommonResult<Boolean> modify(@Valid @RequestBody TraineeLeaveModifyReqVO reqVO) {
        return success(leaveService.modify(reqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "请假审批-----学员修改")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:deal')")
    @PermitAll
    public CommonResult<Boolean> traineeUpdate(@Valid @RequestBody TraineeLeaveUpdateReqVO reqVO) {
        return success(leaveService.traineeUpdate(reqVO));
    }


    @Resource
    private RedissonClient redissonClient;


    //班主任审批/转办
    @PostMapping("/deal")
    @ApiOperation(value = "请假审批-----班主任审批/转办")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:deal')")
    public CommonResult<Boolean> deal(@Valid @RequestBody TraineeLeaveDealReqVO reqVO) {


        String lockKey = "lock:deal:" + reqVO.getLeaveId();
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean isLocked = lock.tryLock(10, 3000, TimeUnit.MILLISECONDS); // 尝试获取锁，等待10毫秒，锁自动释放时间为3秒
            if (isLocked) {
                Boolean result = leaveService.deal(reqVO);
                return CommonResult.success(result);
            } else {
                return CommonResult.error(500,"审批中...");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return CommonResult.error(500 ,"操作失败");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    //OA转办人审批后回调
    @PostMapping("/transferBack")
    @ApiOperation(value = "请假审批-----业中转办人审批后回调")
    @PermitAll
    public CommonResult<Boolean> transferBack(@Valid @RequestBody TraineeLeaveDealReqVO reqVO) {
        return success(leaveService.transferBack(reqVO));
    }

    @GetMapping("/getLeaveById")
    @ApiOperation(value = "请假管理-----根据id获取单个请假")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:manage')")
    public CommonResult<TraineeLeaveRespVO> getLeaveById(Long leaveId) {
        return success(leaveService.getLeaveById(leaveId));
    }

    @PostMapping("/getList")
    @ApiOperation(value = "请假管理-----根据id获取列表")
    @PreAuthorize("@ss.hasPermission('edu:trainee:leave:manage')")
    public CommonResult<List<TraineeLeaveRespVO>> getList(@RequestBody GetTraineeLeaveListReqVO reqVO) {
        return success(leaveService.getList(reqVO.getLeaveIds()));
    }

    @GetMapping("/download-template")
    public CommonResult<String> proxyDownloadFile(@RequestParam Long leaveId) {
        try {
            // 1. 构建数据
            Map<String, Object> dataMap = leaveService.getLeaveDateMap(leaveId);

            // 2. 生成 Word 文件流
            InputStreamResource fileResource = DocUtils.saveWord(dataMap);

            // 3. 转换为 byte[]
            byte[] fileBytes;
            try (InputStream in = fileResource.getInputStream();
                 ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                IOUtils.copy(in, out);
                fileBytes = out.toByteArray();
            }

            // 4. 构建上传请求
            FileUploadReqDTO uploadReqDTO = new FileUploadReqDTO();
            uploadReqDTO.setName("请假模板.docx");
            uploadReqDTO.setModule("leave_template"); // 指定文件存储模块
            uploadReqDTO.setContent(fileBytes);

            // 5. 调用文件服务上传
            return fileApi.uploadFile(uploadReqDTO);
        } catch (Exception e) {
            throw new RuntimeException("生成请假模板失败", e);
        }
    }

}
