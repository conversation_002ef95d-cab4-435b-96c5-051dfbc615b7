package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: zhouhk
 * @Date: 2024/12/20
 */

@Data
@ApiModel(value = "报名详情分页返回VO")
public class TraineeStatExportVO {

    @ExcelProperty(value = "班次名称")
    private String className;

    @ExcelProperty(value = "年度")
    private Integer year;

    @ExcelProperty(value = "学期，1-上学期，2-下学期")
    private String semester;

    @ExcelProperty(value = "开班日期")
    private String classOpenTime;

    @ExcelProperty(value = "结业日期")
    private String completionTime;

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "性别")
    private String sex;

    @ExcelProperty("报名时间")
    private LocalDateTime createTime;

    @ExcelProperty(value = "出生日期")
    private String birthDay;

    @ExcelProperty(value = "手机号")
    private String phone;

    @ExcelProperty(value = "职级")
    private String jobLevel;

    @ExcelProperty(value = "职务")
    private String position;

    /**
     * 最近参训日期
     */
    @ExcelProperty(value = "最近参训日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate lastTrainingTime;

    /**
     * 最近培训班次
     */
    @ExcelProperty(value = "最近培训班次")
    private String lastTrainingClass;

    /**
     * 最近参加任职资格考试日期
     */
    @ExcelProperty(value = "最近参加任职资格考试日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate latestQualExamDate;
}
