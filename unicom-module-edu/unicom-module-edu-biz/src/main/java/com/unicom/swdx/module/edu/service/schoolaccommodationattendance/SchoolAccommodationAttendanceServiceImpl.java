package com.unicom.swdx.module.edu.service.schoolaccommodationattendance;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo.ClassClockCalendarUpdateReqVO;
import com.unicom.swdx.module.edu.convert.classclockcalendar.ClassClockCalendarConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classclockcalendar.ClassClockCalendarDO;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;
import com.unicom.swdx.module.edu.dal.mysql.classclockcalendar.ClassClockCalendarMapper;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.enums.attendance.AttendanceTypeEnum;
import com.unicom.swdx.module.edu.enums.clockcalendar.ClockCalendarStatusEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.ClockStatusEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.MealPeriodEnum;
import com.unicom.swdx.module.edu.service.classcourse.ClassCourseService;
import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.schoolaccommodationattendance.SchoolAccommodationAttendanceDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.convert.schoolaccommodationattendance.SchoolAccommodationAttendanceConvert;
import com.unicom.swdx.module.edu.dal.mysql.schoolaccommodationattendance.SchoolAccommodationAttendanceMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 全校就餐住宿考勤 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SchoolAccommodationAttendanceServiceImpl extends ServiceImpl<SchoolAccommodationAttendanceMapper, SchoolAccommodationAttendanceDO> implements SchoolAccommodationAttendanceService {

    @Resource
    private SchoolAccommodationAttendanceMapper schoolAccommodationAttendanceMapper;

    @Resource
    private ClassCourseService classCourseService;

    @Resource
    private ClassClockCalendarMapper classClockCalendarMapper;

    @Resource
    private ClockInInfoService clockInInfoService;

    @Resource
    private ClockInInfoMapper clockInInfoMapper;


    @Override
    public Integer createSchoolAccommodationAttendance(SchoolAccommodationAttendanceCreateReqVO createReqVO) {

        // 获取一年中的天数
        List<SchoolAccommodationAttendanceBaseVO> yearData = generateYearData(createReqVO.getYear());

        // 获取节假日
        Set<String> result = classCourseService.getVacation(createReqVO.getYear(), 0);

        List<SchoolAccommodationAttendanceDO> batchInsertList = new ArrayList<>();

        for (int i = 0; i < yearData.size(); i++) {
            SchoolAccommodationAttendanceBaseVO vo = yearData.get(i);

            // 将 LocalDate 转换为字符串
            String formattedDate = vo.getClockDate().toString();

            boolean isHoliday = result.contains(formattedDate);

            // 插入日期
            createReqVO.setClockDate(vo.getClockDate());

            if (isHoliday) {
                // 1- 关
                createReqVO.setBreakfast(1);
                createReqVO.setLunch(1);
                createReqVO.setDinner(1);
                createReqVO.setPutUp(1);
                // 是节假日
                createReqVO.setIsHoliday(0);
            } else {
                // 0 - 开
                createReqVO.setBreakfast(0);
                createReqVO.setLunch(0);
                createReqVO.setDinner(0);
                createReqVO.setPutUp(0);
                // 非节假日
                createReqVO.setIsHoliday(1);
            }

            // 检查当天为工作日，第二天为休息日的情况
            if (!isHoliday && i + 1 < yearData.size()) {
                String nextDayFormattedDate = yearData.get(i + 1).getClockDate().toString();
                if (result.contains(nextDayFormattedDate)) {
                    // 第二天下班不考勤
                    createReqVO.setDinner(1);
                    createReqVO.setPutUp(1);
                }
            }

            // 检查当天为休息日，第二天为工作日的情况
            if (isHoliday && i + 1 < yearData.size()) {
                String nextDayFormattedDate = yearData.get(i + 1).getClockDate().toString();
                if (!result.contains(nextDayFormattedDate)) {
                    // 第二天上班不考勤
                    createReqVO.setPutUp(0);
                }
            }

            // 转换并添加到批量插入列表
            SchoolAccommodationAttendanceDO schoolAccommodationAttendance = SchoolAccommodationAttendanceConvert.INSTANCE.convert(createReqVO);
            batchInsertList.add(schoolAccommodationAttendance);
        }

        // 批量插入
        if (!batchInsertList.isEmpty()) {
            schoolAccommodationAttendanceMapper.insertBatch(batchInsertList);
        }

        // 返回
        return yearData.size();
    }

    @Override
    public void updateSchoolAccommodationAttendance(SchoolAccommodationAttendanceUpdateReqVO updateReqVO) {
        // 校验存在
        SchoolAccommodationAttendanceDO schoolAccommodationAttendanceDO = this.validateSchoolAccommodationAttendanceExists(updateReqVO.getId());
        // 更新
        SchoolAccommodationAttendanceDO updateObj = SchoolAccommodationAttendanceConvert.INSTANCE.convert(updateReqVO);

        LambdaQueryWrapperX<ClassClockCalendarDO> lqw=new LambdaQueryWrapperX<>();
        lqw.eqIfPresent(ClassClockCalendarDO::getClockDate,updateObj.getClockDate());
        List<ClassClockCalendarDO> classClockCalendarDOS = classClockCalendarMapper.selectList(lqw);

        // 无法关闭的考勤状态 已打卡和迟到
        List<Integer> unCloseStatus = Arrays.asList(ClockStatusEnum.DONE.getCode(), ClockStatusEnum.LATE.getCode());
        // 该天的所有考勤
        List<ClockInInfoDO> clockInInfoDOList = new ArrayList<>();
        // 是否开启了考勤保护
        Boolean enableAttendanceProtection = clockInInfoService.isEnableAttendanceProtection(getTenantId());
        if (Boolean.TRUE.equals(enableAttendanceProtection)) {
            clockInInfoDOList = clockInInfoMapper.selectListByClockDateAndClassId(schoolAccommodationAttendanceDO.getClockDate(),
                    null, unCloseStatus);
        }

        for (ClassClockCalendarDO classClockCalendarDO : classClockCalendarDOS) {
            classClockCalendarDO.setBreakfast(updateObj.getBreakfast());
            classClockCalendarDO.setLunch(updateObj.getLunch());
            classClockCalendarDO.setDinner(updateObj.getDinner());
            classClockCalendarDO.setPutUp(updateObj.getPutUp());

            // 开启了考勤保护则 如果该课程有考勤数据且学员已打卡 则不允许关闭
            if (Boolean.TRUE.equals(enableAttendanceProtection)) {
                // 获取该天该班考勤记录
                List<ClockInInfoDO> classClockInDOSByDate = clockInInfoDOList.stream()
                        .filter(clockInInfoDO -> Objects.equals(clockInInfoDO.getDate(), classClockCalendarDO.getClockDate())
                                && Objects.equals(clockInInfoDO.getClassId(), classClockCalendarDO.getClassId()))
                        .collect(Collectors.toList());
                // 校验早餐、午餐、晚餐、住宿考勤是否可以关闭
                checkAttendanceProtectionClosable(classClockCalendarDO, classClockInDOSByDate);
            }
        }



        if (!classClockCalendarDOS.isEmpty()){
            classClockCalendarMapper.updateBatch(classClockCalendarDOS);
        }

        // 删除全校指定时间的考勤记录
        deletedClockRecordsByUpdateSchoolClockCalendarAndDates(updateReqVO, Collections.singletonList(schoolAccommodationAttendanceDO.getClockDate()));

        schoolAccommodationAttendanceMapper.updateById(updateObj);

        //修改考勤日历后刷新签到表
        // 刷新签到表,立即提交任务，不等待结果
        CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                .exceptionally(e -> {
                    log.error(e.getMessage(), e);
                    return null;
                });
    }

    private void deletedClockRecordsByUpdateSchoolClockCalendarAndDates(SchoolAccommodationAttendanceUpdateReqVO updateReqVO, List<LocalDate> dates) {
        // 就餐考勤类型列表 0-早餐 1-午餐 2-晚餐
        List<Integer> deleteMealPeriodList = new ArrayList<>();

        // 根据关闭情况 删除对应考勤记录
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getBreakfast())) {
            deleteMealPeriodList.add(MealPeriodEnum.BREAKFAST.getCode());
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getLunch())) {
            deleteMealPeriodList.add(MealPeriodEnum.LUNCH.getCode());
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getDinner())) {
            deleteMealPeriodList.add(MealPeriodEnum.DINNER.getCode());
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getPutUp())) {
            // 删除住宿考勤记录
            clockInInfoMapper.deleteAccommodationByDateList(dates);
        }

        if (CollUtil.isNotEmpty(deleteMealPeriodList)){
            // 根据就餐考勤记录
            clockInInfoMapper.deleteMealByDateListAndMealPeriodList(dates, deleteMealPeriodList);
        }
    }

    /**
     * 检查考勤保护是否可以关闭
     *
     * @param updateReqVO 更新请求对象，包含早、中、晚餐和起床状态
     * @param classClockInDOS 班级打卡信息列表
     */
    private void checkAttendanceProtectionClosable(ClassClockCalendarDO updateReqVO, List<ClockInInfoDO> classClockInDOS) {
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getBreakfast())) {
            boolean breakfastClock = classClockInDOS.stream().anyMatch(clockInInfoDO ->
                    AttendanceTypeEnum.MEAL_ATTENDANCE.getStatus().equals(clockInInfoDO.getType())
                            && MealPeriodEnum.BREAKFAST.getCode().equals(clockInInfoDO.getMealPeriod())
                            && Objects.equals(clockInInfoDO.getClassId(), updateReqVO.getClassId()));
            if (breakfastClock) {
                throw exception(CLOCK_NOT_CLASS_STATUS_NOT_SUPPORT_CLOSE);
            }
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getLunch())) {
            boolean lunchClock = classClockInDOS.stream().anyMatch(clockInInfoDO ->
                    AttendanceTypeEnum.MEAL_ATTENDANCE.getStatus().equals(clockInInfoDO.getType())
                            && MealPeriodEnum.LUNCH.getCode().equals(clockInInfoDO.getMealPeriod())
                            && Objects.equals(clockInInfoDO.getClassId(), updateReqVO.getClassId()));
            if (lunchClock) {
                throw exception(CLOCK_NOT_CLASS_STATUS_NOT_SUPPORT_CLOSE);
            }
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getDinner())) {
            boolean dinnerClock = classClockInDOS.stream().anyMatch(clockInInfoDO ->
                    AttendanceTypeEnum.MEAL_ATTENDANCE.getStatus().equals(clockInInfoDO.getType())
                            && MealPeriodEnum.DINNER.getCode().equals(clockInInfoDO.getMealPeriod())
                            && Objects.equals(clockInInfoDO.getClassId(), updateReqVO.getClassId()));
            if (dinnerClock) {
                throw exception(CLOCK_NOT_CLASS_STATUS_NOT_SUPPORT_CLOSE);
            }
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getPutUp())) {
            boolean putUpClock = classClockInDOS.stream().anyMatch(clockInInfoDO ->
                    AttendanceTypeEnum.ACCOMMODATION_ATTENDANCE.getStatus().equals(clockInInfoDO.getType())
                            && Objects.equals(clockInInfoDO.getClassId(), updateReqVO.getClassId()));
            if (putUpClock) {
                throw exception(CLOCK_NOT_CLASS_STATUS_NOT_SUPPORT_CLOSE);
            }
        }
    }

    @Override
    public void updateSchoolAccommodationAttendanceBatch(SchoolAccommodationAttendanceParamsVO updateReqVO) {
        List<Integer> ids = updateReqVO.getIds();

        if (CollUtil.isEmpty(ids)) {
            return;
        }

        List<SchoolAccommodationAttendanceDO> schoolAccommodationAttendanceDOS = schoolAccommodationAttendanceMapper.selectBatchIds(ids);

        List<LocalDate> dates = schoolAccommodationAttendanceDOS.stream().map(SchoolAccommodationAttendanceDO::getClockDate).collect(Collectors.toList());
        // 无法关闭的考勤状态 已打卡和迟到
        List<Integer> unCloseStatus = Arrays.asList(ClockStatusEnum.DONE.getCode(), ClockStatusEnum.LATE.getCode());
        // 指定时间段的所有考勤
        List<ClockInInfoDO> clockInInfoDOList = new ArrayList<>();

        // 是否开启了考勤保护
        Boolean enableAttendanceProtection = clockInInfoService.isEnableAttendanceProtection(getTenantId());
        if (Boolean.TRUE.equals(enableAttendanceProtection)) {
            clockInInfoDOList = clockInInfoMapper.selectListByClockDatesAndClassId(dates, null, unCloseStatus);
        }

        // 指定时间段的所有班级考勤日历
        List<ClassClockCalendarDO> classClockCalendarDOSByDates = classClockCalendarMapper.selectListByDates(dates);

        // 更新学校考勤和班级考勤日历
        List<SchoolAccommodationAttendanceDO> updateSchoolAccommodationAttendanceDOS = new ArrayList<>();
        List<ClassClockCalendarDO> updateClassClockCalendarDOS = new ArrayList<>();
        //todo 优化
        for (SchoolAccommodationAttendanceDO schoolAccommodationAttendanceDO : schoolAccommodationAttendanceDOS){
            Integer id = schoolAccommodationAttendanceDO.getId();
            // 更新学校考勤
            updateReqVO.getSchoolAccommodationAttendanceUpdateReqVO().setId(id);
            SchoolAccommodationAttendanceDO updateObj = SchoolAccommodationAttendanceConvert.INSTANCE.convert(updateReqVO.getSchoolAccommodationAttendanceUpdateReqVO());
            updateSchoolAccommodationAttendanceDOS.add(updateObj);

            // 获取该天所有班级考勤日历
            List<ClassClockCalendarDO> classClockCalendarDOS = classClockCalendarDOSByDates.stream().filter(classClockCalendarDO ->
                            Objects.equals(classClockCalendarDO.getClockDate(), schoolAccommodationAttendanceDO.getClockDate()))
                    .collect(Collectors.toList());

            for (ClassClockCalendarDO classClockCalendarDO : classClockCalendarDOS) {
                classClockCalendarDO.setBreakfast(updateObj.getBreakfast());
                classClockCalendarDO.setLunch(updateObj.getLunch());
                classClockCalendarDO.setDinner(updateObj.getDinner());
                classClockCalendarDO.setPutUp(updateObj.getPutUp());

                // 开启了考勤保护则 如果该课程有考勤数据且学员已打卡 则不允许关闭
                if (Boolean.TRUE.equals(enableAttendanceProtection)) {
                    // 获取该天该班考勤记录
                    List<ClockInInfoDO> classClockInDOSByDate = clockInInfoDOList.stream()
                            .filter(clockInInfoDO -> Objects.equals(clockInInfoDO.getDate(), classClockCalendarDO.getClockDate())
                                    && Objects.equals(clockInInfoDO.getClassId(), classClockCalendarDO.getClassId()))
                            .collect(Collectors.toList());
                    // 校验早餐、午餐、晚餐、住宿考勤是否可以关闭
                    checkAttendanceProtectionClosable(classClockCalendarDO, classClockInDOSByDate);
                }
            }

            updateClassClockCalendarDOS.addAll(classClockCalendarDOS);
        }

        if (!updateSchoolAccommodationAttendanceDOS.isEmpty()){
            schoolAccommodationAttendanceMapper.updateBatch(updateSchoolAccommodationAttendanceDOS);
        }

        if (!updateClassClockCalendarDOS.isEmpty()){
            classClockCalendarMapper.updateBatch(updateClassClockCalendarDOS);
        }

        // 删除全校指定时间的考勤记录
        deletedClockRecordsByUpdateSchoolClockCalendarAndDates(updateReqVO.getSchoolAccommodationAttendanceUpdateReqVO(), dates);


        //修改考勤日历后刷新签到表
        // 刷新签到表,立即提交任务，不等待结果
        CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                .exceptionally(e -> {
                    log.error(e.getMessage(), e);
                    return null;
                });
    }

    @Override
    public void deleteSchoolAccommodationAttendance(Integer id) {
        // 校验存在
        this.validateSchoolAccommodationAttendanceExists(id);
        // 删除
        schoolAccommodationAttendanceMapper.deleteById(id);
    }

    private SchoolAccommodationAttendanceDO validateSchoolAccommodationAttendanceExists(Integer id) {
        SchoolAccommodationAttendanceDO schoolAccommodationAttendanceDO = schoolAccommodationAttendanceMapper.selectById(id);
        if (schoolAccommodationAttendanceDO == null) {
            throw exception(SCHOOL_ACCOMMODATION_ATTENDANCE_NOT_EXISTS);
        }
        return schoolAccommodationAttendanceDO;
    }

    @Override
    public SchoolAccommodationAttendanceDO getSchoolAccommodationAttendance(Integer id) {
        return schoolAccommodationAttendanceMapper.selectById(id);
    }

    @Override
    public List<SchoolAccommodationAttendanceDO> getSchoolAccommodationAttendanceList(Collection<Integer> ids) {
        return schoolAccommodationAttendanceMapper.selectBatchIds(ids);
    }

    @Override
    public List<SchoolAccommodationAttendanceDO> getSchoolAccommodationAttendancePage(SchoolAccommodationAttendancePageReqVO pageReqVO) {
        return schoolAccommodationAttendanceMapper.selectDateRangeList(pageReqVO);
    }


    /**
     * 创建一年的所有日期
     * @param year
     * @return
     */
    public static List<SchoolAccommodationAttendanceBaseVO> generateYearData(int year) {
        List<SchoolAccommodationAttendanceBaseVO> yearData = new ArrayList<>();
        LocalDate startDate = LocalDate.of(year, 1, 1);
        LocalDate endDate = LocalDate.of(year, 12, 31);

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            SchoolAccommodationAttendanceBaseVO vo = new SchoolAccommodationAttendanceBaseVO();
            vo.setClockDate(currentDate);
            yearData.add(vo);
            currentDate = currentDate.plusDays(1);
        }

        return yearData;
    }

}
