package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class TraineeBaseVO {


    @ApiModelProperty(value = "学员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 学员名称
     */
    @ApiModelProperty(value = "学员名称")
    @NotBlank(message = "学员姓名不能为空")
    @Size(max = 50, message = "文本长度不能超过50个字符")
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @NotBlank(message = "学员性别不能为空")
    private String sex;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    @TableField(typeHandler = SM4EncryptTypeHandler.class)
    @NotBlank(message = "学员身份证不能为空")
    private String cardNo;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @TableField(typeHandler = SM4EncryptTypeHandler.class)
    @NotBlank(message = "学员手机号不能为空")
    //@Mobile
    private String phone;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 班级id
     */
    @ApiModelProperty(value = "班级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 文化程度
     */
    @ApiModelProperty(value = "文化程度")
    private Integer educationalLevel;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private LocalDate birthday;

    /**
     * 种族
     */
    @ApiModelProperty(value = "民族")
    private Integer ethnic;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
//    @Size(max = 200, message = "文本长度不能超过200个字符")
    @TableField(typeHandler = SM4EncryptTypeHandler.class)
    private String position;

    /**
     * 职级
     */
    @ApiModelProperty(value = "职级")
    private Integer jobLevel;

    /**
     * 毕业院校
     */
    @ApiModelProperty(value = "毕业院校")
    private String graduationSchool;

    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    private Long politicalIdentity;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    /**
     * 学员状态
     */
    @ApiModelProperty(value = "学员状态")
    private Integer status;

    /**
     * 学员照片
     */
    @ApiModelProperty(value = "学员照片")
    private String photo;

    /**
     * 小组id
     */
    @ApiModelProperty(value = "小组id")
    private Long groupId;

    /**
     * 小组排序
     */
    @ApiModelProperty(value = "小组排序")
    private Integer groupSort;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "单位分类")
    private Long unitClassification;

    @ApiModelProperty(value = "租户id")
    private Integer tenantId;

    @ApiModelProperty(value = "模板单位id")
    private Long parentUnitId;

    /**
     * 任职时间
     */
    @ApiModelProperty(value = "任职时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate employmentTime;

    /**
     * 最近参训日期
     */
    @ApiModelProperty(value = "最近参训日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate lastTrainingTime;

    /**
     * 最近培训班次
     */
    @ApiModelProperty(value = "最近培训班次")
    private String lastTrainingClass;

    /**
     * 最近参加任职资格考试日期
     */
    @ApiModelProperty(value = "最近参加任职资格考试日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate latestQualExamDate;

    /**
     * 同步标记，用于防止递归同步
     */
    @ApiModelProperty(value = "同步标记，用于防止递归同步", hidden = true)
    private Boolean syncFlag;
}
