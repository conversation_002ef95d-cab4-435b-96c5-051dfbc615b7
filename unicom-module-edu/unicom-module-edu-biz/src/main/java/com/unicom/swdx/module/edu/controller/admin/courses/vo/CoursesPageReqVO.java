package com.unicom.swdx.module.edu.controller.admin.courses.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 课程库分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CoursesPageReqVO extends PageParam {

    @ApiModelProperty(value = "课程名称")
    private String name;

    @ApiModelProperty(value = "课程分类字典ID")
    private Long themeId;

    @ApiModelProperty(value = "教学形式字典ID")
    private Long educateFormId;

    @ApiModelProperty(value = "教学方式字典ID")
    private Long teachingMethodId;

    @ApiModelProperty(value = "授课教师姓名")
    private String teacherName;

    @ApiModelProperty(value = "活动类型")
    private Integer activityType;

    @ApiModelProperty(value = "课程状态，0启用，1封库")
    private Integer status;

    @ApiModelProperty(value = "开发时间 - 开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startDate;

    @ApiModelProperty(value = "开发时间 - 结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endDate;

    @ApiModelProperty(value = "课程类型(1-专题课、2-选修课、3-教学活动)")
    private Integer coursesType;

    @ApiModelProperty(value = "排序字段(默认按创建时间) 0-按创建时间 1-按课程名称")
    @Range(min = 0, max = 1, message = "无法按该字段进行排序")
    private Integer sortField;

    @ApiModelProperty(value = "是否降序(默认降序)")
    private Boolean isDesc;

    @ApiModelProperty(value = "序号是否倒排(默认正排)")
    private Boolean isSerialDesc;

    @ApiModelProperty(value = "多选id（删除导出时使用）")
    private List<Long> ids;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;
}
