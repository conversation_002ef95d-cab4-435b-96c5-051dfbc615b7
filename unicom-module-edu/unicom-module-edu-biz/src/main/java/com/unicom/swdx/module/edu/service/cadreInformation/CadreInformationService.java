package com.unicom.swdx.module.edu.service.cadreInformation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.*;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.AddTraineeInfoReqVO;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.EditTraineeInfoReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.cadreInformation.CadreInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;

import java.util.List;

/**
 * 班主任待办事项 Service 接口
 *
 * <AUTHOR>
 */
public interface CadreInformationService extends IService<CadreInformationDO> {
    Long addCadreInformation(AddTraineeInfoReqVO reqVO);

    Long editCadreInformation(EditTraineeInfoReqVO reqVO);

    Boolean batchDelete(CadreBatchDeleteReqVO reqVO);

    Boolean delete(Long id);

    /**
     * 根据干部身份证查询干部信息
     */
    CadreInformationDO queryByCadreIdCard(String cadreIdCard,Long unitId,Integer tenantId);

    Page<PageCadreInformationRespVO> pageCadreInformation(PageCadreInformationReqVO reqVO);

    CadreImportRespVO importInfo(List<CadreInfoImportExcelVO> list, Long unitId,Integer cover);

    List<ExportCadreInfoExcelVO> getCadreInfoList(ExportCadreInformationReqVO reqVO);

    Page<CadreInfoDetailRespVO> getCadreInfoDetailPage(CadreInfoDetailReqVO reqVO);

    List<ExportCadreInfoDetailExcelVO> getCadreInfoDetailList(CadreInfoDetailReqVO reqVO);

    PageResult<TraineeReportPageRespVO> traineeReportPage(TraineeReportPageReqVO reqVO);

    List<TraineeReportExcelVO> exportTraineeReport(TraineeReportPageReqVO reqVO);

    Page<TraineeInfoPageRespVO> traineeInfoByUnitId(TraineeInfoPageReqVO reqVO);

    CadreImportRespVO confirm(TraineeConfirmReqVO reqVO);

    List<CadreInformationDO> getCadreInfoByTrainees(List<TraineeDO> rightData);
}
