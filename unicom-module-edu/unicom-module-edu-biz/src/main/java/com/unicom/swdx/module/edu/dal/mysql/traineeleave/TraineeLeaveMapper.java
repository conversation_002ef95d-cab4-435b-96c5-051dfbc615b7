package com.unicom.swdx.module.edu.dal.mysql.traineeleave;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveExportReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveMyReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeavePageReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface TraineeLeaveMapper extends BaseMapperX<TraineeLeaveDO> {

    List<TraineeLeaveRespVO> selectPageByReqVO(IPage page, @Param("reqVO") TraineeLeavePageReqVO reqVO);

    List<TraineeLeaveRespVO> selectListByReqVO(@Param("reqVO") TraineeLeaveExportReqVO reqVO);

    List<TraineeLeaveRespVO> selectMyList(@Param("reqVO") TraineeLeaveMyReqVO reqVO);

    TraineeLeaveRespVO getDetailById(@Param("leaveId") Long leaveId);

    /**
     * 根据学员id列表，时间段，请假状态查询请假记录
     * (请假时间完全覆盖时间段，交叉不会查询出来)
     * @param traineeIdList 学员id列表 (该列表为查询范围)
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param leaveStatusList 请假状态列表
     * @return 请假记录
     */
    List<TraineeLeaveDO> selectListByTraineeIdListAndTime(@Param("traineeIdList") List<Long> traineeIdList,
                                                          @Param("beginTime") LocalDateTime beginTime,
                                                          @Param("endTime") LocalDateTime endTime,
                                                          @Param("leaveStatusList") List<Integer> leaveStatusList);

    /**
     * 根据学员id列表，时间段，请假状态查询请假记录
     * (查询到beginTime在请假时间段内的记录 )
     * @param traineeIdList 学员id列表 (该列表为查询范围)
     * @param beginTime 到课考勤开始时间
     * @param leaveStatusList 请假状态列表
     * @return 请假记录
     */
    List<TraineeLeaveDO> selectListByTraineeIdListAndStartTime(@Param("traineeIdList") List<Long> traineeIdList,
                                                          @Param("beginTime") LocalDateTime beginTime,
                                                          @Param("leaveStatusList") List<Integer> leaveStatusList);


    Integer getLeaveCountOfTeacher(@Param("userId") Long userId, @Param("classId") Long classId);

    List<TraineeLeaveRespVO> selectTeacherList(@Param("classId") Long classId, @Param("status") Integer status);

    /**
     * 根据学员id查询请假记录
     * @param traineeId 学员id
     * @return 请假信息列表
     */
    default List<TraineeLeaveDO> selectListByTraineeId(Long traineeId) {
        return selectList(new LambdaQueryWrapperX<TraineeLeaveDO>()
                .eqIfPresent(TraineeLeaveDO::getTraineeId, traineeId));
    }

    /**
     * 根据学员id查询请假记录
     * @param traineeIds 学员id列表
     * @return 请假信息列表
     */
    default List<TraineeLeaveDO> selectListByTraineeIds(List<Long> traineeIds) {
        return selectList(new LambdaQueryWrapperX<TraineeLeaveDO>()
                .inIfPresent(TraineeLeaveDO::getTraineeId, traineeIds));
    }

    List<TraineeLeaveRespVO> selectListByLeaveIds(@Param("leaveIds") List<Long> leaveIds);

    String getTenant(@Param("tenantId") Long tenantId);

}
