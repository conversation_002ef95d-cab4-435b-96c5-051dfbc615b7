package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: zhouhk
 * @Date: 2024/12/20
 */

@Data
@ApiModel(value = "报名详情分页返回VO")
public class TraineeStatPageRespVO {

    @ApiModelProperty(value = "学员id")
    private Long id;

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "学期，1-上学期，2-下学期")
    private String semester;

    @ApiModelProperty(value = "开班日期")
    private String classOpenTime;

    @ApiModelProperty(value = "结业日期")
    private String completionTime;

    @ApiModelProperty(value = "学员姓名")
    private String name;

    @ApiModelProperty(value = "学员性别")
    private String sex;

    @ApiModelProperty("报名时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "出生日期")
    private String birthDay;

    @ApiModelProperty(value = "学员手机号")
    private String phone;

    @ApiModelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "职级")
    private String jobLevel;

    @ApiModelProperty(value = "老教务职级")
    private String oldjobLevel;

    /**
     * 最近参训日期
     */
    @ApiModelProperty(value = "最近参训日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate lastTrainingTime;

    /**
     * 最近培训班次
     */
    @ApiModelProperty(value = "最近培训班次")
    private String lastTrainingClass;

    /**
     * 最近参加任职资格考试日期
     */
    @ApiModelProperty(value = "最近参加任职资格考试日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate latestQualExamDate;
}
