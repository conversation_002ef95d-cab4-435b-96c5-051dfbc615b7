package com.unicom.swdx.module.edu.controller.admin.homepage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class TraineeLevelAnalysisRespVO {

	/**
	 * 总数
	 */
	@ApiModelProperty(value="总数")
	private Integer total;

	List<DataVO> itemTypeList;
	@Data
    @AllArgsConstructor
    @NoArgsConstructor
	public static class DataVO{


        /**
         * 职级id
         */
        @ApiModelProperty(value="职级id")
        private Integer id;

		/**
		 * 名称
		 */
		@ApiModelProperty(value="名称")
		private String itemName;

		/**
		 * 类型数量
		 */
		@ApiModelProperty(value="类型数量")
		private Integer itemNum;

		/**
		 * 类型占比
		 */
		@ApiModelProperty(value="类型占比")
		private String itemRate;
	}
}
