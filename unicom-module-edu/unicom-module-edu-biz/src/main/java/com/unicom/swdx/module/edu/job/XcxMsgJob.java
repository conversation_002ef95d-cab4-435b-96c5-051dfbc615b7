package com.unicom.swdx.module.edu.job;

import cn.hutool.core.collection.CollUtil;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxGroupMsg;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.dataobject.xcxmsg.XcxMsgConfigDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.xcxmsg.XcxMsgConfigMapper;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.enums.xcxmsg.XcxMsgSendType;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import com.unicom.swdx.module.edu.service.xcxMsg.XcxMsgService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.unicom.swdx.module.edu.enums.xcxmsg.XcxMsgSendType.BJ_KQ;
import static com.unicom.swdx.module.edu.enums.xcxmsg.XcxMsgSendType.LEAVE_FIN;

/**
 * @ClassName: XcxMsgJob
 * @Author: youxiaoyan
 * @Date: 2024/11/21 14:51
 */
@Service
@Slf4j
public class XcxMsgJob {

    @Resource
    private XcxMsgService xcxMsgService;

    @Resource
    private XcxMsgConfigMapper configMapper;

    private final String redirectBaseUri = "pages-home/subMessage/index";

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

    private DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");

    //@Scheduled(cron = "0 * * * * ?")
    @XxlJob("XcxMsgJob")
    @TenantIgnore
    public void dailySendXcxMsg() {
        log.info("--------检查小程序服务通知定时发送任务--------");
        List<XcxMsgConfigDO> configs = configMapper.selectList(new LambdaQueryWrapperX<XcxMsgConfigDO>()
                .eq(XcxMsgConfigDO::getStatus, true));
        if(CollUtil.isEmpty(configs)){
            return ;
        }
        // 遍历每个目标时间点
        for (XcxMsgConfigDO config : configs) {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();

            // 解析目标时间
            LocalTime targetTime = LocalTime.parse(config.getInformTime());

            // 组合目标时间和当前日期
            LocalDateTime targetDateTime = LocalDateTime.of(now.toLocalDate(), targetTime);

            String nowTime = now.format(timeFormatter);
            String targetMinuteTime = targetDateTime.format(timeFormatter);

            if(nowTime.equals(targetMinuteTime)){
                // 立即发送小程序服务通知
                sendXcxMsg(config, targetDateTime.format(formatter),targetDateTime,config.getTenantId());
            }
        }
    }

    private void sendXcxMsg(XcxMsgConfigDO config, String informTime,LocalDateTime targetDateTime,Long tenantId) {
        XcxGroupMsg msg = new XcxGroupMsg();

        msg.setInformContent(config.getInformContent());
        msg.setRemark(config.getRemark());
        msg.setInformTime(informTime);
        if(Objects.equals(config.getTag(), 1)){
            msg.setUserIds(configMapper.selectCourseMsgIds(targetDateTime.plusDays(1).format(dateFormatter),tenantId));
            msg.setPageUri(XcxMsgSendType.PC_SK_TX.getPath());
        } else if(Objects.equals(config.getTag(), 2)){
            msg.setUserIds(configMapper.selectAttendanceMsgIds(targetDateTime.format(dateFormatter),tenantId));
            msg.setPageUri(redirectBaseUri+XcxMsgSendType.BJ_KQ.getPath()+"&next_query="+"type_1");
        } else if(Objects.equals(config.getTag(), 3)){
            msg.setUserIds(configMapper.selectEvaluateMsgIds(targetDateTime.format(formatter2),tenantId));
            msg.setPageUri(redirectBaseUri+XcxMsgSendType.KC_PJ_ML.getPath());
        }
        xcxMsgService.sendBatchXcxMsg(msg);
    }

}
