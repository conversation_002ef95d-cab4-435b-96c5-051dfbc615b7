package com.unicom.swdx.module.edu.mq.producer;

import com.alibaba.fastjson.JSON;
import com.unicom.swdx.framework.dict.core.util.DictFrameworkUtils;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.enums.trainee.TraineeDictTypeEnum;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaHeaderDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaMessageDTO;
import com.unicom.swdx.module.system.api.kafka.dto.OldKafkaTraineeDTO;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.Objects;

import static com.unicom.swdx.module.system.enums.kafka.member.TraineeEventType.*;

@Component
public class TraineeProducer {

    @Value("${sendTopic.oldKafka}")
    private String oldKafkaTopic;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private KafkaTemplate<Object, Object> kafkaTemplate;

    @Async
    public void sendTraineeToKafka(String eventType, TraineeDO traineeDO){

        OldKafkaMessageDTO traineeMsg = getTrainee(eventType,traineeDO);
        kafkaTemplate.send(oldKafkaTopic, JSON.toJSONString(traineeMsg));

    }

    public OldKafkaMessageDTO getTrainee(String eventType, TraineeDO traineeDO){

        OldKafkaHeaderDTO header = new OldKafkaHeaderDTO();
        header.setSender(memberSender);
        header.setEventType(eventType);
        String tenantCode = tenantApi.getTenantCodeById(traineeDO.getTenantId().longValue()).getCheckedData();
        header.setTenant_code(tenantCode);

        OldKafkaTraineeDTO body = new OldKafkaTraineeDTO();
        OldKafkaTraineeDTO.StudentDTO student = new OldKafkaTraineeDTO.StudentDTO();
        OldKafkaTraineeDTO.TrainClassStudentDTO trainClassStudent = new OldKafkaTraineeDTO.TrainClassStudentDTO();

        student.setBirthday(traineeDO.getBirthday().toString());
        student.setFlag("1");
        student.setNation(DictFrameworkUtils.getDictDataLabel("nation",traineeDO.getEthnic().toString()));
        student.setIdCard(traineeDO.getCardNo());
        student.setSex(traineeDO.getSex());
        if(Objects.nonNull(traineeDO.getSex())){
            student.setSex("男".equals(traineeDO.getSex())?"1":"2");
        }
        student.setCurrentClassId(traineeDO.getClassId().toString());
        student.setCurrentJigouId(traineeDO.getUnitId().toString());
        student.setPost(traineeDO.getPosition());
        student.setPhone(traineeDO.getPhone());
        student.setCulture(DictFrameworkUtils.getDictDataLabel(TraineeDictTypeEnum.EDUCATIONAL_LEVEL.getType(),traineeDO.getEducationalLevel().toString()));
        student.setName(traineeDO.getName());
        student.setRank(DictFrameworkUtils.getDictDataLabel(TraineeDictTypeEnum.JOB_LEVEL.getType(),traineeDO.getJobLevel().toString()));
        student.setZzmm(DictFrameworkUtils.getDictDataLabel("political_identity",traineeDO.getPoliticalIdentity().toString()));
        student.setId(traineeDO.getId().toString());
        student.setTenantCode(tenantCode);

        trainClassStudent.setStudentId(traineeDO.getId().toString());
        trainClassStudent.setClassId(traineeDO.getClassId().toString());
        trainClassStudent.setId(traineeDO.getId().toString());
        trainClassStudent.setStatus(traineeDO.getStatus().toString());

        body.setStudent(student);
        body.setTrainClassStudent(trainClassStudent);
        body.setSource("1");

        OldKafkaMessageDTO oldKafkaMessageDTO = new OldKafkaMessageDTO();
        oldKafkaMessageDTO.setHeader(header);
        oldKafkaMessageDTO.setBody(body);
        return oldKafkaMessageDTO;
    }

}
