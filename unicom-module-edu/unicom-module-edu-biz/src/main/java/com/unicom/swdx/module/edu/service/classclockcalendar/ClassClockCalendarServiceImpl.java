package com.unicom.swdx.module.edu.service.classclockcalendar;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance.vo.SchoolAccommodationAttendanceBaseVO;
import com.unicom.swdx.module.edu.convert.schoolaccommodationattendance.SchoolAccommodationAttendanceConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassClockInDO;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;
import com.unicom.swdx.module.edu.dal.dataobject.schoolaccommodationattendance.SchoolAccommodationAttendanceDO;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.dal.mysql.courses.HolidayMapper;
import com.unicom.swdx.module.edu.enums.attendance.AttendanceTypeEnum;
import com.unicom.swdx.module.edu.enums.clockcalendar.ClockCalendarStatusEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.ClockStatusEnum;
import com.unicom.swdx.module.edu.enums.clockininfo.MealPeriodEnum;
import com.unicom.swdx.module.edu.service.classcourse.ClassCourseService;
import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoService;
import com.unicom.swdx.module.edu.utils.holiday.HolidayUtils;
import groovy.lang.Lazy;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classclockcalendar.ClassClockCalendarDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.convert.classclockcalendar.ClassClockCalendarConvert;
import com.unicom.swdx.module.edu.dal.mysql.classclockcalendar.ClassClockCalendarMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 班级考勤日历 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ClassClockCalendarServiceImpl extends ServiceImpl<ClassClockCalendarMapper,ClassClockCalendarDO> implements ClassClockCalendarService {

    @Resource
    private ClassClockCalendarMapper classClockCalendarMapper;

    @Resource
    private ClockInInfoService clockInInfoService;

    @Resource
    private ClockInInfoMapper clockInInfoMapper;

    @Resource
    private HolidayMapper holidayMapper;


    // 生成一个班级的某个年份考勤日历
    public Integer createClassClockCalendarForYear(Integer year, Long classId, Set<String> holidays, List<ClassClockCalendarBaseVO> yearData) {
        LocalDate startDate = LocalDate.of(year, 1, 1);
        LocalDate endDate = LocalDate.of(year, 12, 31);
        List<ClassClockCalendarDO> classClockCalendarDOList = classClockCalendarMapper.selectListByClassOneYear(
                Collections.singletonList(classId),
                startDate, endDate);

        Map<LocalDate, ClassClockCalendarDO> existsClassClockCalendar = classClockCalendarDOList.stream().collect(Collectors
                .toMap(ClassClockCalendarDO::getClockDate, o -> o, (o1, o2) -> o1));

        List<ClassClockCalendarDO> batchInsertList = new ArrayList<>();

        ClassClockCalendarCreateReqVO createReqVO = new ClassClockCalendarCreateReqVO();
        createReqVO.setClassId(classId);
        createReqVO.setYear(year);

        for (int i = 0; i < yearData.size(); i++) {
            ClassClockCalendarBaseVO vo = yearData.get(i);

            // 将 LocalDate 转换为字符串
            String formattedDate = vo.getClockDate().toString();

            boolean isHoliday = holidays.contains(formattedDate);

            // 插入日期
            createReqVO.setClockDate(vo.getClockDate());

            if (isHoliday) {
                // 1- 关
                createReqVO.setBreakfast(1);
                createReqVO.setLunch(1);
                createReqVO.setDinner(1);
                createReqVO.setPutUp(1);
                // 是节假日
                createReqVO.setIsHoliday(0);
            } else {
                // 0 - 开
                createReqVO.setBreakfast(0);
                createReqVO.setLunch(0);
                createReqVO.setDinner(0);
                createReqVO.setPutUp(0);
                // 非节假日
                createReqVO.setIsHoliday(1);
            }

            // 检查当天为工作日，第二天为休息日的情况
            if (!isHoliday && i + 1 < yearData.size()) {
                String nextDayFormattedDate = yearData.get(i + 1).getClockDate().toString();
                if (holidays.contains(nextDayFormattedDate)) {
                    // 第二天下班不考勤
                    createReqVO.setDinner(1);
                    createReqVO.setPutUp(1);
                }
            }

            // 检查当天为休息日，第二天为工作日的情况
            if (isHoliday && i + 1 < yearData.size()) {
                String nextDayFormattedDate = yearData.get(i + 1).getClockDate().toString();
                if (!holidays.contains(nextDayFormattedDate)) {
                    // 第二天上班不考勤
                    createReqVO.setPutUp(0);
                }
            }

            // 转换并添加到批量插入列表
            ClassClockCalendarDO classClockCalendar = ClassClockCalendarConvert.INSTANCE.convert(createReqVO);
            batchInsertList.add(classClockCalendar);

        }

        return null;
    }

    @Override
    public Integer createClassClockCalendar(ClassClockCalendarCreateReqVO createReqVO) {

        // 自动获取本年的年份
        LocalDate currentDate = LocalDate.now();
        Integer year = currentDate.getYear();

        createReqVO.setYear(year);

        // 获取一年中的天数
        List<ClassClockCalendarBaseVO> yearData = generateYearData(createReqVO.getYear());

        // 获取节假日
        Set<String> result = getVacation(createReqVO.getYear(), 0);

        List<ClassClockCalendarDO> batchInsertList = new ArrayList<>();

        for (int i = 0; i < yearData.size(); i++) {
            ClassClockCalendarBaseVO vo = yearData.get(i);

            // 将 LocalDate 转换为字符串
            String formattedDate = vo.getClockDate().toString();

            boolean isHoliday = result.contains(formattedDate);

            // 插入日期
            createReqVO.setClockDate(vo.getClockDate());

            if (isHoliday) {
                // 1- 关
                createReqVO.setBreakfast(1);
                createReqVO.setLunch(1);
                createReqVO.setDinner(1);
                createReqVO.setPutUp(1);
                // 是节假日
                createReqVO.setIsHoliday(0);
            } else {
                // 0 - 开
                createReqVO.setBreakfast(0);
                createReqVO.setLunch(0);
                createReqVO.setDinner(0);
                createReqVO.setPutUp(0);
                // 非节假日
                createReqVO.setIsHoliday(1);
            }

            // 检查当天为工作日，第二天为休息日的情况
            if (!isHoliday && i + 1 < yearData.size()) {
                String nextDayFormattedDate = yearData.get(i + 1).getClockDate().toString();
                if (result.contains(nextDayFormattedDate)) {
                    // 第二天下班不考勤
                    createReqVO.setDinner(1);
                    createReqVO.setPutUp(1);
                }
            }

            // 检查当天为休息日，第二天为工作日的情况
            if (isHoliday && i + 1 < yearData.size()) {
                String nextDayFormattedDate = yearData.get(i + 1).getClockDate().toString();
                if (!result.contains(nextDayFormattedDate)) {
                    // 第二天上班不考勤
                    createReqVO.setPutUp(0);
                }
            }


            // 转换并添加到批量插入列表
            ClassClockCalendarDO classClockCalendar = ClassClockCalendarConvert.INSTANCE.convert(createReqVO);
            batchInsertList.add(classClockCalendar);


        }

        // 批量插入
        if (!batchInsertList.isEmpty()) {
            classClockCalendarMapper.insertBatch(batchInsertList);
        }

        // 返回
        return yearData.size();
    }

    /**
     * 获取节假日
     * @param year 年
     * @param month 月
     * @return 节假日日期
     */
    public Set<String> getVacation(int year, int month) {
        return HolidayUtils.JJR(year, month, holidayMapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateClassClockCalendar(ClassClockCalendarUpdateReqVO updateReqVO) {

        // 校验存在
        ClassClockCalendarDO classClockCalendarDO = this.validateClassClockCalendarExists(updateReqVO.getId());

        // 开启了考勤保护则 如果该课程有考勤数据且学员已打卡 则不允许关闭
        if (Boolean.TRUE.equals(clockInInfoService.isEnableAttendanceProtection(getTenantId()))){
            // 无法关闭的考勤状态 已打卡和迟到
            List<Integer> unCloseStatus = Arrays.asList(ClockStatusEnum.DONE.getCode(), ClockStatusEnum.LATE.getCode());
            // 获取该天考勤记录
            List<ClockInInfoDO> classClockInDOS =  clockInInfoMapper.selectListByClockDateAndClassId(classClockCalendarDO.getClockDate(),
                    classClockCalendarDO.getClassId(), unCloseStatus);
            // 校验早餐、午餐、晚餐、住宿考勤是否可以关闭
            checkAttendanceProtectionClosable(updateReqVO, classClockInDOS);
        }

        // 关闭班级考勤后同步删除对应学员的考勤记录
        deletedClockRecordsByUpdateClassClockCalendarAndDates(updateReqVO, Collections.singletonList(classClockCalendarDO.getClockDate()));

        // 更新
        ClassClockCalendarDO updateObj = ClassClockCalendarConvert.INSTANCE.convert(updateReqVO);
        classClockCalendarMapper.updateById(updateObj);

        //修改考勤日历后刷新签到表
        // 刷新签到表,立即提交任务，不等待结果
        CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                .exceptionally(e -> {
                    log.error(e.getMessage(), e);
                    return null;
                });
    }

    /**
     * 关闭班级考勤后同步删除对应学员的考勤记录
     * @param updateReqVO 更新请求对象
     * @param dates 指定日期
     */
    private void deletedClockRecordsByUpdateClassClockCalendarAndDates(ClassClockCalendarUpdateReqVO updateReqVO,
                                                          List<LocalDate> dates) {
        // 就餐考勤类型列表 0-早餐 1-午餐 2-晚餐
        List<Integer> deleteMealPeriodList = new ArrayList<>();

        // 根据关闭情况 删除对应考勤记录
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getBreakfast())) {
            deleteMealPeriodList.add(MealPeriodEnum.BREAKFAST.getCode());
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getLunch())) {
            deleteMealPeriodList.add(MealPeriodEnum.LUNCH.getCode());
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getDinner())) {
            deleteMealPeriodList.add(MealPeriodEnum.DINNER.getCode());
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getPutUp())) {
            // 删除住宿考勤记录
            clockInInfoMapper.deleteAccommodationByClassIdAndDateList(updateReqVO.getClassId(),
                    dates);
        }

        if (CollUtil.isNotEmpty(deleteMealPeriodList)){
            // 根据就餐考勤记录
            clockInInfoMapper.deleteMealByClassIdAndDateListAndMealPeriodList(updateReqVO.getClassId(),
                    dates, deleteMealPeriodList);
        }
    }


    /**
     * 检查考勤保护是否可以关闭
     *
     * @param updateReqVO 更新请求对象，包含早、中、晚餐和起床状态
     * @param classClockInDOS 班级打卡信息列表
     */
    private void checkAttendanceProtectionClosable(ClassClockCalendarUpdateReqVO updateReqVO, List<ClockInInfoDO> classClockInDOS) {
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getBreakfast())) {
            boolean breakfastClock = classClockInDOS.stream().anyMatch(clockInInfoDO ->
                    AttendanceTypeEnum.MEAL_ATTENDANCE.getStatus().equals(clockInInfoDO.getType())
                            && MealPeriodEnum.BREAKFAST.getCode().equals(clockInInfoDO.getMealPeriod())
                            && Objects.equals(clockInInfoDO.getClassId(), updateReqVO.getClassId()));
            if (breakfastClock) {
                throw exception(CLOCK_NOT_CLASS_STATUS_NOT_SUPPORT_CLOSE);
            }
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getLunch())) {
            boolean lunchClock = classClockInDOS.stream().anyMatch(clockInInfoDO ->
                    AttendanceTypeEnum.MEAL_ATTENDANCE.getStatus().equals(clockInInfoDO.getType())
                            && MealPeriodEnum.LUNCH.getCode().equals(clockInInfoDO.getMealPeriod())
                            && Objects.equals(clockInInfoDO.getClassId(), updateReqVO.getClassId()));
            if (lunchClock) {
                throw exception(CLOCK_NOT_CLASS_STATUS_NOT_SUPPORT_CLOSE);
            }
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getDinner())) {
            boolean dinnerClock = classClockInDOS.stream().anyMatch(clockInInfoDO ->
                    AttendanceTypeEnum.MEAL_ATTENDANCE.getStatus().equals(clockInInfoDO.getType())
                            && MealPeriodEnum.DINNER.getCode().equals(clockInInfoDO.getMealPeriod())
                            && Objects.equals(clockInInfoDO.getClassId(), updateReqVO.getClassId()));
            if (dinnerClock) {
                throw exception(CLOCK_NOT_CLASS_STATUS_NOT_SUPPORT_CLOSE);
            }
        }
        if (ClockCalendarStatusEnum.OFF.getStatus().equals(updateReqVO.getPutUp())) {
            boolean putUpClock = classClockInDOS.stream().anyMatch(clockInInfoDO ->
                    AttendanceTypeEnum.ACCOMMODATION_ATTENDANCE.getStatus().equals(clockInInfoDO.getType())
                            && Objects.equals(clockInInfoDO.getClassId(), updateReqVO.getClassId()));
            if (putUpClock) {
                throw exception(CLOCK_NOT_CLASS_STATUS_NOT_SUPPORT_CLOSE);
            }
        }
    }

    /**
     * 批量更新班级考勤日历
     *
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateClassClockCalendarBatch(ClassClockCalendarParamsVO updateReqVO) {
        List<Integer> ids = updateReqVO.getIds();
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        // 获取批量更新的考勤日历数据对象列表
        List<ClassClockCalendarDO> classClockCalendarDOList = classClockCalendarMapper.selectBatchIds(ids);

        if (classClockCalendarDOList.isEmpty()){
            return;
        }

        // 是否开启了考勤保护
        Boolean enableAttendanceProtection = clockInInfoService.isEnableAttendanceProtection(getTenantId());
        // 无法关闭的考勤状态 已打卡和迟到
        List<Integer> unCloseStatus = Arrays.asList(ClockStatusEnum.DONE.getCode(), ClockStatusEnum.LATE.getCode());

        // 获取这些日期的考勤记录
        List<LocalDate> dates = classClockCalendarDOList.stream().map(ClassClockCalendarDO::getClockDate).collect(Collectors.toList());

        // 获取该天考勤记录
        List<ClockInInfoDO> classClockInDOS =  clockInInfoMapper.selectListByClockDatesAndClassId(dates,
                classClockCalendarDOList.get(0).getClassId(), unCloseStatus);


        // 更新列表
        List<ClassClockCalendarDO> updateList = new ArrayList<>();
        classClockCalendarDOList.forEach(classClockCalendarDO -> {
            Integer id = classClockCalendarDO.getId();
            // 更新
            updateReqVO.getClassClockCalendarUpdateReqVO().setId(id);
            // 开启了考勤保护则 如果该课程有考勤数据且学员已打卡 则不允许关闭
            if (Boolean.TRUE.equals(enableAttendanceProtection)){
                // 获取该天考勤记录
                List<ClockInInfoDO> classClockInDOSByDate = classClockInDOS.stream()
                        .filter(clockInInfoDO -> Objects.equals(clockInInfoDO.getDate(), classClockCalendarDO.getClockDate()))
                        .collect(Collectors.toList());
                // 校验早餐、午餐、晚餐、住宿考勤是否可以关闭
                checkAttendanceProtectionClosable(updateReqVO.getClassClockCalendarUpdateReqVO(), classClockInDOSByDate);
            }
            ClassClockCalendarDO updateObj = ClassClockCalendarConvert.INSTANCE.convert(updateReqVO.getClassClockCalendarUpdateReqVO());
            updateList.add(updateObj);
        });

        if (updateList.isEmpty()){
            return;
        }

        // 关闭班级考勤后同步删除对应学员的考勤记录
        deletedClockRecordsByUpdateClassClockCalendarAndDates(updateReqVO.getClassClockCalendarUpdateReqVO(), dates);

        // 更新
        classClockCalendarMapper.updateBatch(updateList);

        //修改考勤日历后刷新签到表
        // 刷新签到表,立即提交任务，不等待结果
        CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                .exceptionally(e -> {
                    log.error(e.getMessage(), e);
                    return null;
                });
    }

    @Override
    public void deleteClassClockCalendar(Long classId) {
        // 删除 对应班级的 考勤数据
        classClockCalendarMapper.deleteByClassId(classId);
    }

    private ClassClockCalendarDO validateClassClockCalendarExists(Integer id) {
        ClassClockCalendarDO classClockCalendarDO = classClockCalendarMapper.selectById(id);
        if ( classClockCalendarDO == null) {
            throw exception(CLASS_CLOCK_CALENDAR_NOT_EXISTS);
        }
        return classClockCalendarDO;
    }

    @Override
    public ClassClockCalendarDO getClassClockCalendar(Integer id) {
        return classClockCalendarMapper.selectById(id);
    }

//    @Override
//    public List<ClassClockCalendarDO> getClassClockCalendarList(Collection<Integer> ids) {
//        return classClockCalendarMapper.selectBatchIds(ids);
//    }

    @Override
    public List<ClassClockCalendarDO> getClassClockCalendarPage(ClassClockCalendarListReqVO pageReqVO) {
        return classClockCalendarMapper.selectDateRangeList(pageReqVO);
    }


    /**
     * 创建一年的所有日期
     * @param year
     * @return
     */
    public static List<ClassClockCalendarBaseVO> generateYearData(int year) {

        List<ClassClockCalendarBaseVO> yearData = new ArrayList<>();

        LocalDate startDate = LocalDate.of(year, 1, 1);
        LocalDate endDate = LocalDate.of(year, 12, 31);

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            ClassClockCalendarBaseVO vo = new ClassClockCalendarBaseVO();
            vo.setClockDate(currentDate);
            yearData.add(vo);
            currentDate = currentDate.plusDays(1);
        }

        return yearData;
    }
}
