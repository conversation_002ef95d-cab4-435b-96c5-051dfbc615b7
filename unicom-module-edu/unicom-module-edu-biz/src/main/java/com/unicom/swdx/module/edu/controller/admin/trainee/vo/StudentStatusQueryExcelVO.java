package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class StudentStatusQueryExcelVO {
    @ExcelProperty(value = "序号")
    private Integer index;

    @ExcelProperty(value = "学员姓名")
    private String name;

    @ExcelProperty(value = "班次名称")
    private String className;


    @ExcelProperty(value = "学员手机号")
    private String phone;

    @ExcelProperty(value = "单位")
    private String unitName;

    @ExcelProperty(value = "职务")
    private String position;

    @ExcelProperty(value = "学员状态")
    private String status;

    @ExcelProperty(value = "更新时间")
    private String statusUpdateTime;

}
