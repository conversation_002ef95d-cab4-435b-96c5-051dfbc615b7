package com.unicom.swdx.module.edu.service.rollcallrecord;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallrecord.RollcallRecordDO;

import java.util.List;

/**
 * 学员点名签到记录 Service 接口
 *
 * <AUTHOR>
 */
public interface RollcallRecordService  extends IService<RollcallRecordDO> {

    /**
     * 获取签到详情-未签到、已签到人员信息
     *
     * @param id     点名签到id
     * @param status 签到状态筛选 0-未签到 1-已签到
     * @return 签到详情学员详情列表
     */
    List<AppTraineeGroupRespVO> getRollcallSignInTraineeInfo(Long id, Integer status);

    /**
     * 获取签到详情-未签到、已签到人员信息
     *
     * @param id     大课考勤id
     * @param status 签到状态筛选 0-未签到 1-已签到
     * @return 签到详情学员详情列表
     */
    List<AppTraineeGroupRespVO> getLectureAttendanceTraineeInfo(Long id, Integer status);

    void deleteRollCallRecordByTraineeId(Long traineeId);

    void deleteRollCallRecordByTraineeIds(List<Long> ids);
}
