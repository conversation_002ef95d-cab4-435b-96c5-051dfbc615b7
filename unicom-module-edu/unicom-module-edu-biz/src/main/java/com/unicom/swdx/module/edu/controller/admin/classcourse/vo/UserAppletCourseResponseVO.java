package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoRespVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class UserAppletCourseResponseVO {
    private int code;
    private String msg;
    private Data data;


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public static class Data {
        private List<Sql1> sql1;

        public List<Sql1> getSql1() {
            return sql1;
        }

        public void setSql1(List<Sql1> sql1) {
            this.sql1 = sql1;
        }

        public static class Sql1 {
            //班次id
            private String class_id;
            //班次名称
            private String class_name;
            //课程名称
            private String KC_NAME;
            //学员数
            private String stu_num;
            //上课日期
            private String sk_sj;
            //上课时段
            private String sk_sd;
            //课程类型
            private String KC_TYPE;
            //教室名称
            private String room_name;
            //任课教师ID(主)(业中)
            private String MAJOR_TEACHER_ID_YZ;
            //任课教师名称(主)
            private String MAJOR_TEACHERNAME;
            //任课教师ID(辅)(业中)
            private String MINOR_TEACHER_ID_YZ;
            //任课教师名称(辅)
            private String MINOR_TEACHERNAME;

            //@ApiModelProperty(value = "是否党政领导讲课，0不是，1是")
            private Boolean isLeaderLecture;

            private Boolean isMerge;

            private List<ClassInfoRespVO> mergedClass;

            public Boolean getIsMerge() {
                return this.isMerge;
            }

            public void setIsMerge(Boolean merge) {
                this.isMerge = merge;
            }

            public List<ClassInfoRespVO> getMergedClass() {
                return mergedClass;
            }

            public void setMergedClass(List<ClassInfoRespVO> mergedClass) {
                this.mergedClass = mergedClass;
            }



            public Boolean getLeaderLecture() {
                return isLeaderLecture;
            }

            public void setLeaderLecture(Boolean leaderLecture) {
                isLeaderLecture = leaderLecture;
            }

            public String getBeginTime() {
                return beginTime;
            }

            public void setBeginTime(String beginTime) {
                this.beginTime = beginTime;
            }

            public String getEndTime() {
                return endTime;
            }

            public void setEndTime(String endTime) {
                this.endTime = endTime;
            }

            //@ApiModelProperty(value = "开始时间")
            private String beginTime;

            //@ApiModelProperty(value = "结束时间")
            private String endTime;

            public String getClass_id() {
                return class_id;
            }

            public String getClass_name() {
                return class_name;
            }

            public String getKC_NAME() {
                return KC_NAME;
            }

            public String getStu_num() {
                return stu_num;
            }

            public String getSk_sj() {
                return sk_sj;
            }

            public String getSk_sd() {
                return sk_sd;
            }

            public String getKC_TYPE() {
                return KC_TYPE;
            }

            public String getRoom_name() {
                return room_name;
            }

            public String getMAJOR_TEACHER_ID_YZ() {
                return MAJOR_TEACHER_ID_YZ;
            }

            public String getMAJOR_TEACHERNAME() {
                return MAJOR_TEACHERNAME;
            }

            public String getMINOR_TEACHER_ID_YZ() {
                return MINOR_TEACHER_ID_YZ;
            }

            public String getMINOR_TEACHERNAME() {
                return MINOR_TEACHERNAME;
            }

            public void setClass_id(String class_id) {
                this.class_id = class_id;
            }

            public void setClass_name(String class_name) {
                this.class_name = class_name;
            }

            public void setKC_NAME(String KC_NAME) {
                this.KC_NAME = KC_NAME;
            }

            public void setStu_num(String stu_num) {
                this.stu_num = stu_num;
            }

            public void setSk_sj(String sk_sj) {
                this.sk_sj = sk_sj;
            }

            public void setSk_sd(String sk_sd) {
                this.sk_sd = sk_sd;
            }

            public void setKC_TYPE(String KC_TYPE) {
                this.KC_TYPE = KC_TYPE;
            }

            public void setRoom_name(String room_name) {
                this.room_name = room_name;
            }

            public void setMAJOR_TEACHER_ID_YZ(String MAJOR_TEACHER_ID_YZ) {
                this.MAJOR_TEACHER_ID_YZ = MAJOR_TEACHER_ID_YZ;
            }

            public void setMAJOR_TEACHERNAME(String MAJOR_TEACHERNAME) {
                this.MAJOR_TEACHERNAME = MAJOR_TEACHERNAME;
            }

            public void setMINOR_TEACHER_ID_YZ(String MINOR_TEACHER_ID_YZ) {
                this.MINOR_TEACHER_ID_YZ = MINOR_TEACHER_ID_YZ;
            }

            public void setMINOR_TEACHERNAME(String MINOR_TEACHERNAME) {
                this.MINOR_TEACHERNAME = MINOR_TEACHERNAME;
            }
        }
    }

}
