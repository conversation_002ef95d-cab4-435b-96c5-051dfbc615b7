package com.unicom.swdx.module.edu.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.enums.classmanagement.ClassManageDictTypeEnum;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公共 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class EduCommonServiceImpl implements EduCommonService {

//    @Resource
//    @Lazy
//    private EduCommonService self;

    @Resource
    private DictDataApi dictDataApi;

    @Getter
    private volatile Map<Long, Map<Long, String>> campusCacheData;

    @Scheduled(fixedRate = 5*60*1000) // 同步一次
    public void syncCampusDataCache() {
        log.info("开始定时同步校区数据缓存");
        initCampusDataCache();
    }

    /**
     * 初始化校区数据缓存
     */
    @PostConstruct
    @Override
    @TenantIgnore
    public void initCampusDataCache() {
        try {
            // 获取校区数据字典
                List<DictDataRespDTO> dictDataRespDTOS = dictDataApi.getByDictTypes(Collections.singletonList(ClassManageDictTypeEnum.CAMPUS.getType())).getCheckedData();
            if (CollUtil.isEmpty(dictDataRespDTOS)) {
                log.warn("校区数据字典为空");
                return;
            }
            campusCacheData = new HashMap<>();
            // 按租户 ID 分组并构建缓存
            Map<Long, List<DictDataRespDTO>> groupedByTenant = dictDataRespDTOS.stream()
                    .filter(item -> item.getTenantId() != null) // 过滤掉 tenantId 为 null 的数据
                    .collect(Collectors.groupingBy(DictDataRespDTO::getTenantId));
            groupedByTenant.forEach((tenantId, dictDataRespDTOList) -> {
                Map<Long, String> campusCache = dictDataRespDTOList.stream()
                        .collect(Collectors.toMap(DictDataRespDTO::getId, DictDataRespDTO::getLabel));
                campusCacheData.put(tenantId, campusCache);
            });
            log.info("校区数据缓存初始化成功，租户数量: {}", campusCacheData.size());
        } catch (Exception e) {
            log.error("初始化校区数据缓存失败", e);
            campusCacheData = new HashMap<>();
        }
    }

    /**
     * 根据租户ID和校区ID获取校区名称
     * @param tenantId 租户ID
     * @param campusId 校区ID
     * @return 校区名称，如果不存在则返回null
     */
    @Override
    public String getCampusName(Long tenantId, Long campusId) {
        if (campusCacheData == null){
            return null;
        }
        Map<Long, String> cache = campusCacheData.get(tenantId);
        return cache != null ? cache.getOrDefault(campusId, null) : null;
    }

    // 根据校区名称列表获取校区ID列表
    public Long getCampusIdByTenantIdAndCampusName(Long tenantId, String campusName){
        if (campusCacheData == null){
            return null;
        }

        Map<Long, String> campusData = campusCacheData.get(tenantId);
        if (campusData != null) {
            for (Map.Entry<Long, String> entry : campusData.entrySet()) {
                if (entry.getValue().equals(campusName)) {
                    return entry.getKey();
                }
            }
        }
        return null;

    }

    // 根据校区名称列表获取校区ID列表
    @Override
    public List<Long> getCampusIdsByTenantIdAndCampusName(Long tenantId, List<String> campusNames){
        if (campusCacheData == null){
            return Collections.emptyList();
        }
        List<Long> campusIds = new ArrayList<>();
        campusNames.forEach(name -> {
            Long campusId = getCampusIdByTenantIdAndCampusName(tenantId, name);
            if (campusId != null) {
                campusIds.add(campusId);
            }
        });
        return campusIds;
    }

    /**
     * 根据校区ID获取校区名称
     * @param campusId 校区ID
     * @return 校区名称，如果不存在则返回null
     */
    @Override
    public String getCampusNameById(Long campusId) {
        if (campusCacheData == null){
            return null;
        }
        for (Map<Long, String> campusData : campusCacheData.values()) {
            if (campusData.containsKey(campusId)) {
                return campusData.get(campusId);
            }
        }
        return null;
    }

    /**
     * 根据校区ID获取校区名称
     * @param campusIds 校区ID
     * @return 校区名称，如果不存在则返回null
     */
    @Override
    public String getCampusNamesByIds(List<Long> campusIds) {
        if (campusCacheData == null || CollUtil.isEmpty(campusIds)){
            return null;
        }

        List<String> campusNames = campusIds.stream().map(this::getCampusNameById).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        return StrUtil.join(",", campusNames);
    }

    /**
     * 根据租户ID和校区ID获取校区名称
     * @param tenantId 租户ID
     * @return 校区名称，如果不存在则返回null
     */
    @Override
    public Map<Long, String> getCampusDataByTenantId(Long tenantId) {
        if (campusCacheData == null){
            return new HashMap<>();
        }
        return campusCacheData.get(tenantId);
    }

    @Override
    public Map<Long, String> getCampusMapByCampusIds(List<Long> campusIdList){
        if (CollUtil.isEmpty(campusIdList) || campusCacheData == null){
            return new HashMap<>();
        }

        Map<Long, String> campusMap = new HashMap<>();
        for (Long campusId : campusIdList) {
            String nameById = getCampusNameById(campusId);
            if (StrUtil.isNotBlank(nameById)){
                campusMap.put(campusId, nameById);
            }
        }
        return campusMap;
    }

}
