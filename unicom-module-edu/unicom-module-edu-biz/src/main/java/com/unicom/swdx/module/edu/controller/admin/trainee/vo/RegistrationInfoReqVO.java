package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页请求VO")
public class RegistrationInfoReqVO extends PageParam {

    @ApiModelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "学期")
    private String semester;

    @ApiModelProperty(value = "班级状态  1：待发布 2：已发布 3：报名中 4：报名结束  5：开班中 6：已结束")
    private Integer status;

    @ApiModelProperty(value = "id列表")
    private List<Long> idList;

    @ApiModelProperty(value = "班级id")
    private Long classId;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "排序字段")
    private Integer orderStatus;

    @ApiModelProperty(value = "导出字段")
    List<String> headers;

    @ApiModelProperty(value = "导出列索引")
    Set<Integer> includeColumnIndexes;

}
