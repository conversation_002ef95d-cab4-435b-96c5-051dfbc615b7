package com.unicom.swdx.module.edu.dal.mysql.electivetraineeselection;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.ElectiveReleaseCoursesSubRespVO;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.dto.ElectiveTraineeSelectedCoursesAndReleaseDTO;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo.AppElectiveTraineeSelectionReleaseReqVO;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo.AppElectiveTraineeSelectionSimpleRespVO;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo.ElectiveCourseTraineeSelectedPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo.ElectiveCourseTraineeSelectedRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.electivetraineeselection.ElectiveTraineeSelectionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 选修课学员选课 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ElectiveTraineeSelectionMapper extends BaseMapperX<ElectiveTraineeSelectionDO> {

    default int deleteByReleaseId(Long releaseId) {
        return delete(new LambdaQueryWrapperX<ElectiveTraineeSelectionDO>()
                .eq(ElectiveTraineeSelectionDO::getReleaseId, releaseId));
    }

    default int deleteBatchByReleaseId(List<Long> releaseIdList) {
        return delete(new LambdaQueryWrapperX<ElectiveTraineeSelectionDO>()
                .in(ElectiveTraineeSelectionDO::getReleaseId, releaseIdList));
    }

    List<ElectiveCourseTraineeSelectedRespVO> getCourseSelectedTraineePage(IPage<ElectiveCourseTraineeSelectedRespVO> page,
                                                                           @Param("reqVO") ElectiveCourseTraineeSelectedPageReqVO reqVO);

    /**
     * 获取选修课发布 选课学员信息
     * @param reqVO 选课学员信息
     * @return 选课学员信息
     */
    List<AppElectiveTraineeSelectionSimpleRespVO> selectSelectionTraineeInfoList(@Param("reqVO") AppElectiveTraineeSelectionReleaseReqVO reqVO);

    /**
     * 获取学员已选课程列表
     * @param traineeId 学员ID
     * @return 选修课课程列表
     */
    List<ElectiveTraineeSelectedCoursesAndReleaseDTO> getSelectedCoursesListByTraineeId(@Param("traineeId") Long traineeId);

    List<ElectiveTraineeSelectionDO> getSelectedTrainee(@Param("endTime") LocalDateTime time,@Param("now") LocalDateTime now);

    /**
     * 获取选修课发布课程列表
     * @param traineeId 学员id
     * @return 选修课发布课程列表
     */
    default List<ElectiveTraineeSelectionDO> getElectiveInfoByTraineeId(Long traineeId) {
        return selectList(new LambdaQueryWrapperX<ElectiveTraineeSelectionDO>()
                .eqIfPresent(ElectiveTraineeSelectionDO::getTraineeId, traineeId));
    }

    default List<ElectiveTraineeSelectionDO> getElectiveInfoByTraineeIds(List<Long> traineeIds) {
        return selectList(new LambdaQueryWrapperX<ElectiveTraineeSelectionDO>()
                .inIfPresent(ElectiveTraineeSelectionDO::getTraineeId, traineeIds));
    }
}
