package com.unicom.swdx.module.edu.controller.admin.xcxmsg;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigRespVO;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgConfigUpdateReqVO;
import com.unicom.swdx.module.edu.controller.admin.xcxmsg.vo.XcxMsgSendReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.xcxmsg.XcxMsgConfigDO;
import com.unicom.swdx.module.edu.service.xcxMsg.XcxMsgConfigService;
import com.unicom.swdx.module.edu.service.xcxMsg.XcxMsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "教务学员- 服务通知")
@RestController
@RequestMapping("/edu/xcxMsg")
@Validated
public class XcxMsgController {

    @Resource
    private XcxMsgConfigService xcxMsgConfigService;

    @Resource
    private XcxMsgService xcxMsgService;

    //服务通知列表
    @GetMapping("/configPage")
    @ApiOperation(value = "服务通知-----分页")
    @PreAuthorize("@ss.hasPermission('edu:xcxMsg:config')")
    public CommonResult<PageResult<XcxMsgConfigRespVO>> pageList(XcxMsgConfigPageReqVO reqVO) {
        return success(xcxMsgConfigService.pageList(reqVO));
    }

    @GetMapping("/selectSingle")
    @ApiOperation(value = "服务通知-----查询单个")
    @PreAuthorize("@ss.hasPermission('edu:xcxMsg:config')")
    public CommonResult<XcxMsgConfigDO> pageList(XcxMsgConfigUpdateReqVO reqVO) {
        return success(xcxMsgConfigService.getById(reqVO.getId()));
    }

    //编辑
    @PostMapping("/update")
    @ApiOperation(value = "服务通知-----编辑")
    @PreAuthorize("@ss.hasPermission('edu:xcxMsg:config')")
    public CommonResult<Boolean> update(@RequestBody XcxMsgConfigUpdateReqVO reqVO) {
        return success(xcxMsgConfigService.updateConfig(reqVO));
    }

    @PostMapping("/sendBatchXcxMsg")
    @ApiOperation(value = "服务通知-----批量发送")
    @PreAuthorize("@ss.hasPermission('edu:xcxMsg:send')")
    public CommonResult<Boolean> sendBatchXcxMsg(@RequestBody XcxMsgSendReqVO reqVO) {
        xcxMsgService.sendBatchXcxMsg(reqVO);
        return success(true);
    }

}
