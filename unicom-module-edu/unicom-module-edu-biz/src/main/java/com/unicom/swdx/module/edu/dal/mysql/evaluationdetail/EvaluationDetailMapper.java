package com.unicom.swdx.module.edu.dal.mysql.evaluationdetail;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationdetail.EvaluationDetailDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 评估详情 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EvaluationDetailMapper extends BaseMapperX<EvaluationDetailDO> {

    default PageResult<EvaluationDetailDO> selectPage(EvaluationDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EvaluationDetailDO>()
                .eqIfPresent(EvaluationDetailDO::getQuestionnaireId, reqVO.getQuestionnaireId())
                .eqIfPresent(EvaluationDetailDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(EvaluationDetailDO::getQuestionType, reqVO.getQuestionType())
                .eqIfPresent(EvaluationDetailDO::getStudentId, reqVO.getStudentId())
                .eqIfPresent(EvaluationDetailDO::getScore, reqVO.getScore())
                .eqIfPresent(EvaluationDetailDO::getOptionId, reqVO.getOptionId())
                .eqIfPresent(EvaluationDetailDO::getContent, reqVO.getContent())
                .eqIfPresent(EvaluationDetailDO::getClassCourseId, reqVO.getClassCourseId())
                .betweenIfPresent(EvaluationDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EvaluationDetailDO::getId));
    }

    void submitEvaluation(@Param("submitVO") QuestionAnswerSubmitVO submitVO, @Param("userId") Long loginUserId);

    void batchSubmitEvaluation(@Param("answers") List<QuestionAnswerSubmitVO> answers,
                               @Param("userId") Long userId);

    List<CommentRespVO> selectCommentPage(IPage<CommentRespVO> page, @Param("reqVO") CommentPageReqVO reqVO);

    default List<EvaluationDetailDO> selectRatedQuestionnaireDetail(Long questionnaireId, Long studentId, Long classCourseId) {
        return selectList(new LambdaQueryWrapperX<EvaluationDetailDO>()
                .eq(EvaluationDetailDO::getQuestionnaireId, questionnaireId)
                .eq(EvaluationDetailDO::getClassCourseId, classCourseId)
                .eq(EvaluationDetailDO::getStudentId, studentId));
    }

    default List<EvaluationDetailDO> selectLastEvaluationDetail(Long questionnaireId, Long studentId,
            Long classCourseId) {
        return selectList(new LambdaQueryWrapperX<EvaluationDetailDO>()
                .eq(EvaluationDetailDO::getQuestionnaireId, questionnaireId)
                .eq(EvaluationDetailDO::getClassCourseId, classCourseId)
                .eq(EvaluationDetailDO::getStudentId, studentId)
                .orderByDesc(EvaluationDetailDO::getUpdateTime));
    }

    void revoke(@Param("classCourseId") Long classCourseId, @Param("questionnaireId") Long questionnaireId, @Param("studentId") Long studentId);

    List<String> selectComment(@Param("classCourseId")Long classCourseId, @Param("studentId") Long studentId);

    List<EvaluationCommentRespVO> selectCommentByClassCourseIdList(@Param("classCourseIdList")List<Long> classCourseIdList);
}
