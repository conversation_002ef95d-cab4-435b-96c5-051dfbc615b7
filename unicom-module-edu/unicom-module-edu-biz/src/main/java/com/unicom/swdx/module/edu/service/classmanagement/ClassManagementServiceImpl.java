package com.unicom.swdx.module.edu.service.classmanagement;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.LoginUser;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo.ClassClockCalendarCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate.vo.ClassCompletionTemplateCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.*;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.controller.admin.notificationmessage.vo.NotificationMessageCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.signupunit.vo.SignUpUnitPageReqVO;
import com.unicom.swdx.module.edu.convert.classmanagement.ClassManagementConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassClockInDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;
import com.unicom.swdx.module.edu.dal.dataobject.plan.PlanDO;
import com.unicom.swdx.module.edu.dal.dataobject.ruletemplate.RuleTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcompletiontemplate.ClassCompletionTemplateMapper;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.dal.mysql.plan.PlanMapper;
import com.unicom.swdx.module.edu.dal.mysql.ruletemplate.RuleTemplateMapper;
import com.unicom.swdx.module.edu.dal.mysql.signupunit.SignUpUnitMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.enums.classmanagement.ClassManageDictTypeEnum;
import com.unicom.swdx.module.edu.enums.classmanagement.PublishEnum;
import com.unicom.swdx.module.edu.enums.plan.PlanStatusEnum;
import com.unicom.swdx.module.edu.enums.signunit.UnitStatusEnum;
import com.unicom.swdx.module.edu.enums.signunit.UnitTemplateEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.service.classclockcalendar.ClassClockCalendarService;
import com.unicom.swdx.module.edu.service.classcommen.CommenService;
import com.unicom.swdx.module.edu.service.classcompletiontemplate.ClassCompletionTemplateServiceImpl;

import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoServiceImpl;
import com.unicom.swdx.module.edu.service.notificationmessage.NotificationMessageService;
import com.unicom.swdx.module.edu.service.plan.PlanServiceImpl;
import com.unicom.swdx.module.edu.service.signupunit.SignUpUnitServiceImpl;
import com.unicom.swdx.module.edu.service.teacherinformation.TeacherInformationService;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.Objects;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;


/**
 * EduClassManagement Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ClassManagementServiceImpl extends ServiceImpl<ClassManagementMapper,ClassManagementDO> implements ClassManagementService {

    @Resource
    private CommenService commenService;

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;

    @Lazy
    @Resource
    private TraineeService traineeService;

    @Resource
    private SignUpUnitMapper signUpUnitMapper;

    @Autowired
    private ClassCourseMapper classCourseMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TeacherInformationService teacherInformationService;

    @Resource
    private RuleTemplateMapper ruleTemplateMapper;

    @Resource
    private PlanServiceImpl planService;

    @Resource
    SignUpUnitServiceImpl signUpUnitService;

    @Resource
    @Lazy
    private ClassClockCalendarService classClockCalendarService;

    @Resource
    private ClassCompletionTemplateServiceImpl classCompletionTemplateService;

    @Resource
    private ClassCompletionTemplateMapper classCompletionTemplateMapper;

    @Resource
    @Lazy
    private ClockInInfoMapper clockInInfoMapper;
    @Resource
    private NotificationMessageService notificationMessageService;

    @Resource
    private DictDataApi dictDataApi;

    // 无班级
    public static final long NONE_CLASS_ID = -1L;

    public static final String lockKey = "Lock:createClassManagement";

    @Resource
    private PlanMapper planMapper;

    @Lazy
    @Resource
    private ClockInInfoServiceImpl clockInInfoService;


//    @Override
//    public Integer createClassManagement(ClassManagementCreateReqVO createReqVO) {
//
//
//        String lockKey = "Lock:WxPayOutTradeNo";
//        RLock lock = redissonClient.getLock(lockKey);
//        try {
//
//            // 尝试获取锁，设置等待时间为0，表示不等待
//            Boolean isLocked = lock.tryLock(5, 10, TimeUnit.SECONDS);
//
//            if (isLocked) {
//                // 如果成功获取锁，执行业务逻辑
//                System.out.println("Lock acquired, executing business logic...");
//
//                //学期 代号
//                String semester = null;
//                // 顺序号
//                String serialNumber = null;
//                //获取数据库里面数据  获取顺序号
//                Integer serialNumberCount = classManagementMapper.selectClassCount(createReqVO.getYear()) + 1;
//
//                if(serialNumberCount < 10){
//                    serialNumber = "0" + serialNumberCount;
//                }else{
//                    serialNumber = serialNumberCount.toString();
//                }
//
//                if(createReqVO.getSemester() == 1){
//                    semester = "01";
//                }else {
//                    semester = "02";
//                }
//
//                //自动生成班次编码
//                String classCode = createReqVO.getYear() + semester + serialNumber;
//
//                createReqVO.setClassNameCode(classCode);
//
//
//                // 判断是否为主体班 为空或主体班 打开考勤
//                if(createReqVO.getClassAttribute() == null || createReqVO.getClassAttribute() == 805){
//                    createReqVO.setAttendanceCheck(0);
//                    createReqVO.setMealAttendance(0);
//                    createReqVO.setCheckIn(0);
//                }
//
//                //委托班 关闭考勤
//                if(createReqVO.getClassAttribute() != null && createReqVO.getClassAttribute() == 806){
//                    createReqVO.setAttendanceCheck(1);
//                    createReqVO.setMealAttendance(1);
//                    createReqVO.setCheckIn(1);
//                }
//
//
//                // 插入
//                ClassManagementDO classManagement = ClassManagementConvert.INSTANCE.convert(createReqVO);
//                classManagementMapper.insert(classManagement);
//
//                //中间表存入 班级id 与 考勤规则id
//
//                ClassClockInDO classClockInDO = new ClassClockInDO();
//                classClockInDO.setClassId(classManagement.getId());
//                //去规则表中拿默认规则的id
//                //目前没写  默认规则  暂留
//                classClockInDO.setAttendanceCheck(0L);
//                classClockInDO.setMealAttendance(0L);
//                classClockInDO.setCheckIn(0L);
//
//                //插入到中间表中 规则id
//                classManagementMapper.insertClassClockIn(classClockInDO);
//
//                //遍历单位表 插入
//                List<SignUpUnitDO> signUpUnitDOList = signUpUnitMapper.getSignUpUnitDOList();
//                for(SignUpUnitDO list : signUpUnitDOList){
//                    SignUpUnitDO insertData = new SignUpUnitDO();
//                    insertData.setUnitName(list.getUnitName());
//                    insertData.setUnitClassification(list.getUnitClassification());
//                    insertData.setUnitChargePeople(list.getUnitChargePeople());
//                    insertData.setPhone(list.getPhone());
//                    insertData.setStatus(list.getStatus());
//                    insertData.setSort(list.getSort());
//                    insertData.setCapacity(list.getCapacity());
//                    insertData.setIsRestrict(list.getIsRestrict());
//
//                    //班级id
//                    insertData.setClassId(classManagement.getId());
//
//                    signUpUnitMapper.insert(insertData);
//
//
//                    // 返回
//                    return classManagement.getId();
//
//                }
//
//
//                // ... 执行其他业务逻辑
//            } else {
//                // 如果没有获取到锁，则直接返回失败信息
//                System.out.println("Failed to acquire lock, lock is already held by another process.");
//
//                return null;
//            }
//
//
//        } catch (Exception e){
//            log.info(e.getMessage());
//        } finally {
//            // 释放锁
//            lock.unlock();
//        }
//
//
//
//
//    }

    @Override
    public Long createClassManagement(ClassManagementCreateReqVO createReqVO) {

        LambdaQueryWrapperX<ClassManagementDO> lqw=new LambdaQueryWrapperX<>();
        lqw.eqIfPresent(ClassManagementDO::getClassName,createReqVO.getClassName());
        lqw.last("limit 1");

        ClassManagementDO classManagementDO = classManagementMapper.selectOne(lqw);

        if(classManagementDO!=null){
            throw exception(CLASS_NAME_REPEAT);
        }

       // 获取锁
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，设置等待时间为0，表示不等待
            if (lock.tryLock(5, 10, TimeUnit.SECONDS)) {
                // 如果成功获取锁，执行业务逻辑
                System.out.println("Lock acquired, executing business logic...");

                // 学期代号
                String semester = (createReqVO.getSemester() == 1) ? "01" : "02";

                // 获取数据库里面数据 获取顺序号
                Integer serialNumberCount = classManagementMapper.selectClassCount(createReqVO.getYear()) + 1;
                String serialNumber = (serialNumberCount < 10) ? "0" + serialNumberCount : serialNumberCount.toString();

                // 自动生成班次编码
                String classCode = createReqVO.getYear() + semester + serialNumber;
                createReqVO.setClassNameCode(classCode);

                // 判断是否为主体班 为空或主体班 打开考勤
                //正式环境、测试环境字典值不同，主体班：正式 1451 ， 测试 805      委托班：正式 1452 ， 测试 806
                if (createReqVO.getClassAttribute() == null || createReqVO.getClassAttribute() == 1451) {
                    createReqVO.setAttendanceCheck(0);
                    createReqVO.setMealAttendance(0);
                    createReqVO.setCheckIn(0);
                } else if (createReqVO.getClassAttribute() != null && createReqVO.getClassAttribute() == 1452) {
                    // 委托班 关闭考勤
                    createReqVO.setAttendanceCheck(1);
                    createReqVO.setMealAttendance(1);
                    createReqVO.setCheckIn(1);
                }

                // 定义正则表达式
//                String regex = "^(\\d{4})年(春|秋)季学期.*";

                // 创建匹配器
//                Matcher matcher = pattern.matcher(createReqVO.getClassName());

//                if (!matcher.matches()){
//                    throw exception(CLASS_NAME_NOT_MATCHES);
//                }

                // 插入
                ClassManagementDO classManagement = ClassManagementConvert.INSTANCE.convert(createReqVO);



                classManagementMapper.insert(classManagement);
                checkAndAddTeacherRole(classManagement);
                // 中间表存入 班级id 与 考勤规则id
                ClassClockInDO classClockInDO = new ClassClockInDO();
                classClockInDO.setClassId(classManagement.getId());

                // 去规则表中拿默认规则的id
                //拿默认规则  如果没有默认规则 就不插入
                List<RuleTemplateDO> ruleList = ruleTemplateMapper.getClassDefaultRule(createReqVO.getCampus());
                for (RuleTemplateDO list : ruleList){
                    if(list.getId() != null){
                        //到课
                        if(list.getRuleType() == 0){
                            //该校区 到课考勤默认规则
                            classClockInDO.setAttendanceCheck(list.getId());
                        }

                        //就餐
                        if(list.getRuleType() == 1){
                            //该校区 就餐考勤默认规则
                            classClockInDO.setMealAttendance(list.getId());
                        }

                        //住宿
                        if(list.getRuleType() == 2){
                            //该校区 住宿考勤默认规则
                            classClockInDO.setCheckIn(list.getId());
                        }

                    }
                }


                // 插入到中间表中 规则id
                classManagementMapper.insertClassClockIn(classClockInDO);

                // 遍历单位表 插入  暂留  单位表不在班级新增时插入
//                List<SignUpUnitDO> signUpUnitDOList = signUpUnitMapper.getSignUpUnitDOList();
//                for (SignUpUnitDO list : signUpUnitDOList) {
//                    SignUpUnitDO insertData = new SignUpUnitDO();
//                    insertData.setUnitName(list.getUnitName());
//                    insertData.setUnitClassification(list.getUnitClassification());
//                    insertData.setUnitChargePeople(list.getUnitChargePeople());
//                    insertData.setPhone(list.getPhone());
//                    insertData.setStatus(list.getStatus());
//                    insertData.setSort(list.getSort());
//                    insertData.setCapacity(list.getCapacity());
//                    insertData.setIsRestrict(list.getIsRestrict());
//
//                    // 班级id
//                    insertData.setClassId(classManagement.getId());
//
//                    signUpUnitMapper.insert(insertData);
//                }

                //调用班级考勤日历接口 为该班级创建班级日历
                ClassClockCalendarCreateReqVO classClockCalendarCreateReqVO = new ClassClockCalendarCreateReqVO();
                classClockCalendarCreateReqVO.setClassId(Long.valueOf(classManagement.getId()));
                classClockCalendarService.createClassClockCalendar(classClockCalendarCreateReqVO);



                // 返回
                return classManagement.getId();

            } else {
                // 如果没有获取到锁，则直接返回失败信息
                System.out.println("Failed to acquire lock, lock is already held by another process.");

                //错误提示
                throw exception(CLASS_MANAGEMENT_CREATE_ERROR);
            }

        }
        catch (Exception e) {
            log.error("Error occurred: ", e);

            return null;
        }
        finally {
            // 释放锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 业中教职工分配学员和小程序入口
     * @param classManagementDO 此次班级操作
     */
    private void checkAndAddTeacherRole(ClassManagementDO classManagementDO){
        List<Long> teacherIdList = getTeacherIdList(classManagementDO);
        if (!teacherIdList.isEmpty()){
            List<Long> distinctTeacherIdList = teacherIdList.stream().distinct().collect(Collectors.toList());
            List<TeacherInformationDO> teacherInformationDOS = teacherInformationMapper.selectBatchIds(distinctTeacherIdList);
            List<Long> userIds = teacherInformationDOS.stream().map(TeacherInformationDO::getSystemId).filter(Objects::nonNull).collect(Collectors.toList());
            commenService.addTeacherRole(userIds);
        }
    }
    private List<Long> getTeacherIdList(ClassManagementDO classManagementDO) {
        List<Long> teacherIdList = new ArrayList<>();
        if (Objects.nonNull(classManagementDO.getClassTeacherLead()) && classManagementDO.getClassTeacherLead() != 0){
            teacherIdList.add(classManagementDO.getClassTeacherLead());
        }
        if (StringUtils.isNotBlank(classManagementDO.getCoachTeacher())){
            teacherIdList.addAll(Arrays.stream(classManagementDO.getCoachTeacher().split(",")).map(Long::valueOf).collect(Collectors.toList()));
        }
        return teacherIdList;
    }


    @Override
    public void updateClassManagement(ClassManagementUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateClassManagementExists(updateReqVO.getId());

        // 更新
        ClassManagementDO updateObj = ClassManagementConvert.INSTANCE.convert(updateReqVO);
        checkAndAddTeacherRole(updateObj);
        classManagementMapper.updateById(updateObj);
    }

    @Override
    public void deleteClassManagement(Long id) {
        // 校验存在
        this.validateClassManagementExists(id);

        //删除对应班级的 规则
        classManagementMapper.deleteClassClockById(id);

        //删除对应班级的结业考核模版
        classCompletionTemplateMapper.deletedByClassId(Long.valueOf(id));

        // 删除
        classManagementMapper.deleteById(id);

        //删除单位表中的班级对应的单位
        try {
            signUpUnitMapper.deleteByClassId(id);
        } catch (Exception e) {
            // 捕获并处理异常
            throw exception(CLASS_MANAGEMENT_SIGN_UP_UNIT);
        }

        //删除对应班级的考勤日历
        classClockCalendarService.deleteClassClockCalendar(id);


        //删除对应班级的学员
        Long classId = Long.valueOf(id);
        List<Long> classIds = Collections.singletonList(classId);
        traineeService.unshackleTrainee(classIds);

        //删除对应班级的教学计划、课程表、签到表
        LambdaQueryWrapper<PlanDO> planWrapper = new LambdaQueryWrapper<>();
        planWrapper.eq(PlanDO::getClassId, classId);
        planMapper.delete(planWrapper);

        LambdaQueryWrapper<ClassCourseDO> classCourseWrapper = new LambdaQueryWrapper<>();
        classCourseWrapper.eq(ClassCourseDO::getClassId, classId);
        classCourseMapper.delete(classCourseWrapper);

        LambdaQueryWrapper<ClockInInfoDO> clockInInfoWrapper = new LambdaQueryWrapper<>();
        clockInInfoWrapper.eq(ClockInInfoDO::getClassId, classId);
        clockInInfoMapper.delete(clockInInfoWrapper);


    }

    /**
     * 批量删除EduClassManagement
     *
     * @param classManagerDeleteVO
     */
    @Override
    public void deleteClassManagerDeleteBatch(ClassManagerDeleteVO classManagerDeleteVO) {
        String ids = classManagerDeleteVO.getIds();

        // 将 IDs 字符串按逗号分割成数组
        String[] idArray = ids.split(",");

        for (String idStr : idArray){

            //转化成 int 类型
            Long id = Long.parseLong(idStr);

            // 校验存在
            this.validateClassManagementExists(id);

            //删除对应班级的 规则
            classManagementMapper.deleteClassClockById(id);

            // 删除
            classManagementMapper.deleteById(id);

            //删除单位表中的班级对应的单位
            try {
                signUpUnitMapper.deleteByClassId(id);
            } catch (Exception e) {
                // 捕获并处理异常
                throw exception(CLASS_MANAGEMENT_SIGN_UP_UNIT);
            }

            //删除对应班级的考勤日历
            classClockCalendarService.deleteClassClockCalendar(id);
        }
        //删除对应班级的学员
        List<Long> classIds = Arrays.stream(idArray).map(Long::parseLong).collect(Collectors.toList());
        traineeService.unshackleTrainee(classIds);

        //删除对应班级的教学计划、课程表、签到表
        LambdaQueryWrapper<PlanDO> planWrapper = new LambdaQueryWrapper<>();
        planWrapper.in(PlanDO::getClassId, classIds);
        planMapper.delete(planWrapper);

        LambdaQueryWrapper<ClassCourseDO> classCourseWrapper = new LambdaQueryWrapper<>();
        classCourseWrapper.in(ClassCourseDO::getClassId, classIds);
        classCourseMapper.delete(classCourseWrapper);

        LambdaQueryWrapper<ClockInInfoDO> clockInInfoWrapper = new LambdaQueryWrapper<>();
        clockInInfoWrapper.in(ClockInInfoDO::getClassId, classIds);
        clockInInfoMapper.delete(clockInInfoWrapper);

    }

    private void validateClassManagementExists(Long id) {
        if (classManagementMapper.selectById(id) == null) {
            throw exception(CLASS_MANAGEMENT_NOT_EXISTS);
        }
    }

    @Override
    public ClassManagementDO getClassManagement(Long id) {

        ClassManagementDO list =  classManagementMapper.selectById(id);

        if(list == null){
            throw exception(CLASS_MANAGEMENT_NOT_EXISTS);
        }

        //返回考勤状态
        if(list.getAttendanceCheck() != null && list.getMealAttendance() != null && list.getCheckIn() != null){
            if(list.getAttendanceCheck() == 1 && list.getMealAttendance() == 1 && list.getCheckIn() == 1){
                list.setAttendanceStatus(1);
            }else{
                list.setAttendanceStatus(0);
            }
        }

        // 返回考勤规则id
        ClassRuleClockingInVO attendanceCheckVO =  classManagementMapper.getClassClockInByAttendanceCheck(id);

        ClassRuleClockingInVO mealAttendanceVO =  classManagementMapper.getClassClockInByMealAttendance(id);

        ClassRuleClockingInVO checkInVO =  classManagementMapper.getClassClockInByCheckIn(id);

        if(attendanceCheckVO != null){
            if(attendanceCheckVO.getAttendanceCheck() != null){
                list.setAttendanceCheckId(attendanceCheckVO.getAttendanceCheck());
            }
        }

        if(mealAttendanceVO != null){
            if(mealAttendanceVO.getMealAttendance() != null){
                list.setMealAttendanceId(mealAttendanceVO.getMealAttendance());
            }
        }

        if(checkInVO != null){
            if(checkInVO.getCheckIn()!= null){
                list.setCheckInId(checkInVO.getCheckIn());
            }
        }

        // 返回班主任老师  辅导老师

        //班主任可能为空
        String classTeacherLead = null;
        if(list.getClassTeacherLead() != null){
            classTeacherLead = classManagementMapper.getClassTeacherLeadInfo(list.getClassTeacherLead());
        }
        list.setClassTeacherLeadName(classTeacherLead);


        //辅导老师
        String coachTeacher = null;
        if (StringUtils.isNotBlank(list.getCoachTeacher())) {
            StringBuilder classCoachTeacherBuilder = new StringBuilder();

            // 使用逗号分割字符串
            String[] teacherIds = list.getCoachTeacher().split(",");

            // 遍历所有教师ID，查询信息并拼接
            for (String teacherId : teacherIds) {
                // 去掉可能的空格
                teacherId = teacherId.trim();


                // 获取教师信息
                String teacherInfo = classManagementMapper.getClassTeacherLeadInfo(Long.parseLong(teacherId));

                // 如果教师信息不为空，拼接到结果中
                if (teacherInfo != null) {
                    if (classCoachTeacherBuilder.length() > 0) {
                        classCoachTeacherBuilder.append(", ");
                    }
                    classCoachTeacherBuilder.append(teacherInfo);
                }
            }

            // 转换为最终字符串
            coachTeacher = classCoachTeacherBuilder.toString();
        }
        list.setCoachTeacherName(coachTeacher);



        return list;
    }

    @Override
    public List<ClassManagementDO> getClassManagementList(Collection<Integer> ids) {
        return classManagementMapper.selectBatchIds(ids);
    }

    /**
     * 业中首页-仪表盘-获取班级下钻列表
     *
     * @param reqVO 指定租户id
     * @return 班级列表
     */
    @Override
    public List<ClassManagementSimpleForBusinessCenterRespVO> simpleListForBusinessCenter(ClassManagementSimpleForBusinessCenterReqVO reqVO) {
        // 设置默认时间范围，如果年份不为空则覆盖为该年的全年
        if (Objects.nonNull(reqVO.getYear())) {
            reqVO.setStartTime(LocalDate.of(reqVO.getYear(), Month.JANUARY, 1));
            reqVO.setEndTime(LocalDate.of(reqVO.getYear(), Month.DECEMBER, 31));
        }
        return classManagementMapper.simpleListForBusinessCenter(reqVO);
    }

    @Override
    public List<ClassManagementDO> getClassByUnitId(Integer unitId) {

        //根据父单位id获取单位id
        List<SignUpUnitDO> unitList = signUpUnitMapper.selectByParentId(Collections.singletonList(unitId));
        List<Long> classIds = unitList.stream().map(SignUpUnitDO::getClassId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(classIds)){
            return Collections.emptyList();
        }
        return classManagementMapper.selectBatchIds(classIds);
    }

    @Override
    public ClassManagementImportRespVO unitImportClassInfo(List<UnitImportExcelVO> list, Long classId) {
        if (CollUtil.isEmpty(list)) {
            return ClassManagementImportRespVO.builder()
                    .count(0)
                    .tag(3)
                    .errorMessages(Collections.singletonList("导入数据为空"))
                    .build();
        }
        int num;
        if ("示例：测试单位名称（勿删）".equals(list.get(0).getUnitName())) {
            list.remove(0);
            num = 3;
        } else {
            num = 2;
        }

        // 用于存储校验通过和校验失败的数据
        List<UnitImportExcelVO> rightData = new ArrayList<>();

        List<String> errorMessages = new ArrayList<>();
        // 校验导入字段是否合规，返回正确的数据
        List<UnitImportExcelVO> checkedList = checkList(list,errorMessages,num);

        //如果checkedList为空，则直接返回。

        List<String> importNameList = checkedList.stream()
                .map(UnitImportExcelVO::getUnitName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        SignUpUnitPageReqVO reqVO = new SignUpUnitPageReqVO();
        reqVO.setClassId(classId);
        List<SignUpUnitDO> existUnitList = signUpUnitMapper.selectUnitList(reqVO);
        List<SignUpUnitDO> allExistTemplateUnitList = signUpUnitMapper.selectAllTemplateByClassId();

        Map<String, SignUpUnitDO> existUnitMap = existUnitList.stream()
                .collect(Collectors.toMap(
                        unit -> unit.getUnitName().trim(),  // 去除首尾空白（包括 \n）
                        Function.identity(),
                        (a, b) -> a));  // 冲突时保留旧值

        Map<String, SignUpUnitDO> templateMap = allExistTemplateUnitList.stream()
                .collect(Collectors.toMap(
                        unit -> unit.getUnitName().trim(),  // 去除首尾空白（包括 \n）
                        Function.identity(),
                        (a, b) -> a));

        List<SignUpUnitDO> updateList = new ArrayList<>();
        List<SignUpUnitDO> deleteList = new ArrayList<>();
        List<SignUpUnitDO> insertList = new ArrayList<>();


        List<String> containList = new ArrayList<>();

        for (UnitImportExcelVO vo : checkedList) {

            Boolean flag = true;
            if (StringUtils.isBlank(vo.getUnitName())){
                flag = false;
                errorMessages.add("第 " + (num) + " 行: 单位名称不能为空");
            }

            if (StringUtils.isNotBlank(vo.getNumber())) {

                try {
                    int number = Integer.parseInt(vo.getNumber());
                    if (number <= 0) {
                        flag = false;
                        errorMessages.add("第 " + (num) + " 行: 名额数（人）需填写正整数");
                    }
                } catch (NumberFormatException e) {
                    flag = false;
                    errorMessages.add("第 " + (num) + " 行: 名额数（人）需填写正整数");
                }

            }else {
                flag = false;
                errorMessages.add("第 " + (num) + " 行: 单位名额数不能为空");
            }

            String unitName = vo.getUnitName();


            if (existUnitMap.containsKey(unitName)) {
                SignUpUnitDO existUnit = existUnitMap.get(unitName);
                existUnit.setCapacity(Integer.parseInt(vo.getNumber()));
                existUnit.setIsRestrict(0);
                if (flag){
                    updateList.add(existUnit);
                }
            } else if (templateMap.containsKey(unitName)) {
                SignUpUnitDO template = templateMap.get(unitName);
                SignUpUnitDO newUnit = new SignUpUnitDO();
                BeanUtil.copyProperties(template, newUnit);
                newUnit.setId(null);
                newUnit.setClassId(classId);
                newUnit.setTemplate(0);
                newUnit.setUsername(null);
                newUnit.setCapacity(Integer.parseInt(vo.getNumber()));
                newUnit.setParentId(Long.valueOf(template.getId()));
                newUnit.setIsRestrict(0);
                if (!containList.contains(unitName) && flag){
                    insertList.add(newUnit);
                }

            } else {
                errorMessages.add("第 " + num + " 行: 在系统中未找到该调训单位");
            }
            num++;
            containList.add(unitName);
        }

        for (String existUnitName : existUnitMap.keySet()) {
            if (!importNameList.contains(existUnitName)) {
                deleteList.add(existUnitMap.get(existUnitName));
            }
        }

        if (CollUtil.isNotEmpty(updateList)) {
            signUpUnitMapper.updateBatch(updateList);
        }

        if (CollUtil.isNotEmpty(insertList)) {
            signUpUnitMapper.insertBatch(insertList);
        }

        if (CollUtil.isNotEmpty(deleteList)) {
            List<Integer> deleteUnitIds = deleteList.stream().map(SignUpUnitDO::getId).collect(Collectors.toList());
            signUpUnitMapper.deleteBatchIds(deleteUnitIds);

            try {
                traineeService.deleteByUnitIds(deleteUnitIds); // 实际实现请替换
            } catch (Exception e) {
                log.info("名额分配学员删除失败：{}",e.getMessage());
            }
        }

        // 处理 tag 返回值
        int successCount = updateList.size() + insertList.size();
        int totalCount = list.size();

        int tag;
        if (successCount == 0) {
            tag = 3; // 全部失败
        } else if (successCount < totalCount) {
            tag = 2; // 部分成功
        } else {
            tag = 1; // 全部成功
        }

        return ClassManagementImportRespVO.builder()
                .count(successCount)
                .tag(tag)
                .errorMessages(errorMessages)
                .build();
    }

    @Override
    public List<String> checkUnitImportClassInfo(List<UnitImportExcelVO> list, Long classId) {
        
        //找出所有需要删除的单位，然后将删除提示放入list
        SignUpUnitPageReqVO reqVO = new SignUpUnitPageReqVO();
        reqVO.setClassId(classId);
        List<SignUpUnitDO> existUnitList = signUpUnitMapper.selectUnitList(reqVO);

        Map<String, SignUpUnitDO> existUnitMap = existUnitList.stream()
                .collect(Collectors.toMap(
                        unit -> unit.getUnitName().trim(),  // 去除首尾空白（包括 \n）
                        Function.identity(),
                        (a, b) -> a));  // 冲突时保留旧值

        List<SignUpUnitDO> deleteList = new ArrayList<>();

        List<String> importNameList = list.stream()
                .map(UnitImportExcelVO::getUnitName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        for (String existUnitName : existUnitMap.keySet()) {
            if (!importNameList.contains(existUnitName)) {
                deleteList.add(existUnitMap.get(existUnitName));
            }
        }
        
        return deleteList.stream().map(SignUpUnitDO::getUnitName).collect(Collectors.toList());
    }


    private List<UnitImportExcelVO> checkList(List<UnitImportExcelVO> list,List<String> errorMessages,Integer num) {



        // 用于存储校验通过和校验失败的数据
        List<UnitImportExcelVO> rightData = new ArrayList<>();

//        for (int i = 0; i < list.size(); i++) {
//            UnitImportExcelVO unitImportExcelVO = list.get(i);
//
//            if (StringUtils.isBlank(unitImportExcelVO.getUnitName())){
//                errorMessages.add("第 " + (i + num) + " 行: 单位名称不能为空");
//            }
//
//
//            if (StringUtils.isNotBlank(unitImportExcelVO.getNumber())) {
//
//                try {
//                    int number = Integer.parseInt(unitImportExcelVO.getNumber());
//                    if (number <= 0) {
//                        errorMessages.add("第 " + (i + num) + " 行: 名额数（人）需填写正整数");
//                    }
//                } catch (NumberFormatException e) {
//                    errorMessages.add("第 " + (i + num) + " 行: 名额数（人）需填写正整数");
//                }
//
//            }else {
//                errorMessages.add("第 " + (i + num) + " 行: 单位名额数不能为空");
//            }
//
//            if (CollUtil.isEmpty(errorMessages)){
//                rightData.add(unitImportExcelVO);
//            }
//
//        }

        return list;
    }

    @Override
    public PageResult<ClassManagementRespVO> getClassManagementPage(HttpServletRequest request, ClassManagementPageReqVO pageReqVO) {

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(pageReqVO.getClassName())){
            pageReqVO.setClassName(pageReqVO.getClassName().replaceAll("([%_])", "\\\\$1"));
        }

        Page buildPage = MyBatisUtils.buildPage(pageReqVO);


        //判断该账户是否是学员或者管理员
        Long userIds = SecurityFrameworkUtils.getLoginUserId();

        if(!traineeService.selectTraineeByUserId(request,userIds)){
            throw exception(NO_PERMISSION_ERROR);
        }


        //结业管理模块
        if(StringUtils.isNotBlank(pageReqVO.getComplete())){

            if(pageReqVO.getComplete().equals("1")){
                //获取登录用户信息 获取user_id
                Long userId = SecurityFrameworkUtils.getLoginUserId();


                Long teacherId = classManagementMapper.getTeacherId(userId);

                pageReqVO.setClassTeacherLead(teacherId);

            }else {
                pageReqVO.setClassTeacherLead(null);
            }
        }


        //是否班主任管理班级管理，传1-只看当前班主任管理相的班级
        if(StringUtils.isNotBlank(pageReqVO.getIsClassTeacherHead())){

            //班主任传1，管理员传2
            if(pageReqVO.getIsClassTeacherHead().equals("1")){
                //获取登录用户信息 获取user_id
                Long userId = SecurityFrameworkUtils.getLoginUserId();
                Long teacherId = null;
                try{
                    //获取对应师资表中的 id
                    teacherId = classManagementMapper.getTeacherId(userId);
                    //测试
                }catch (Exception e){
                    throw exception(CLASS_MANAGEMENT_TEACHER_ID_NOT_EXIST);
                }

                pageReqVO.setClassTeacherLead(teacherId);

            }

        }

        List<ClassManagementDO> classManagementDOList = classManagementMapper.selectPageList(buildPage, pageReqVO);

        //返回班级状态
        for(ClassManagementDO list : classManagementDOList){


            if(list.getPublish() == 2){

                list.setClassStatus("待发布");

            }else if(list.getPublish() == 1){

                list.setClassStatus("已发布");

                LocalDateTime currentTime = LocalDateTime.now();

//                if (currentTime.isAfter(list.getRegistrationStartTime()) && currentTime.isBefore(list.getRegistrationEndTime())) {
//                    list.setClassStatus("报名中");
//                }
//
//                else if (currentTime.isAfter(list.getRegistrationEndTime()) && currentTime.isBefore(list.getClassOpenTime())) {
//                    list.setClassStatus("报名结束");
//                }
//
//                else if (currentTime.isAfter(list.getClassOpenTime()) && currentTime.isBefore(list.getCompletionTime())) {
//                    list.setClassStatus("开班中");
//                }
//
//                else if (currentTime.isAfter(list.getClassOpenTime())) {
//                    list.setClassStatus("已结束");
//                }
                if (currentTime.isAfter(list.getCompletionTime())) {
                    list.setClassStatus("已结束");
                } else if (currentTime.isAfter(list.getClassOpenTime())) {
                    list.setClassStatus("开班中");
                } else if (currentTime.isAfter(list.getRegistrationEndTime())) {
                    list.setClassStatus("报名结束");
                } else if (currentTime.isAfter(list.getRegistrationStartTime())) {
                    list.setClassStatus("报名中");
                }


            }

            //添加学员的人数
            Integer count =classManagementMapper.getClassPeopleCount(list.getId());
            list.setClassPeopleCount(count);


            //班主任可能为空
            String classTeacherLead = null;
            if(list.getClassTeacherLead() != null){
                classTeacherLead = classManagementMapper.getClassTeacherLeadInfo(list.getClassTeacherLead());
            }
            list.setClassTeacherLeadName(classTeacherLead);


            //辅导老师
            String coachTeacher = null;
            if (StringUtils.isNotBlank(list.getCoachTeacher())) {
                StringBuilder classCoachTeacherBuilder = new StringBuilder();

                // 使用逗号分割字符串
                String[] teacherIds = list.getCoachTeacher().split(",");

                // 遍历所有教师ID，查询信息并拼接
                for (String teacherId : teacherIds) {
                    // 去掉可能的空格
                    teacherId = teacherId.trim();


                    // 获取教师信息
                    String teacherInfo = classManagementMapper.getClassTeacherLeadInfo(Long.parseLong(teacherId));

                    // 如果教师信息不为空，拼接到结果中
                    if (teacherInfo != null) {
                        if (classCoachTeacherBuilder.length() > 0) {
                            classCoachTeacherBuilder.append(", ");
                        }
                        classCoachTeacherBuilder.append(teacherInfo);
                    }
                }

                // 转换为最终字符串
                coachTeacher = classCoachTeacherBuilder.toString();
            }
            list.setCoachTeacherName(coachTeacher);


            //返回考勤状态
            if(list.getAttendanceCheck() != null && list.getMealAttendance() != null && list.getCheckIn() != null){
                if(list.getAttendanceCheck() == 1 && list.getMealAttendance() == 1 && list.getCheckIn() == 1){
                    list.setAttendanceStatus(1);
                }else{
                    list.setAttendanceStatus(0);
                }
            }




        }

        PageResult<ClassManagementDO> pageList = new PageResult<>(classManagementDOList, buildPage.getTotal());

        PageResult<ClassManagementRespVO> result = ClassManagementConvert.INSTANCE.convertPage(pageList);


        handleDictValue(result.getList());

        return result;
    }

    /**
     * 选修课发布创建-根据上课时间段选择班级范围分页列表
     *
     * @param reqVO 分页请求参数
     * @return 分页列表
     */
    @Override
    public PageResult<ClassManagementElectiveReleaseRespVO> getPageForElectiveReleaseCreate(ClassManagementElectiveReleasePageReqVO reqVO) {
        Page<ClassManagementElectiveReleaseRespVO> buildPage = MyBatisUtils.buildPage(reqVO);
        List<ClassManagementElectiveReleaseRespVO> list = classManagementMapper.getPageForElectiveReleaseCreate(buildPage, reqVO);
        return new PageResult<>(list, buildPage.getTotal());
    }

    /**
     * 更新排序
     *
     * @param id
     * @return
     */
    public Integer updateClassManagementSort(Long id, Integer sort) {

        return classManagementMapper.updateClassManagementSortById(id, sort);

    }

    /**
     * 批量发布EduClassManagement
     *
     * @param classManagerDeleteVO
     */
    @Override
    public void updateClassManagementPublish(ClassManagerDeleteVO classManagerDeleteVO) {
        String ids = classManagerDeleteVO.getIds();

        //批量发布
        if(classManagerDeleteVO.getIsPublish() == 1){
            // 将 IDs 字符串按逗号分割成数组
            String[] idArray = ids.split(",");

            for (String idStr : idArray){

                //转化成 int 类型
                Long id = Long.parseLong(idStr);

                // 校验存在
                this.validateClassManagementExists(id);

                ClassManagementDO classManagementDO = classManagementMapper.selectById(id);

                //校验发布时间要早于报名时间 校验
//                if (LocalDateTime.now().isAfter(classManagementDO.getRegistrationStartTime())) {
//                    throw exception(CLASS_MANAGEMENT_TIME_ERROR);
//                }

                // 发布
                classManagementMapper.updateClassManagementPublishById(id);

                // 站内信生成
                SignUpUnitPageReqVO sign = new SignUpUnitPageReqVO();
                sign.setClassId(id);
                List<SignUpUnitDO> signUpUnits = signUpUnitMapper.selectUnitList(sign);
                for(SignUpUnitDO unit : signUpUnits){
                    NotificationMessageCreateReqVO reqVO = new NotificationMessageCreateReqVO();
                    reqVO.setTitle(classManagementDO.getClassName()+"已发布，请及时为相关人员报名！");
                    reqVO.setContent("尊敬的"+unit.getUnitName()+"单位管理员您好，培训班"+classManagementDO.getClassName()+"已为贵单位分配"+unit.getCapacity()+"个名额，请尽快完成学员报名。");
                    reqVO.setIsTop(0);
                    List<Integer> units = new ArrayList<>();
                    units.add(unit.getParentId().intValue());
                    reqVO.setUnits(units);
                    reqVO.setIsPublish(1);
                    reqVO.setStatus(1);
                    reqVO.setPublishTime(LocalDateTime.now());
                    reqVO.setPublisher("系统");
                    notificationMessageService.createNotificationMessage(reqVO);
                }

            }
        }

        //批量撤销发布

        if(classManagerDeleteVO.getIsPublish() == 2){
            // 将 IDs 字符串按逗号分割成数组
            String[] idArray = ids.split(",");

            for (String idStr : idArray){

                //转化成 int 类型
                Long id = Long.parseLong(idStr);

                // 校验存在
                this.validateClassManagementExists(id);
                // 发布
                classManagementMapper.updateClassManagementCancelPublishById(id);

            }
            //解绑绑定的学员
            List<Long> classIds = Arrays.stream(idArray).map(Long::parseLong).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(classIds)){
                traineeService.unshackleTrainee(classIds);
            }
        }


    }

    /**
     * 导出
     *
     * @param reqVO
     * @return
     */
    @Override
    public List<ClassManagementExcelVO> getClassManagementInfoList(ClassManagementExportParamsVO reqVO) {

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(reqVO.getClassName())){
            reqVO.setClassName(reqVO.getClassName().replaceAll("([%_])", "\\\\$1"));
        }


        List<ClassManagementExcelVO> excelVOList = new ArrayList<>();


        //结业管理模块
        if(StringUtils.isNotBlank(reqVO.getComplete())){

            if(reqVO.getComplete().equals("1")){
                //获取登录用户信息 获取user_id
                Long userId = SecurityFrameworkUtils.getLoginUserId();


                //特殊处理
                if(userId == 1 || userId == 26){

                    reqVO.setClassTeacherLead(null);

                }else{
                    Long teacherId = null;
                    try{
                        //获取对应师资表中的 id
                        teacherId = classManagementMapper.getTeacherId(userId);
                        //测试
                        //teacherId = 12L;
                    }catch (Exception e){
                        throw exception(CLASS_MANAGEMENT_TEACHER_ID_NOT_EXIST);
                    }


                    if(teacherId == null){
                        reqVO.setClassTeacherLead(null);
                    }else {
                        reqVO.setClassTeacherLead(teacherId);
                    }
                }

            }
        }


        //是否班主任管理班级管理，传1-只看当前班主任管理相的班级
        if(StringUtils.isNotBlank(reqVO.getIsClassTeacherHead())){

            if(reqVO.getIsClassTeacherHead().equals("1")){
                //获取登录用户信息 获取user_id
                Long userId = SecurityFrameworkUtils.getLoginUserId();
                Long teacherId = null;
                try{
                    //获取对应师资表中的 id
                    teacherId = classManagementMapper.getTeacherId(userId);
                    //测试
                    //teacherId = 12L;
                }catch (Exception e){
                    throw exception(CLASS_MANAGEMENT_TEACHER_ID_NOT_EXIST);
                }

                reqVO.setClassTeacherLead(teacherId);

            }

        }

        List<ClassManagementDO> listData =  classManagementMapper.getClassManagementInfo(reqVO);

        for(ClassManagementDO data: listData){

            ClassManagementExcelVO excelVO = new ClassManagementExcelVO();

            // 将 ClassManagementDO 的属性映射到 ClassManagementExcelVO 中
            excelVO.setClassNameCode(data.getClassNameCode());
            excelVO.setClassName(data.getClassName());
            //去字典表中查 班级属性 id  导出

            if(data.getClassAttribute() != null){
                String classAttribute = classManagementMapper.getDictLabelById(data.getClassAttribute());
                excelVO.setClassAttribute(classAttribute);
            }

            //班主任可能为空
            String classTeacherLead = null;
            if(data.getClassTeacherLead() != null){
                classTeacherLead = classManagementMapper.getClassTeacherLeadInfo(data.getClassTeacherLead());
            }
            excelVO.setClassTeacherLead(classTeacherLead);


            //辅导老师
            String coachTeacher = null;
            if (StringUtils.isNotBlank(data.getCoachTeacher())) {
                StringBuilder classCoachTeacherBuilder = new StringBuilder();

                // 使用逗号分割字符串
                String[] teacherIds = data.getCoachTeacher().split(",");

                // 遍历所有教师ID，查询信息并拼接
                for (String teacherId : teacherIds) {
                    // 去掉可能的空格
                    teacherId = teacherId.trim();


                    // 获取教师信息
                    String teacherInfo = classManagementMapper.getClassTeacherLeadInfo(Long.parseLong(teacherId));

                    // 如果教师信息不为空，拼接到结果中
                    if (teacherInfo != null) {
                        if (classCoachTeacherBuilder.length() > 0) {
                            classCoachTeacherBuilder.append(", ");
                        }
                        classCoachTeacherBuilder.append(teacherInfo);
                    }
                }

                // 转换为最终字符串
                coachTeacher = classCoachTeacherBuilder.toString();
            }

            excelVO.setCoachTeacher(coachTeacher);


            //学员人数

            Integer count =classManagementMapper.getClassPeopleCount(data.getId());
            excelVO.setClassPeopleCount(count);



            LocalDate classOpenTime = data.getClassOpenTime().toLocalDate();
            LocalDate completionTime = data.getCompletionTime().toLocalDate();
            excelVO.setClassOpenTime(classOpenTime);
            excelVO.setCompletionTime(completionTime);

            //去字典表中查 办班类型  id  导出
            String classTypeDict = classManagementMapper.getDictLabelById(data.getClassTypeDictId());
            excelVO.setClassTypeDict(classTypeDict);

            excelVO.setTurn(data.getTurn());

            //去字典表中查 校区  id  导出
            String campus = classManagementMapper.getDictLabelById(data.getCampus());
            excelVO.setCampus(campus);


            if(data.getPublish() == 2){

                excelVO.setClassStatus("待发布");

            }else if(data.getPublish() == 1){

                excelVO.setClassStatus("已发布");

                LocalDateTime currentTime = LocalDateTime.now();

//                if (currentTime.isAfter(data.getRegistrationStartTime()) && currentTime.isBefore(data.getRegistrationEndTime())) {
//                    excelVO.setClassStatus("报名中");
//                }
//
//                else if (currentTime.isAfter(data.getRegistrationEndTime()) && currentTime.isBefore(data.getClassOpenTime())) {
//                    excelVO.setClassStatus("报名结束");
//                }
//
//                else if (currentTime.isAfter(data.getClassOpenTime()) && currentTime.isBefore(data.getCompletionTime())) {
//                    excelVO.setClassStatus("开班中");
//                }
//
//                else if (currentTime.isAfter(data.getClassOpenTime())) {
//                    excelVO.setClassStatus("已结束");
//                }


                if (currentTime.isAfter(data.getCompletionTime())) {
                    excelVO.setClassStatus("已结束");
                } else if (currentTime.isAfter(data.getClassOpenTime())) {
                    excelVO.setClassStatus("开班中");
                } else if (currentTime.isAfter(data.getRegistrationEndTime())) {
                    excelVO.setClassStatus("报名结束");
                } else if (currentTime.isAfter(data.getRegistrationStartTime())) {
                    excelVO.setClassStatus("报名中");
                }

            }

            excelVO.setSort(data.getSort());

            excelVOList.add(excelVO);


            //返回考勤状态
            if(data.getAttendanceCheck() != null && data.getMealAttendance() != null && data.getCheckIn() != null){
                if(data.getAttendanceCheck() == 1 && data.getMealAttendance() == 1 && data.getCheckIn() == 1){
                    excelVO.setClockInStatus("已关闭");
                }else{
                    excelVO.setClockInStatus("开启中");
                }
            }

            //学期状态
            if(data.getSemester() != null){
                if(data.getSemester() == 1){
                    excelVO.setSemester("上学期");
                }else{
                    excelVO.setSemester("下学期");
                }
            }

        }

        return excelVOList;
    }

    /**
     * 导入班级信息
     *
     * @param importPosts
     */

    @Override
    public ClassManagementImportRespVO importClassInfo(List<ClassInfoImportStrExcelVO> importPosts) {

        // 判空情况
        if (CollUtil.isEmpty(importPosts)) {
            throw exception(CLASS_MANAGEMENT_IMPORT_LIST_IS_EMPTY);
        }

        // 用于存储错误信息
        List<String> errorMessages = new ArrayList<>();

        // 用于存储校验通过和校验失败的数据
        List<ClassManagementDO> rightData = new ArrayList<>();

        List<String> classSourceList = new ArrayList<>();
        classSourceList.add("省委组织部");
        classSourceList.add("省直机关");
        classSourceList.add("韶山干部学院");
        for (int i = 0; i < importPosts.size(); i++) {
            ClassInfoImportStrExcelVO classInfo = importPosts.get(i);
            List<String> currentErrors = new ArrayList<>(); // 当前数据的错误信息列表

            // 排序号
            if (StringUtils.isNotBlank(classInfo.getSort())) {
                if (classInfo.getSort().length() > 20 || !isArabicNumber(classInfo.getSort())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 排序号格式不对");
                }
            }

            // 班次名称
            if (StringUtils.isNotBlank(classInfo.getClassName())) {
                if (classInfo.getClassName().length() > 50) {
                    currentErrors.add("第 " + (i + 2) + " 行: 班次名称格式不对");
                }
                LambdaQueryWrapperX<ClassManagementDO> lqw=new LambdaQueryWrapperX<>();
                lqw.eqIfPresent(ClassManagementDO::getClassName,classInfo.getClassName());
                lqw.last("limit 1");

                ClassManagementDO classManagementDO = classManagementMapper.selectOne(lqw);

                if(classManagementDO!=null){
                    currentErrors.add("第 " + (i + 2) + " 行: 班次名称重复，请查看命名规则");
                }

                // 定义正则表达式
//                String regex = "^(\\d{4})年(春|秋)季学期.*";

                // 编译正则表达式
//                Pattern pattern = Pattern.compile(regex);

//                // 编译正则表达式
//                Pattern pattern = Pattern.compile(regex);
//
//                // 创建匹配器
//                Matcher matcher = pattern.matcher(classInfo.getClassName());
//
//                if (!matcher.matches()){
//                    currentErrors.add("第 " + (i + 2) + " 行: 请在班次名称前添加年份、春/秋季学期，例如：2025年春季学期中青年干部培训一班");
//                }


            } else {
                currentErrors.add("第 " + (i + 2) + " 行: 班次名称不能为空");
            }

            // 班次来源 只有省委需要
            if (Objects.equals(25L,SecurityFrameworkUtils.getTenantId()) || Objects.equals(230L,SecurityFrameworkUtils.getTenantId())){
                if (StringUtils.isNotBlank(classInfo.getClassSource())) {
                    if (!classSourceList.contains(classInfo.getClassSource())){
                        currentErrors.add("第 " + (i + 2) + " 行: 班次来源格式不正确");
                    }
                } else {
                    currentErrors.add("第 " + (i + 2) + " 行: 班次来源不能为空");
                }
            }

            // 年度
            if (StringUtils.isNotBlank(classInfo.getYear())) {
                if (!isArabicNumber(classInfo.getYear())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 年度格式不对");
                }
            } else {
                currentErrors.add("第 " + (i + 2) + " 行: 年度不能为空");
            }

            // 学期
            if (StringUtils.isNotBlank(classInfo.getSemester())) {
                if (!"上学期".equals(classInfo.getSemester()) && !"下学期".equals(classInfo.getSemester())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 学期格式不对");
                }
            } else {
                currentErrors.add("第 " + (i + 2) + " 行: 学期不能为空");
            }

            // 学制
            if (StringUtils.isNotBlank(classInfo.getLearningSystem())) {
                if (classInfo.getLearningSystem().length() > 20 || !isArabicNumber(classInfo.getLearningSystem())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 学制格式不对");
                }
            }

            // 学制单位
            if (StringUtils.isNotBlank(classInfo.getLearningSystemUnit())) {
                if (!("天".equals(classInfo.getLearningSystemUnit()) ||
                        "周".equals(classInfo.getLearningSystemUnit()) ||
                        "月".equals(classInfo.getLearningSystemUnit()))) {
                    currentErrors.add("第 " + (i + 2) + " 行: 学制单位只能是天、周、月");
                }
            } else {
                // 如果学制单位为空，默认为"天"
                classInfo.setLearningSystemUnit("天");
            }

            // 培训对象
            if (StringUtils.isNotBlank(classInfo.getTrainingObject())) {
                if (classInfo.getTrainingObject().length() > 500) {
                    currentErrors.add("第 " + (i + 2) + " 行: 培训对象长度超出限制");
                }
            }

            // 预计人数
            if (StringUtils.isNotBlank(classInfo.getPeopleNumber())) {
                if (classInfo.getPeopleNumber().length() > 20 || !isArabicNumber(classInfo.getPeopleNumber())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 预计人数格式不对");
                }
            } /*else {
                currentErrors.add("第 " + (i + 2) + " 行: 预计人数不能为空");
            }*/

            // 轮次
            /*if (!StringUtils.isNotBlank(classInfo.getTurn())) {
                currentErrors.add("第 " + (i + 2) + " 行: 轮次不能为空");
            }*/

            //班次属性
//            if (!StringUtils.isNotBlank(classInfo.getClassAttribute())) {
//                currentErrors.add("第 " + (i + 1) + " 行: 班次属性不能为空");
//            }

            // 办班类型
            if (!StringUtils.isNotBlank(classInfo.getClassTypeDictId())) {
                currentErrors.add("第 " + (i + 2) + " 行: 办班类型不能为空");
            }

            // 校区
            if (!StringUtils.isNotBlank(classInfo.getCampus())) {
                currentErrors.add("第 " + (i + 2) + " 行: 校区不能为空");
            }

            // 报到时间
            if (StringUtils.isNotBlank(classInfo.getReportingTime())) {
                if (!isValidDate(classInfo.getReportingTime())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 报到时间格式不对");
                }
            } else {
                currentErrors.add("第 " + (i + 2) + " 行: 报到时间不能为空");
            }

            // 报名开始时间
            if (StringUtils.isNotBlank(classInfo.getRegistrationStartTime())) {
                if (!isValidDate(classInfo.getRegistrationStartTime())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 报名开始时间格式不对");
                }
            } else {
                currentErrors.add("第 " + (i + 2) + " 行: 报名开始时间不能为空");
            }

            // 报名结束时间
            if (StringUtils.isNotBlank(classInfo.getRegistrationEndTime())) {
                if (!isValidDate(classInfo.getRegistrationEndTime())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 报名结束时间格式不对");
                }
            } else {
                currentErrors.add("第 " + (i + 2) + " 行: 报名结束时间不能为空");
            }

            // 开班时间
            if (StringUtils.isNotBlank(classInfo.getClassOpenTime())) {
                if (!isValidDate(classInfo.getClassOpenTime())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 开班时间格式不对");
                }
            } else {
                currentErrors.add("第 " + (i + 2) + " 行: 开班时间不能为空");
            }

            // 结业时间
            if (StringUtils.isNotBlank(classInfo.getCompletionTime())) {
                if (!isValidDate(classInfo.getCompletionTime())) {
                    currentErrors.add("第 " + (i + 2) + " 行: 结业时间格式不对");
                }
            } else {
                currentErrors.add("第 " + (i + 2) + " 行: 结业时间不能为空");
            }


            //时间判断

          // 验证报到时间和开班时间的顺序
            if (StringUtils.isNotBlank(classInfo.getRegistrationStartTime()) &&
                    StringUtils.isNotBlank(classInfo.getRegistrationEndTime()) &&
                    StringUtils.isNotBlank(classInfo.getClassOpenTime()) &&
                    StringUtils.isNotBlank(classInfo.getCompletionTime()) &&
                    StringUtils.isNotBlank(classInfo.getReportingTime())) {

                LocalDateTime registrationStartTime = convertToLocalDateTime(classInfo.getRegistrationStartTime());
                LocalDateTime registrationEndTime = convertToLocalDateTime(classInfo.getRegistrationEndTime());
                LocalDateTime classOpenTime = convertToLocalDateTime(classInfo.getClassOpenTime());
                LocalDateTime completionTime = convertToLocalDateTime(classInfo.getCompletionTime());

                LocalDateTime reportingTime = convertToLocalDateTime(classInfo.getReportingTime());

                if (registrationStartTime != null && registrationEndTime != null && classOpenTime != null && completionTime != null && reportingTime!= null) {
                    if (registrationStartTime.isAfter(registrationEndTime)) {
                        currentErrors.add("第 " + (i + 2) + " 行: 报名开始时间不能晚于报名结束时间");
                    }



                    if (registrationEndTime.isAfter(classOpenTime)) {
                        currentErrors.add("第 " + (i + 2) + " 行: 报名结束时间不能晚于开班时间");
                    }


                    if (classOpenTime.isAfter(completionTime)) {
                        currentErrors.add("第 " + (i + 2) + " 行: 开班时间不能晚于结业时间");
                    }


                    if(reportingTime.isAfter(classOpenTime)){
                        currentErrors.add("第 " + (i + 2) + " 行: 报道时间不能晚于开班时间");
                    }
                } else {
                    currentErrors.add("第 " + (i + 2) + " 行: 时间解析失败");
                }
            }



            // 缴费报道
            if (!StringUtils.isNotBlank(classInfo.getPaymentReport())) {
                currentErrors.add("第 " + (i + 2) + " 行: 缴费报道不能为空");
            }

            // 考情评课
            if (!StringUtils.isNotBlank(classInfo.getEvaluate())) {
                currentErrors.add("第 " + (i + 2) + " 行: 考勤评课不能为空");
            }

            // 备注
            if (StringUtils.isNotBlank(classInfo.getRemark())) {
                if (classInfo.getRemark().length() > 250) {
                    currentErrors.add("第 " + (i + 2) + " 行: 备注长度超出限制");
                }
            }

            // 判断是否有错误
            if (!currentErrors.isEmpty()) {
                // 如果当前行有错误，添加到错误信息列表
                errorMessages.addAll(currentErrors);
            } else {
                // 记录正确的数据
                ClassManagementDO rightEntry = new ClassManagementDO();
                if(StringUtils.isNotBlank(classInfo.getSort())){
                    rightEntry.setSort(Integer.parseInt(classInfo.getSort()));
                }
                rightEntry.setClassName(classInfo.getClassName());

                if (Objects.equals(25L,SecurityFrameworkUtils.getTenantId()) || Objects.equals(230L,SecurityFrameworkUtils.getTenantId())){
                    if (classInfo.getClassSource().equals("省委组织部")){
                        rightEntry.setClassSource(1);
                    } else if (classInfo.getClassSource().equals("省直机关")) {
                        rightEntry.setClassSource(2);
                    }else {
                        rightEntry.setClassSource(3);
                    }
                }

                rightEntry.setYear(Integer.parseInt(classInfo.getYear()));
                if("上学期".equals(classInfo.getSemester())){
                    rightEntry.setSemester(1);
                }else {
                    rightEntry.setSemester(2);
                }
                if(StringUtils.isNotBlank(classInfo.getLearningSystem())){
                    rightEntry.setLearningSystem(Integer.parseInt(classInfo.getLearningSystem()));
                }

                // 设置学制单位
                if (StringUtils.isNotBlank(classInfo.getLearningSystemUnit())) {
                    if ("天".equals(classInfo.getLearningSystemUnit())) {
                        rightEntry.setLearningSystemUnit(1);
                    } else if ("周".equals(classInfo.getLearningSystemUnit())) {
                        rightEntry.setLearningSystemUnit(2);
                    } else if ("月".equals(classInfo.getLearningSystemUnit())) {
                        rightEntry.setLearningSystemUnit(3);
                    } else {
                        // 默认为天
                        rightEntry.setLearningSystemUnit(1);
                    }
                } else {
                    // 默认为天
                    rightEntry.setLearningSystemUnit(1);
                }

                rightEntry.setTrainingObject(classInfo.getTrainingObject());
                if(StringUtils.isNotBlank(classInfo.getPeopleNumber())){
                    rightEntry.setPeopleNumber(Integer.parseInt(classInfo.getPeopleNumber()));
                }
                rightEntry.setTurn(classInfo.getTurn());
                //办班类型
                //根据名称查询  id
                try{
                    Integer classTypeId = classManagementMapper.getIdByDictLabel(classInfo.getClassTypeDictId() , 1);
                    rightEntry.setClassTypeDictId(classTypeId);
                }catch (Exception e){
                    throw exception(CLASS_MANAGEMENT_DICTIONARY_ERROR);
                }

                //班次属性  可以为null
                if(StringUtils.isNotBlank(classInfo.getClassAttribute())){
                    try{
                        Integer attributeId = classManagementMapper.getIdByDictLabel(classInfo.getClassAttribute(), 2);
                        rightEntry.setClassAttribute(attributeId);
                    }catch (Exception e){
                        throw exception(CLASS_MANAGEMENT_DICTIONARY_ERROR);
                    }
                }


                //校区
                try{
                    Integer campusId = classManagementMapper.getIdByDictLabelcampus(classInfo.getCampus(), 3, SecurityFrameworkUtils.getTenantId());
                    rightEntry.setCampus(campusId);
                }catch (Exception e){
                    throw exception(CLASS_MANAGEMENT_DICTIONARY_ERROR);
                }



                //时间
                rightEntry.setReportingTime(convertToLocalDateTime(classInfo.getReportingTime()));
                rightEntry.setRegistrationStartTime(convertToLocalDateTime(classInfo.getRegistrationStartTime()));

                //结束时间为 23:59:59
                rightEntry.setRegistrationEndTime(convertToLocalDateTimes(classInfo.getRegistrationEndTime(), true));

                rightEntry.setClassOpenTime(convertToLocalDateTime(classInfo.getClassOpenTime()));

                //结业时间 23:59:59
                rightEntry.setCompletionTime(convertToLocalDateTimes(classInfo.getCompletionTime(), true));

                if(classInfo.getPaymentReport().equals("是")){
                    rightEntry.setPaymentReport(1);
                }else{
                    rightEntry.setPaymentReport(2);
                }

                if(classInfo.getEvaluate().equals("是")){
                    rightEntry.setEvaluate(1);
                }else{
                    rightEntry.setEvaluate(2);
                }

                rightEntry.setRemark(classInfo.getRemark());


                rightData.add(rightEntry);
            }
        }


        //加一把分布式锁

        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，设置等待时间为0，表示不等待
            if (lock.tryLock(5, 10, TimeUnit.SECONDS)) {

                // 处理校验通过的数据：保存到数据库
                if (CollUtil.isNotEmpty(rightData)) {
                    //一个一个插入
                    for (ClassManagementDO right : rightData) {


                        //学期 代号
                        String semester = null;
                        // 顺序号
                        String serialNumber = null;
                        //获取数据库里面数据  获取顺序号
                        Integer serialNumberCount = classManagementMapper.selectClassCount(right.getYear()) + 1;

                        if(serialNumberCount < 10){
                            serialNumber = "0" + serialNumberCount;
                        }else{
                            serialNumber = serialNumberCount.toString();
                        }



                        if(right.getSemester() == 1){
                            semester = "01";
                        }else {
                            semester = "02";
                        }

                        //自动生成班次编码
                        String classCode = right.getYear() + semester + serialNumber;

                        right.setClassNameCode(classCode);


                        //判断是否位主体班 为空或主体班 打开考勤
                        //正式环境、测试环境字典值不同，主体班：正式 1451 ， 测试 805      委托班：正式 1452 ， 测试 806
                        if(right.getClassAttribute() == null || right.getClassAttribute() == 1451){
                            right.setAttendanceCheck(0);
                            right.setMealAttendance(0);
                            right.setCheckIn(0);
                        }

                        //委托班 关闭考勤
                        if(right.getClassAttribute() != null && right.getClassAttribute() == 1452){
                            right.setAttendanceCheck(1);
                            right.setMealAttendance(1);
                            right.setCheckIn(1);
                        }

                        // 插入
                        classManagementMapper.insert(right);

                        //中间表存入 班级id 与 考勤规则id

                        ClassClockInDO classClockInDO = new ClassClockInDO();
                        classClockInDO.setClassId(right.getId());

                        // 去规则表中拿默认规则的id
                        //拿默认规则  如果没有默认规则 就不插入
                        List<RuleTemplateDO> ruleList = ruleTemplateMapper.getClassDefaultRule(right.getCampus());
                        for (RuleTemplateDO list : ruleList){
                            if(list.getId() != null){
                                //到课
                                if(list.getRuleType() == 0){
                                    //该校区 到课考勤默认规则
                                    classClockInDO.setAttendanceCheck(list.getId());
                                }

                                //就餐
                                if(list.getRuleType() == 1){
                                    //该校区 就餐考勤默认规则
                                    classClockInDO.setMealAttendance(list.getId());
                                }

                                //住宿
                                if(list.getRuleType() == 2){
                                    //该校区 住宿考勤默认规则
                                    classClockInDO.setCheckIn(list.getId());
                                }

                            }
                        }

                        // 插入到中间表中 规则id
                        classManagementMapper.insertClassClockIn(classClockInDO);



                        // 遍历单位表 插入  暂留  单位表不在班级新增时插入
//                        List<SignUpUnitDO> signUpUnitDOList = signUpUnitMapper.getSignUpUnitDOList();
//                        for(SignUpUnitDO list : signUpUnitDOList){
//                            SignUpUnitDO insertData = new SignUpUnitDO();
//                            insertData.setUnitName(list.getUnitName());
//                            insertData.setUnitClassification(list.getUnitClassification());
//                            insertData.setUnitChargePeople(list.getUnitChargePeople());
//                            insertData.setPhone(list.getPhone());
//                            insertData.setStatus(list.getStatus());
//                            insertData.setSort(list.getSort());
//                            insertData.setCapacity(list.getCapacity());
//                            insertData.setIsRestrict(list.getIsRestrict());
//
//                            //班级id
//                            insertData.setClassId(right.getId());
//
//                            signUpUnitMapper.insert(insertData);
//
//                        }


                        //调用班级考勤日历接口 为该班级创建班级日历
                        ClassClockCalendarCreateReqVO classClockCalendarCreateReqVO = new ClassClockCalendarCreateReqVO();
                        classClockCalendarCreateReqVO.setClassId(Long.valueOf(right.getId()));
                        classClockCalendarService.createClassClockCalendar(classClockCalendarCreateReqVO);

                    }

                    if(rightData.size() == importPosts.size()){
                        return ClassManagementImportRespVO.builder()
                                .count(rightData.size())
                                .tag(1)
                                .build();
                    }else{
                        return ClassManagementImportRespVO.builder()
                                .errorMessages(errorMessages)
                                .count(rightData.size())
                                .tag(2)
                                .build();
                    }


                }else{
                    return ClassManagementImportRespVO.builder()
                            .errorMessages(errorMessages)
                            .count(rightData.size())
                            .tag(3)
                            .build();
                }

            } else {
                // 如果没有获取到锁，则直接返回失败信息
                System.out.println("Failed to acquire lock, lock is already held by another process.");

                //错误提示
                throw exception(CLASS_MANAGEMENT_CREATE_ERROR);

            }

        } catch (Exception e) {
            log.error("Error occurred: ", e);

            return null;
        } finally {
            // 释放锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }


    }

    /**
     * 小程序切换班级
     * @param pageReqVO 分页查询
     * @return
     */

    @Override
    public List<ClassManagementDO> getClassManagementPageApplet(ClassManagementPageReqVO pageReqVO) {

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(pageReqVO.getClassName())){
            pageReqVO.setClassName(pageReqVO.getClassName().replaceAll("([%_])", "\\\\$1"));
        }

//        Long userId = SecurityFrameworkUtils.getLoginUserId();

        if(pageReqVO.getUserId() != null){
            Long teacherId = null;
            try{
                //获取对应师资表中的 id
                Long userId = traineeService.getUserBySystemId(pageReqVO.getUserId().toString());
                teacherId = classManagementMapper.getTeacherId(userId);
            }catch (Exception e){
                throw exception(CLASS_MANAGEMENT_TEACHER_ID_NOT_EXIST);
            }


            pageReqVO.setClassTeacherLead(teacherId);
        }


        List<ClassManagementDO> classManagementDOList = classManagementMapper.selectPageListApplet(pageReqVO);


        //返回班级状态
        for(ClassManagementDO list : classManagementDOList){


            if(list.getPublish() == 2){

                list.setClassStatus("待发布");

            }else if(list.getPublish() == 1){

                list.setClassStatus("已发布");

                LocalDateTime currentTime = LocalDateTime.now();

                if (currentTime.isAfter(list.getRegistrationStartTime()) && currentTime.isBefore(list.getRegistrationEndTime())) {
                    list.setClassStatus("报名中");
                }

                else if (currentTime.isAfter(list.getRegistrationEndTime()) && currentTime.isBefore(list.getClassOpenTime())) {
                    list.setClassStatus("报名结束");
                }

                else if (currentTime.isAfter(list.getClassOpenTime()) && currentTime.isBefore(list.getCompletionTime())) {
                    list.setClassStatus("开班中");
                }

                else if (currentTime.isAfter(list.getClassOpenTime())) {
                    list.setClassStatus("已结束");
                }


            }

            //添加学员的人数
            Integer count =classManagementMapper.getClassPeopleCount(list.getId());
            list.setClassPeopleCount(count);


            //班主任可能为空
            String classTeacherLead = null;
            if(list.getClassTeacherLead() != null){
                classTeacherLead = classManagementMapper.getClassTeacherLeadInfo(list.getClassTeacherLead());
            }
            list.setClassTeacherLeadName(classTeacherLead);


            //辅导老师
            String coachTeacher = null;
            if (StringUtils.isNotBlank(list.getCoachTeacher())) {
                StringBuilder classCoachTeacherBuilder = new StringBuilder();

                // 使用逗号分割字符串
                String[] teacherIds = list.getCoachTeacher().split(",");

                // 遍历所有教师ID，查询信息并拼接
                for (String teacherId : teacherIds) {
                    // 去掉可能的空格
                    teacherId = teacherId.trim();


                    // 获取教师信息
                    String teacherInfo = classManagementMapper.getClassTeacherLeadInfo(Long.parseLong(teacherId));

                    // 如果教师信息不为空，拼接到结果中
                    if (teacherInfo != null) {
                        if (classCoachTeacherBuilder.length() > 0) {
                            classCoachTeacherBuilder.append(", ");
                        }
                        classCoachTeacherBuilder.append(teacherInfo);
                    }
                }

                // 转换为最终字符串
                coachTeacher = classCoachTeacherBuilder.toString();
            }
            list.setCoachTeacherName(coachTeacher);



        }



        return classManagementDOList;
    }

    @Override
    public List<ClassInfoRespVO> getClassPageByStatus(Integer status,Long userId, Integer clockIn) {

        List<Long> ids;

        if(clockIn != null){
            //查询所有开启考勤的班级 id
            if (userId == null){
                ids = new LambdaQueryChainWrapper<>(classManagementMapper)
                        .eq(ClassManagementDO::getPublish, 1)
                        .and(
                                wrapper -> wrapper
                                        .eq(ClassManagementDO::getAttendanceCheck, 0)
                                        .or()
                                        .eq(ClassManagementDO::getMealAttendance, 0)
                                        .or()
                                        .eq(ClassManagementDO::getCheckIn, 0)
                        )
                        .list()
                        .stream()
                        .map(ClassManagementDO::getId)
                        .collect(Collectors.toList());
            }else {
                userId = teacherInformationService.getTeacherId(userId);
                if (userId == null){
                    throw exception(CLASS_MANAGEMENT_TEACHER_ID_NOT_EXIST);
                }
                ids =  classManagementMapper.getClassIdListByTeacherId1(userId);

                if (ids == null){
                    return new ArrayList<>();
                }
            }

        }else{

            if (userId == null){
                ids = new LambdaQueryChainWrapper<>(classManagementMapper)
                        .eq(ClassManagementDO::getPublish, 1)
                        .list()
                        .stream()
                        .map(ClassManagementDO::getId)
                        .collect(Collectors.toList());

            }else {
                userId = teacherInformationService.getTeacherId(userId);
                if (userId == null){
                    throw exception(CLASS_MANAGEMENT_TEACHER_ID_NOT_EXIST);
                }
                ids =  classManagementMapper.getClassIdListByTeacherId(userId);
                if (ids == null){
                    return new ArrayList<>();
                }
            }
        }

        Set<Long> classIds = new HashSet<>(ids);

        List<ClassManagementDO> classManagementDOList = classManagementMapper.selectPageListByStatus(status,classIds);

        return ClassManagementConvert.INSTANCE.convertClassList(classManagementDOList);
    }

    @Override
    public List<String> getDefaultClassByTeacherId(Long teacherId , Integer clockIn) {


        if(clockIn != null){
            List<String> result = new ArrayList<>();
            List<ClassInfoRespVO> list = this.getClassPageByStatus(2, teacherId, 1);
            if (CollUtil.isNotEmpty(list)){
                result.add("2");
                result.add(list.get(0).getId().toString());
                return result;
            }else {
                list = this.getClassPageByStatus(4, teacherId, 1);
                if (CollUtil.isNotEmpty(list)){
                    result.add("4");
                    result.add(list.get(0).getId().toString());
                    return result;
                }else{
                    return Collections.emptyList();
                }
            }

        }else {
            List<String> result = new ArrayList<>();
            List<ClassInfoRespVO> list = this.getClassPageByStatus(2, teacherId, null);
            if (CollUtil.isNotEmpty(list)){
                result.add("2");
                result.add(list.get(0).getId().toString());
                return result;
            }else {
                list = this.getClassPageByStatus(4, teacherId, null);
                if (CollUtil.isNotEmpty(list)){
                    result.add("4");
                    result.add(list.get(0).getId().toString());
                    return result;
                }else {
                    list = this.getClassPageByStatus(3, teacherId, null);
                    if (CollUtil.isNotEmpty(list)){
                        result.add("3");
                        result.add(list.get(0).getId().toString());
                        return result;
                    }else {
                        return Collections.emptyList();
                    }
                }
            }
        }
    }

    /**
     * @param clockingInUpdateReqVO
     * @return 班级id集合
     */
    @Override
    public void updateClassClockingInRule(ClassClockingInUpdateReqVO clockingInUpdateReqVO) {


        //如果传空值  就用校区默认规则
        //到课 0
        if(clockingInUpdateReqVO.getAttendanceCheckId() == null){
            RuleTemplateDO ruleTemplateDO = ruleTemplateMapper.getClassDefaultRuleSingle(clockingInUpdateReqVO.getCampus(),0);
            if(ruleTemplateDO != null && ruleTemplateDO.getId() != null) {
                clockingInUpdateReqVO.setAttendanceCheckId(Long.valueOf(ruleTemplateDO.getId()));
            }
        }
        //就餐 1
        if(clockingInUpdateReqVO.getMealAttendanceId() == null){
            RuleTemplateDO ruleTemplateMealDO = ruleTemplateMapper.getClassDefaultRuleSingle(clockingInUpdateReqVO.getCampus(),1);
            if(ruleTemplateMealDO != null && ruleTemplateMealDO.getId() != null) {
                clockingInUpdateReqVO.setMealAttendanceId(Long.valueOf(ruleTemplateMealDO.getId()));
            }
        }
        //住宿 2
        if(clockingInUpdateReqVO.getCheckInId() == null){
            RuleTemplateDO ruleTemplateCheckInDO = ruleTemplateMapper.getClassDefaultRuleSingle(clockingInUpdateReqVO.getCampus(),2);
            if(ruleTemplateCheckInDO != null && ruleTemplateCheckInDO.getId() != null){
                clockingInUpdateReqVO.setCheckInId(Long.valueOf(ruleTemplateCheckInDO.getId()));
            }

        }

        //更新表中的状态
        classManagementMapper.updateClassClockingInRule(clockingInUpdateReqVO);

        //更新表中的规则id
        classManagementMapper.updateClassClockingInRuleId(clockingInUpdateReqVO);

        //设置考勤规则后刷新签到表
        // 刷新签到表,立即提交任务，不等待结果
        CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                .exceptionally(e -> {
                    log.error(e.getMessage(), e);
                    return null;
                });
    }

    /**
     * @param classSignUpUnitBaseVO
     * @return
     */
    @Override
    public Integer createClassSignUp(List<ClassSignUpUnitBaseVO> classSignUpUnitBaseVO) {

        // 处理每个 ClassSignUpUnitBaseVO 对象
        for (ClassSignUpUnitBaseVO vo : classSignUpUnitBaseVO) {
            // 1.在单位表中插入数据
            // 2.对应班级不存在  新增对应班级下的单位
            // 3.对应班已经存在该单位 更新对应班级下的单位

            // 根据classID 与 unit_name 判断
            SignUpUnitDO signUpUnitDO = signUpUnitMapper.selectByClassIdAndUnitName(vo.getClassId(), vo.getUnitName());

            if(signUpUnitDO == null){
                // 为null  说明不存在  需要插入

                SignUpUnitDO insertData = new SignUpUnitDO();
                insertData.setUnitName(vo.getUnitName());
                insertData.setUnitClassification(vo.getUnitClassification());
                insertData.setUnitChargePeople(vo.getUnitChargePeople());
                insertData.setCapacity(vo.getCapacity());
                insertData.setIsRestrict(vo.getIsRestrict());
                //班级id
                insertData.setClassId(vo.getClassId());
                insertData.setSort(vo.getSort());

                signUpUnitMapper.insert(insertData);

            }else{
                //不为null说明存在  需要更新

                signUpUnitDO.setCapacity(vo.getCapacity());
                signUpUnitDO.setIsRestrict(vo.getIsRestrict());

                signUpUnitMapper.updateById(signUpUnitDO);
            }
        }
        // 返回处理结果，例如处理的对象数量
        return classSignUpUnitBaseVO.size();
    }


    /**
     * 通过用户id获取这个用户教师管理的班级id集合
     *
     * @param userId 用户
     * @return 班级id集合
     */
    @Override
    public List<Long> getClassIdListByUserId(Long userId) {
        List<Long> result = new ArrayList<>();
        if (Objects.nonNull(userId)){
            //获取对应师资表中的 id
            Long teacherId = classManagementMapper.getTeacherId(userId);
            if (Objects.isNull(teacherId)){
                throw exception(CLASS_MANAGEMENT_TEACHER_ID_NOT_EXIST);
            }
            result = classManagementMapper.getClassIdListByTeacherId(teacherId);
        }
        return result;
    }

    /**
     * 获取当前登录用户班主任权限下的班级id集合
     *
     * @return 班级id集合
     */
    @Override
    public List<Long> getLoginUserClassMasterLimitClassList() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (Objects.isNull(loginUser)){
            throw exception(ELECTIVE_RELEASE_CLASS_MASTER_USER_NOT_EXISTS);
        }
        List<Long> classIdListByUserId = getClassIdListByUserId(loginUser.getId());
        // 若该用户没有班级，则班级权限中加入不存在班级是的查询结果为空
        if (classIdListByUserId.isEmpty()) {
            classIdListByUserId.add(NONE_CLASS_ID);
        }
        return classIdListByUserId;
    }

    // 方法检查字符串是否只包含阿拉伯数字
    public static boolean isArabicNumber(String s) {
        // 正则表达式模式，表示仅包含数字
        String regex = "^[0-9]{1,9}$";
        // 返回匹配结果
        return Pattern.matches(regex, s);
    }

    //String日期格式转化
    public static LocalDateTime convertToLocalDateTime(String dateStr) {
        // 定义日期格式
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy/M/d");
        try {
            // 解析日期字符串为 LocalDate0
            LocalDate localDate = LocalDate.parse(dateStr, dateFormat);
            // 将 LocalDate 转换为 LocalDateTime
            return localDate.atStartOfDay();
        } catch (DateTimeParseException e) {
            return null; // 如果解析失败，返回 null
        }
    }

    public static LocalDateTime convertToLocalDateTimes(String dateStr, boolean isEndTime) {
        // 定义日期格式
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy/M/d");
        try {
            // 解析日期字符串为 LocalDate
            LocalDate localDate = LocalDate.parse(dateStr, dateFormat);
            // 如果 isEndTime 为 true，则设置时间为 23:59:59
            if (isEndTime) {
                return localDate.atTime(23, 59, 59);
            } else {
                // 否则设置时间为 00:00:00
                return localDate.atStartOfDay();
            }
        } catch (DateTimeParseException e) {
            return null; // 如果解析失败，返回 null
        }
    }

    //String日期格式校验
    public static boolean isValidDate(String dateStr) {
        // 定义日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/M/d");
        dateFormat.setLenient(false); // 设置为严格解析

        try {
            // 尝试解析日期字符串
            dateFormat.parse(dateStr);
            return true; // 如果成功解析，返回 true
        } catch (ParseException e) {
            return false; // 如果解析失败，返回 false
        }
    }

    @Override
    public ClassSimpleInfo getSimpleClassInfo(Long id) {
        return classManagementMapper.selectSimpleClassInfo(id);
    }

    @Override
    public List<ClassSimpleInfo> getSimpleClassInfoList(Long teacherId, String className) {
        return classManagementMapper.selectSimpleClassInfoList(teacherId, className);
    }

    @Override
    public List<ClassHaveCourseVO> getClassHasCourseList(String className){
//        List<PlanDO> planList = planService.lambdaQuery().eq(PlanDO::getStatus, PlanStatusEnum.ON.getCode()).list();
//        if(planList.size()==0){
////            throw exception(CLASS_HAVE_COURSE_NOT_EXISTS);
//            return new ArrayList<>();
//        }
//
//        List<ClassCourseDO> classCourseDOList;
//
//        LambdaQueryWrapper<ClassCourseDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.selectDistinct(ClassCourseDO::getClassId);
//        List<Long> distinctClassIds = classCourseMapper.selectObjs(lambdaQueryWrapper)
//                .stream()
//                .map(obj -> (Long) obj)
//                .collect(Collectors.toList());
//
//
//        List<Long> classIdListLong = planList.stream().map(PlanDO::getClassId).collect(Collectors.toList());
//
//
//
//        List<Integer> classIdList = classIdListLong.stream()
//                .map(Long::intValue)
//                .collect(Collectors.toList());
        //获取当前租户下所有已排课的班级id todo 验证sql写法及tenantId是否生效隔离
        List<Long> classIdList = classManagementMapper.getClassIdList();

        if(classIdList.isEmpty()){
            return new ArrayList<>();
        }

        LambdaQueryChainWrapper<ClassManagementDO> queryWrapper = this.lambdaQuery()
                .in(ClassManagementDO::getId, classIdList)
                .ge(ClassManagementDO::getCompletionTime, LocalDateTime.now())
                .orderByDesc(ClassManagementDO::getClassOpenTime)
                .orderByAsc(ClassManagementDO::getClassName);
        if (StringUtils.isNotEmpty(className)) {
            queryWrapper.like(ClassManagementDO::getClassName, className);
        }
        List<ClassManagementDO> classList = queryWrapper.list();
        List<ClassHaveCourseVO> returnList =  new ArrayList<>();
        classList.forEach(classDO -> {
            try{
                ClassHaveCourseVO classHaveCourseVO = new ClassHaveCourseVO();
                classHaveCourseVO.setId(classDO.getId());
                classHaveCourseVO.setClassName(classDO.getClassName());
                classHaveCourseVO.setClassOpenTime(classDO.getClassOpenTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                classHaveCourseVO.setCompletionTime(classDO.getCompletionTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                returnList.add(classHaveCourseVO);
            }catch (Exception e){
                log.error(e.getMessage(),e);
            }
        });
        return returnList;
    }

    public static Cache<String,  StatRespVO > resultCache=
            CacheBuilder.newBuilder()
                    .initialCapacity(1) // 初始容量
                    .maximumSize(10000)   // 设定最大容量
                    .expireAfterWrite(5L, TimeUnit.MINUTES) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();

    @Override
    public StatRespVO getStat(){

        Long tenantId = SecurityFrameworkUtils.getTenantId();

        StatRespVO  respVO =null;
        try {
            respVO = resultCache.get("homepageStat" + tenantId, () -> {

                StatRespVO statRespVO = new StatRespVO();
                //报名单位数
                List<SignUpUnitDO> unitList = signUpUnitService.lambdaQuery().eq(SignUpUnitDO::getStatus, UnitStatusEnum.ENABLE.getStatus()).eq(SignUpUnitDO::getTemplate, UnitTemplateEnum.TEMPLATE.getStatus()).list();
                statRespVO.setSignUpUnitNum(unitList.size());
                //学员数
                List<TraineeDO> traineeList = traineeService.list();
                List<TraineeDO> traineeRegisteredList = traineeList.stream()
                        .filter(trainee -> TraineeStatusEnum.REGISTERED.getStatus().equals(trainee.getStatus()))
                        .collect(Collectors.toList());
                List<TraineeDO> traineeReportedList = traineeList.stream()
                        .filter(trainee -> TraineeStatusEnum.REPORTED.getStatus().equals(trainee.getStatus()))
                        .collect(Collectors.toList());
                List<TraineeDO> traineeGraduatedList = traineeList.stream()
                        .filter(trainee -> TraineeStatusEnum.GRADUATED.getStatus().equals(trainee.getStatus()))
                        .collect(Collectors.toList());
                statRespVO.setTraineeRegisteredNum(traineeRegisteredList.size() + traineeReportedList.size() + traineeGraduatedList.size());
                statRespVO.setTraineeReportedNum(traineeReportedList.size() + traineeGraduatedList.size());
                //班级数
                List<ClassManagementDO> classManagementDOS = classManagementMapper.selectList();
                List<ClassManagementDO> classNotStartedDOS = classManagementDOS.stream()
                        .filter(classDO -> LocalDateTime.now().isBefore(classDO.getClassOpenTime()) && PublishEnum.ON.getCode().equals(classDO.getPublish()))
                        .collect(Collectors.toList());
                List<ClassManagementDO> classStartingDOS = classManagementDOS.stream()
                        .filter(classDO -> LocalDateTime.now().isAfter(classDO.getClassOpenTime()) && LocalDateTime.now().isBefore(classDO.getCompletionTime()) && PublishEnum.ON.getCode().equals(classDO.getPublish()))
                        .collect(Collectors.toList());
                List<ClassManagementDO> classFinishedDOS = classManagementDOS.stream()
                        .filter(classDO -> LocalDateTime.now().isAfter(classDO.getCompletionTime()) && PublishEnum.ON.getCode().equals(classDO.getPublish()))
                        .collect(Collectors.toList());
                statRespVO.setClassNum(classNotStartedDOS.size() + classStartingDOS.size() + classFinishedDOS.size());
                statRespVO.setClassNotStartedNum(classNotStartedDOS.size());
                statRespVO.setClassStartingNum(classStartingDOS.size());
                statRespVO.setClassFinishedNum(classFinishedDOS.size());
                return statRespVO;

            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        return respVO;

    }

    /**
     * @param classCompletionUpdateReqVO
     * @return
     */
    @Override
    public void updateCompletionTemplate(ClassCompletionUpdateReqVO classCompletionUpdateReqVO) {

        ClassCompletionTemplateCreateReqVO classCompletionTemplateCreateReqVO = new ClassCompletionTemplateCreateReqVO();
        classCompletionTemplateCreateReqVO.setClassId(classCompletionUpdateReqVO.getClassId());
        classCompletionTemplateCreateReqVO.setIdCode(classCompletionUpdateReqVO.getIdCode());

        //更新班级结业模版数据
        classCompletionTemplateService.createClassCompletionTemplate(classCompletionTemplateCreateReqVO);

        classManagementMapper.updateCompletionTemplate(classCompletionUpdateReqVO);
    }

    @Override
    public List<ClassManagementDO> getClassList() {
        return baseMapper.getClassList();
    }

    @Override
    public List<ClassManagementDO> getComplateClassList() {
        return baseMapper.getComplateClassList();
    }

    private void handleDictValue(List<ClassManagementRespVO> list) {
        // 查出字典中所有办班类型
        List<String> dict = new ArrayList<>();
        dict.add(ClassManageDictTypeEnum.CLASS_TYPE.getType());
        List<DictDataRespDTO> dictList = dictDataApi.getByDictTypes(dict).getCheckedData();
        Map<Long, String> map = dictList.stream()
                .collect(Collectors.toMap(DictDataRespDTO::getId, DictDataRespDTO::getLabel, (k1, k2) -> k1));

        list.forEach(item -> {
            try {
                Long classTypeDictId = item.getClassTypeDictId() != null ? Long.valueOf(item.getClassTypeDictId()) : null;
                item.setClassTypeDictName(map.getOrDefault(classTypeDictId, StrUtil.EMPTY));
            } catch (NumberFormatException e) {
                // 处理无法转换为 Long 的情况
                item.setClassTypeDictName(StrUtil.EMPTY);
            }
        });
    }
}
