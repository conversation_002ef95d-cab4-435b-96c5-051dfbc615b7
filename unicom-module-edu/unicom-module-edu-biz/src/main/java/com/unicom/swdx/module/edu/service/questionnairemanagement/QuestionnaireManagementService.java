package com.unicom.swdx.module.edu.service.questionnairemanagement;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairemanagement.QuestionnaireManagementDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;

/**
 * 评估问卷管理 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionnaireManagementService {

    /**
     * 创建评估问卷管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createQuestionnaireManagement(@Valid QuestionnaireManagementSaveReqVO createReqVO);

    /**
     * 更新评估问卷管理
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestionnaireManagement(@Valid QuestionnaireManagementSaveReqVO updateReqVO);

    /**
     * 删除评估问卷管理
     *
     * @param id 编号
     */
    void deleteQuestionnaireManagement(Long id);

    /**
     * 获得评估问卷管理
     *
     * @param id            编号
     * @param userId        用户ID
     * @param classCourseId 课程ID
     * @return 评估问卷管理
     */
    QuestionnaireManagementRespVO getQuestionnaireManagement(Long id, Long userId, Long classCourseId);

    /**
     * 获得评估问卷管理分页
     *
     * @param pageReqVO 分页查询
     * @return 评估问卷管理分页
     */
    PageResult<QuestionnaireManagementRespVO> getQuestionnaireManagementPage(QuestionnaireManagementPageReqVO pageReqVO);

    List<QuestionRespVO> getQuestionList(QuestionListReqVO listReqVO);

    void batchDeleteQuestionnaireManagement(List<Long> ids);

    Long getByEducateForm(Long educateFormId, Long tenantId);

    Long getDefaultQuestionnaireId(Long tenantId);

    QuestionnaireManagementRespVO getRatedQuestionnaireManagement(Long questionnaireId, Long userId, Long classCourseId);

    List<Long> getCollectingEducateForm();
}