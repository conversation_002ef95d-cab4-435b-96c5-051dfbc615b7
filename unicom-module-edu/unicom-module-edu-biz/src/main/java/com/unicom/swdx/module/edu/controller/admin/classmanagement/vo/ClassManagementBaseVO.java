package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
* EduClassManagement Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ClassManagementBaseVO {

    @ApiModelProperty(value = "班次编码")
    private String classNameCode;

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty(value = "办班类型")
    private Integer classTypeDictId;

    @ApiModelProperty(value = "班级属性")
    private Integer classAttribute;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "学期，1-上学期，2-下学期")
    private Integer semester;

    @ApiModelProperty(value = "学制")
    private Integer learningSystem;

    @ApiModelProperty(value = "学制单位，1-天，2-周，3-月，默认为1")
    private Integer learningSystemUnit;

    @ApiModelProperty(value = "培训对象")
    private String trainingObject;

    @ApiModelProperty(value = "预计人数")
    private Integer peopleNumber;

    @ApiModelProperty(value = "轮次")
    private String turn;

    @ApiModelProperty(value = "校区")
    private Integer campus;

    @ApiModelProperty(value = "报道时间")
    private LocalDateTime reportingTime;

    @ApiModelProperty(value = "开班时间")
    private LocalDateTime classOpenTime;

    @ApiModelProperty(value = "结业时间")
    private LocalDateTime completionTime;

    @ApiModelProperty(value = "报名开始时间")
    private LocalDateTime registrationStartTime;

    @ApiModelProperty(value = "缴费报道，1-是，2-否")
    private Integer paymentReport;

    @ApiModelProperty(value = "考勤评课，1-是，2-否")
    private Integer evaluate;

    @ApiModelProperty(value = "附件")
    private String accessory;

    @ApiModelProperty(value = "排序号")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "1-已发布，2-待发布")
    private Integer publish;

    @ApiModelProperty(value = "报名结束时间")
    private LocalDateTime registrationEndTime;

    @ApiModelProperty(value = "班主任")
    private Long classTeacherLead;

    @ApiModelProperty(value = "辅导老师")
    private String coachTeacher;

    @ApiModelProperty(value = "班级状态")
    private String classStatus;

    @ApiModelProperty(value = "班级学员人数")
    private Integer classPeopleCount;

    @ApiModelProperty(value = "班主任姓名")
    private String classTeacherLeadName;

    @ApiModelProperty(value = "辅导老师姓名")
    private String coachTeacherName;

    @ApiModelProperty(value = "到课考勤，0-开，1关")
    private Integer attendanceCheck;

    @ApiModelProperty(value = "就餐考勤，0-开，1关")
    private Integer mealAttendance;

    @ApiModelProperty(value = "住宿考勤，0-开，1关")
    private Integer checkIn;

    @ApiModelProperty(value = "考勤状态 0-开启，1-关闭")
    private Integer attendanceStatus;

    @ApiModelProperty(value = "到课考勤规则Id")
    private Integer attendanceCheckId;

    @ApiModelProperty(value = "就餐考勤规则Id")
    private Integer mealAttendanceId;

    @ApiModelProperty(value = "住宿考勤规则Id")
    private Integer checkInId;

    @ApiModelProperty(value = "班次来源")
    private Integer classSource;


}
