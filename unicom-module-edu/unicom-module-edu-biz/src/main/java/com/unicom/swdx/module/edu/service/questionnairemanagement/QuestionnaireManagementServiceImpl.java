package com.unicom.swdx.module.edu.service.questionnairemanagement;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo.QuestionCategoryManagementListReqVO;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.QuestionnaireDetailRespVO;
import com.unicom.swdx.module.edu.convert.question.QuestionManagementConvert;
import com.unicom.swdx.module.edu.convert.questionnaire.QuestionLogicConvert;
import com.unicom.swdx.module.edu.convert.questionnaire.QuestionnaireDetailConvert;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationdetail.EvaluationDetailDO;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import com.unicom.swdx.module.edu.dal.dataobject.questioncategorymanagement.QuestionCategoryManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionlogic.QuestionLogicDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionmanagement.QuestionManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairedetail.QuestionnaireDetailDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.evaluationdetail.EvaluationDetailMapper;
import com.unicom.swdx.module.edu.dal.mysql.options.OptionsMapper;
import com.unicom.swdx.module.edu.dal.mysql.questioncategorymanagement.QuestionCategoryManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.questionlogic.QuestionLogicMapper;
import com.unicom.swdx.module.edu.dal.mysql.questionmanagement.QuestionManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.questionnairedetail.QuestionnaireDetailMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.service.questioncategorymanagement.QuestionCategoryManagementService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairemanagement.QuestionnaireManagementDO;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.util.object.BeanUtils;

import com.unicom.swdx.module.edu.dal.mysql.questionnairemanagement.QuestionnaireManagementMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.edu.utils.serialnumber.PageDataSerialNumberUtil.generateSerialNumberList;

/**
 * 评估问卷管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionnaireManagementServiceImpl implements QuestionnaireManagementService {

    @Resource
    private QuestionnaireManagementMapper questionnaireManagementMapper;

    @Resource
    private QuestionCategoryManagementMapper questionCategoryManagementMapper;

    @Resource
    private QuestionCategoryManagementService questionCategoryManagementService;

    @Resource
    private QuestionManagementMapper questionManagementMapper;

    @Resource
    private OptionsMapper optionsMapper;

    @Resource
    private QuestionnaireDetailMapper questionnaireDetailMapper;

    @Resource
    private QuestionLogicMapper questionLogicMapper;

    @Resource
    private EvaluationDetailMapper evaluationDetailMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Override
    public Long createQuestionnaireManagement(QuestionnaireManagementSaveReqVO createReqVO) {
        // 默认问卷时查询是否已存在默认问卷
        if(createReqVO.getIsDefault().equals("1") && createReqVO.getStatus().equals("1")) {
            validateDefaultQuestionnaireExists();
        }
        // 验证该教学形式问卷是否已存在
        validateEducateFormExists(createReqVO.getTopicEducateForm(), -1L, createReqVO.getStatus());
        // 插入
        QuestionnaireManagementDO questionnaireManagement = BeanUtils.toBean(createReqVO, QuestionnaireManagementDO.class);
        questionnaireManagementMapper.insert(questionnaireManagement);
        // 新增问卷与评估项关联关系
        List<QuestionnaireDetailDO> questionnaireDetail = QuestionnaireDetailConvert.Instance.convertList(createReqVO.getQuestions());
        questionnaireDetail.forEach(questionnaireDetailDO -> questionnaireDetailDO.setQuestionnaireId(questionnaireManagement.getId()));
        questionnaireDetailMapper.insertBatch(questionnaireDetail);
        // 新增问卷的逻辑
        List<QuestionLogicDO> questionLogic = QuestionLogicConvert.Instance.convertList(createReqVO.getQuestionLogic());
        questionLogic.forEach(questionLogicDO -> questionLogicDO.setQuestionnaireId(questionnaireManagement.getId()));
        questionLogicMapper.insertBatch(questionLogic);
        // 返回
        return questionnaireManagement.getId();
    }

    private void validateEducateFormExists(String topicEducateForm, Long id, String status) {
        if(status.equals("1")) {
            List<Long> educateForms = Arrays.stream(topicEducateForm.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            List<Long> existEducateForms = getCollectingEducateForm();
            // Long count = questionnaireManagementMapper.countByEducateForm(topicEducateForm);
            // 新增
            if (id.equals(-1L)) {
                if (CollectionUtil.containsAny(existEducateForms, educateForms)) {
                    throw exception(EDUCATE_FORM_QUESTIONNAIRE_EXIST);
                }
            } else { // 修改
                QuestionnaireManagementDO questionnaireManagementDO = questionnaireManagementMapper.selectById(id);

                List<Long> questionEducateForms = Arrays.stream(questionnaireManagementDO.getTopicEducateForm().split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                List<Long> newEducateForms = educateForms.stream()
                        .filter(educateForm -> !questionEducateForms.contains(educateForm))
                        .collect(Collectors.toList());
                if (!newEducateForms.isEmpty() && CollectionUtil.containsAny(existEducateForms, newEducateForms)) {
                    throw exception(EDUCATE_FORM_QUESTIONNAIRE_EXIST);
                }
            }
        }
    }

    @Override
    public void updateQuestionnaireManagement(QuestionnaireManagementSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionnaireManagementExists(updateReqVO.getId(), true);

        // 默认问卷时查询是否已存在默认问卷
        if(updateReqVO.getIsDefault().equals("1") && updateReqVO.getStatus().equals("1")) {
            validateUpdateDefaultQuestionnaire(updateReqVO.getId());
        }

        validateEducateFormExists(updateReqVO.getTopicEducateForm(), updateReqVO.getId(), updateReqVO.getStatus());

        // 更新评估项信息
        List<QuestionnaireDetailDO> questionnaireDetails = questionnaireDetailMapper.selectByQuestionnaireId(updateReqVO.getId());
        List<Long> existDetailIds = questionnaireDetails.stream().map(QuestionnaireDetailDO::getId).collect(Collectors.toList());
        List<QuestionnaireDetailDO> updateDetails = QuestionnaireDetailConvert.Instance.convertList(updateReqVO.getQuestions());
        List<Long> updateIds = updateDetails.stream().map(QuestionnaireDetailDO::getId).collect(Collectors.toList());

        // 获取要删除的id
        List<Long> toBeDeletedIds = existDetailIds.stream()
                .filter(id -> !updateIds.contains(id))
                .collect(Collectors.toList());
        if (!toBeDeletedIds.isEmpty()) {
            questionnaireDetailMapper.deleteBatchIds(toBeDeletedIds);
        }
        // 获取要新增的评估项
        List<QuestionnaireDetailDO> toBeInserted = updateDetails.stream()
                .filter(detailDO-> detailDO.getId() == null)
                .collect(Collectors.toList());
        toBeInserted.forEach(detail -> detail.setQuestionnaireId(updateReqVO.getId()));
        questionnaireDetailMapper.insertBatch(toBeInserted);
        // 获取需要更新的评估项
        List<QuestionnaireDetailDO> toBeUpdated = updateDetails.stream()
                .filter(detailDO-> detailDO.getId() != null)
                .collect(Collectors.toList());
        if (toBeUpdated != null && !toBeUpdated.isEmpty()) {
            questionnaireDetailMapper.updateBatch(toBeUpdated);
        }
        // 更新逻辑关系
        List<QuestionLogicDO> questionLogicDOS = questionLogicMapper.selectListByQuestionnaireId(updateReqVO.getId());
        List<Integer> existLogicIds = questionLogicDOS.stream().map(QuestionLogicDO::getId).collect(Collectors.toList());
        List<QuestionLogicDO> updateLogic = QuestionLogicConvert.Instance.convertList(updateReqVO.getQuestionLogic());
        List<Integer> updateLogicIds = updateLogic.stream().map(QuestionLogicDO::getId).collect(Collectors.toList());

        // 获取要删除的逻辑id
        List<Integer> toBeDeletedLogicIds = existLogicIds.stream()
                .filter(id -> !updateLogicIds.contains(id))
                .collect(Collectors.toList());
        if (!toBeDeletedLogicIds.isEmpty()) {
            questionLogicMapper.deleteBatchIds(toBeDeletedLogicIds);
        }
        // 获取要新增的逻辑
        List<QuestionLogicDO> toBeInsertedLogic = updateLogic.stream()
                .filter(logicDO-> logicDO.getId() == null)
                .collect(Collectors.toList());
        toBeInsertedLogic.forEach(logic -> logic.setQuestionnaireId(updateReqVO.getId()));
        questionLogicMapper.insertBatch(toBeInsertedLogic);
        // 获取要更新的逻辑
        List<QuestionLogicDO> toBeUpdatedLogic = updateLogic.stream()
                .filter(logicDO-> logicDO.getId() != null)
                .collect(Collectors.toList());
        if (toBeUpdatedLogic != null && !toBeUpdatedLogic.isEmpty()) {
            questionLogicMapper.updateBatch(toBeUpdatedLogic);
        }
        // 更新
        QuestionnaireManagementDO updateObj = BeanUtils.toBean(updateReqVO, QuestionnaireManagementDO.class);
        questionnaireManagementMapper.updateById(updateObj);
    }

    @Override
    public void deleteQuestionnaireManagement(Long id) {
        // 校验存在
        validateQuestionnaireManagementExists(id, false);
        // 删除
        questionnaireManagementMapper.deleteById(id);
        // 删除对应的逻辑
        questionLogicMapper.deleteByQuestionnaireId(id);
        // 删除对应的评估项信息
        questionnaireDetailMapper.deleteByQuestionnaireId(id);
    }

    @Override
    public void batchDeleteQuestionnaireManagement(List<Long> ids) {
        // 校验能否删除
        validateQuestionnaireDeletable(ids);
        // 批量删除
        questionnaireManagementMapper.deleteBatchIds(ids);
        // 删除对应的逻辑
        questionLogicMapper.batchDeleteByQuestionnaireId(ids);
        // 删除对应的评估项信息
        questionnaireDetailMapper.batchDeleteByQuestionnaireId(ids);
    }

    private void validateQuestionnaireManagementExists(Long id, Boolean isUpdate) {
        QuestionnaireManagementDO questionnaireManagementDO = questionnaireManagementMapper.selectById(id);
        if (questionnaireManagementDO == null) {
            throw exception(QUESTIONNAIRE_MANAGEMENT_NOT_EXISTS);
        }
        if (!isUpdate && questionnaireManagementDO.getStatus().equals("1")) {
            throw exception(QUESTIONNAIRE_MANAGEMENT_PUBLISHED);
        }
    }

    private void validateDefaultQuestionnaireExists() {
        Integer count = questionnaireManagementMapper.countDefault(getTenantId());
        if (count != 0) {
            throw exception(DEFAULT_QUESTIONNAIRE_EXIST);
        }
    }

    private void validateUpdateDefaultQuestionnaire(Long id) {
        QuestionnaireManagementDO questionnaireManagementDO = questionnaireManagementMapper.selectById(id);
        if (!(questionnaireManagementDO.getIsDefault().equals("1") && questionnaireManagementDO.getStatus().equals("1"))) {
            Integer count = questionnaireManagementMapper.countDefault(getTenantId());
            if (count != 0) {
                throw exception(DEFAULT_QUESTIONNAIRE_EXIST);
            }
        }
    }


    private void validateQuestionnaireDeletable(List<Long> ids) {
        List<QuestionnaireManagementDO> questionnaireManagementDOS = questionnaireManagementMapper.selectBatchIds(ids);
        if (questionnaireManagementDOS.size() != ids.size()) {
            throw exception(QUESTIONNAIRE_MANAGEMENT_NOT_EXISTS);
        }
        Set<String> collect = questionnaireManagementDOS.stream().map(QuestionnaireManagementDO::getStatus).collect(Collectors.toSet());
        if (collect.contains("1")) {
            throw exception(QUESTIONNAIRE_MANAGEMENT_PUBLISHED);
        }
    }

    @Override
    public QuestionnaireManagementRespVO getQuestionnaireManagement(Long id, Long userId, Long classCourseId) {
        QuestionnaireManagementDO questionnaireManagement = questionnaireManagementMapper.selectById(id);
        QuestionnaireManagementRespVO questionnaireManagementRespVO = QuestionnaireDetailConvert.Instance.convertDO(questionnaireManagement);
        // 通过问卷id获取问题详情
        List<QuestionnaireDetailDO> questionnaireDetails = questionnaireDetailMapper.selectByQuestionnaireId(id);
         if (questionnaireDetails != null && questionnaireDetails.size() != 0) {
            List<Long> questionIds = questionnaireDetails.stream().map(QuestionnaireDetailDO::getQuestionId).collect(Collectors.toList());
            List<QuestionManagementDO> questionManagementDOS = questionManagementMapper.selectList(QuestionManagementDO::getId, questionIds);
            List<QuestionManagementRespVO> questionManagementRespVO = QuestionManagementConvert.Instance.convertList(questionManagementDOS);
            // 单选题获取选项
            List<OptionsDO> options = optionsMapper.selectListsByQuestionIds(questionIds);
            // 通过问卷id获取逻辑详情
            List<QuestionLogicDO> questionLogicDOS = questionLogicMapper.selectListByQuestionnaireId(id);
            questionManagementRespVO.forEach(questionManagement -> {
                // 是否一票否决、是否必选、一票否决选项
                List<QuestionnaireDetailDO> questionnaireDetail = questionnaireDetails.stream()
                        .filter(questionnaireDetailDO -> questionnaireDetailDO.getQuestionId().equals(questionManagement.getId()))
                        .collect(Collectors.toList());
                questionManagement.setDetailId(questionnaireDetail.get(0).getId());
                questionManagement.setSerialNumber(questionnaireDetail.get(0).getSerialNumber());
                questionManagement.setRequired(questionnaireDetail.get(0).getRequired());
                if (questionManagement.getQuestionType().equals("2")) {
                    questionManagement.setOneBallotVeto(questionnaireDetail.get(0).getOneBallotVeto());
                    if (questionnaireDetail.get(0).getOptionId() != null) {
                        questionManagement.setOptionId(questionnaireDetail.get(0).getOptionId());
                    }
                    // 选择题的选项
                    List<OptionsDO> optionsDOS = options.stream()
                            .filter(optionsDO -> optionsDO.getQuestionId().equals(questionManagement.getId()))
                            .collect(Collectors.toList());
                    questionManagement.setOptions(optionsDOS);
                }
                // 逻辑关联
                List<QuestionLogicDO> questionLogic = questionLogicDOS.stream()
                        .filter(questionLogicDO -> questionLogicDO.getQuestionId().equals(questionManagement.getId()))
                        .collect(Collectors.toList());
                // 被另一个问题关联
                List<QuestionLogicDO> logicQuestion = questionLogicDOS.stream()
                        .filter(questionLogicDO -> questionLogicDO.getLogicQuestionId().equals(questionManagement.getId()))
                        .collect(Collectors.toList());
                questionManagement.setQuestionLogic(QuestionLogicConvert.Instance.convertList01(questionLogic));
                questionManagement.setLogicQuestion(QuestionLogicConvert.Instance.convertList01(logicQuestion));

                // 获取用户撤回前的评价记录
                if (userId != null && classCourseId != null) {
                    List<EvaluationDetailDO> lastEvaluationDetails = evaluationDetailMapper
                            .selectLastEvaluationDetail(id, userId, classCourseId);
                    Optional<EvaluationDetailDO> lastDetail = lastEvaluationDetails.stream()
                            .filter(detail -> detail.getQuestionId().equals(questionManagement.getId()))
                            .findFirst();
                    if (lastDetail.isPresent()) {
                        EvaluationDetailDO detail = lastDetail.get();
                        if (detail.getContent() != null) {
                            questionManagement.setRateContent(detail.getContent());
                        }
                        if (detail.getScore() != null) {
                            questionManagement.setRateScore(detail.getScore().toString());
                        }
                        if (detail.getOptionId() != null) {
                            questionManagement.setRateOptionId(detail.getOptionId().toString());
                        }
                    }
                }
            });
            questionManagementRespVO = questionManagementRespVO.stream().sorted(Comparator.comparing(QuestionManagementRespVO::getSerialNumber)).collect(Collectors.toList());
            questionnaireManagementRespVO.setQuestions(questionManagementRespVO);
        }
        return questionnaireManagementRespVO;
    }

    @Override
    public QuestionnaireManagementRespVO getRatedQuestionnaireManagement(Long questionnaireId, Long userId, Long classCourseId) {
        QuestionnaireManagementRespVO result = getQuestionnaireManagement(questionnaireId, userId, classCourseId);
        if (userId == null) {
            userId = getLoginUserId();
        }
        TraineeDO traineeDO = traineeMapper.selectByUserId(userId);
        List<EvaluationDetailDO> ratedDetails = evaluationDetailMapper.selectRatedQuestionnaireDetail(questionnaireId, traineeDO.getId(), classCourseId);
        List<QuestionManagementRespVO> questions = result.getQuestions();
        questions.forEach(question -> {
            Optional<EvaluationDetailDO> details = ratedDetails.stream()
                    .filter(ratedDetail -> ratedDetail.getQuestionId().equals(question.getId()) )
                    .findFirst();
            if (details.isPresent()) {
                EvaluationDetailDO detail = details.get();

                if (detail.getContent() != null) {
                    question.setRateContent(detail.getContent());
                }
                if (detail.getScore() != null) {
                    question.setRateScore(detail.getScore().toString());
                }
                if (detail.getOptionId() != null) {
                    question.setRateOptionId(detail.getOptionId().toString());
                }
            }
        });
        result.setQuestions(questions);
        return result;
    }

    @Override
    public PageResult<QuestionnaireManagementRespVO> getQuestionnaireManagementPage(QuestionnaireManagementPageReqVO pageReqVO) {
        IPage<QuestionnaireManagementRespVO> page = MyBatisUtils.buildPage(pageReqVO);
        Long count = questionnaireManagementMapper.countBuiltId();
        // 没有内置问卷就新建一个
        if (count == 0) {
            QuestionnaireManagementDO questionnaireManagement = new QuestionnaireManagementDO()
                    .setBuiltIn(true)
                    .setIsDefault("0")
                    .setStatus("0")
                    .setTitle("讲授式课堂教学评价表")
                    .setTopicEducateForm("1632")
                    .setLowscore(70)
                    .setLowscoreTag(true)
                    .setLowword(0)
                    .setLowwordTag(true)
                    .setTimeTag(true)
                    .setTimeLimit(3);
            questionnaireManagementMapper.insert(questionnaireManagement);
            Long builtInCategoryId = questionCategoryManagementMapper.getBuiltInCategoryId();
            if (builtInCategoryId == null) {
                List<QuestionCategoryManagementDO> questionCategoryManagementList = questionCategoryManagementService.getQuestionCategoryManagementList(new QuestionCategoryManagementListReqVO().setBuiltIn(true));
                builtInCategoryId = questionCategoryManagementList.get(0).getId();
            }
            List<Long> questionIds = questionManagementMapper.selectBuiltInQuestionId(builtInCategoryId);
            List<Long> optionIds = optionsMapper.selectOptionId(questionIds.get(1));
            QuestionnaireDetailDO questionnaireDetail0 = new QuestionnaireDetailDO()
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setQuestionId(questionIds.get(1))
                    .setOneBallotVeto(true)
                    .setRequired(true)
                    .setSerialNumber(0L)
                    .setOptionId(optionIds.get(1));
            questionnaireDetailMapper.insert(questionnaireDetail0);
            QuestionnaireDetailDO questionnaireDetail1 = new QuestionnaireDetailDO()
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setQuestionId(questionIds.get(0))
                    .setOneBallotVeto(false)
                    .setRequired(false)
                    .setSerialNumber(1L);
            questionnaireDetailMapper.insert(questionnaireDetail1);
            QuestionnaireDetailDO questionnaireDetail2 = new QuestionnaireDetailDO()
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setQuestionId(questionIds.get(4))
                    .setOneBallotVeto(false)
                    .setRequired(true)
                    .setSerialNumber(2L);
            questionnaireDetailMapper.insert(questionnaireDetail2);
            QuestionnaireDetailDO questionnaireDetail3 = new QuestionnaireDetailDO()
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setQuestionId(questionIds.get(3))
                    .setOneBallotVeto(false)
                    .setRequired(false)
                    .setSerialNumber(3L);
            questionnaireDetailMapper.insert(questionnaireDetail3);
            QuestionnaireDetailDO questionnaireDetail4 = new QuestionnaireDetailDO()
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setQuestionId(questionIds.get(2))
                    .setOneBallotVeto(false)
                    .setRequired(false)
                    .setSerialNumber(4L);
            questionnaireDetailMapper.insert(questionnaireDetail4);
            // 新增问卷的逻辑
            QuestionLogicDO questionLogic = new QuestionLogicDO()
                    .setQuestionId(questionIds.get(1))
                    .setLogicQuestionId(questionIds.get(0))
                    .setQuestionnaireId(questionnaireManagement.getId())
                    .setScore(0)
                    .setOption(optionIds.get(1));
            questionLogicMapper.insert(questionLogic);
        }
        List<QuestionnaireManagementRespVO> pageResult = questionnaireManagementMapper.selectPageByPageVO(page, pageReqVO);
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(null ,
                page.getTotal(),
                pageReqVO,
                pageResult.size());
        for (int i = 0; i < pageResult.size(); i++) {
            pageResult.get(i).setSerialNumber(serialNumberList.get(i));
        }
        if(pageReqVO.getIsSerialDesc() == null || pageReqVO.getIsSerialDesc() == false) {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(QuestionnaireManagementRespVO::getSerialNumber)
            ).collect(Collectors.toList());
        } else {
            pageResult = pageResult.parallelStream().sorted(
                    Comparator.comparing(QuestionnaireManagementRespVO::getSerialNumber).reversed()
            ).collect(Collectors.toList());
        }
        return new PageResult<>(pageResult, page.getTotal());
    }

    @Override
    public List<QuestionRespVO> getQuestionList(QuestionListReqVO listReqVO) {
        List<QuestionRespVO> categories = questionCategoryManagementMapper.selectCategoryList();
        List<QuestionRespVO> questions = questionManagementMapper.selectQuestionList();
        List<Long> choices = questions.stream()
                .filter(questionRespVO -> questionRespVO.getQuestionType().equals("2"))
                .map(QuestionRespVO::getQuestionId)
                .collect(Collectors.toList());
        List<OptionsDO> optionsDOS = optionsMapper.selectListsByQuestionIds(choices);
        questions.forEach(questionRespVO -> {
            if (questionRespVO.getQuestionType().equals("2")) {
                List<OptionsDO> options = optionsDOS.stream()
                        .filter(optionsDO -> optionsDO.getQuestionId().equals(questionRespVO.getQuestionId()))
                        .collect(Collectors.toList());
                questionRespVO.setOptions(options);
            }
        });
        categories.forEach(category -> category.setType(0L));
        questions.forEach(question -> question.setType(1L));
        List<QuestionRespVO> result = new ArrayList<>();
        result.addAll(categories);
        result.addAll(questions);
        return result;
    }

    @Override
    public Long getByEducateForm(Long educateFormId, Long tenantId) {
        return questionnaireManagementMapper.getByEducateForm(educateFormId, tenantId);
    }

    @Override
    public Long getDefaultQuestionnaireId(Long tenantId) {
        return questionnaireManagementMapper.getDefaultQuestionnaire(tenantId);
    }

    @Override
    public List<Long> getCollectingEducateForm() {
        Long tenantId = getTenantId();
        List<String> collectingEducateForm = questionnaireManagementMapper.getCollectingEducateForm(tenantId);
        List<Long> educateForm = new ArrayList<>();
        collectingEducateForm.forEach(collecting -> {
            List<Long> collect = Arrays.stream(collecting.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            educateForm.addAll(collect);
        });
        return educateForm;
    }
}
