package com.unicom.swdx.module.edu.dal.mysql.questionnairemanagement;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairemanagement.QuestionnaireManagementDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 评估问卷管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionnaireManagementMapper extends BaseMapperX<QuestionnaireManagementDO> {

    default PageResult<QuestionnaireManagementDO> selectPage(QuestionnaireManagementPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionnaireManagementDO>()
                .eqIfPresent(QuestionnaireManagementDO::getTitle, reqVO.getTitle())
                .eqIfPresent(QuestionnaireManagementDO::getIsDefault, reqVO.getIsDefault())
                .eqIfPresent(QuestionnaireManagementDO::getStatus, reqVO.getStatus())
                .eqIfPresent(QuestionnaireManagementDO::getCreateDept, reqVO.getCreateDept())
                .betweenIfPresent(QuestionnaireManagementDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(QuestionnaireManagementDO::getCreator, reqVO.getCreator())
                .eqIfPresent(QuestionnaireManagementDO::getRemark, reqVO.getRemark())
                .eqIfPresent(QuestionnaireManagementDO::getTopicEducateForm, reqVO.getTopicEducateForm())
                .eqIfPresent(QuestionnaireManagementDO::getLowscore, reqVO.getLowscore())
                .eqIfPresent(QuestionnaireManagementDO::getLowscoreTag, reqVO.getLowscoreTag())
                .eqIfPresent(QuestionnaireManagementDO::getLowword, reqVO.getLowword())
                .eqIfPresent(QuestionnaireManagementDO::getLowwordTag, reqVO.getLowwordTag())
                .eqIfPresent(QuestionnaireManagementDO::getTimeTag, reqVO.getTimeTag())
                .eqIfPresent(QuestionnaireManagementDO::getTimeLimit, reqVO.getTimeLimit())
                .orderByDesc(QuestionnaireManagementDO::getId));
    }

    List<QuestionnaireManagementRespVO> selectPageByPageVO(IPage<QuestionnaireManagementRespVO> page, @Param("reqVO") QuestionnaireManagementPageReqVO pageReqVO);

    Integer countDefault(@Param("tenantId") Long tenantId);

    Long getByEducateForm(@Param("educateFormId") Long educateFormId, @Param("tenantId") Long tenantId);

    Long getDefaultQuestionnaire(@Param("tenantId") Long tenantId);

    Long countByEducateForm(@Param("educateForm") String topicEducateForm);

    List<String> getCollectingEducateForm(@Param("tenantId") Long tenantId);

    Long countBuiltId();
}
