package com.unicom.swdx.module.edu.service.rollcallrecord;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.module.edu.controller.admin.ruletemplate.vo.RuleTemplateRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallrecord.RollcallRecordDO;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallsignin.RollcallSignInDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.dal.mysql.rollcallrecord.RollcallRecordMapper;
import com.unicom.swdx.module.edu.dal.mysql.rollcallsignin.RollcallSignInMapper;
import com.unicom.swdx.module.edu.dal.mysql.traineeleave.TraineeLeaveMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.service.ruletemplate.RuleTemplateService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 学员点名签到记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RollcallRecordServiceImpl extends ServiceImpl<RollcallRecordMapper, RollcallRecordDO> implements RollcallRecordService {

    @Resource
    private RollcallRecordMapper rollcallRecordMapper;

    @Resource
    private ClockInInfoMapper clockInInfoMapper;

    @Resource
    private RollcallSignInMapper rollcallSignInMapper;

    @Resource
    private TraineeLeaveMapper traineeLeaveMapper;

    @Resource
    private ClassCourseMapper classCourseMapper;

    @Resource
    private RuleTemplateService ruleTemplateService;

    @Resource
    private TraineeMapper traineeMapper;

    /**
     * 获取签到详情-未签到、已签到人员信息
     *
     * @param id     点名签到id
     * @param status 签到状态筛选 0-未签到 1-已签到
     * @return 签到详情学员详情列表
     */
    @Override
    public List<AppTraineeGroupRespVO> getRollcallSignInTraineeInfo(Long id, Integer status) {
        List<AppTraineeGroupRespVO> rollcallSignInTraineeInfo = rollcallRecordMapper.getRollcallSignInTraineeInfo(id, status);
        if (Objects.isNull(rollcallSignInTraineeInfo) || rollcallSignInTraineeInfo.isEmpty()){
            return Collections.emptyList();
        }

        // 获取学员ID列表
        List<Long> traineeIds = rollcallSignInTraineeInfo.stream().map(AppTraineeGroupRespVO::getId).collect(Collectors.toList());
        //获取点名签到时间
        RollcallSignInDO rollcallSignInDO = rollcallSignInMapper.selectById(id);
        // 获取在这个大课考勤时间范围内的请假学员
        List<TraineeLeaveDO> traineeLeaveDOS = traineeLeaveMapper.selectListByTraineeIdListAndTime(traineeIds,
                rollcallSignInDO.getCheckStartTime(),
                rollcallSignInDO.getCheckEndTime(),
                null);
        // 学员id -> 学员请假状态映射
        Map<Long, Integer> traineeLeaveMap = traineeLeaveDOS.stream()
                .collect(Collectors.toMap(TraineeLeaveDO::getTraineeId,
                        TraineeLeaveDO::getStatus,
                        (v1, v2) -> v2));
        /*
         * 组装学员请假状态返回
         * 0草稿1已撤回2待审批3审批中4已通过5已拒绝
         * 2待审批3审批中4已通过 前端会回显
         * 0草稿1已撤回5已拒绝 null前端都不显示
         */
        rollcallSignInTraineeInfo.forEach(o -> {
            Integer leaveStatus = traineeLeaveMap.getOrDefault(o.getId(), null);
            o.setLeaveStatus(leaveStatus);
        });
        return rollcallSignInTraineeInfo;
    }

    /**
     * 获取签到详情-未签到、已签到人员信息
     *
     * @param id     大课考勤id
     * @param status 签到状态筛选 0-未签到 1-已签到
     * @return 签到详情学员详情列表
     */
    @Override
    public List<AppTraineeGroupRespVO> getLectureAttendanceTraineeInfo(Long id, Integer status) {
        // 获取大课考勤信息
        RollcallSignInDO rollcallSignInDO = rollcallSignInMapper.selectById(id);
        if (Objects.isNull(rollcallSignInDO)){
            throw exception(ROLLCALL_SIGN_IN_NOT_EXISTS);
        }
        // 根据大课考勤的班级id和班级课表id查询班级签到信息
        List<AppTraineeGroupRespVO> lectureAttendanceTraineeInfo = clockInInfoMapper.getLectureAttendanceTraineeInfo(rollcallSignInDO.getClassId(),
                rollcallSignInDO.getClassCourseId(), status);
        if (Objects.isNull(lectureAttendanceTraineeInfo) || lectureAttendanceTraineeInfo.isEmpty()){
            return Collections.emptyList();
        }
        // 获取学员ID列表
        List<Long> traineeIds = lectureAttendanceTraineeInfo.stream().map(AppTraineeGroupRespVO::getId).collect(Collectors.toList());
        // 获取课程开始时间、结束时间信息
        ClassCourseDO classCourseDO = classCourseMapper.selectById(rollcallSignInDO.getClassCourseId());
        if (Objects.isNull(classCourseDO)){
            throw exception(CLASS_COURSE_NOT_EXISTS);
        }
        // 获取班级的到课考勤签到规则模板信息
        RuleTemplateRespVO courseRuleTemplate = ruleTemplateService.getAttendanceCheckRuleTemplateByClassId(rollcallSignInDO.getClassId());
        // 到课考勤签到时间段
        LocalDateTime checkStartTime = classCourseDO.getBeginTime().minusMinutes(courseRuleTemplate
                .getBeforeClassTime().longValue());
        LocalDateTime checkEndTime = classCourseDO.getBeginTime().plusMinutes(courseRuleTemplate
                .getAfterClassTime().longValue());
        // 获取该课请假的学员 （课程考勤开始时间在请假时间段内的请假学员）
        List<TraineeLeaveDO> traineeLeaveDOS = traineeLeaveMapper.selectListByTraineeIdListAndStartTime(traineeIds, checkStartTime, null);

        // 学员id -> 学员请假状态映射
        Map<Long, Integer> traineeLeaveMap = traineeLeaveDOS.stream()
                .collect(Collectors.toMap(TraineeLeaveDO::getTraineeId,
                        TraineeLeaveDO::getStatus, (v1, v2) -> v2));
        /*
         * 组装学员请假状态返回
         * 0草稿1已撤回2待审批3审批中4已通过5已拒绝
         * 2待审批3审批中4已通过 前端会回显
         * 0草稿1已撤回5已拒绝 null前端都不显示
         */
        lectureAttendanceTraineeInfo.forEach(o -> {
            Integer leaveStatus = traineeLeaveMap.getOrDefault(o.getId(), null);
            o.setLeaveStatus(leaveStatus);

            // 新增头像赋值逻辑
            // 直接通过o.getId查询TraineeDO
            TraineeDO traineeDO = traineeMapper.selectById(o.getId());
            if (traineeDO != null && traineeDO.getPhoto() != null) {
                o.setAvatar(traineeDO.getPhoto());
            }
        });
        return lectureAttendanceTraineeInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void deleteRollCallRecordByTraineeId(Long traineeId) {
        List<RollcallRecordDO> list = rollcallRecordMapper.getRollcallRecordByTraineeId(traineeId);

        if (CollUtil.isEmpty(list)){
            return;
        }
        this.removeBatchByIds(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void deleteRollCallRecordByTraineeIds(List<Long> traineeIds) {
        List<RollcallRecordDO> list = rollcallRecordMapper.getRollcallRecordByTraineeIds(traineeIds);

        if (CollUtil.isEmpty(list)){
            return;
        }
        this.removeBatchByIds(list);
    }

    private void validateRollcallRecordExists(Long id) {
        if (rollcallRecordMapper.selectById(id) == null) {
            throw exception(ROLLCALL_RECORD_NOT_EXISTS);
        }
    }
}
