package com.unicom.swdx.module.edu.dal.mysql.signupunit;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoImportDictLableExcelVO;
import com.unicom.swdx.module.edu.controller.admin.signupunit.vo.SignUpUnitPageReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO;
import com.unicom.swdx.module.edu.enums.signunit.UnitTemplateEnum;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.*;
import java.util.stream.Collectors;

/**
 * EduSignUpUnit Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SignUpUnitMapper extends BaseMapperX<SignUpUnitDO> {

    default PageResult<SignUpUnitDO> selectPage(SignUpUnitPageReqVO reqVO) {

        LambdaQueryWrapperX<SignUpUnitDO> wrapperX = new LambdaQueryWrapperX<SignUpUnitDO>()
                .likeIfPresent(SignUpUnitDO::getUnitName, reqVO.getUnitName())
                .eqIfPresent(SignUpUnitDO::getUnitChargePeople, reqVO.getUnitChargePeople())
                .eqIfPresent(SignUpUnitDO::getPhone, reqVO.getPhone())
                .eqIfPresent(SignUpUnitDO::getStatus, reqVO.getStatus())
                .eqIfPresent(SignUpUnitDO::getCapacity, reqVO.getCapacity())
                .eqIfPresent(SignUpUnitDO::getClassId, reqVO.getClassId())
                .eqIfPresent(SignUpUnitDO::getUnitClassification, reqVO.getUnitClassification())
                .eq(SignUpUnitDO::getTemplate, UnitTemplateEnum.TEMPLATE.getStatus());

        if (Objects.equals(reqVO.getSort(), 1)) {
            wrapperX.orderByAsc(SignUpUnitDO::getSort)
                    .orderByAsc(SignUpUnitDO::getCreateTime)
                    .orderByAsc(SignUpUnitDO::getId);
        } else {
            wrapperX.orderByDesc(SignUpUnitDO::getSort)
                    .orderByDesc(SignUpUnitDO::getCreateTime)
                    .orderByDesc(SignUpUnitDO::getId);
        }
        return selectPage(reqVO, wrapperX);

    }

    /**
     * 更新限制状态
     *
     * @param id sort
     * @return
     */
    Integer updateSignUpUnitRestrictById(@Param("id") Integer id, @Param("restrict") Integer restrict);

    /**
     * 唯一性校验
     *
     * @param unitName
     * @return
     */
//    default SignUpUnitDO selectByUnitName(String UnitName) {
//        return selectOne(SignUpUnitDO::getUnitName, UnitName);
//    }

    SignUpUnitDO selectByUnitName(@Param("unitName") String unitName, @Param("unitId") Integer unitId);


    /**
     * 根据班级id查询
     *
     * @param classId 班级id
     * @return SignUpUnitDO 列表
     */
    default List<SignUpUnitDO> getByClassId(Integer classId) {
        LambdaQueryWrapper<SignUpUnitDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SignUpUnitDO::getClassId, classId);

        return this.selectList(wrapper);
    }

    /**
     * 遍历单位表
     *
     * @param
     * @return SignUpUnitDO 列表
     */
    List<SignUpUnitDO> getSignUpUnitDOList();

    /**
     * 删除单位表中对应的单位
     *
     * @param
     * @return
     */
    void deleteByClassId(@Param("id") Long id);

    /**
     * 获取单位表中的信息
     *
     * @param
     * @return
     */
    SignUpUnitDO selectByClassIdAndUnitName(@Param("classId") Long classId, @Param("unitName") String unitName);
    SignUpUnitDO selectByClassIdAndParentId(@Param("classId") Long classId, @Param("unitId") Integer unitId);

    List<ClassInfoImportDictLableExcelVO> getUnitType();

    /**
     * 获取单位表中的信息
     *
     * @param
     * @return
     */
    @MapKey("label")
    Map<String, Map<String, Long>> getSignUnitClassification();

    /**
     * 获取所有单位名称
     *
     * @return
     */
    default Set<String> getAllUnitName() {

        LambdaQueryWrapper<SignUpUnitDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SignUpUnitDO::getTemplate, 1);
        wrapper.eq(SignUpUnitDO::getClassId, 0);

        List<SignUpUnitDO> list = selectList(wrapper);
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newHashSet();
        }
        return list.stream().map(SignUpUnitDO::getUnitName).collect(Collectors.toSet());
    }

    /**
     * 导出单位信息
     *
     * @param reqVO 查询条件
     * @return 单位信息
     */
    default List<SignUpUnitDO> selectExportUnitList(SignUpUnitPageReqVO reqVO) {
        LambdaQueryWrapperX<SignUpUnitDO> wrapperX = new LambdaQueryWrapperX<>();

        if (CollUtil.isNotEmpty(reqVO.getIdList())) {
            wrapperX.in(SignUpUnitDO::getId, reqVO.getIdList());
        } else {
            wrapperX.likeIfPresent(SignUpUnitDO::getUnitName, reqVO.getUnitName())
                    .eqIfPresent(SignUpUnitDO::getUnitChargePeople, reqVO.getUnitChargePeople())
                    .eqIfPresent(SignUpUnitDO::getPhone, reqVO.getPhone())
                    .eqIfPresent(SignUpUnitDO::getStatus, reqVO.getStatus())
                    .eqIfPresent(SignUpUnitDO::getCapacity, reqVO.getCapacity())
                    .eqIfPresent(SignUpUnitDO::getClassId, reqVO.getClassId())
                    .eqIfPresent(SignUpUnitDO::getUnitClassification, reqVO.getUnitClassification())
                    .eq(SignUpUnitDO::getTemplate, UnitTemplateEnum.TEMPLATE.getStatus());
        }

        if (Objects.equals(reqVO.getSort(), 1)) {
            wrapperX.orderByAsc(SignUpUnitDO::getSort)
                    .orderByAsc(SignUpUnitDO::getCreateTime)
                    .orderByAsc(SignUpUnitDO::getId);
        } else {
            wrapperX.orderByDesc(SignUpUnitDO::getSort)
                    .orderByDesc(SignUpUnitDO::getCreateTime)
                    .orderByDesc(SignUpUnitDO::getId);
        }

        return selectList(wrapperX);
    }

    /**
     * 根据id查询单位信息
     *
     * @param reqVO 查询条件
     * @return 单位信息
     */
    default List<SignUpUnitDO> selectUnitList(SignUpUnitPageReqVO reqVO) {
        LambdaQueryWrapperX<SignUpUnitDO> wrapperX = new LambdaQueryWrapperX<>();

        if (CollUtil.isNotEmpty(reqVO.getIdList())) {
            wrapperX.in(SignUpUnitDO::getId, reqVO.getIdList());
        } else {
            wrapperX.likeIfPresent(SignUpUnitDO::getUnitName, reqVO.getUnitName())
                    .eqIfPresent(SignUpUnitDO::getUnitChargePeople, reqVO.getUnitChargePeople())
                    .eqIfPresent(SignUpUnitDO::getPhone, reqVO.getPhone())
                    .eqIfPresent(SignUpUnitDO::getStatus, reqVO.getStatus())
                    .eqIfPresent(SignUpUnitDO::getCapacity, reqVO.getCapacity())
                    .eqIfPresent(SignUpUnitDO::getClassId, reqVO.getClassId())
                    .eqIfPresent(SignUpUnitDO::getUnitClassification, reqVO.getUnitClassification());
        }

        if (Objects.equals(reqVO.getSort(), 1)) {
            wrapperX.orderByAsc(SignUpUnitDO::getSort).orderByAsc(SignUpUnitDO::getId);
        } else {
            wrapperX.orderByDesc(SignUpUnitDO::getSort).orderByDesc(SignUpUnitDO::getId);
        }

        return selectList(wrapperX);
    }

    /**
     * 获取所有template=1的单位
     *
     * @return 单位列表
     */
    default List<SignUpUnitDO> getAllTemplateList() {
        return selectList(new LambdaQueryWrapperX<SignUpUnitDO>()
                .eq(SignUpUnitDO::getTemplate, UnitTemplateEnum.TEMPLATE.getStatus()));
    }

    /**
     * 根据userId获取单位
     *
     * @param userId 用户id
     * @return 单位
     */
    default SignUpUnitDO getByUserId(Long userId) {
        return selectOne(new LambdaQueryWrapperX<SignUpUnitDO>()
                .eq(SignUpUnitDO::getUserId, userId)
                .eq(SignUpUnitDO::getTemplate, UnitTemplateEnum.TEMPLATE.getStatus()));
    }

    /**
     * 根据单位的parentId获取所有子单位的id
     *
     * @param parentId 父单位id
     * @return 子单位id列表
     */
    default List<Long> getUnitIdByParentId(Long parentId) {
        return selectList(new LambdaQueryWrapper<SignUpUnitDO>()
                .eq(SignUpUnitDO::getParentId, parentId))
                .stream()
                .map(SignUpUnitDO::getId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    default List<SignUpUnitDO> getAllUnitByClassId(Long classId) {
        LambdaQueryWrapper<SignUpUnitDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SignUpUnitDO::getClassId, classId);
        wrapper.eq(SignUpUnitDO::getTemplate, UnitTemplateEnum.NOT_TEMPLATE.getStatus());
        return selectList(wrapper);

    }

    /**
     * 根据手机号获取单位数量
     *
     * @param phone 手机号
     * @return 单位数量
     */
    Integer countByPhone(@Param("phone") String phone);
    /**
     * 根据手机号获取单位数量 编辑时过滤自身
     *
     * @param phone 手机号
     * @return 单位数量
     */
    Integer countByPhoneAndUnitId(@Param("phone") String phone, @Param("id") Integer id);

    /**
     * template=1的所有单位中根据用户名获取单位
     *
     * @param username
     * @return
     */
    default List<SignUpUnitDO> selectByUsernameFromTemplate(String username) {
        return selectList(new LambdaQueryWrapper<SignUpUnitDO>()
                .eq(SignUpUnitDO::getUsername, username)
                .eq(SignUpUnitDO::getTemplate, UnitTemplateEnum.TEMPLATE.getStatus()));
    }

    /**
     * template=1的所有单位中根据用户名获取单位
     *
     * @return
     */
    default List<SignUpUnitDO> selectAllTemplateByClassId() {
        return selectList(new LambdaQueryWrapper<SignUpUnitDO>()
                .eq(SignUpUnitDO::getTemplate, UnitTemplateEnum.TEMPLATE.getStatus()));
    }

    /**
     * template=1的所有单位中根据userId获取单位
     *
     * @param userId 用户id
     * @return 单位列表
     */
    default SignUpUnitDO selectByUserIdFromTemplate(Long userId) {
        return selectOne(new LambdaQueryWrapper<SignUpUnitDO>()
                .eq(SignUpUnitDO::getUserId, userId)
                .eq(SignUpUnitDO::getTemplate, UnitTemplateEnum.TEMPLATE.getStatus())
                .orderByDesc(SignUpUnitDO::getCreateTime)
                .last("limit 1"));
    }

    /**
     * template=1的所有单位中根据userIds获取单位
     *
     * @param userIds 用户id列表
     * @return 单位列表
     */
    default List<SignUpUnitDO> selectListByUserIdListFromTemplate(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapper<SignUpUnitDO>()
                .in(SignUpUnitDO::getUserId, userIds)
                .eq(SignUpUnitDO::getTemplate, UnitTemplateEnum.TEMPLATE.getStatus()));
    }

    /**
     * 获取所有单位的手机号
     *
     * @return 手机号列表
     */
    List<String> getPhoneList();

    /**
     * 根据单位的parentId获取所有子单位
     * @param parentIds 父单位id列表
     * @return 子单位列表
     */
    default List<SignUpUnitDO> selectByParentId(List<Integer> parentIds) {
        if (CollUtil.isEmpty(parentIds)){
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapper<SignUpUnitDO>()
                .in(SignUpUnitDO::getParentId, parentIds));
    }
}
