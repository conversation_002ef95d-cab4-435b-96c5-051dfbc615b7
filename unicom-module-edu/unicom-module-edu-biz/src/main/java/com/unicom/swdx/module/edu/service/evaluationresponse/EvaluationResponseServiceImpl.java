package com.unicom.swdx.module.edu.service.evaluationresponse;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.common.util.desensitize.DesensitizeUtils;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.ClassCourseExportReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.dto.EvaluationRankScoreDetailDTO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationPageRespVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationStatsExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.RankDetailVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationRespVO;
import com.unicom.swdx.module.edu.convert.evaluation.EvaluationResponseConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse.EvaluationResponseDO;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairemanagement.QuestionnaireManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.dal.mysql.courses.CoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.evaluationdetail.EvaluationDetailMapper;
import com.unicom.swdx.module.edu.dal.mysql.evaluationresponse.EvaluationResponseMapper;
import com.unicom.swdx.module.edu.dal.mysql.questionnairemanagement.QuestionnaireManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.courses.CoursesDictEnum;
import com.unicom.swdx.module.edu.enums.courses.CoursesTypeEnum;
import com.unicom.swdx.module.edu.enums.electiverelease.ClassDayPeriodEnum;
import com.unicom.swdx.module.edu.enums.evaluationresponse.EvaluationStatusEnum;
import com.unicom.swdx.module.edu.enums.ratio.RatioValValueEnum;
import com.unicom.swdx.module.edu.utils.tree.TreeDataUtil;
import com.unicom.swdx.module.edu.utils.tree.dto.TreeNodeDTO;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.EvaluationRevokeReqVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE;
import static com.unicom.swdx.framework.web.core.util.WebFrameworkUtils.getLoginUserId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.edu.utils.serialnumber.PageDataSerialNumberUtil.generateSerialNumberList;

/**
 * 课程评价记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EvaluationResponseServiceImpl implements EvaluationResponseService {

    @Resource
    private EvaluationResponseMapper evaluationResponseMapper;

    @Resource
    private EvaluationDetailMapper evaluationDetailMapper;

    @Resource
    private QuestionnaireManagementMapper questionnaireManagementMapper;

    @Resource
    private ClassCourseMapper classCourseMapper;

    @Resource
    private CoursesMapper coursesMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;

    @Resource
    private DictDataApi dictDataApi;


    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private ClockInInfoMapper clockInInfoMapper;

    // 排名分上限
    private static final float MAX_RANK_SCORE = 100.00F;
    // 排名分下限
    private static final float MIN_RANK_SCORE = 70.00F;

    private static final String ZERO_RATE_STRING = "0.00";

    private static final Float ZERO_FLOAT = 0.00F;

    // 省委仪表盘固定部门
    private static final Map<Long, String> BoardDeptMap = new HashMap<>();

    @PostConstruct
    public void initBoardDeptMap() {
        BoardDeptMap.put(436L,"湖南干部教育与人才发展研究中心");
        BoardDeptMap.put(214L,"妇女理论教研部（湖南省妇女干部学校）");
        BoardDeptMap.put(212L,"青年与社会建设教研部（湖南省共产主义青年团学校）");
        BoardDeptMap.put(208L,"公共管理教研部（湖南行政管理学会）");
        BoardDeptMap.put(211L,"科技与生态文明教研部");
        BoardDeptMap.put(228L,"湖南经济社会发展研究中心");
        BoardDeptMap.put(437L,"纪检监察教研部");
        BoardDeptMap.put(205L,"党史党建教研部");
        BoardDeptMap.put(204L,"经济学教研部");
        BoardDeptMap.put(203L,"哲学教研部");
        BoardDeptMap.put(227L,"马克思主义学院");
        BoardDeptMap.put(210L,"文史教研部（湖南省民族干部学校）");
        BoardDeptMap.put(207L,"科学社会主义教研部（政治学教研部）");
        BoardDeptMap.put(209L,"法学教研部");
    }



    @Override
    public Long createEvaluationResponse(EvaluationResponseSaveReqVO createReqVO) {
        // 插入
        EvaluationResponseDO evaluationResponse = BeanUtils.toBean(createReqVO, EvaluationResponseDO.class);
        evaluationResponseMapper.insert(evaluationResponse);
        // 返回
        return evaluationResponse.getId();
    }

    @Override
    public void updateEvaluationResponse(EvaluationResponseSaveReqVO updateReqVO) {
        // 校验存在
        validateEvaluationResponseExists(updateReqVO.getId());
        // 更新
        EvaluationResponseDO updateObj = BeanUtils.toBean(updateReqVO, EvaluationResponseDO.class);
        evaluationResponseMapper.updateById(updateObj);
    }

    @Override
    public void deleteEvaluationResponse(Long id) {
        // 校验存在
        validateEvaluationResponseExists(id);
        // 删除
        evaluationResponseMapper.deleteById(id);
    }

    private void validateEvaluationResponseExists(Long id) {
        if (evaluationResponseMapper.selectById(id) == null) {
            throw exception(EVALUATION_RESPONSE_NOT_EXISTS);
        }
    }

    @Override
    public EvaluationResponseDO getEvaluationResponse(Long id) {
        return evaluationResponseMapper.selectById(id);
    }

    @Override
    public PageResult<EvaluationResponseDO> getEvaluationResponsePage(EvaluationResponsePageReqVO pageReqVO) {
        return evaluationResponseMapper.selectPage(pageReqVO);
    }

    /**
     * 我的评估-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    @Override
    public PageResult<MyEvaluationPageRespVO> getMyEvaluationPage(MyEvaluationPageReqVO reqVO) {
        // 该接口由于需要计算排名, 所以分页方式得为 查全部，再裁剪分页，所以此处不使用分页插件

        // 校验教师权限
        if (Boolean.TRUE.equals(reqVO.getIsTeacher()) && CollectionUtil.isEmpty(reqVO.getTeacherIdList())) {
            TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectByUserId(getLoginUserId());
            if (Objects.isNull(teacherInformationDO)) {
                return PageResult.empty();
            }
            reqVO.setTeacherId(teacherInformationDO.getId());
        }

        // 统计数据 由于计算班级排名需要全量数据 该处sql仅筛选时间
        List<MyEvaluationPageRespVO> sqlList = evaluationResponseMapper.getMyEvaluationPage(reqVO);

        // 应用筛选条件
        List<MyEvaluationPageRespVO> tempList = filterMyEvaluationRespVO(reqVO, sqlList);

        List<MyEvaluationPageRespVO> list = tempList;

        // ManyIds获取指定结果列表 teacherId, courseId, classCourseId
        if (CollectionUtil.isNotEmpty(reqVO.getManyIds())){
            list = tempList.stream().filter(o-> reqVO.getManyIds().stream().anyMatch(manyId -> Objects.equals(o.getTeacherId(), manyId.getTeacherId()) &&
                    Objects.equals(o.getClassCourseId(), manyId.getClassCourseId()) &&
                    Objects.equals(o.getCourseId(), manyId.getCourseId()))).collect(Collectors.toList());
        }


        // list 按照班级分组
        Map<Long, List<MyEvaluationPageRespVO>> classIdToData = sqlList.stream()
                .collect(Collectors.groupingBy(MyEvaluationPageRespVO::getClassId));

        // 组内按照平均分降序排序
        classIdToData.forEach((classId, data) -> data.sort(Comparator.comparing(MyEvaluationPageRespVO::getAverageScore).reversed()));

        Map<Long, String> map;
        if (Objects.isNull(reqVO.getSetEducateForm()) || Boolean.TRUE.equals(reqVO.getSetEducateForm())) {
            // 获取课程分类 教学形式 业中同步过来的字典数据
            List<DictDataRespDTO> dictData = dictDataApi.getByDictTypes(Collections.singletonList(CoursesDictEnum.EDUCATE_FORM.getType())).getCheckedData();
            // 字典数据列表转树节点用于计算
            List<TreeNodeDTO> treeNodeDTOList = packageDictDataAsTreeNodeDTOList(dictData);
            // 课程分类、教学形式 字典id->完整路径表示ID
            map = TreeDataUtil.generateNodeIdToFullPathMap(treeNodeDTOList);
        } else {
            map = new HashMap<>();
        }

        list.forEach(vo -> {
            // 授课时间组装
            String classDuration = vo.getClassDate() +
                    " " + ClassDayPeriodEnum.getDescByPeriod(vo.getDayPeriod()) +
                    " " + DateUtils.format(vo.getClassStartTime(), FORMAT_HOUR_MINUTE) +
                    "-" + DateUtils.format(vo.getClassEndTime(), FORMAT_HOUR_MINUTE);
            vo.setClassStartTimeStr(DateUtils.format(vo.getClassStartTime(), FORMAT_HOUR_MINUTE));
            vo.setClassEndTimeStr(DateUtils.format(vo.getClassEndTime(), FORMAT_HOUR_MINUTE));
            vo.setClassDuration(classDuration);
            // 限制比率最大值
            vo.setRatio(RatioValValueEnum.getValidValueByDecimal(vo.getRatio()));
            vo.setAverageScore(vo.getAverageScore());
            if (Objects.nonNull(vo.getRatio())) {
                // 参评率两位小数
                vo.setRatioStr(String.format("%.2f", vo.getRatio() * 100));
            } else {
                vo.setRatioStr(ZERO_RATE_STRING);
                vo.setRatio(ZERO_FLOAT);
            }
            if (Objects.nonNull(vo.getAverageScore())) {
                // 平均分两位小数
                vo.setAverageScoreStr(String.format("%.2f", vo.getAverageScore()));
            } else {
                vo.setAverageScoreStr(ZERO_RATE_STRING);
                vo.setAverageScore(ZERO_FLOAT);
            }

            // 组装排名和排名分
            // 获取当前课程排名
            List<Float> averageScoreList = classIdToData.get(vo.getClassId()).stream()
                    .map(MyEvaluationPageRespVO::getAverageScore).distinct().collect(Collectors.toList());
            int rank = averageScoreList.indexOf(vo.getAverageScore()) + 1;
            int size = averageScoreList.size();
            vo.setRankStr(rank + "/" + size);
            vo.setRankScore(getRankScore(size, rank));
            vo.setRankScoreStr(String.format("%.2f", vo.getRankScore()));
            // 组装教学形式
            if ((Objects.isNull(reqVO.getSetEducateForm()) || Boolean.TRUE.equals(reqVO.getSetEducateForm()))
                    && Objects.nonNull(vo.getEducateFormId())) {
                vo.setEducateForm(map.get(vo.getEducateFormId())); // 教学形式
            }
        });

        // 裁剪分页
        Integer pageNo = reqVO.getPageNo(); // 当前页码
        Integer pageSize = reqVO.getPageSize(); // 每页数量
        long total = list.size();

        List<MyEvaluationPageRespVO> resultList;

        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = (int) Math.min(startIndex + pageSize, total);

        if (startIndex >= total) {
            resultList = Collections.emptyList();
        }else {
            resultList = list.subList(startIndex, endIndex);
        }

        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(reqVO.getIsSerialDesc(),
                total,
                reqVO,
                resultList.size());
        for (int i = 0; i < resultList.size(); i++) {
            resultList.get(i).setSerialNumber(serialNumberList.get(i));
        }
        return new PageResult<>(resultList, total);
    }

    // 筛选
    private static List<MyEvaluationPageRespVO> filterMyEvaluationRespVO(MyEvaluationPageReqVO reqVO, List<MyEvaluationPageRespVO> sqlList) {
        return sqlList.stream().filter(o -> {

            // 教师id匹配
            if (CollectionUtil.isNotEmpty(reqVO.getTeacherIdList()) && !reqVO.getTeacherIdList().contains(o.getTeacherId())) {
                return false;
            }

            if (Objects.nonNull(reqVO.getTeacherId()) && !reqVO.getTeacherId().equals(o.getTeacherId())) {
                return false;
            }

            // 班级id 匹配
            if (Objects.nonNull(reqVO.getClassId()) && !reqVO.getClassId().equals(o.getClassId())) {
                return false;
            }

            // 课程名称模糊匹配
            if (StringUtils.isNotBlank(reqVO.getCourseName())) {
                if (StringUtils.isBlank(o.getCourseName())) {
                    return false;
                }
                if (!o.getCourseName().contains(reqVO.getCourseName())) {
                    return false;
                }
            }

            // 教师名称模糊匹配
            if (StringUtils.isNotBlank(reqVO.getTeacherName())) {
                if (StringUtils.isBlank(o.getTeacherName())) {
                    return false;
                }
                if (!o.getTeacherName().contains(reqVO.getTeacherName())) {
                    return false;
                }
            }

            // 部门名称模糊匹配
            if (StringUtils.isNotBlank(reqVO.getDeptName())) {
                if (StringUtils.isBlank(o.getDeptNames())) {
                    return false;
                }
                if (!o.getDeptNames().contains(reqVO.getDeptName())) {
                    return false;
                }
            }

            return true;
        }).collect(Collectors.toList());
    }


    /**
     * 我的评估-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    @Override
    public List<MyEvaluationExcelVO> getExportMyEvaluationExcel(MyEvaluationPageReqVO reqVO) {
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        List<MyEvaluationPageRespVO> respVOList = getMyEvaluationPage(reqVO).getList();
        return EvaluationResponseConvert.INSTANCE.convertList(respVOList);
    }

    @Override
    public List<TeacherEvaluationResponseVO> getTeacherEvaluationList(EvaluationResponsePageReqVO reqVO) {
        List<EvaluationResponseDO> evaluationResponseDOS = evaluationResponseMapper.selectList(reqVO);
        List<TeacherEvaluationResponseVO> results = EvaluationResponseConvert.INSTANCE.convertList07(evaluationResponseDOS);
        Long classId = reqVO.getClassId();
        List<ClassCourseDO> classCourseDOS = classCourseMapper.selectList(new ClassCourseExportReqVO().setClassId(classId));
        List<Long> classCourseIds = classCourseDOS.stream().map(ClassCourseDO::getId).collect(Collectors.toList());
        results = results.stream().filter(result -> classCourseIds.contains(result.getClassCourseId())).collect(Collectors.toList());

        results = results.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()
                -> new TreeSet<>(Comparator.comparing(TeacherEvaluationResponseVO::getClassCourseId))), ArrayList::new));

        results.forEach(result -> {
            if (result.getDepartment()) {
                result.setTeacherName(result.getTeacherId());
            } else {
                List<Long> teacherIds = Arrays.stream(result.getTeacherId().split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                List<TeacherInformationRespVO> teacherInformation = teacherInformationMapper.selectListByIds(teacherIds);
                List<String> teacherNames = teacherInformation.stream().map(TeacherInformationRespVO::getName).collect(Collectors.toList());
                String teacherName = teacherNames.stream().map(String::valueOf).collect(Collectors.joining(","));
                result.setTeacherName(teacherName);
            }
            ClassCourseDO classCourseDO = classCourseMapper.selectById(result.getClassCourseId());
            result.setClassCourseDO(classCourseDO);
            try {
                result.setCourseName( classCourseDO.getCourseId().equals(-1l)?"选修课":coursesMapper.selectById(classCourseDO.getCourseId()).getName());
            }catch (Exception e0){
                e0.printStackTrace();
            }
            if(StrUtil.isEmpty(result.getCourseName())){
                result.setCourseName("选修课");
            }
            EvaluationResponsePageReqVO unrated = new EvaluationResponsePageReqVO().setQuestionnaireId(result.getQuestionnaireId().intValue()).setClassCourseId(result.getClassCourseId().intValue()).setHandle(false);
            EvaluationResponsePageReqVO rated = new EvaluationResponsePageReqVO().setQuestionnaireId(result.getQuestionnaireId().intValue()).setClassCourseId(result.getClassCourseId().intValue()).setHandle(true);

            List<EvaluationResponseDO> unratedList = evaluationResponseMapper.selectList(unrated);

            List<Long> unratedIdList = unratedList.stream().map(EvaluationResponseDO::getStudentId).collect(Collectors.toList());

            List<EvaluationResponseDO> ratedList = evaluationResponseMapper.selectList(rated);

            List<Long> ratedIdList = ratedList.stream().map(EvaluationResponseDO::getStudentId).collect(Collectors.toList());
            if (!unratedList.isEmpty()) {
                List<TraineeDO> unratedStudent = traineeMapper.selectByIdList(unratedIdList);
                result.setUnratedStudent(unratedStudent);
            }
            if (!ratedList.isEmpty()) {
                List<TraineeDO> ratedStudent = traineeMapper.selectByIdList(ratedIdList);
                result.setRatedStudent(ratedStudent);
            }


            result.setRated(ratedList.size());
            result.setUnrated(unratedList.size());


            if (unratedList.isEmpty()) {
                result.setIsDone(true);
            } else {
                result.setIsDone(false);
            }
        });
        if (reqVO.getIsDone()!= null && reqVO.getIsDone()) {
            results = results.stream().filter(result -> result.getIsDone()).collect(Collectors.toList());
        } else {
            results = results.stream().filter(result -> !result.getIsDone()).collect(Collectors.toList());
        }

        return results;
    }

    /**
     * 课程评估详情-APP端
     *
     * @param classId 班次ID
     * @param isDone  是否已完成评估
     * @return 课程评估详情列表
     */
    @Override
    public List<CourseEvaluationResponseVO> getCourseEvaluationForApp(Long classId, Boolean isDone) {

        // 专题课评估详情
        List<CourseEvaluationResponseVO> topicCourseEvaluationList = evaluationResponseMapper.getTopicCourseEvaluation(classId, isDone);
        // 标记专题课
        topicCourseEvaluationList.forEach(courseVO -> courseVO.setCoursesType(CoursesTypeEnum.TOPIC_COURSE.getType()));
        // 专题+选修课程评估详情列表
        List<CourseEvaluationResponseVO> courseEvaluationList = new ArrayList<>(topicCourseEvaluationList);

        // 选修课评估详情
        List<OptionalCourseEvaluationDetailVO> electiveSubCourseEvaluationList = evaluationResponseMapper.getElectiveCourseEvaluation(classId);

        if (CollectionUtil.isNotEmpty(electiveSubCourseEvaluationList)){
            // 根据排课id进行分组
            Map<Long, List<OptionalCourseEvaluationDetailVO>> electiveSubCourseEvaluationMap = electiveSubCourseEvaluationList.stream()
                    .collect(Collectors.groupingBy(OptionalCourseEvaluationDetailVO::getClassCourseId));
            // 选修课评估详情列表
            electiveSubCourseEvaluationMap.forEach((classCourseId, list) -> {
                if (CollectionUtil.isEmpty(list)) {
                    return;
                }

                // 未评估人数
                long unevaluatedCount = list.stream().mapToLong(OptionalCourseEvaluationDetailVO::getUnevaluatedCount).sum();

                // 如果仅获取已完成评估, 则跳过有未评估的选修课
                if (isDone && unevaluatedCount > 0) {
                    return;
                }

                // 如果仅获取未完成评估, 则跳过已无待评估的选修课
                if (!isDone && unevaluatedCount == 0) {
                    return;
                }


                CourseEvaluationResponseVO courseEvaluationResponseVO = new CourseEvaluationResponseVO();
                // 选修课排课id
                courseEvaluationResponseVO.setClassCourseId(classCourseId);
                // 问卷id
                courseEvaluationResponseVO.setQuestionnaireId(list.get(0).getQuestionnaireId());
                // 选修课上课日期
                courseEvaluationResponseVO.setClassDate(list.get(0).getClassDate());
                // 选修课开始上课时间
                courseEvaluationResponseVO.setClassBeginTime(list.get(0).getClassBeginTime());
                // 选修课教师姓名
                String teacherNames = list.stream().map(OptionalCourseEvaluationDetailVO::getTeacherName).collect(Collectors.joining(","));
                courseEvaluationResponseVO.setTeacherName(teacherNames);

                // 总待评估人数
                courseEvaluationResponseVO.setExpectedCount(list.stream().mapToLong(OptionalCourseEvaluationDetailVO::getExpectedCount).sum());
                // 已评估人数
                courseEvaluationResponseVO.setEvaluatedCount(list.stream().mapToLong(OptionalCourseEvaluationDetailVO::getEvaluatedCount).sum());
                // 未评估人数
                courseEvaluationResponseVO.setUnevaluatedCount(unevaluatedCount);

                courseEvaluationResponseVO.setCoursesType(CoursesTypeEnum.OPTIONAL_COURSE.getType());

                courseEvaluationResponseVO.setOptionalCourseEvaluationDetailVOS(list);

                courseEvaluationList.add(courseEvaluationResponseVO);
            });
        }
        // courseEvaluationList 开始上课时间倒序排序
        courseEvaluationList.sort((o1, o2) -> {
            if (Objects.equals(o1.getClassBeginTime(), null)) {
                return 1;
            } else if (Objects.equals(o2.getClassBeginTime(), null)) {
                return -1;
            } else {
                return o2.getClassBeginTime().compareTo(o1.getClassBeginTime());
            }
        });
        return courseEvaluationList;
    }

    /**
     * 教师移动端-课评查看-选修课、专题课查看学员评估详情
     *
     * @param classCourseId 排课id
     * @param courseId      课程id (选修课才有)
     * @param isDone        是否已完成评估
     * @return 学员评估详情列表
     */
    @Override
    public List<CourseEvaluationTraineeDetailVO> getCourseEvaluationTraineeDetailForApp(Long classCourseId, Long courseId, Boolean isDone) {
        // 选修课评估详情
        if (Objects.nonNull(courseId)){
            return evaluationResponseMapper.getElectiveCourseEvaluationTraineeDetailForApp(classCourseId, courseId, isDone);
        }
        return evaluationResponseMapper.getTopicCourseEvaluationTraineeDetailForApp(classCourseId, isDone);
    }

    @Override
    public Long countClassUnfinished(Long classId) {
        List<ClassCourseDO> classCourseDOS = classCourseMapper.selectList(new ClassCourseExportReqVO().setClassId(classId));
        List<Long> classCourseIds = classCourseDOS.stream().map(ClassCourseDO::getId).collect(Collectors.toList());
        List<EvaluationResponseDO> evaluationResponseDOS = evaluationResponseMapper.selectList(new EvaluationResponsePageReqVO());
        return null;
    }

    @Override
    public void distribute(DistributeVO reqVO) {
        EvaluationResponseDO evaluationResponseDO = evaluationResponseMapper.selectById(reqVO.getId());
        QuestionnaireManagementDO questionnaireManagementDO = questionnaireManagementMapper.selectById(evaluationResponseDO.getQuestionnaireId());
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expireTime = null;
        if (Boolean.TRUE.equals(questionnaireManagementDO.getTimeTag())) {
            expireTime = now.plusDays(questionnaireManagementDO.getTimeLimit());
        }

        evaluationResponseMapper.distribute(reqVO.getId(), expireTime);

        Long classCourseId = evaluationResponseDO.getClassCourseId();
        Long questionnaireId = evaluationResponseDO.getQuestionnaireId();
        Long studentId = evaluationResponseDO.getStudentId();
        evaluationDetailMapper.revoke(classCourseId, questionnaireId, studentId);
    }

    @Override
    public void revoke(DistributeVO reqVO) {
        EvaluationResponseDO evaluationResponseDO = evaluationResponseMapper.selectById(reqVO.getId());
        evaluationResponseMapper.revoke(reqVO.getId());

        Long classCourseId = evaluationResponseDO.getClassCourseId();
        Long questionnaireId = evaluationResponseDO.getQuestionnaireId();
        Long studentId = evaluationResponseDO.getStudentId();
        evaluationDetailMapper.revoke(classCourseId, questionnaireId, studentId);
    }

    @Override
    public void deleteByClassCourseId(List<Long> classCourseIds) {
        evaluationResponseMapper.delete(new LambdaQueryWrapperX<EvaluationResponseDO>()
                .in(EvaluationResponseDO::getClassCourseId, classCourseIds));
    }

    @Override
    public List<Long> getExistEvaluationTrainee(Long classCourseId) {
        return evaluationResponseMapper.getExistEvaluationTrainee(classCourseId);
    }

    /**
     * 业中首页-仪表盘 - 课程评估情况
     *
     * @param reqVO 请求参数
     * @return 响应
     */
    @Override
    public ClassEvaluationForBusinessCenterRespVO classEvaluationForBusinessCenter(ClassEvaluationForBusinessCenterReqVO reqVO) {
        // 设置预设部门id
        List<Long> deptIdList = new ArrayList<>(BoardDeptMap.keySet());
        // 限定课程结束时间
        reqVO.setCourseEndTime(LocalDateTime.now());
        switch (reqVO.getResType()){
            case 0:// 全部部门评分情况
                // 屏蔽部门影响
                reqVO.setDeptId(null);
                // 设置预设部门id范围
                reqVO.setDeptIdList(deptIdList);
                return getClassEvaluationListForAll(reqVO);
            case 1:// 一个部门内教师评分情况
                // 设置预设部门id范围
                reqVO.setDeptIdList(deptIdList);
                return getClassEvaluationListForDept(reqVO);
            case 2:// 一个教师的评分情况
                // 屏蔽部门影响
                reqVO.setDeptId(null);
                reqVO.setDeptIdList(null);
                return getClassEvaluationListForTeacher(reqVO);
        }
        return null;
    }

    /**
     * 业中首页-仪表盘 - 课程评估情况  全部部门评分情况
     * @param reqVO 请求参数
     * @return  响应
     */
    private ClassEvaluationForBusinessCenterRespVO getClassEvaluationListForAll(ClassEvaluationForBusinessCenterReqVO reqVO){
        // 年度转为开始时间-结束时间
        if (Objects.nonNull(reqVO.getYear())) {
            reqVO.setStartTime(LocalDate.of(reqVO.getYear(), Month.JANUARY, 1));
            reqVO.setEndTime(LocalDate.of(reqVO.getYear(), Month.DECEMBER, 31));
        }

        // 评课率、评课平均分 查询
        ClassEvaluationForBusinessCenterRespVO classEvaluationForBusinessCenterRespVO = evaluationResponseMapper.getCourseEvaluationRateAndAvgScore(reqVO);
        if (Objects.isNull(classEvaluationForBusinessCenterRespVO)) {
            classEvaluationForBusinessCenterRespVO = new ClassEvaluationForBusinessCenterRespVO();
        }

        // 部门课程详情
        List<RankDetailVO> rankDetailVOList = evaluationResponseMapper.getDeptRankDetailVOList(reqVO);
        for (int i = 0; i < rankDetailVOList.size(); i++) {
            RankDetailVO rankDetailVO = rankDetailVOList.get(i);
            rankDetailVO.setRankNumber(i + 1); // 设置排名
            // 根据部门id回显部门名称
            rankDetailVO.setDepartmentName(BoardDeptMap.get(rankDetailVO.getDeptId()));
        }

        // 部门评分详情
        classEvaluationForBusinessCenterRespVO.setRankDetailVOList(rankDetailVOList);

        return classEvaluationForBusinessCenterRespVO;
    }

    /**
     * 业中首页-仪表盘 - 课程评估情况  一个部门内教师评分情况
     * @param reqVO 请求参数
     * @return  响应
     */
    private ClassEvaluationForBusinessCenterRespVO getClassEvaluationListForDept(ClassEvaluationForBusinessCenterReqVO reqVO){
        ClassEvaluationForBusinessCenterRespVO classEvaluationForYZDeptRespVO = new ClassEvaluationForBusinessCenterRespVO();

        // 年度转为开始时间-结束时间
        if (Objects.nonNull(reqVO.getYear())) {
            reqVO.setStartTime(LocalDate.of(reqVO.getYear(), Month.JANUARY, 1));
            reqVO.setEndTime(LocalDate.of(reqVO.getYear(), Month.DECEMBER, 31));
        }

        // 部门教师课程评课详情
        List<RankDetailVO> rankDetailVOList = evaluationResponseMapper.getTeacherRankDetailVOList(reqVO);
        for (int i = 0; i < rankDetailVOList.size(); i++) {
            RankDetailVO rankDetailVO = rankDetailVOList.get(i);
            rankDetailVO.setRankNumber(i + 1); // 设置排名
        }

        // 设置排名数据
        classEvaluationForYZDeptRespVO.setRankDetailVOList(rankDetailVOList);

        // 获取所有部门排名和评分 this.getClassEvaluationListForAll 评课率会根据dept_id字段筛选
        ClassEvaluationForBusinessCenterRespVO classEvaluationListForAll = this.getClassEvaluationListForAll(reqVO);

        // 部门的评课率
        classEvaluationForYZDeptRespVO.setEvalRate(classEvaluationListForAll.getEvalRate());

        // 部门的评课平均分、排名 从classEvaluationListForAll 的rankDetailVOList 查询
        List<RankDetailVO> rankDetailVOListForAll = classEvaluationListForAll.getRankDetailVOList();
        if (Objects.nonNull(rankDetailVOListForAll) && !rankDetailVOListForAll.isEmpty()){
            for (RankDetailVO rankDetailVO : rankDetailVOListForAll) {
                if(reqVO.getDeptId().equals(rankDetailVO.getDeptId())){
                    // 设置排名
                    classEvaluationForYZDeptRespVO.setDeptRank(rankDetailVO.getRankNumber());
                    // 设置评课平均分
                    classEvaluationForYZDeptRespVO.setCourseAvgScore(rankDetailVO.getAvgScore());
                    break;
                }
            }
        }
        return classEvaluationForYZDeptRespVO;
    }

    /**
     * 业中首页-仪表盘 - 课程评估情况  一个教师的评分情况
     * @param reqVO 请求参数
     * @return  响应
     */
    private ClassEvaluationForBusinessCenterRespVO getClassEvaluationListForTeacher(ClassEvaluationForBusinessCenterReqVO reqVO){
        ClassEvaluationForBusinessCenterRespVO classEvaluationForBusinessCenterRespVO = new ClassEvaluationForBusinessCenterRespVO();


        // 根据业中systemId 查询教师信息
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectBySystemId(reqVO.getTeacherSystemId());
        if (Objects.isNull(teacherInformationDO)) {
            return classEvaluationForBusinessCenterRespVO;
        }

        // 年度转为开始时间-结束时间
        if (Objects.nonNull(reqVO.getYear())) {
            reqVO.setStartTime(LocalDate.of(reqVO.getYear(), Month.JANUARY, 1));
            reqVO.setEndTime(LocalDate.of(reqVO.getYear(), Month.DECEMBER, 31));
        }

        // 设置教务教师id用与过滤查询
        reqVO.setTeacherId(teacherInformationDO.getId());

        // 查询课程平均分
        List<RankDetailVO> rankDetailVOList = evaluationResponseMapper.getTeacherRankDetailVOList(reqVO);
        if (Objects.nonNull(rankDetailVOList) && !rankDetailVOList.isEmpty()){
            for (RankDetailVO rankDetailVO : rankDetailVOList) {
                // 设置评课平均分
                classEvaluationForBusinessCenterRespVO.setCourseAvgScore(rankDetailVO.getAvgScore());
                break;
            }
        }

        // 查询教师评课率
        // 评课率、评课平均分 查询 限制字段 teacherId
        ClassEvaluationForBusinessCenterRespVO vo = evaluationResponseMapper.getCourseEvaluationRateAndAvgScore(reqVO);
        if (Objects.nonNull(vo)) {
            classEvaluationForBusinessCenterRespVO.setEvalRate(vo.getEvalRate());
        }

        return classEvaluationForBusinessCenterRespVO;
    }

    @Override
    public PageResult<AllEvaluationPageRespVO> getAllEvaluationPage(AllEvaluationPageReqVO reqVO) {
        if (reqVO.getYear() == null) {
            reqVO.setYear(LocalDateTime.now().getYear());
        }
        IPage<AllEvaluationPageRespVO> page = MyBatisUtils.buildPage(reqVO);
        if (reqVO.getStartTime() != null && reqVO.getEndTime() != null) {
            LocalDateTime startOfDay = reqVO.getStartTime().atStartOfDay();
            LocalDateTime endOfDay = reqVO.getEndTime().atTime(23, 59, 59);
            reqVO.setStartOfDay(startOfDay);
            reqVO.setEndOfDay(endOfDay);
        }
        List<AllEvaluationPageRespVO> result = evaluationResponseMapper.selectAllPage(page, reqVO);
        result.forEach(resp -> {
            if(resp.getDepartment()) {
                resp.setTeacherName(resp.getTeacherId());
            } else {
                List<Long> teacherIds = Arrays.stream(resp.getTeacherId().split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                List<TeacherInformationRespVO> teacherInformation = teacherInformationMapper.selectListByIds(teacherIds);
                List<String> teacherNames = teacherInformation.stream().map(TeacherInformationRespVO::getName).collect(Collectors.toList());
                String teacherName = teacherNames.stream().map(String::valueOf).collect(Collectors.joining(","));
                resp.setTeacherName(teacherName);
            }
            // 查询学员考勤信息
            resp.setTraineeStatus(clockInInfoMapper.getTraineeStatus(resp.getClassCourseId(), resp.getStudentId()));
            String day = resp.getBeginTime().toLocalDate().toString();
            String start = resp.getBeginTime().toLocalTime().toString();
            String end = resp.getEndTime().toLocalTime().toString();
            int hour = resp.getBeginTime().toLocalTime().getHour();
            String lectureTime;
            if (hour < 12) {
                lectureTime = day + " 上午 " + start + '-' + end;
            } else {
                lectureTime = day + " 下午 " + start + '-' + end;
            }
            resp.setLectureTime(lectureTime);
        });
        return new PageResult<>(result, page.getTotal());
    }

    /**
     * 课评详情(我的评估)-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    @Override
    public List<MyEvaluationDetailExcelVO> getExportMyEvaluationExcelByDetail(MyEvaluationPageReqVO reqVO) {
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        List<TeacherEvaluationPageRespVO> respVOList = getTeacherEvaluationPage(reqVO).getList();
        return EvaluationResponseConvert.INSTANCE.convertList09(respVOList);
    }

    /**
     * 教师评估-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    @Override
    public PageResult<TeacherEvaluationPageRespVO> getTeacherEvaluationPage(MyEvaluationPageReqVO reqVO) {
        PageResult<MyEvaluationPageRespVO> myEvaluationPage = getMyEvaluationPage(reqVO);
        return new PageResult<>(
                EvaluationResponseConvert.INSTANCE.convertList10(myEvaluationPage.getList()),
                myEvaluationPage.getTotal()
        );
    }

    /**
     * 教师评估-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    @Override
    public List<TeacherEvaluationExcelVO> getExportTeacherEvaluationExcel(MyEvaluationPageReqVO reqVO) {
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        List<TeacherEvaluationPageRespVO> respVOList = getTeacherEvaluationPage(reqVO).getList();
        return EvaluationResponseConvert.INSTANCE.convertList02(respVOList);
    }

    /**
     * 我的评估-评估详情
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    @Override
    public PageResult<MyEvaluationDetailPageRespVO> getMyEvaluationDetail(MyEvaluationDetailPageReqVO reqVO) {
        Page<MyEvaluationDetailPageRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<MyEvaluationDetailPageRespVO> list = evaluationResponseMapper.getMyEvaluationDetail(page, reqVO);
        // 分数设置两位小数
        list.forEach(vo -> vo.setScoreStr(String.format("%.2f", vo.getScore())));
        return new PageResult<>(list, page.getTotal());
    }


    /**
     * 部门评估-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    @Override
    public PageResult<DeptEvaluationPageRespVO> getDeptEvaluationPage(DeptEvaluationPageReqVO reqVO) {
        Page<DeptEvaluationPageRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<DeptEvaluationPageRespVO> list = evaluationResponseMapper.getDeptEvaluationPage(page, reqVO);
        // 获取每个教师的每个课程的平均排名分
        MyEvaluationPageReqVO myEvaluationPageReqVO = new MyEvaluationPageReqVO();
        myEvaluationPageReqVO.setPageNo(1);
        myEvaluationPageReqVO.setPageSize(Integer.MAX_VALUE);
        myEvaluationPageReqVO.setSetEducateForm(false);
        myEvaluationPageReqVO.setStartTime(reqVO.getStartTime());
        myEvaluationPageReqVO.setEndTime(reqVO.getEndTime());
        myEvaluationPageReqVO.setTeacherName(reqVO.getName());
        myEvaluationPageReqVO.setDeptName(reqVO.getDeptName());
        myEvaluationPageReqVO.setTeacherIdList(list.stream().map(DeptEvaluationPageRespVO::getTeacherId).collect(Collectors.toList()));
        List<TeacherEvaluationPageRespVO> myEvaluationPageRespVOS = getTeacherEvaluationPage(myEvaluationPageReqVO).getList()
                .stream().filter(vo -> Objects.nonNull(vo.getTeacherId()) && Objects.nonNull(vo.getAverageScore())).collect(Collectors.toList());
        Map<Long, Double> teacherIdToRankScoreSum = myEvaluationPageRespVOS.stream().collect(Collectors.groupingBy(
                TeacherEvaluationPageRespVO::getTeacherId,
                Collectors.averagingDouble(TeacherEvaluationPageRespVO::getRankScore)
        ));
        list.forEach(vo -> {
            vo.setAverageRankScore(teacherIdToRankScoreSum.getOrDefault(vo.getTeacherId(), 0D));
            vo.setAverageRankScoreStr(String.format("%.2f", vo.getAverageRankScore()));
            vo.setAverageScoreStr(String.format("%.2f", vo.getAverageScore()));
        });
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(reqVO.getIsSerialDesc(),
                page.getTotal(),
                reqVO,
                list.size());
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSerialNumber(serialNumberList.get(i));
        }
        return new PageResult<>(list, page.getTotal());
    }

    /**
     * 部门评估-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    @Override
    public List<DeptEvaluationExcelVO> getExportDeptEvaluationExcel(DeptEvaluationPageReqVO reqVO) {
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        List<DeptEvaluationPageRespVO> list = getDeptEvaluationPage(reqVO).getList();
        return EvaluationResponseConvert.INSTANCE.convertList03(list);
    }

    /**
     * 班次评估统计-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    @Override
    public PageResult<ClassEvaluationStatsPageRespVO> getClassEvaluationStatsPage(ClassEvaluationStatsPageReqVO reqVO) {
        Page<ClassEvaluationStatsPageRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<ClassEvaluationStatsPageRespVO> list = evaluationResponseMapper.getClassEvaluationStatsPage(page, reqVO);
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(reqVO.getIsSerialDesc(),
                page.getTotal(),
                reqVO,
                list.size());
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSerialNumber(serialNumberList.get(i));
            ClassEvaluationStatsPageRespVO classEvaluationStatsPageRespVO = list.get(i);
            if (Objects.nonNull(classEvaluationStatsPageRespVO.getAverageScore())) {
                classEvaluationStatsPageRespVO.setAverageScoreStr(String.format("%.2f", classEvaluationStatsPageRespVO.getAverageScore()));
            } else {
                classEvaluationStatsPageRespVO.setAverageScoreStr(ZERO_RATE_STRING);
                classEvaluationStatsPageRespVO.setAverageScore(ZERO_FLOAT);
            }
            classEvaluationStatsPageRespVO.setAverageRatio(RatioValValueEnum.getValidValueByDecimal(
                    classEvaluationStatsPageRespVO.getAverageRatio()));
            if (Objects.nonNull(classEvaluationStatsPageRespVO.getAverageRatio())) {
                classEvaluationStatsPageRespVO.setAverageRatioStr(String.format("%.2f", classEvaluationStatsPageRespVO.getAverageRatio() * 100));
            } else {
                classEvaluationStatsPageRespVO.setAverageRatioStr(ZERO_RATE_STRING);
                classEvaluationStatsPageRespVO.setAverageRatio(ZERO_FLOAT);
            }
        }
        return new PageResult<>(list, page.getTotal());
    }

    /**
     * 班次评估统计-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    @Override
    public List<ClassEvaluationStatsExcelVO> getExportClassEvaluationStatsExcel(ClassEvaluationStatsPageReqVO reqVO) {
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        List<ClassEvaluationStatsPageRespVO> list = getClassEvaluationStatsPage(reqVO).getList();
        return EvaluationResponseConvert.INSTANCE.convertList04(list);
    }

    /**
     * 班次评估统计-学员参评情况-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    @Override
    public PageResult<TraineeEvaluationDetailPageRespVO> getTraineeEvaluationDetailPage(TraineeEvaluationDetailPageReqVO reqVO) {
        Page<TraineeEvaluationDetailPageRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<TraineeEvaluationDetailPageRespVO> list = evaluationResponseMapper.getTraineeEvaluationDetailPage(page, reqVO);
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(false,
                page.getTotal(),
                reqVO,
                list.size());
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSerialNumber(serialNumberList.get(i));
            TraineeEvaluationDetailPageRespVO traineeEvaluationDetailPageRespVO = list.get(i);
            traineeEvaluationDetailPageRespVO.setRatio(RatioValValueEnum.getValidValueByDecimal(
                    traineeEvaluationDetailPageRespVO.getRatio()));
            if (Objects.nonNull(traineeEvaluationDetailPageRespVO.getRatio())) {
                traineeEvaluationDetailPageRespVO.setRatioStr(String.format("%.2f", traineeEvaluationDetailPageRespVO.getRatio() * 100));
            } else {
                traineeEvaluationDetailPageRespVO.setRatioStr(ZERO_RATE_STRING);
                traineeEvaluationDetailPageRespVO.setRatio(ZERO_FLOAT);
            }
            // 设置未评
            traineeEvaluationDetailPageRespVO.setNotActualCount(traineeEvaluationDetailPageRespVO.getExpectedCount()
                    - traineeEvaluationDetailPageRespVO.getActualCount());
        }
        return new PageResult<>(list, page.getTotal());
    }

    /**
     * 班次评估统计-学员参评情况-导出
     *
     * @param reqVO    查询参数
     * @param response 响应
     */
    @Override
    public void exportTraineeEvaluationDetailExcel(TraineeEvaluationDetailPageReqVO reqVO, HttpServletResponse response) throws IOException {
        ClassManagementDO classManagementDO = classManagementMapper.selectById(reqVO.getClassId());
        if (Objects.isNull(classManagementDO)) {
            throw exception(CLASS_MANAGEMENT_NOT_EXISTS);
        }
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        List<TraineeEvaluationDetailPageRespVO> list = getTraineeEvaluationDetailPage(reqVO).getList();
        List<TraineeEvaluationDetailExcelVO> excelList = EvaluationResponseConvert.INSTANCE.convertList05(list);
        ExcelUtils.writeByIncludeColumnIndexes(response, classManagementDO.getClassName() + "参评情况.xlsx",
                "数据", TraineeEvaluationDetailExcelVO.class, excelList, reqVO.getIncludeColumnIndexes());

    }

    /**
     * 班次评估统计-学员参评情况-学员已评、未评详情-分页
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    @Override
    public PageResult<TraineeEvaluatedAndUnEvaluatedDetailPageRespVO> getTraineeEvaluatedAndUnEvaluatedDetailPage(TraineeEvaluatedAndUnEvaluatedDetailPageReqVO reqVO) {
        Page<TraineeEvaluatedAndUnEvaluatedDetailPageRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<TraineeEvaluatedAndUnEvaluatedDetailPageRespVO> list = evaluationResponseMapper.getTraineeEvaluatedAndUnEvaluatedDetailPage(page, reqVO);
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(false, page.getTotal(), reqVO, list.size());
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSerialNumber(serialNumberList.get(i));
            TraineeEvaluatedAndUnEvaluatedDetailPageRespVO respVO = list.get(i);
            // 授课时间组装
            String classDuration = respVO.getClassDate() + " " + ClassDayPeriodEnum.getDescByPeriod(respVO.getDayPeriod())
                    + " " + DateUtils.format(respVO.getClassStartTime(), FORMAT_HOUR_MINUTE)
                    + "-" + DateUtils.format(respVO.getClassEndTime(), FORMAT_HOUR_MINUTE);
            respVO.setClassDuration(classDuration);

        }
        return new PageResult<>(list, page.getTotal());
    }

    /**
     * 班次评估统计-学员参评情况-学员已评、未评详情-导出
     *
     * @param reqVO    查询参数
     * @param response 响应
     */
    @Override
    public void exportTraineeEvaluatedAndUnEvaluatedDetailExcel(TraineeEvaluatedAndUnEvaluatedDetailPageReqVO reqVO, HttpServletResponse response) throws IOException {
        TraineeDO traineeDO = traineeMapper.selectById(reqVO.getTraineeId());
        if (Objects.isNull(traineeDO)) {
            throw exception(TRAINEE_NOT_EXISTS);
        }
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        List<TraineeEvaluatedAndUnEvaluatedDetailPageRespVO> list = getTraineeEvaluatedAndUnEvaluatedDetailPage(reqVO).getList();
        List<TraineeEvaluatedAndUnEvaluatedDetailExcelVO> excelList = EvaluationResponseConvert.INSTANCE.convertList06(list);
        ExcelUtils.writeByIncludeColumnIndexes(response, traineeDO.getName() + EvaluationStatusEnum.getDescByStatus(reqVO.getStatus())
                        + "详情.xlsx", "数据", TraineeEvaluatedAndUnEvaluatedDetailExcelVO.class,
                excelList, reqVO.getIncludeColumnIndexes());
    }

    /**
     * 班次评估统计-课评详情-学员详情-分页
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    @Override
    public PageResult<CourseTraineeEvaluationDetailPageRespVO> getCourseTraineeEvaluationDetailPage(MyEvaluationDetailPageReqVO reqVO) {
        Page<CourseTraineeEvaluationDetailPageRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<CourseTraineeEvaluationDetailPageRespVO> list = evaluationResponseMapper.getCourseTraineeEvaluationDetailPage(page, reqVO);
        LocalDateTime now = LocalDateTime.now();
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(false, page.getTotal(), reqVO, list.size());
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSerialNumber(serialNumberList.get(i));
            CourseTraineeEvaluationDetailPageRespVO respVO = list.get(i);
            // 问卷是否过期标志位
            if (Objects.isNull(respVO.getExpireTime())) {
                respVO.setIsExpired(false);
            } else {
                respVO.setIsExpired(now.isAfter(respVO.getExpireTime()));
            }
            // 两位数问卷分数
            if (Objects.nonNull(respVO.getScore())) {
                respVO.setScoreStr(String.format("%.2f", respVO.getScore()));
            }
            // 手机号脱敏
            respVO.setPhone(DesensitizeUtils.sliderDesensitize(
                    respVO.getPhone(),
                    DesensitizeUtils.PHONE_PREFIX_KEEP,
                    DesensitizeUtils.PHONE_SUFFIX_KEEP
            ));
        }
        return new PageResult<>(list, page.getTotal());
    }

    /**
     * 部门评估统计-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    @Override
    public PageResult<DeptEvaluationPageRespVO> getDeptEvaluationStatsPage(DeptEvaluationPageReqVO reqVO) {
        Page<DeptEvaluationPageRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<DeptEvaluationPageRespVO> list = evaluationResponseMapper.getDeptEvaluationStatsPage(page, reqVO);
        DeptEvaluationPageReqVO deptEvaluationPageReqVO = new DeptEvaluationPageReqVO();
        deptEvaluationPageReqVO.setPageNo(1);
        deptEvaluationPageReqVO.setPageSize(Integer.MAX_VALUE);
        deptEvaluationPageReqVO.setStartTime(reqVO.getStartTime());
        deptEvaluationPageReqVO.setEndTime(reqVO.getEndTime());
        deptEvaluationPageReqVO.setDeptName(reqVO.getDeptName());
        List<DeptEvaluationPageRespVO> deptEvaluationPageRespVOS = getDeptEvaluationPage(deptEvaluationPageReqVO).getList()
                .stream().filter(vo -> Objects.nonNull(vo.getDeptId()) && Objects.nonNull(vo.getAverageRankScore()))
                .collect(Collectors.toList());
        // 计算平均排名分
        Map<Long, Double> deptIdToAverageRankScoreSumMap = new HashMap<>();
        Map<Long, Integer> deptIdToTeacherCountMap = new HashMap<>();

        for (DeptEvaluationPageRespVO vo : deptEvaluationPageRespVOS) {
            Long id = vo.getDeptId();
            Double sum = deptIdToAverageRankScoreSumMap.get(id);
            if (Objects.isNull(sum)) {
                deptIdToAverageRankScoreSumMap.put(id, vo.getAverageRankScore());
            } else {
                deptIdToAverageRankScoreSumMap.put(id, sum + vo.getAverageRankScore());
            }
            Integer count = deptIdToTeacherCountMap.get(id);
            if (Objects.isNull(count)) {
                deptIdToTeacherCountMap.put(id, 1);
            } else {
                deptIdToTeacherCountMap.put(id, count + 1);
            }
        }
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(false, page.getTotal(), reqVO, list.size());
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSerialNumber(serialNumberList.get(i));
            DeptEvaluationPageRespVO respVO = list.get(i);
            Double deptSum = deptIdToAverageRankScoreSumMap.get(respVO.getDeptId());
            Integer deptCount = deptIdToTeacherCountMap.get(respVO.getDeptId());
            if (Objects.nonNull(deptSum) && Objects.nonNull(deptCount)) {
                // 部门平均排名分
                Double averageRankScore = deptSum / deptCount;
                respVO.setAverageRankScore(averageRankScore);
                respVO.setAverageRankScoreStr(String.format("%.2f", averageRankScore));
            }
            if (Objects.nonNull(respVO.getAverageScore())) {
                // 部门平均分
                respVO.setAverageScoreStr(String.format("%.2f", respVO.getAverageScore()));
            } else {
                respVO.setAverageScoreStr(ZERO_RATE_STRING);
                respVO.setAverageScore(ZERO_FLOAT);
            }
        }
        return new PageResult<>(list, page.getTotal());
    }

    /**
     * 部门评估统计-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    @Override
    public List<DeptEvaluationStatsExcelVO> getExportDeptEvaluationStatsExcel(DeptEvaluationPageReqVO reqVO) {
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        List<DeptEvaluationPageRespVO> list = getDeptEvaluationStatsPage(reqVO).getList();
        return EvaluationResponseConvert.INSTANCE.convertList08(list);
    }

    /**
     * 将字典数据列表转换为树节点列表
     *
     * @param list 字典数据列表
     * @return 树节点列表
     */
    private List<TreeNodeDTO> packageDictDataAsTreeNodeDTOList(List<DictDataRespDTO> list) {
        return list.stream().map(data -> new TreeNodeDTO(data.getId(), data.getParentId(), data.getLabel())).collect(Collectors.toList());
    }

    /**
     * 获取排名分
     *
     * @param size 排名总数
     * @param rank 排名
     * @return 排名分
     */
    private float getRankScore(int size, int rank) {
        // 对于该班次所有课程评分进行排名，第一名100分，最后一名70分，中间根据等差数列换算
        if (rank == 1) {
            return MAX_RANK_SCORE;
        } else if (rank == size) {
            return MIN_RANK_SCORE;
        } else {
            // 计算等差数列
            float rankScore = (MAX_RANK_SCORE - MIN_RANK_SCORE) / (size - 1);
            return MAX_RANK_SCORE - rankScore * (rank - 1);
        }
    }

    @Override
    public boolean revokeEvaluated(EvaluationRevokeReqVO reqVO) {
        EvaluationResponseDO evaluationResponse = evaluationResponseMapper.selectById(reqVO.getId());
        if (evaluationResponse == null) {
            throw exception(EVALUATION_RESPONSE_NOT_EXISTS);
        }

        // 判断是否已评问卷
        if (!Boolean.TRUE.equals(evaluationResponse.getHandle())) {
            // 该问卷未评，无需撤回
            return false;
        }

        // 获取问卷配置
        QuestionnaireManagementDO questionnaireManagement = questionnaireManagementMapper
                .selectById(evaluationResponse.getQuestionnaireId());

        // 判断是否超过撤回有效期
        LocalDateTime now = LocalDateTime.now();

        // 检查撤回有效期，在有效期内才能撤回
        if (evaluationResponse.getRevocableTime() != null && !Boolean.TRUE.equals(reqVO.getForceRevoke())) {
            if (now.isAfter(evaluationResponse.getRevocableTime())) {
                // 已超过撤回有效期，不允许撤回
                return false;
            }
        }

        // 执行撤回
        evaluationResponseMapper.revokeEvaluated(reqVO.getId());

        return true;
    }
}
