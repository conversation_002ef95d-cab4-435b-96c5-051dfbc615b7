package com.unicom.swdx.module.edu.dal.dataobject.questionmanagement;

import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import com.unicom.swdx.module.edu.controller.admin.options.vo.OptionsSaveReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.options.OptionsDO;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 题目管理 DO
 *
 * <AUTHOR>
 */
@TableName("pg_question_management")
@KeySequence("pg_question_management_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionManagementDO extends TenantBaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 题干
     */
    private String stem;
    /**
     * 标题
     */
    private String title;
    /**
     * 题目类型(字典)1打分题、2单选题、3简答题
     */
    private String questionType;
    /**
     * 分数
     */
    private BigDecimal score;
    /**
     * 所属类别
     */
    private Long categoryId;
    /**
     * 描述
     */
    private String description;
    /**
     * 创建部门
     */
    private Long createDept;
    /**
     * 创建人
     */
    // private Long creator;
    /**
     * 一票否决说明题1是0不是
     */
    private String oneBallotVetoResult;
    /**
     * 是否被添加逻辑(0没有 ，1 有 )
     */
    private Short isLogic;

    // 选择题的选项
    @TableField(exist = false)
    private List<OptionsDO> options;

}