package com.unicom.swdx.module.edu.controller.admin.plan.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 教学计划分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlanPageReqVO extends PageParam {

    @ApiModelProperty(value = "计划名称")
    private String name;

    @ApiModelProperty(value = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] beginDate;

    @ApiModelProperty(value = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] endDate;

//    @ApiModelProperty(value = "时间段（0上午，1下午，2晚上）")
//    private String period;
//
//    @ApiModelProperty(value = "开始时间")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//    private LocalDateTime[] beginTime;
//
//    @ApiModelProperty(value = "结束时间")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//    private LocalDateTime[] endTime;

    @ApiModelProperty(value = "常用教室id")
    private Long classroomId;

    @ApiModelProperty(value = "班级id")
    private Long classId;

    @ApiModelProperty(value = "班级id")
    private List<Long> classIdList;

    @ApiModelProperty(value = "状态：0暂存，1应用")
    private Boolean status;
}
