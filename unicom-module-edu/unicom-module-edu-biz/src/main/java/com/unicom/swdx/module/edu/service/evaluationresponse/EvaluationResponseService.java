package com.unicom.swdx.module.edu.service.evaluationresponse;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.EvaluationRevokeReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationPageRespVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationStatsExcelVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse.EvaluationResponseDO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 课程评价记录 Service 接口
 *
 * <AUTHOR>
 */
public interface EvaluationResponseService {

    /**
     * 创建课程评价记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEvaluationResponse(@Valid EvaluationResponseSaveReqVO createReqVO);

    /**
     * 更新课程评价记录
     *
     * @param updateReqVO 更新信息
     */
    void updateEvaluationResponse(@Valid EvaluationResponseSaveReqVO updateReqVO);

    /**
     * 删除课程评价记录
     *
     * @param id 编号
     */
    void deleteEvaluationResponse(Long id);

    /**
     * 获得课程评价记录
     *
     * @param id 编号
     * @return 课程评价记录
     */
    EvaluationResponseDO getEvaluationResponse(Long id);

    /**
     * 获得课程评价记录分页
     *
     * @param pageReqVO 分页查询
     * @return 课程评价记录分页
     */
    PageResult<EvaluationResponseDO> getEvaluationResponsePage(EvaluationResponsePageReqVO pageReqVO);


    /**
     * 我的评估-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    PageResult<MyEvaluationPageRespVO> getMyEvaluationPage(MyEvaluationPageReqVO reqVO);

    List<TeacherEvaluationResponseVO> getTeacherEvaluationList(EvaluationResponsePageReqVO reqVO);

    /**
     * 我的评估-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    List<MyEvaluationExcelVO> getExportMyEvaluationExcel(MyEvaluationPageReqVO reqVO);

    /**
     * 课评详情(我的评估)-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    List<MyEvaluationDetailExcelVO> getExportMyEvaluationExcelByDetail(MyEvaluationPageReqVO reqVO);

    /**
     * 教师评估-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    PageResult<TeacherEvaluationPageRespVO> getTeacherEvaluationPage(MyEvaluationPageReqVO reqVO);

    /**
     * 教师评估-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    List<TeacherEvaluationExcelVO> getExportTeacherEvaluationExcel(MyEvaluationPageReqVO reqVO);

    /**
     * 我的评估-评估详情
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    PageResult<MyEvaluationDetailPageRespVO> getMyEvaluationDetail(MyEvaluationDetailPageReqVO reqVO);

    /**
     * 部门评估-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    PageResult<DeptEvaluationPageRespVO> getDeptEvaluationPage(DeptEvaluationPageReqVO reqVO);

    /**
     * 部门评估-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    List<DeptEvaluationExcelVO> getExportDeptEvaluationExcel(DeptEvaluationPageReqVO reqVO);

    /**
     * 班次评估统计-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    PageResult<ClassEvaluationStatsPageRespVO> getClassEvaluationStatsPage(ClassEvaluationStatsPageReqVO reqVO);

    /**
     * 班次评估统计-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    List<ClassEvaluationStatsExcelVO> getExportClassEvaluationStatsExcel(ClassEvaluationStatsPageReqVO reqVO);

    /**
     * 班次评估统计-学员参评情况-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    PageResult<TraineeEvaluationDetailPageRespVO> getTraineeEvaluationDetailPage(TraineeEvaluationDetailPageReqVO reqVO);

    /**
     * 班次评估统计-学员参评情况-导出
     *
     * @param reqVO    查询参数
     * @param response 响应
     */
    void exportTraineeEvaluationDetailExcel(TraineeEvaluationDetailPageReqVO reqVO, HttpServletResponse response) throws IOException;

    /**
     * 班次评估统计-学员参评情况-学员已评、未评详情-分页
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    PageResult<TraineeEvaluatedAndUnEvaluatedDetailPageRespVO> getTraineeEvaluatedAndUnEvaluatedDetailPage(TraineeEvaluatedAndUnEvaluatedDetailPageReqVO reqVO);

    /**
     * 班次评估统计-学员参评情况-学员已评、未评详情-导出
     *
     * @param reqVO    查询参数
     * @param response 响应
     */
    void exportTraineeEvaluatedAndUnEvaluatedDetailExcel(TraineeEvaluatedAndUnEvaluatedDetailPageReqVO reqVO, HttpServletResponse response) throws IOException;

    /**
     * 班次评估统计-课评详情-学员详情-分页
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    PageResult<CourseTraineeEvaluationDetailPageRespVO> getCourseTraineeEvaluationDetailPage(MyEvaluationDetailPageReqVO reqVO);

    /**
     * 部门评估统计-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    PageResult<DeptEvaluationPageRespVO> getDeptEvaluationStatsPage(DeptEvaluationPageReqVO reqVO);

    /**
     * 部门评估统计-导出
     *
     * @param reqVO 查询参数
     * @return 导出结果列表
     */
    List<DeptEvaluationStatsExcelVO> getExportDeptEvaluationStatsExcel(DeptEvaluationPageReqVO reqVO);

    PageResult<AllEvaluationPageRespVO> getAllEvaluationPage(AllEvaluationPageReqVO reqVO);

    Long countClassUnfinished(Long classId);

    void distribute(DistributeVO reqVO);

    void revoke(DistributeVO reqVO);

    /**
     * 撤回已评问卷
     * 
     * @param reqVO 撤回请求
     * @return 是否撤回成功
     */
    boolean revokeEvaluated(EvaluationRevokeReqVO reqVO);

    void deleteByClassCourseId(List<Long> classCourseIds);

    List<Long> getExistEvaluationTrainee(Long classCourseId);

    /**
     * 业中首页-仪表盘 - 课程评估情况
     * @param reqVO 请求参数
     * @return 响应
     */
    ClassEvaluationForBusinessCenterRespVO classEvaluationForBusinessCenter(ClassEvaluationForBusinessCenterReqVO reqVO);

    /**
     * 课程评估详情-APP端
     * @param classId 班次ID
     * @param isDone 是否已完成评估
     * @return 课程评估详情列表
     */
    List<CourseEvaluationResponseVO> getCourseEvaluationForApp(Long classId, Boolean isDone);

    /**
     * 教师移动端-课评查看-选修课、专题课查看学员评估详情
     * @param classCourseId 排课id
     * @param courseId 课程id (选修课才有)
     * @param isDone 是否已完成评估
     * @return 学员评估详情列表
     */
    List<CourseEvaluationTraineeDetailVO> getCourseEvaluationTraineeDetailForApp(Long classCourseId, Long courseId, Boolean isDone);
}
