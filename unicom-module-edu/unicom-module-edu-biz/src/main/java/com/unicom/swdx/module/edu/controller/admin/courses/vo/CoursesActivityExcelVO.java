package com.unicom.swdx.module.edu.controller.admin.courses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 课程库 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class CoursesActivityExcelVO {

    @ExcelProperty("活动名称")
    private String name;

    @ExcelProperty("活动类型")
    private String activityTypeName;

    @ExcelProperty("教学方式")
    private String teachingMethod;

}
