package com.unicom.swdx.module.edu.dal.mysql.rollcallrecord;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallrecord.RollcallRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学员点名签到记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RollcallRecordMapper extends BaseMapperX<RollcallRecordDO> {

    /**
     * 获取签到详情-未签到、已签到人员信息
     *
     * @param id     点名签到id
     * @param status 签到状态筛选 0-未签到 1-已签到
     * @return 签到详情学员详情列表
     */
    List<AppTraineeGroupRespVO> getRollcallSignInTraineeInfo(@Param("id") Long id,
                                                             @Param("status") Integer status);

    /**
     * 根据学员id获取点名签到数据
     * @param traineeId 学员id
     * @return 点名签到信息
     */
    default List<RollcallRecordDO> getRollcallRecordByTraineeId(Long traineeId) {
        return selectList(new LambdaQueryWrapperX<RollcallRecordDO>()
                .eqIfPresent(RollcallRecordDO::getTraineeId, traineeId));
    }

    /**
     * 根据学员id获取点名签到数据
     * @param traineeIds 学员id列表
     * @return 点名签到信息
     */
    default List<RollcallRecordDO> getRollcallRecordByTraineeIds(List<Long> traineeIds) {
        return selectList(new LambdaQueryWrapperX<RollcallRecordDO>()
                .inIfPresent(RollcallRecordDO::getTraineeId, traineeIds));
    }
}
