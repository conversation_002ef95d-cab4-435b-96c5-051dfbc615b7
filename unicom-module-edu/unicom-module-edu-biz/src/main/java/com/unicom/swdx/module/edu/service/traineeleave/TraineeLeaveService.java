package com.unicom.swdx.module.edu.service.traineeleave;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassManagementExportParamsVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;

import java.util.List;
import java.util.Map;

public interface TraineeLeaveService extends IService<TraineeLeaveDO> {

    TraineeLeaveRespVO getDraft(Long classId);

    Long saveDraft(TraineeLeaveBaseVO reqVO);

    Long createLeave(TraineeLeaveCreateReqVO reqVO);

    TraineeLeaveRespVO getTraineeLeaveDetail(Long leaveId);

    List<TraineeLeaveProcessVO> getTraineeLeaveProcessList(Long leaveId);

    Boolean cancelLeave(Long leaveId, String modifyReason);

    Integer getNumber();

    Boolean updateAccessory(TraineeLeaveCreateReqVO reqVO);

    PageResult<TraineeLeaveRespVO> pageList(TraineeLeavePageReqVO reqVO);

    List<TraineeLeaveExcelVO> getLeaveExportList(TraineeLeaveExportReqVO reqVO);

    Integer getLeaveCount(Long classId);

    List<TraineeLeaveRespVO> teacherList(Long classId, Integer status);

    List<TraineeLeaveRespVO> getMyList(TraineeLeaveMyReqVO reqVO);

    Boolean modify(TraineeLeaveModifyReqVO reqVO);

    Boolean deal(TraineeLeaveDealReqVO reqVO);

    Boolean transferBack(TraineeLeaveDealReqVO reqVO);

    Boolean traineeUpdate(TraineeLeaveUpdateReqVO reqVO);

    void deleteLeaveInfoByTraineeId(Long id);
    void deleteLeaveInfoByTraineeIds(List<Long> ids);

    List<TraineeLeaveRespVO> getList(List<Long> leaveIds);

    TraineeLeaveRespVO getLeaveById(Long leaveId);

    Map<String, Object> getLeaveDateMap(Long leaveId);
}
