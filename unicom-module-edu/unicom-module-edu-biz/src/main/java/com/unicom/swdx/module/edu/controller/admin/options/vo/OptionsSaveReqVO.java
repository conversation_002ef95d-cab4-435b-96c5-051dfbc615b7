package com.unicom.swdx.module.edu.controller.admin.options.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 选项新增/修改 Request VO")
@Data
public class OptionsSaveReqVO {

    @Schema(description = "主键ID",  example = "29054")
    private Long id;

    @Schema(description = "选项类型(字典)", example = "1")
    private String optionsType;

    @Schema(description = "选项内容")
    private String content;

    @Schema(description = "选项分数")
    private BigDecimal score;

    @Schema(description = "创建部门")
    private Long createDept;

    @Schema(description = "创建人")
    private Long creator;

    @Schema(description = "更新人")
    private Long updater;

    @Schema(description = "删除标志")
    private Integer deleted;

    @Schema(description = "题干主键", example = "27397")
    private Long questionId;

}
