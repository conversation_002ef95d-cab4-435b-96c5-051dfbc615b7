package com.unicom.swdx.module.edu.convert.trainee;

import com.unicom.swdx.module.edu.controller.admin.trainee.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * EduClassroomLibrary Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TraineeInfoExportConvert {

    TraineeInfoExportConvert INSTANCE = Mappers.getMapper(TraineeInfoExportConvert.class);

    default List<RegistrationExcelVO> convertList(List<UnitRegistrationPageRespVO> tmpList) {
        if ( tmpList == null ) {
            return null;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<RegistrationExcelVO> list = new ArrayList<RegistrationExcelVO>( tmpList.size() );
        for ( UnitRegistrationPageRespVO unitRegistrationPageRespVO : tmpList ) {
            if ( unitRegistrationPageRespVO == null ) {
                return null;
            }

            RegistrationExcelVO registrationExcelVO = new RegistrationExcelVO();

            if ( unitRegistrationPageRespVO.getIndex() != null ) {
                registrationExcelVO.setIndex( unitRegistrationPageRespVO.getIndex().intValue() );
            }
            registrationExcelVO.setName( unitRegistrationPageRespVO.getName() );
            registrationExcelVO.setSex( unitRegistrationPageRespVO.getSex() );
            registrationExcelVO.setCardNo( unitRegistrationPageRespVO.getCardNo() );
            registrationExcelVO.setPhone( unitRegistrationPageRespVO.getPhone() );
            registrationExcelVO.setCreateTime( unitRegistrationPageRespVO.getCreateTime() );
            registrationExcelVO.setUnitName( unitRegistrationPageRespVO.getUnitName() );
            if ( unitRegistrationPageRespVO.getEducationalLevel() != null ) {
                registrationExcelVO.setEducationalLevel( String.valueOf( unitRegistrationPageRespVO.getEducationalLevel() ) );
            }
            registrationExcelVO.setPosition( unitRegistrationPageRespVO.getPosition() );
            if ( unitRegistrationPageRespVO.getJobLevel() != null ) {
                registrationExcelVO.setJobLevel( String.valueOf( unitRegistrationPageRespVO.getJobLevel() ) );
            }
            if ( unitRegistrationPageRespVO.getStatus() != null ) {
                registrationExcelVO.setStatus( String.valueOf( unitRegistrationPageRespVO.getStatus() ) );
            }
            if ( unitRegistrationPageRespVO.getPoliticalIdentity() != null ) {
                registrationExcelVO.setPoliticalIdentity( String.valueOf( unitRegistrationPageRespVO.getPoliticalIdentity() ) );
            }
            if ( unitRegistrationPageRespVO.getStatusUpdateTime() != null ) {
                registrationExcelVO.setStatusUpdateTime( unitRegistrationPageRespVO.getStatusUpdateTime().format(formatter) );
            }
            registrationExcelVO.setClassName( unitRegistrationPageRespVO.getClassName() );
            list.add(registrationExcelVO);
        }

        return list;
    }

    List<RegistrationDetailExcelVO> convertList1(List<RegistrationDetailVO> list);

    RegistrationDetailExcelVO convert(RegistrationDetailVO item);
}
