package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.historydata;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.math.BigDecimal;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 历史评价数据 Excel VO
 */
@Data
public class HistoryDataExcelVO {

    @ExcelProperty(value = "课程名称")
    private String kcName;

    @ExcelProperty(value = "教学形式")
    private String jxxs;

    @ExcelProperty(value = "授课时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime sksj;

    @ExcelProperty(value = "部门")
    private String orgName;

    @ExcelProperty(value = "师资来源")
    private String teacherSource;

    @ExcelProperty(value = "教师姓名")
    private String teacherName;

    @ExcelProperty(value = "班次名称")
    private String className;

    @ExcelProperty(value = "学员数量")
    private Integer totalNum;

    @ExcelProperty(value = "平均分")
    private BigDecimal score;

    @ExcelProperty(value = "排名分")
    private BigDecimal rankScore;

    // 保留以下字段但不导出到Excel中
    private Integer serialNumber;
    private String teacherId;
    private String classId;
    private Integer ypNum;
    private BigDecimal pkRate;
    private Integer scoreRank;
    private String sksd;
}
