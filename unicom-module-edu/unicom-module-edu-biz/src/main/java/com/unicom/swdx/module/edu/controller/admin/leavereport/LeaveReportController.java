package com.unicom.swdx.module.edu.controller.admin.leavereport;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.security.core.service.SecurityFrameworkService;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoRespVO;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDO;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDetailDO;
import com.unicom.swdx.module.edu.service.leavereport.LeaveReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 班主任离校报备管理")
@RestController
@RequestMapping("/edu/leave-report")
@Validated
public class LeaveReportController {

    @Resource
    private LeaveReportService leaveReportService;

    @Resource
    private SecurityFrameworkService ss; // 注入安全框架服务

    @PostMapping("/create")
    @Operation(summary = "创建离校申请")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:create')")
    public CommonResult<Long> createLeaveReport(@Valid @RequestBody LeaveReportCreateReqVO createReqVO) {
        return success(leaveReportService.createLeaveReport(createReqVO));
    }

//    @PostMapping("/update")
//    @Operation(summary = "更新离校申请")
//    @PreAuthorize("@ss.hasPermission('edu:question-category-management:update')")
//    public CommonResult<Boolean> updateLeaveReport(@Valid @RequestBody LeaveReportSaveReqVO updateReqVO) {
//        LeaveReportService.updateLeaveReport(updateReqVO);
//        return success(true);
//    }

@PostMapping("/delete")
@Operation(summary = "删除离校申请")
@Parameter(name = "id", description = "编号", required = true)
@PreAuthorize("@ss.hasPermission('edu:question-category-management:delete')")
public CommonResult<Boolean> deleteLeaveReport(@RequestParam("id") Long id) {
    leaveReportService.deleteLeaveReport(id);
    return success(true);
}

    @GetMapping("/getDetail")
    @Operation(summary = "获得离校申请详情")
    @Parameter(name = "leaveId", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<TeacherLeaveReportLeaveVO> getLeaveReport(@RequestParam("leaveId") Long leaveId, @RequestParam(value = "fillStatus",required = false) Integer fillStatus) {
        return success(leaveReportService.getLeaveReportDetail(leaveId, fillStatus));
    }



    @GetMapping("/getDetailPage")
    @Operation(summary = "获得离校申请详情")
    @Parameter(name = "leaveId", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<TeacherLeaveReportLeavePageVO> getLeaveReportPage(@RequestParam("leaveId") Long leaveId,
                                                                      @RequestParam(value = "fillStatus",required = false) Integer fillStatus,
                                                                      @RequestParam("pageNo") Integer pageNo,
                                                                      @RequestParam("pageSize") Integer pageSize
                                                                      ) {
        return success(leaveReportService.getLeaveReportDetailPage(leaveId, fillStatus  ,pageNo , pageSize));
    }

    @GetMapping("/getTime")
    @Operation(summary = "获得离校报备起止时间")
    @Parameter(name = "leaveId", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<LeaveReportTimeVO> getLeaveReportTime(@RequestParam("leaveId") Long leaveId) {
        return success(leaveReportService.getLeaveReportTime(leaveId));
    }

//    @GetMapping("/remind")
//    @Operation(summary = "班主任一键提醒")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
//    public CommonResult<LeaveReportRespVO> remind(@RequestParam("id") Long id) {
//        LeaveReportDO LeaveReport = leaveReportService.getLeaveReport(id);
//        return success(BeanUtils.toBean(LeaveReport, LeaveReportRespVO.class));
//    }

    @GetMapping("/getByClassId")
    @Operation(summary = "获得班级已发起的离校申请")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<List<LeaveReportRespVO>> getLeaveReportList(@RequestParam("classId") Long classId) {
        return success(leaveReportService.getLeaveReportList(classId));
    }

    @GetMapping("/list-all")
    @Operation(summary = "获得所有离校申请")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<List<LeaveReportRespVO>> getAllLeaveReportList() {
        return success(leaveReportService.getAllLeaveReportList());
    }

    @GetMapping("/list")
    @Operation(summary = "获得离校申请列表（支持筛选）")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<PageResult<LeaveReportRespVO>> getLeaveReportList(@Valid LeaveReportQueryReqVO queryReqVO) {
        return success(leaveReportService.getLeaveReportPage(queryReqVO));
    }

    @PostMapping("/remind")
    @Operation(summary = "一键提醒")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:create')")
    public CommonResult<Boolean> remind(@Valid @RequestBody LeaveReportRemindReqVO remindReqVO) {
        return success(leaveReportService.remind(remindReqVO));
    }

    @GetMapping("/teacher-permissions")
    @Operation(summary = "获取班主任权限配置")
    public CommonResult<TeacherPermissionRespVO> getTeacherPermissions() {
        TeacherPermissionRespVO permissions = new TeacherPermissionRespVO();

        // 检查离校报备-创建按钮权限
        permissions.setCreateLeaveReportPermission(
                ss.hasPermission("edu:question-category-management:create"));

        // 检查班级考勤-考勤调整权限
        permissions.setAttendanceAdjustmentPermission(
                ss.hasPermission("edu:attendance:adjustment"));

        // 检查班级考勤-未到考勤-补卡权限
        permissions.setAttendanceMakeupPermission(
                ss.hasPermission("edu:attendance:makeup"));

        return success(permissions);
    }

    @GetMapping("/opening-classes")
    @Operation(summary = "获取当前租户下所有开班中的班级")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<List<ClassInfoRespVO>> getOpeningClasses(
            @RequestParam(value = "campus", required = false) Integer campus) {
        return success(leaveReportService.getOpeningClasses(campus));
    }

    @GetMapping("/all-classes")
    @Operation(summary = "获取当前租户下所有班级，支持按名称筛选")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<List<ClassInfoRespVO>> getAllClasses(
            @RequestParam(value = "className", required = false) String className) {
        return success(leaveReportService.getAllClassesWithFilter(className));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出题目类别管理 Excel")
//    @PreAuthorize("@ss.hasPermission('edu:question-category-management:export')")
//
//    public void exportLeaveReportExcel(@Valid LeaveReportListReqVO listReqVO,
//                                                      HttpServletResponse response) throws IOException {
//        List<LeaveReportDO> list = leaveReportService.getLeaveReportList(listReqVO);
//        // 导出 Excel
//        ExcelUtils.write(response, "题目类别管理.xls", "数据", LeaveReportRespVO.class,
//                BeanUtils.toBean(list, LeaveReportRespVO.class));
//    }

    @PostMapping("/export-excel")
    @Operation(summary = "导出离校报备 Excel")
    @PreAuthorize("@ss.hasPermission('edu:leave-report:export')")
    @OperateLog(type = EXPORT)
    public void exportLeaveReportExcel(@Valid @RequestBody LeaveReportExportReqVO exportReqVO,
            HttpServletResponse response) throws IOException {
        // 使用service层方法获取导出数据，只传入IDs参数
        List<LeaveReportExcelVO> list = leaveReportService.getExportAllLeaveReportExcel(exportReqVO.getIds());

        // 获取列过滤选项
        Set<Integer> includeColumnIndexes = exportReqVO.getIncludeColumnIndexes();
        if (includeColumnIndexes != null && !includeColumnIndexes.isEmpty()) {
            // 使用列过滤功能，直接传入列索引集合，无需转换索引
            ExcelUtils.writeByIncludeColumnIndexes(response, "离校报备.xlsx", "数据",
                    LeaveReportExcelVO.class, list, includeColumnIndexes);
        } else {
            // 导出所有列
            ExcelUtils.write(response, "离校报备.xlsx", "数据", LeaveReportExcelVO.class, list);
        }
    }
}
