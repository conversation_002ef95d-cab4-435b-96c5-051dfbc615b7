package com.unicom.swdx.module.edu.dal.mysql.electivereleaseclasses;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.classInfo.ClassInfoVO;
import com.unicom.swdx.module.edu.dal.dataobject.electivereleaseclasses.ElectiveReleaseClassesDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 发布信息班级关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ElectiveReleaseClassesMapper extends BaseMapperX<ElectiveReleaseClassesDO> {


    List<ElectiveReleaseClassesDO> selectListByClassTimePeriod(@Param("classStartDateTime") LocalDateTime classStartDateTime, @Param("classEndDateTime") LocalDateTime classEndDateTime, @Param("excludeId") Long excludeId);

    List<ClassInfoVO> selectClassInfoListByReleaseId(@Param("releaseId") Long releaseId);

    default int deleteByReleaseId(Long releaseId) {
        return delete(new LambdaQueryWrapperX<ElectiveReleaseClassesDO>().eq(ElectiveReleaseClassesDO::getReleaseId, releaseId));
    }

    default List<ElectiveReleaseClassesDO> selectListByReleaseId(Long releaseId) {
        return selectList(new LambdaQueryWrapperX<ElectiveReleaseClassesDO>().eq(ElectiveReleaseClassesDO::getReleaseId, releaseId));
    }

    default int deleteBatchByReleaseId(List<Long> releaseIdList) {
        return delete(new LambdaQueryWrapperX<ElectiveReleaseClassesDO>().in(ElectiveReleaseClassesDO::getReleaseId, releaseIdList));
    }

    List<Long> selectCompleteElectiveIdList();
}
