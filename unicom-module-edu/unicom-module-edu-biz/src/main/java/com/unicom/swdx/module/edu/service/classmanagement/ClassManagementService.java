package com.unicom.swdx.module.edu.service.classmanagement;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.*;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * EduClassManagement Service 接口
 *
 * <AUTHOR>
 */
public interface ClassManagementService {

    /**
     * 创建EduClassManagement
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createClassManagement(@Valid ClassManagementCreateReqVO createReqVO);

    /**
     * 更新EduClassManagement
     *
     * @param updateReqVO 更新信息
     */
    void updateClassManagement(@Valid ClassManagementUpdateReqVO updateReqVO);

    /**
     * 删除EduClassManagement
     *
     * @param id 编号
     */
    void deleteClassManagement(Long id);

    /**
     * 批量删除EduClassManagement
     *
     * @param classManagerDeleteVO
     */
    void deleteClassManagerDeleteBatch(ClassManagerDeleteVO classManagerDeleteVO);

    /**
     * 获得EduClassManagement
     *
     * @param id 编号
     * @return EduClassManagement
     */
    ClassManagementDO getClassManagement(Long id);

    /**
     * 获得EduClassManagement列表
     *
     * @param ids 编号
     * @return EduClassManagement列表
     */
    List<ClassManagementDO> getClassManagementList(Collection<Integer> ids);

    /**
     * 获得EduClassManagement分页
     *
     * @param pageReqVO 分页查询
     * @return EduClassManagement分页
     */
    PageResult<ClassManagementRespVO> getClassManagementPage(HttpServletRequest request, ClassManagementPageReqVO pageReqVO);

    /**
     * 选修课发布创建-根据上课时间段选择班级范围分页列表
     * @param reqVO 分页请求参数
     * @return 分页列表
     */
    PageResult<ClassManagementElectiveReleaseRespVO> getPageForElectiveReleaseCreate(ClassManagementElectiveReleasePageReqVO reqVO);

    /**
     * 更新排序
     *
     * @param id , sort
     * @return
     */
    Integer updateClassManagementSort(Long id, Integer sort);

    /**
     * 批量发布EduClassManagement
     *
     * @param classManagerDeleteVO
     */
    void updateClassManagementPublish(ClassManagerDeleteVO classManagerDeleteVO);


    /**
     * 获得EduClassManagement列表, 用于 Excel 导出
     *
     * @param reqVO
     * @return
     */
    List<ClassManagementExcelVO> getClassManagementInfoList(ClassManagementExportParamsVO reqVO);

    /**
     * 导入班级信息
     */
    ClassManagementImportRespVO importClassInfo(List<ClassInfoImportStrExcelVO> classInfoList);

    /**
     * 获得EduClassManagement分页
     *
     * @param pageReqVO 分页查询
     * @return EduClassManagement分页
     */
    List<ClassManagementDO> getClassManagementPageApplet(ClassManagementPageReqVO pageReqVO);

    /**
     * 通过用户id获取这个用户教师管理的班级id集合
     *
     * @param userId 用户
     * @return 班级id集合
     */
    List<Long> getClassIdListByUserId(Long userId);

    /**
     * 获取当前登录用户班主任权限下的班级id集合
     *
     * @return 班级id集合
     */
    List<Long> getLoginUserClassMasterLimitClassList();

    List<ClassInfoRespVO> getClassPageByStatus(Integer status,Long teacherId, Integer clockIn);

    List<String> getDefaultClassByTeacherId(Long teacherId, Integer clockIn);

    /**
     *
     * @param clockingInUpdateReqVO
     * @return 班级id集合
     */
    void updateClassClockingInRule(ClassClockingInUpdateReqVO clockingInUpdateReqVO);
    /**
     *
     * @param classSignUpUnitBaseVO
     * @return
     */
    Integer createClassSignUp(@Valid List<ClassSignUpUnitBaseVO> classSignUpUnitBaseVO);

    ClassSimpleInfo getSimpleClassInfo(Long id);

    List<ClassSimpleInfo> getSimpleClassInfoList(@Param("teacherId") Long teacherId, @Param("className") String className);

    /**
     * 获取已排课班级
     * @param className
     * @return
     */
    List<ClassHaveCourseVO> getClassHasCourseList(String className);

    List<ClassManagementDO> getClassList();

    List<ClassManagementDO> getComplateClassList();

    /**
     * 获取首页统计信息
     * @return
     */
    StatRespVO getStat();
    /**
     * @param classCompletionUpdateReqVO
     * @return
     */
    void updateCompletionTemplate(ClassCompletionUpdateReqVO classCompletionUpdateReqVO);

    /**
     * 业中首页-仪表盘-获取班级下钻列表
     * @param reqVO 筛选条件
     * @return 班级列表
     */
    List<ClassManagementSimpleForBusinessCenterRespVO> simpleListForBusinessCenter(ClassManagementSimpleForBusinessCenterReqVO reqVO);

    /**
     * 根据父单位id获取班级名称
     * @param unitId 父单位id
     * @return 班级名称列表
     */
    List<ClassManagementDO> getClassByUnitId(Integer unitId);

    ClassManagementImportRespVO unitImportClassInfo(List<UnitImportExcelVO> list,Long classId);

    List<String> checkUnitImportClassInfo(List<UnitImportExcelVO> list, Long classId);
}
