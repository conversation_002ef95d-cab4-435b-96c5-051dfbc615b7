package com.unicom.swdx.module.edu.dal.dataobject.clockininfo;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 考勤签到 DO
 *
 * <AUTHOR>
 */
@TableName("edu_clock_in_info")
@KeySequence("edu_clock_in_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClockInInfoDO extends TenantBaseDO {

    /**
     * 主键id，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 班级id
     */
    private Long classId;
    /**
     * 学员id
     */
    private Long traineeId;
    /**
     * 排课表id
     */
    private Long classCourseId;
    /**
     * 0-到课，1-就餐，2-住宿
     */
    private Integer type;
    /**
     * 0-早餐，1-午餐，2-晚餐（就餐专属）
     */
    private Integer mealPeriod;
    /**
     * 0-未打卡，1-已打卡，2-迟到，3-请假
     */
    private Integer traineeStatus;
    /**
     * 学员打卡时间
     */
    private LocalDateTime clockingTime;
    /**
     * 请假类型1事假2病假3五会假
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer leaveType;
    /**
     * 考勤日期
     */
    private LocalDate date;

    /**
     * 考勤开始时间
     */
    private LocalDateTime checkBeginTime;

    /**
     * 考勤结束时间
     */
    private LocalDateTime checkEndTime;

    /**
     * 时间段（0上午，1下午，2晚上），用于到课考勤定位同一午别下的签到
     */
    private String period;

    /**
     * 标记大课考勤id, 用于撤回签到
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long largeAttendanceId;

    /**
     * 报到时间
     */
    @TableField(exist = false)
    private LocalDateTime reportTime;
}
