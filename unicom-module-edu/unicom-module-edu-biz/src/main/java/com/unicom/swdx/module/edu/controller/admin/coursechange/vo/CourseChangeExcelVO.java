package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.unicom.swdx.framework.excel.core.annotations.DictFormat;
import com.unicom.swdx.framework.excel.core.convert.DictConvert;
import com.unicom.swdx.module.system.enums.DictTypeConstants;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CourseChangeExcelVO {

    @ExcelProperty("班次名称")
    private String className;

    @ExcelProperty("调课时间")
    private LocalDateTime changeTime;

    @ExcelProperty(value = "调课类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.EDU_COURSE_CHANGE_TYPE)
    private Integer changeType;

    @ExcelProperty(value = "操作人")
    private String changeUserName;

    @ExcelProperty(value = "调课原因")
    private String changeReason;

    @ExcelProperty(value = "调课前")
    private String classInfoBefore;

    @ExcelProperty(value = "调课后")
    private String classInfoAfter;
}
