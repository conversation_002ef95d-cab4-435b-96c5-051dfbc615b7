package com.unicom.swdx.module.edu.service.electivetraineeselection;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.electivetraineeselection.ElectiveTraineeSelectionDO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 选修课学员选课 Service 接口
 *
 * <AUTHOR>
 */
public interface ElectiveTraineeSelectionService extends IService<ElectiveTraineeSelectionDO> {

    /**
     * 创建选修课学员选课
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createElectiveTraineeSelection(@Valid ElectiveTraineeSelectionCreateReqVO createReqVO);


    /**
     * 批量创建选修课学员选课
     * @param batchCreateReqVO 学员批量选课
     * @return 是否成功
     */
    Boolean batchCreateElectiveTraineeSelection(List<ElectiveTraineeSelectionCreateReqVO> batchCreateReqVO);

    /**
     * 发布课程已选课人员信息分页
     *
     * @param reqVO 请求参数
     * @return 分页结果
     */
    PageResult<ElectiveCourseTraineeSelectedRespVO> getCourseSelectedTraineePage(ElectiveCourseTraineeSelectedPageReqVO reqVO);

    /**
     * 导出发布课程已选课人员信息
     *
     * @param reqVO    请求参数
     * @param response 响应参数
     */
    void exportCourseSelectedTraineeExcel(ElectiveCourseTraineeSelectedPageReqVO reqVO, HttpServletResponse response) throws IOException;

    /**
     * 获取选修课发布信息-已经选课人员信息列表
     * @param reqVO 请求参数
     * @return 选修课发布信息-已经选课人员信息列表
     */
    List<AppElectiveTraineeSelectionSimpleRespVO> getCourseSelectionTraineeList(AppElectiveTraineeSelectionReleaseReqVO reqVO);

    /**
     * 班主任移动端-选修课管理-班级是否存在未选学员(气泡)
     * @param classId 班级ID
     * @return 是否存在未选学员
     */
    boolean existUnSelectTeacher(Long classId);

    /**
     * 学员移动端-选修课-是否存在未选选修课(气泡)
     * @return 是否存在未选选修课
     */
    boolean existUnSelectTrainee();

    void deleteElectiveInfoByTraineeId(Long traineeId);

    void deleteElectiveInfoByTraineeIds(List<Long> traineeIds);
}
