package com.unicom.swdx.module.edu.dal.mysql.rollcallsignin;

import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo.RollcallSignInListReqVO;
import com.unicom.swdx.module.edu.controller.admin.rollcallsignin.vo.RollcallSignInRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.rollcallsignin.RollcallSignInDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 大课考勤、点名签到信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RollcallSignInMapper extends BaseMapperX<RollcallSignInDO> {

    default RollcallSignInDO selectOneByClassIdAndTypeAndCurrentTime(Long classId, Integer status, LocalDateTime now) {
        return selectOne(new LambdaQueryWrapperX<RollcallSignInDO>().eq(RollcallSignInDO::getClassId, classId).eq(RollcallSignInDO::getType, status).gt(RollcallSignInDO::getCheckEndTime, now).eq(RollcallSignInDO::getEnded, 0).last("limit 1"));
    }

    /**
     * 根据班级id,点名签到状态（进行中、已结束）查询条件，获取点名签到信息列表
     *
     * @param reqVO 查询条件
     * @param now   当前时间
     * @return 点名签到信息列表
     */
    List<RollcallSignInRespVO> selectRollcallSignInListByReqVO(@Param("reqVO") RollcallSignInListReqVO reqVO,
                                                               @Param("currentTime") LocalDateTime now);

    /**
     * 查询班级某堂课是其他正在进行、未开始的大课考勤
     *
     * @param classCourseId 班级课程id
     * @param classId       班级id
     * @return 班级某堂课是其他正在进行、未开始的大课考勤
     */
    List<RollcallSignInDO> selectListByClassIdAndClassCourseId(@Param("classCourseId") Long classCourseId,
                                                               @Param("classId") Long classId,
                                                               @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据班级id,签到状态（进行中、已结束、未开始）查询条件，获取大课考勤信息列表
     *
     * @param reqVO 查询条件
     * @param now   当前时间
     * @return 大课考勤信息列表
     */
    List<RollcallSignInRespVO> selectLectureAttendanceListByReqVO(@Param("reqVO") RollcallSignInListReqVO reqVO,
                                                                  @Param("currentTime") LocalDateTime now);

    /**
     * 获取班级当前正在进行的大课考勤或者点名签到
     * @param classId 班级id
     * @param type 类型
     * @param nowDateTime 当前时间
     * @return 大课考勤或者点名签到
     */
    List<RollcallSignInDO> selectListByCurrentTimeRule(@Param("classId") Long classId,
                                                       @Param("type") Integer type,
                                                       @Param("nowDateTime") LocalDateTime nowDateTime);

    /**
     * 获取该次发起打卡时间与班级当前正在进行的时间段有重叠的大课考勤列表
     * @param classId 班级id
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param nowDateTime 当前时间
     * @return 大课考勤
     */
    List<RollcallSignInDO> selectListByCheckOverlap(@Param("classId") Long classId,
                                                    @Param("beginTime") LocalDateTime beginTime,
                                                    @Param("endTime") LocalDateTime endTime,
                                                    @Param("nowDateTime") LocalDateTime nowDateTime);
}
