package com.unicom.swdx.module.edu.service.training;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.module.edu.controller.admin.todoitems.dto.ECContentDTO;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.*;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 用户信息 Service 接口
 *
 * <AUTHOR>
 */
public interface TraineeService extends IService<TraineeDO> {

    Page<RegistrationPageRespVO> getPage(RegistrationInfoReqVO reqVO);

    List<RegistrationDetailExcelVO> getRegistrationInfoList(RegistrationInfoReqVO reqVO);

    Page<RegistrationInfoRespVO> getPageByClassId(RegistrationInfoReqVO reqVO);

    Workbook exportHighRiskIndustryStatisticsTable(RegistrationInfoReqVO reqVO, List<Map<String, Object>> list, HttpServletResponse response);

    Page<UnitRegistrationPageRespVO> getPageByUnitId(UnitRegistrationPageReqVO reqVO);

    Page<UnitRegistrationPageRespVO> pageTraineeInfo(TraineeInfoReqVO reqVO);

    Long addTrainee(AddTraineeInfoReqVO reqVO);

    Page<ReportPageRespVO> reportPage(ReportPageReqVO reqVO);

    List<RegistrationInfoExcelVO> getUnitRegistrationList(RegistrationInfoReqVO reqVO);

    List<RegistrationExcelVO> getTraineeInfoList(TraineeInfoReqVO reqVO);

    Long editTrainee(EditTraineeInfoReqVO reqVO);

    Boolean deleteTrainee(Long ids);

    TraineeDO getTrainById(Long id);

    Boolean unshackleTrainee(List<Long> classIds);

    List<ReportPageRespVO> getreportList(ReportPageReqVO reqVO);

    void sendAllTraineeInfo(String url);

    List<UnitRegistrationPageRespVO> getAllTraineeInfo(TraineeInfoReqVO reqVO);

    Boolean checkInByIds(List<Long> ids);

    Boolean setGroupOrCommittee(SetGroupCommitteeReqVO reqVO);

    /**
     * 检查学员权限并禁止访问
     *
     */
    void checkTrainPermissionAndAccessDenied(HttpServletRequest request,Long userId);

    Boolean traineeOrder(TraineeOrderReqVO reqVO);

    Boolean batchSetGroup(BatchSetGroupReqVO reqVO);

    Map<String, Object> getTraineeType(String phone);


    Map<String, Object> getTraineeTypebyTraineeId(String traineeId);

    TraineeByCardNoVO getTrainByCardNo(String cardNo,Long classId);

    /**
     * 获取某个班级 总人数、报名已确认、报名未确认人数信息
     * @param  classId 班级id
     * @return ECContentDTO
     */
    ECContentDTO getECContent(Long classId);

    TraineeImportRespVO importInfo(List<TraineeInfoImportStrExcelVO> list,Long classId);

    List<TraineeDO> getAllTraineeByClassIds(List<Long> classIdList);

    List<TraineeDO> selectTraineeByClassCommitteeId(Long id);


    Boolean selectTraineeByUserId(HttpServletRequest request, Long id);
    List<Long> removeJWUser(List<Long> ids);

    TraineeDO getTraineeByUserId(Long userId);

    Long getUserBySystemId(String id);

    void updateUserSystemId();

    void setGroupOrder(Long groupId);
    /**
     * 参训统计
     * @param reqVO
     * @return
     */
    Page<TraineeStatPageRespVO> getTraineeStat(@Valid TraineeInfoStatReqVO reqVO);


    String sendTraineeBatchToMid(List<TraineeDO> list);

    /**
     * 参训统计导出
     * @param response
     * @param reqVO
     */
    void traineeStatExport(HttpServletResponse response, @Valid TraineeInfoStatReqVO reqVO);

    /**
     * 单位统计导出
     * @param response
     * @param reqVO
     */
    void traineeStatByUnitExport(HttpServletResponse response, @Valid TraineeInfoStatByUnitReqVO reqVO);

    Page<TraineeInfoPageRespVO> getTraineeInfoPage(TraineeInfoPageReqVO reqVO);

    List<ExportTraineeInfoExcelVO> exportTraineeInfo(TraineeInfoPageReqVO reqVO);

    PageResult<TraineeGroupRespVO> getTrainByGroup(TraineeGroupReqVO reqVO);

    /**
     * 单位报名统计
     * @param reqVO 请求信息
     * @return Page<TraineeStatByUnitPageRespVO>
     */
    Page<TraineeStatByUnitPageRespVO> getTraineeStatByUnit(@Valid TraineeInfoStatByUnitReqVO reqVO);


    void updateTraineeUnitInfo(List<Integer> unitIds,SignUpUnitDO updateObj);
    Boolean batchDeleteTrainee(TraineeDeleteReqVO reqVO);

    void deleteByUnitIds(List<Integer> deleteUnitIds);

    /**
     * 根据id获取学员详细信息，返回TraineeBaseVO
     * 
     * @param id 学员id
     * @return 学员详细信息
     */
    TraineeBaseVO getTraineeDetailById(Long id);
}
