package com.unicom.swdx.module.edu.controller.admin.leavereport.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 班主任权限配置 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TeacherPermissionRespVO {

    @Schema(description = "离校报备-创建按钮权限", required = true)
    private Boolean createLeaveReportPermission;

    @Schema(description = "班级考勤-考勤调整权限", required = true)
    private Boolean attendanceAdjustmentPermission;

    @Schema(description = "班级考勤-未到考勤-补卡权限", required = true)
    private Boolean attendanceMakeupPermission;
}