package com.unicom.swdx.module.edu.service.completiontemplate;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.edu.constant.CompletionTemplateConstant;
import com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate.vo.ClassCompletionTemplateCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.dto.attendancerate.TraineeAttendanceInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClockInInfoPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClockInInfoStudentStatusVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceRateReqVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceRateTraineeInfoVO;
import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.TraineeEvaluationDetailPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.TraineeEvaluationDetailPageRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeavePageReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveRespVO;
import com.unicom.swdx.module.edu.convert.classcompletion.ClassCompletionConvert;
import com.unicom.swdx.module.edu.convert.completiontemplate.CompletionTemplateConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletion.ClassCompletionDO;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletiontemplate.ClassCompletionTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcompletion.ClassCompletionMapper;
import com.unicom.swdx.module.edu.dal.mysql.classcompletiontemplate.ClassCompletionTemplateMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.completiontemplate.CompletionTemplateMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.enums.classcompletion.DataSourceEnum;
import com.unicom.swdx.module.edu.enums.classcompletion.DefaultCompletionEnum;
import com.unicom.swdx.module.edu.enums.traineeleave.LeaveTaskStatus;
import com.unicom.swdx.module.edu.enums.traineeleave.TraineeLeaveType;
import com.unicom.swdx.module.edu.service.classcompletiontemplate.ClassCompletionTemplateServiceImpl;
import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoService;
import com.unicom.swdx.module.edu.service.evaluationresponse.EvaluationResponseService;
import com.unicom.swdx.module.edu.service.traineeleave.TraineeLeaveService;
import com.unicom.swdx.module.edu.service.training.TraineeServiceImpl;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 结业考核模版设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CompletionTemplateServiceImpl implements CompletionTemplateService {

    @Resource
    private CompletionTemplateMapper completionTemplateMapper;

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private TraineeServiceImpl traineeService;

    @Resource
    private ClassCompletionMapper classCompletionMapper;

    @Resource
    private ClassCompletionTemplateServiceImpl classCompletionTemplateService;

    @Resource
    private ClassCompletionTemplateMapper classCompletionTemplateMapper;

    @Resource
    private ClockInInfoService clockInInfoService;

    @Resource
    private TraineeLeaveService leaveService;

    @Resource
    private EvaluationResponseService evaluationResponseService;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;
    @Qualifier("com.unicom.swdx.module.system.api.dict.DictDataApi")
    @Autowired
    private DictDataApi dictDataApi;

    @Override
    public Integer createCompletionTemplate(CompletionTemplateCreateReqVO createReqVO) {
        // 插入
        CompletionTemplateDO completionTemplate = CompletionTemplateConvert.INSTANCE.convert(createReqVO);
        completionTemplateMapper.insert(completionTemplate);
        // 返回
        return completionTemplate.getId();
    }

    /**
     * 创建结业考核模版设置
     *
     * @param addReqVO 创建信息
     * @return 编号
     */
    @Override
    public List<Integer> addCompletionTemplate(List<CompletionTemplateCreateReqVO> addReqVO) {

        // 生成一个随机的 UUID
        UUID uuid = UUID.randomUUID();
        String uniqueCode = uuid.toString();

        // 模版名称唯一性校验
        campusNameRules(null, addReqVO.get(0).getTemplateName());

        // 同校区 默认规则只能拥有一个
        campusDefaultRules(null, addReqVO.get(0).getDefaultRule(), addReqVO.get(0).getCampus());


        // 新建一个数组
        List<CompletionTemplateDO> completionTemplateList = new ArrayList<>();

        for (CompletionTemplateCreateReqVO createReqVO : addReqVO) {
            createReqVO.setIdCode(uniqueCode);
            CompletionTemplateDO completionTemplate = CompletionTemplateConvert.INSTANCE.convert(createReqVO);
            completionTemplateList.add(completionTemplate);
        }

        // 批量插入
        completionTemplateMapper.insertBatch(completionTemplateList);

        // 返回插入记录的ID列表
        List<Integer> ids = new ArrayList<>();
        for (CompletionTemplateDO completionTemplate : completionTemplateList) {
            ids.add(completionTemplate.getId());
        }

        return ids;
    }

    @Override
    public void updateCompletionTemplate(CompletionTemplateUpdateReqVO updateReqVO) {

        //先删后插  下下策

        // 拿到所有数据  存在就修改，不存在就插入
        //根据主键 id 判断

        // 校验存在
        this.validateCompletionTemplateExists(updateReqVO.getId());
        // 更新
        CompletionTemplateDO updateObj = CompletionTemplateConvert.INSTANCE.convert(updateReqVO);
        completionTemplateMapper.updateById(updateObj);
    }

    /**
     * 更新结业考核模版设置
     *
     * @param editReqVO 更新信息
     */
    @Override
    public void updateCompletionTemplateData(List<CompletionTemplateUpdateReqVO> editReqVO) {

        //保存改数据的 idCode
        String idCode = editReqVO.get(0).getIdCode();


        List<CompletionTemplateDO> list = completionTemplateMapper.selectByTemplateCode(idCode);
        if (!list.isEmpty()) {
            //默认模板需要删除数据
            ClassCompletionTemplateCreateReqVO reqVO = new ClassCompletionTemplateCreateReqVO();
            reqVO.setIdCode(idCode);
            reqVO.setCampus(editReqVO.get(0).getCampus());

            reqVO.setDefaultRule(list.get(0).getDefaultRule());
            classCompletionTemplateService.deleteCompletionInfo(reqVO);
        }


        // 模版名称唯一性校验
        editNameRules(idCode, editReqVO.get(0).getTemplateName());

        // 同校区 默认规则只能拥有一个
        editDefaultRules(editReqVO.get(0).getDefaultRule(), editReqVO.get(0).getCampus(), editReqVO.get(0).getIdCode());

        //先删后插
        completionTemplateMapper.deleteCompletionTemplateByName(idCode);


        // 批量插入
        List<CompletionTemplateDO> completionTemplateList = new ArrayList<>();

        for (CompletionTemplateUpdateReqVO editVO : editReqVO) {
            editVO.setIdCode(idCode);
            CompletionTemplateDO completionTemplate = CompletionTemplateConvert.INSTANCE.convert(editVO);
            completionTemplateList.add(completionTemplate);
        }

        // 批量插入
        completionTemplateMapper.insertBatch(completionTemplateList);

        ClassCompletionTemplateCreateReqVO classCompletionTemplateCreateReqVO = new ClassCompletionTemplateCreateReqVO();
        classCompletionTemplateCreateReqVO.setIdCode(idCode);

        classCompletionTemplateService.createClassCompletionTemplate(classCompletionTemplateCreateReqVO);



    }

    @Override
    public void deleteCompletionTemplate(Integer id) {
        // 校验存在
        this.validateCompletionTemplateExists(id);
        // 删除
        completionTemplateMapper.deleteById(id);
    }

    /**
     * 删除模版
     *
     * @param idCode 模版名称
     */
    @Override
    public void deleteCompletionTemplateByName(String idCode) {

        //默认模板无法删除
        List<CompletionTemplateDO> list = completionTemplateMapper.selectOneByIdCode(idCode);

        if (list.isEmpty()){
            //模板不存在
            throw exception(TEMPLATE_NOT_EXISTS);
        }

        //默认模板
        if (Objects.equals(list.get(0).getDefaultRule(),0)){
            throw exception(DEFAULT_TEMPLATE);
        }

        //判断是否有班级在绑定使用
        int count  = completionTemplateMapper.selectCountByIdCode(idCode);
        if(count > 0){
            throw exception(COMPLETION_TEMPLATE_CLASS_EXISTS);
        }

        completionTemplateMapper.deleteCompletionTemplateByName(idCode);
    }

    /**
     * 批量删除
     *
     * @param idCode 模版名称
     */
    @Override
    public void deleteCompletionTemplateByNameBatch(List<String> idCode) {

        for(String id : idCode){
            //判断是否有班级在绑定使用
            int count  = completionTemplateMapper.selectCountByIdCode(id);
            if(count > 0){
                throw exception(COMPLETION_TEMPLATE_CLASS_BAN_EXISTS);
            }
        }


        for(String id : idCode){
            completionTemplateMapper.deleteCompletionTemplateByName(id);
        }

    }

    private void validateCompletionTemplateExists(Integer id) {
        if (completionTemplateMapper.selectById(id) == null) {
            throw exception(COMPLETION_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public CompletionTemplateDO getCompletionTemplate(Integer id) {
        return completionTemplateMapper.selectById(id);
    }

    @Override
    public List<CompletionTemplateDO> getCompletionTemplateList(String idCode) {

        return completionTemplateMapper.selectOneByIdCode(idCode);
    }

    @Override
    public PageResult<CompletionTemplateDO> getCompletionTemplatePage(HttpServletRequest request,CompletionTemplatePageReqVO pageReqVO) {

        //判断该账户是否是学员
        Long userIds = SecurityFrameworkUtils.getLoginUserId();

        if(!traineeService.selectTraineeByUserId(request,userIds)){
            throw exception(NO_PERMISSION_ERROR);
        }

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(pageReqVO.getTemplateName())){
            pageReqVO.setTemplateName(pageReqVO.getTemplateName().replaceAll("([%_])", "\\\\$1"));
        }

        Page buildPage = MyBatisUtils.buildPage(pageReqVO);

        List<CompletionTemplateDO> list = completionTemplateMapper.selectPageList(buildPage, pageReqVO);

        PageResult<CompletionTemplateDO> pageList = new PageResult<>(list, buildPage.getTotal());

        return pageList;
    }

    @Override
    public List<CompletionTemplateDO> getCompletionTemplatePageList(HttpServletRequest request, CompletionTemplatePageReqVO pageReqVO) {

        //判断该账户是否是学员
        Long userIds = SecurityFrameworkUtils.getLoginUserId();

        if(!traineeService.selectTraineeByUserId(request,userIds)){
            throw exception(NO_PERMISSION_ERROR);
        }

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(pageReqVO.getTemplateName())){
            pageReqVO.setTemplateName(pageReqVO.getTemplateName().replaceAll("([%_])", "\\\\$1"));
        }


        List<CompletionTemplateDO> list = completionTemplateMapper.selectPageListAll(pageReqVO);


        return list;
    }

    @Override
    public List<CompletionTempleExcelVO> getCompletionTemplateListExport(CompletionTemplateExportReqVO exportReqVO) {

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(exportReqVO.getTemplateName())){
            exportReqVO.setTemplateName(exportReqVO.getTemplateName().replaceAll("([%_])", "\\\\$1"));
        }

        //数据库查询的数据 与分页接口保持一致
        List<CompletionTemplateDO> list = completionTemplateMapper.selectListAll(exportReqVO);

        //导出类 数列表
        List<CompletionTempleExcelVO> exportList = new ArrayList<>();

        //选中导出
        if(exportReqVO.getIdList() != null){

            List<CompletionTemplateDO> listIdList = new ArrayList<>();

            for (CompletionTemplateDO vo : list){

                for (String idCode : exportReqVO.getIdList()){
                    if(vo.getIdCode().equals(idCode)){
                        listIdList.add(vo);
                    }
                }
            }


            for(CompletionTemplateDO vo : listIdList){
                CompletionTempleExcelVO export = new CompletionTempleExcelVO();

                //模版名称
                export.setTemplateName(vo.getTemplateName());

                //去字典表中查 校区  id  导出
                String campus = classManagementMapper.getDictLabelById(vo.getCampus());
                export.setCampus(campus);

                //默认值
                if(vo.getDefaultRule() == 0){
                    export.setDefaultRule("是");
                }else{
                    export.setDefaultRule("否");
                }

                exportList.add(export);
            }


        }else {
            for (CompletionTemplateDO vo : list){

                CompletionTempleExcelVO export = new CompletionTempleExcelVO();

                //模版名称
                export.setTemplateName(vo.getTemplateName());

                //去字典表中查 校区  id  导出
                String campus = classManagementMapper.getDictLabelById(vo.getCampus());
                export.setCampus(campus);

                //默认值
                if(vo.getDefaultRule() == 0){
                    export.setDefaultRule("是");
                }else{
                    export.setDefaultRule("否");
                }

                exportList.add(export);

            }
        }



        return exportList;
    }

    @Override
    public void exportExcel(HttpServletRequest request,Integer classId,Integer type,HttpServletResponse response) throws IOException {
        // 使用SXSSFWorkbook替代XSSFWorkbook，以提高大数据量时的性能
        SXSSFWorkbook workbook = new SXSSFWorkbook(100); // 设置内存中保留的行数为100
        Sheet sheet = workbook.createSheet("成绩单");

        List<ClassCompletionTemplateDO> list = getCompletionTemplateList(classManagementMapper.selectById(classId));

        List<String> lastHeaders = list.stream()
                .filter(item -> item.getSerialNumber().contains("S"))
                .map(ClassCompletionTemplateDO::getColumnName)
                .collect(Collectors.toList());

        // 构建表头，移除"学员id"列
        List<String> fixedHeaders = Arrays.asList("学生姓名", "单位职务", "班内职务");
        List<Map<String, Map<String, List<String>>>> dynamicHeaders = getDynamicHeaders(classId,list);

        buildHeader(sheet, fixedHeaders, dynamicHeaders, lastHeaders);

        int totalSize = 0; // 用于统计所有里层列表的总大小
        for (Map<String, Map<String, List<String>>> outerMap : dynamicHeaders) {
            for (Map<String, List<String>> middleMap : outerMap.values()) {
                for (List<String> innerList : middleMap.values()) {
                    totalSize += innerList.size(); // 获取最里层列表的大小
                }
            }
        }

        // 填充学生数据
        List<Map<String, Object>> students = getStudentData(request,classId);
        fillStudentData(workbook, sheet, students, type, totalSize + lastHeaders.size() + fixedHeaders.size() - 1);

        // 设置响应头并写入数据
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=成绩单.xlsx");

        // 使用带缓冲的输出流提高写入性能
        try (BufferedOutputStream outputStream = new BufferedOutputStream(response.getOutputStream())) {
            workbook.write(outputStream);
            // 释放临时文件
            workbook.dispose();
        } finally {
            workbook.close();
        }
    }

    @Override
    public ClassCompletionRespVO getClassCompletion(Long classId) {
        //根据classId获取模板

        ClassCompletionRespVO respVO = new ClassCompletionRespVO();

        List<ClassCompletionTemplateDO> list;

        ClassManagementDO classDO = classManagementMapper.selectById(classId);

        if (StrUtil.isNotBlank(classDO.getIdCode())){
//            list = completionTemplateMapper.selectByTemplateCode(classDO.getIdCode());
            list = classCompletionTemplateMapper.selectByTemplateCode(classDO.getId(),classDO.getIdCode());
        }else {
            //根据班级id查询对应的表头内容
            list = completionTemplateMapper.selectClassDefaultRule(0, classDO.getCampus());
        }
//        list.sort(Comparator.comparing(ClassCompletionTemplateDO::getSerialNumber));
//        list.removeIf(item -> item.getModuleName() == null);
        List<TraineeTreeBuilder.TreeNode> treeNodes = TraineeTreeBuilder.buildTree(list);

        respVO.setTreeNodes(treeNodes);



        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void regenerate(Long classId) {
        //如果模板已经变更，刷新模板

        ClassManagementDO classDO = classManagementMapper.selectById(classId);
        if (Objects.isNull(classDO)){
            return;
        }

        //根据班级id获取结业考核表中学员信息
        //查询是否有暂存的，有才进行更新，否则直接退出
        List<ClassCompletionTemplateDO> list = classCompletionTemplateMapper.selectTemplateByIdCode(classDO.getId());

        if (!list.isEmpty()){
            //删除正在使用的模板
            classCompletionTemplateMapper.deleteTemplate(classDO.getId());

            //更新暂存模板
            classCompletionTemplateMapper.updateTemplate(classDO.getId());
        }

        // 删除已经存在的学员数据
        List<Long> ids = classCompletionMapper.listByClassId(classDO.getId())
                .stream()
                .map(ClassCompletionDO::getId)
                .collect(Collectors.toList());

        if (!ids.isEmpty()){
            classCompletionMapper.deleteBatchIds(ids);
        }

    }

    @Override
    public List<ClassCompletionInfoRespVO> getClassCompletionInfo(HttpServletRequest request, Integer classId) {
        // 查询班级信息
        ClassManagementDO classDO = classManagementMapper.selectById(classId);

        // 获取学员信息（如果班级有绑定模板则使用绑定模板，否则使用默认模板）
        List<ClassCompletionDO> traineeInfoList = getTraineeInfoList(classDO, classId);
        Map<Long, List<ClassCompletionDO>> traineeInfoMap = groupByTraineeId(traineeInfoList);

        // 获取相关数据（请假、考勤、迟到、评价等）
        Map<Long, List<TraineeLeaveRespVO>> leaveMap = getLeaveMap(classId);
        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap = getAttendanceMap(classId);
        Map<Long, List<ClockInInfoStudentStatusVO>> lateMap = getLateMap(request, classId);
        Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap = getEvaluationMap(classId);

        // 构建返回结果
        return buildResponse(traineeInfoMap, leaveMap, attendanceMap, lateMap, evaluationMap);
    }

    /**
     * 获取学员信息列表
     */
    private List<ClassCompletionDO> getTraineeInfoList(ClassManagementDO classDO, Integer classId) {
        if (StrUtil.isNotBlank(classDO.getIdCode())) {
            return classCompletionMapper.getInfoByClassId(classId);
        } else {
            return classCompletionMapper.getDefaultRuleInfoByClassId(classDO);
        }
    }

    /**
     * 按 TraineeId 进行分组
     */
    private Map<Long, List<ClassCompletionDO>> groupByTraineeId(List<ClassCompletionDO> traineeInfoList) {
        return traineeInfoList.stream()
                .collect(Collectors.groupingBy(ClassCompletionDO::getTraineeId, LinkedHashMap::new, Collectors.toList()));
    }

    /**
     * 获取请假数据并按学员 ID 分组
     */
    private Map<Long, List<TraineeLeaveRespVO>> getLeaveMap(Integer classId) {
        List<TraineeLeaveRespVO> allLeaveList = leaveService.pageList(buildLeaveReqVO(classId, 10000)).getList();
        return allLeaveList.stream().collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId));
    }

    /**
     * 获取考勤数据并按学员 ID 分组
     */
    private Map<Long, List<AttendanceRateTraineeInfoVO>> getAttendanceMap(Integer classId) {
        List<AttendanceRateTraineeInfoVO> allAttendanceRates = clockInInfoService
                .getListForAttendanceThreeRate(buildAttendanceReqVO(classId))
                .getTraineeInfoList();

        //去除百分号
        allAttendanceRates.forEach(item -> {
            if (item != null) {
                if (item.getAccommodationRate() != null) {
                    item.setAccommodationRate(item.getAccommodationRate().replace("%", ""));
                }
                if (item.getClassRate() != null) {
                    item.setClassRate(item.getClassRate().replace("%", ""));
                }
                if (item.getMealRate() != null) {
                    item.setMealRate(item.getMealRate().replace("%", ""));
                }
            }
        });

        return allAttendanceRates.stream().collect(Collectors.groupingBy(AttendanceRateTraineeInfoVO::getTraineeId));
    }

    /**
     * 获取迟到数据并按学员 ID 分组
     */
    private Map<Long, List<ClockInInfoStudentStatusVO>> getLateMap(HttpServletRequest request, Integer classId) {
        List<ClockInInfoStudentStatusVO> allLateInfo = clockInInfoService
                .getStudentClockingStatusPage(request, buildLateReqVO(classId))
                .getList();
        return allLateInfo.stream().collect(Collectors.groupingBy(ClockInInfoStudentStatusVO::getTraineeId));
    }

    /**
     * 获取学员评价数据并按学员 ID 分组
     */
    private Map<Long, List<TraineeEvaluationDetailPageRespVO>> getEvaluationMap(Integer classId) {
        List<TraineeEvaluationDetailPageRespVO> allEvaluationDetails = evaluationResponseService
                .getTraineeEvaluationDetailPage(buildEvaluationReqVO(classId))
                .getList();
        return allEvaluationDetails.stream().collect(Collectors.groupingBy(TraineeEvaluationDetailPageRespVO::getTraineeId));
    }

    /**
     * 组装返回数据
     */
    private List<ClassCompletionInfoRespVO> buildResponse(
            Map<Long, List<ClassCompletionDO>> traineeInfoMap,
            Map<Long, List<TraineeLeaveRespVO>> leaveMap,
            Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap,
            Map<Long, List<ClockInInfoStudentStatusVO>> lateMap,
            Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap) {

        List<ClassCompletionInfoRespVO> respVOS = new ArrayList<>();

        traineeInfoMap.forEach((traineeId, infoList) -> {
            ClassCompletionInfoRespVO respVO = new ClassCompletionInfoRespVO();
            Map<String, String> scoreMap = new LinkedHashMap<>();

            for (ClassCompletionDO info : infoList) {
                setBasicInfo(respVO, info);
                String score = getScoreByDataSource(info, leaveMap, attendanceMap, lateMap, evaluationMap);
                scoreMap.put(info.getSerialNumber(), score);
            }
            respVO.setMapScore(scoreMap);
            respVOS.add(respVO);
        });

        return respVOS;
    }

    /**
     * 设置学员的基本信息
     */
    private void setBasicInfo(ClassCompletionInfoRespVO respVO, ClassCompletionDO info) {
        respVO.setId(info.getId());
        respVO.setTraineeId(info.getTraineeId());
        respVO.setTraineeName(info.getTraineeName());
        respVO.setGroupName(info.getGroupName());
        respVO.setUnitPosition(info.getUnitPosition());
        respVO.setClassPosition(info.getClassPosition());
    }

    private String getScoreByDataSource(ClassCompletionDO info,
                                        Map<Long, List<TraineeLeaveRespVO>> leaveMap,
                                        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap,
                                        Map<Long, List<ClockInInfoStudentStatusVO>> lateMap,
                                        Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap) {
        // 如果已有成绩，直接返回
        if (info.getScore() != null) {
            return info.getScore();
        }

        // 仅处理 acquisitionMode == 1 的情况 自动获取
        if (Objects.equals(info.getAcquisitionMode(), 1)) {
            int dataSource = info.getDataSource();
            Long traineeId = info.getTraineeId();

            // 处理请假相关数据
            if (isLeaveDataSource(dataSource)) {
                return getLeaveScore(traineeId, dataSource, leaveMap);
            }

            // 处理考勤率相关数据
            if (isAttendanceRateDataSource(dataSource)) {
                return getAttendanceRateScore(traineeId, dataSource, attendanceMap);
            }

            // 处理课程评价率
            if (Objects.equals(dataSource, DataSourceEnum.COURSE_EVALUATION_RATE.getCode())) {
                return getEvaluationScore(traineeId, evaluationMap);
            }

            // 处理迟到次数
            if (Objects.equals(dataSource, DataSourceEnum.LATE_COUNT.getCode())) {
                return getLateCountScore(traineeId, lateMap);
            }
        }

        return "0";
    }

    // 判断是否是请假数据来源
    private boolean isLeaveDataSource(int dataSource) {
        return (dataSource >= DataSourceEnum.PERSONAL_LEAVE_NUM.getCode() && dataSource <= DataSourceEnum.FIVE_CANS_LEAVE_NUM.getCode()) ||
                (dataSource >= DataSourceEnum.PERSONAL_LEAVE_DAYS.getCode() && dataSource <= DataSourceEnum.FIVE_CANS_LEAVE_DAYS.getCode());
    }

    // 计算请假相关数据
    private String getLeaveScore(Long traineeId, int dataSource, Map<Long, List<TraineeLeaveRespVO>> leaveMap) {
        List<TraineeLeaveRespVO> leaves = leaveMap.get(traineeId);
        if (CollUtil.isEmpty(leaves)) {
            return "0";
        }

        TraineeLeaveType leaveType = getLeaveTypeByDataSource(dataSource);
        if (leaveType == null) {
            return "0";
        }
        if (isLeaveCountDataSource(dataSource)) {
            return String.valueOf(leaves.stream()
                    .filter(leave -> Objects.equals(leave.getLeaveType(), leaveType.getCode()))
                    .count());
        } else {
            double totalDays = leaves.stream()
                    .filter(leave -> Objects.equals(leave.getLeaveType(), leaveType.getCode()))
                    .mapToDouble(TraineeLeaveRespVO::getDays)
                    .sum();
            return String.format("%.1f", totalDays);
        }
    }

    // 判断是否是请假次数来源
    private boolean isLeaveCountDataSource(int dataSource) {
        return dataSource == DataSourceEnum.PERSONAL_LEAVE_NUM.getCode() ||
                dataSource == DataSourceEnum.SICK_LEAVE_NUM.getCode() ||
                dataSource == DataSourceEnum.FIVE_CANS_LEAVE_NUM.getCode();
    }

    // 判断是否是请假天数来源
    private boolean isLeaveDaysDataSource(int dataSource) {
        return dataSource == DataSourceEnum.PERSONAL_LEAVE_DAYS.getCode() ||
                dataSource == DataSourceEnum.SICK_LEAVE_DAYS.getCode() ||
                dataSource == DataSourceEnum.FIVE_CANS_LEAVE_DAYS.getCode();
    }

    // 根据数据来源获取请假类型
    private TraineeLeaveType getLeaveTypeByDataSource(int dataSource) {
        if (dataSource == DataSourceEnum.PERSONAL_LEAVE_NUM.getCode() || dataSource == DataSourceEnum.PERSONAL_LEAVE_DAYS.getCode()) {
            return TraineeLeaveType.BUSINESS;
        } else if (dataSource == DataSourceEnum.SICK_LEAVE_NUM.getCode() || dataSource == DataSourceEnum.SICK_LEAVE_DAYS.getCode()) {
            return TraineeLeaveType.SICK;
        } else if (dataSource == DataSourceEnum.FIVE_CANS_LEAVE_NUM.getCode() || dataSource == DataSourceEnum.FIVE_CANS_LEAVE_DAYS.getCode()) {
            return TraineeLeaveType.FIVE_CANS;
        }
        return null;
    }

    // 判断是否是考勤率相关数据来源
    private boolean isAttendanceRateDataSource(int dataSource) {
        return dataSource >= DataSourceEnum.CLASS_ATTENDANCE_RATE.getCode() &&
                dataSource <= DataSourceEnum.ACCOMMODATION_RATE.getCode();
    }

    // 计算考勤率
    private String getAttendanceRateScore(Long traineeId, int dataSource, Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap) {
        List<AttendanceRateTraineeInfoVO> rates = attendanceMap.get(traineeId);
        if (CollUtil.isEmpty(rates)) {
            return "0";
        }
        AttendanceRateTraineeInfoVO rateInfo = rates.get(0);

        if (Objects.equals(dataSource, DataSourceEnum.CLASS_ATTENDANCE_RATE.getCode())) {
            return rateInfo.getClassRate();
        } else if (Objects.equals(dataSource, DataSourceEnum.MEAL_ATTENDANCE_RATE.getCode())) {
            return rateInfo.getMealRate();
        } else if (Objects.equals(dataSource, DataSourceEnum.ACCOMMODATION_RATE.getCode())) {
            return rateInfo.getAccommodationRate();
        }
        return "0";
    }

    // 计算课程评价率
    private String getEvaluationScore(Long traineeId, Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap) {
        List<TraineeEvaluationDetailPageRespVO> evaluations = evaluationMap.get(traineeId);
        if (CollUtil.isEmpty(evaluations)) {
            return "0";
        }
        return String.format("%.2f", evaluations.get(0).getRatio() * 100);
    }

    // 计算迟到次数
    private String getLateCountScore(Long traineeId, Map<Long, List<ClockInInfoStudentStatusVO>> lateMap) {
        List<ClockInInfoStudentStatusVO> lateInfo = lateMap.get(traineeId);
        return String.valueOf(CollUtil.isEmpty(lateInfo) ? 0 : lateInfo.size());
    }

    private TraineeLeavePageReqVO buildLeaveReqVO(Integer classId, int pageSize) {
        TraineeLeavePageReqVO reqVO = new TraineeLeavePageReqVO();
        reqVO.setClassId(Long.valueOf(classId));
        reqVO.setPageNo(1);
        reqVO.setStatus(LeaveTaskStatus.APPROVE.getCode());
        reqVO.setPageSize(pageSize);
        return reqVO;
    }

    private AttendanceRateReqVO buildAttendanceReqVO(Integer classId) {
        AttendanceRateReqVO reqVO = new AttendanceRateReqVO();
        reqVO.setClassId(Long.valueOf(classId));
        return reqVO;
    }

    private ClockInInfoPageReqVO buildLateReqVO(Integer classId) {
        ClockInInfoPageReqVO reqVO = new ClockInInfoPageReqVO();
        reqVO.setClassId(Long.valueOf(classId));
        reqVO.setPageNo(1);
        reqVO.setPageSize(10000);
        reqVO.setTraineeStatus(2);
        reqVO.setType(0);
        reqVO.setClockInType(0);
        return reqVO;
    }

    private TraineeEvaluationDetailPageReqVO buildEvaluationReqVO(Integer classId) {
        TraineeEvaluationDetailPageReqVO reqVO = new TraineeEvaluationDetailPageReqVO();
        reqVO.setClassId(Long.valueOf(classId));
        reqVO.setPageNo(1);
        reqVO.setPageSize(10000);

        return reqVO;
    }

    @Override
    public boolean saveInfo(List<SaveClassCompletionInfoReqVO> reqVOS) {
        if (reqVOS.isEmpty()) {
            return false;
        }

        // 查询现有的班级完成信息
        List<ClassCompletionDO> traineeInfoList = classCompletionMapper.listByClassId(reqVOS.get(0).getClassId());

        List<Long> traineeList = traineeInfoList.stream().map(ClassCompletionDO::getTraineeId).collect(Collectors.toList());


        // 将输入按 TraineeId 分组
        Map<Long, List<SaveClassCompletionInfoReqVO>> groupedMap = reqVOS.stream()
                .collect(Collectors.groupingBy(SaveClassCompletionInfoReqVO::getTraineeId));

        Map<Long, List<ClassCompletionDO>> traineeMap = traineeInfoList.stream()
                .collect(Collectors.groupingBy(ClassCompletionDO::getTraineeId));

        // 按学员是否已存在拆分为更新和新增的列表
        List<ClassCompletionDO> updateList = new ArrayList<>();
        List<ClassCompletionDO> insertList = new ArrayList<>();

        // 遍历输入数据，区分更新和新增
        for (SaveClassCompletionInfoReqVO reqVO : reqVOS) {

            Map<String, String> mapScore = reqVO.getMapScore();
            if (traineeList.contains(reqVO.getTraineeId())){

                List<ClassCompletionDO> list = traineeMap.get(reqVO.getTraineeId());

                for (ClassCompletionDO classCompletionDO : list) {
                    classCompletionDO.setScore(mapScore.get(classCompletionDO.getSerialNumber()));
                    updateList.add(classCompletionDO);
                }
//
//
//                Map<Long, ClassCompletionDO> map = serialMap.get(key).stream().collect(Collectors.toMap(ClassCompletionDO::getTraineeId, Function.identity()));
//
//
//                ClassCompletionDO classCompletionDO = serialMap.get(key).get(0);
//                classCompletionDO.setScore(reqVO.getMapScore().get(key));
//                updateList.add(classCompletionDO);
            }else {

                mapScore.forEach((key, value) -> {
                    ClassCompletionDO classCompletionDO = new ClassCompletionDO();
                    classCompletionDO.setTraineeId(reqVO.getTraineeId());
                    classCompletionDO.setTraineeName(reqVO.getTraineeName());
                    classCompletionDO.setGroupName(reqVO.getGroupName());
                    classCompletionDO.setUnitPosition(reqVO.getUnitPosition());
                    classCompletionDO.setClassPosition(reqVO.getClassPosition());
                    classCompletionDO.setScore(mapScore.get(key));
                    classCompletionDO.setClassId(reqVO.getClassId());
                    classCompletionDO.setSerialNumber(key);
                    insertList.add(classCompletionDO);

                });

            }

        }

        if (!updateList.isEmpty()) {
            // 批量更新
            classCompletionMapper.updateBatch(updateList);
        }

        if (!insertList.isEmpty()) {
            // 批量插入
            classCompletionMapper.insertBatch(insertList);
        }

        return true;
    }

    public List<CompletionTemplateCreateReqVO> parseJsonToList(String json,Integer campus) {
        ObjectMapper objectMapper = new ObjectMapper();
        List<CompletionTemplateCreateReqVO> list = new ArrayList<>();

        try {
            JsonNode jsonArray = objectMapper.readTree(json);
            for (JsonNode node : jsonArray) {
                CompletionTemplateCreateReqVO vo = new CompletionTemplateCreateReqVO();
                vo.setSerialNumber(node.path("serialNumber").asText(null));
                vo.setColumnName(node.path("columnName").asText(null));
                vo.setConversionAnnouncement(node.path("conversionAnnouncement").asText(null));

                // 处理 maxScore
                String maxScoreStr = node.path("maxScore").asText(null);
                vo.setMaxScore(maxScoreStr == null || maxScoreStr.isEmpty() ? null : Integer.parseInt(maxScoreStr));

                // 处理 initialScore
                String initialScoreStr = node.path("initialScore").asText(null);
                vo.setInitialScore(initialScoreStr == null || initialScoreStr.isEmpty() ? null : Integer.parseInt(initialScoreStr));

//                vo.setAcquisitionMode(node.path("acquisitionMode").isNull() ? null : node.path("acquisitionMode").asInt());
                String acquisitionModeStr = node.path("acquisitionMode").asText(null);
                vo.setAcquisitionMode(acquisitionModeStr == null || acquisitionModeStr.isEmpty() ? null : Integer.parseInt(acquisitionModeStr));
//                vo.setAcquisitionMode(node.path("acquisitionMode").isNull() ? null : node.path("acquisitionMode").asInt());

                String dataSourceStr = node.path("dataSource").asText(null);
                vo.setDataSource(dataSourceStr == null || dataSourceStr.isEmpty() ? null : Integer.parseInt(dataSourceStr));
//                vo.setDataSource(node.path("dataSource").isNull() ? null : node.path("dataSource").asInt());


                vo.setAssessmentName(node.path("assessmentName").asText(null));
                vo.setCampus(campus);
                vo.setDefaultRule(node.path("defaultRule").isNull() ? null : node.path("defaultRule").asInt());
                vo.setTemplateName(node.path("templateName").asText(null));
                vo.setModuleName(node.path("moduleName").asText(null));
                vo.setIdCode(node.path("idCode").asText(null));
                vo.setBuiltinTemplate(1);

                list.add(vo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        return list;
    }
    @Override
    public Boolean generationTemplate() {


        Long campus = completionTemplateMapper.getMainCampus(getTenantId());
//        Integer campus = dictData != null ? dictData.getId().intValue() : null;

        if (Objects.isNull(campus)){
            return true;
        }
        //查该租户下的内置模板
        Long count = completionTemplateMapper.getBuildInTemplate(campus.intValue());

        // 判断改租户下是否存在内置模板，如果存在直接返回
        if (count > 0) {
            return true;
        }

        // 不存在则创建内置模板
        try {
            List<CompletionTemplateCreateReqVO> list = parseJsonToList(CompletionTemplateConstant.JSON_DATA,campus.intValue());
            this.addCompletionTemplate(list);
        } catch (Exception e) {
            log.info("内置模板生成失败：{}",e.getMessage());
        }
        return true;

    }


    @Override
    public SchoolFeedbackFormRespVO schoolFeedbackForm(Long classId) {
        SchoolFeedbackFormRespVO respVO = new SchoolFeedbackFormRespVO();

        // 根据班级id获取班级信息
        ClassManagementDO classManagementDO = classManagementMapper.selectById(classId);
        if (Objects.isNull(classManagementDO)) {
            return respVO;
        }

        // 班级信息赋值
        classInformationAssignment(classManagementDO, respVO);

        // 获取班级所有学员
        List<TraineeDO> traineeList = traineeService.getAllTraineeByClassIds(Collections.singletonList(classId));
        List<SchoolFeedbackFormRespVO.TraineeInfoVO> traineeInfoList = ClassCompletionConvert.INSTANCE.convertTraineeInfoList(traineeList);

        // 获取考核相关数据
        String idCode = getIdCode(classManagementDO);

        // 获取成绩数据
        List<TraineeScoreVO> scoreList = classCompletionMapper.getScoreList(classId, idCode);

        // 创建成绩映射
        List<String> assessmentName = getAssessmentNames(scoreList);

        // 获取请假数据并处理
        List<TraineeLeaveRespVO> leaveList = getTraineeLeaveList(classId);

        // 处理请假天数分组
        Map<Long, Map<Integer, Float>> leaveDaysMap = processLeaveDays(leaveList);

        // 创建各类考核项映射
        Map<Long, Float> businessMap = createBusinessMap(assessmentName, leaveDaysMap, scoreList);
        Map<Long, Float> sickMap = createSickMap(assessmentName, leaveDaysMap, scoreList);
        Map<Long, Float> fiveCansMap = createFiveCansMap(assessmentName, leaveDaysMap, scoreList);
        Map<Long, Float> lateAndLeaveEarlyMap = createLateEarlyMap(scoreList);
        Map<Long, Float> absentClassMap = createAbsentClassMap(scoreList);

        // 处理考勤数据
        Map<Long, AttendanceRateTraineeInfoVO> attendanceMap = processAttendanceData(classManagementDO, assessmentName, scoreList);

        // 学员信息赋值
        traineeInformationAssignment(traineeInfoList, attendanceMap, businessMap, sickMap, fiveCansMap,lateAndLeaveEarlyMap, absentClassMap);

        respVO.setTraineeInfoList(traineeInfoList);
        return respVO;
    }

    /**
     * 获取学员请假列表
     * @param classId 班级ID
     * @return 请假列表
     */
    private List<TraineeLeaveRespVO> getTraineeLeaveList(Long classId) {
        TraineeLeavePageReqVO leaveReqVO = new TraineeLeavePageReqVO();
        leaveReqVO.setClassId(classId);
        leaveReqVO.setPageNo(1);
        leaveReqVO.setPageSize(Integer.MAX_VALUE);
        return leaveService.pageList(leaveReqVO).getList();
    }

    /**
     * 获取序列号列表
     * @param completionDOList 结业模板列表
     * @return 序列号列表
     */
    private List<String> getSerialNumbers(List<CompletionTemplateDO> completionDOList) {
        return completionDOList.stream()
                .map(CompletionTemplateDO::getSerialNumber)
                .collect(Collectors.toList());
    }

    /**
     * 创建成绩映射
     * @param scores 原始成绩数据
     * @return 成绩映射表
     */
    private Map<String, String> createScoreMapping(List<Map<String, String>> scores) {
        Map<String, String> scoreMap = new HashMap<>();
        for (Map<String, String> score : scores) {
            String serialNumber = score.get("assessment_name");
            String scoreValue = String.valueOf(score.get("score"));
            if (serialNumber != null && scoreValue != null) {
                scoreMap.put(serialNumber, scoreValue);
            }
        }
        return scoreMap;
    }

    /**
     * 获取考核名称列表
     * @param scoreList 成绩列表
     * @return 去重后的考核名称列表
     */
    private List<String> getAssessmentNames(List<TraineeScoreVO> scoreList) {
        return scoreList.stream()
                .map(TraineeScoreVO::getAssessmentName)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 处理请假天数分组
     * @param leaveList 请假列表
     * @return 按学员和请假类型分组的请假天数
     */
    private Map<Long, Map<Integer, Float>> processLeaveDays(List<TraineeLeaveRespVO> leaveList) {
        return leaveList.stream()
                .collect(Collectors.groupingBy(
                        TraineeLeaveRespVO::getTraineeId,
                        Collectors.groupingBy(
                                TraineeLeaveRespVO::getLeaveType,
                                Collectors.summingDouble(TraineeLeaveRespVO::getDays)
                        )
                )).entrySet().stream().collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().entrySet().stream().collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().floatValue()
                        ))
                ));
    }

    /**
     * 创建事假扣分映射
     */
    private Map<Long, Float> createBusinessMap(List<String> assessmentName,
                                               Map<Long, Map<Integer, Float>> leaveDaysMap,
                                               List<TraineeScoreVO> scoreList) {
        return createLeaveOrScoreMap(assessmentName, leaveDaysMap, scoreList,
                TraineeLeaveType.BUSINESS.getCode(), CompletionTemplateConstant.BUSINESS_DEDUCTION);
    }

    /**
     * 创建病假扣分映射
     */
    private Map<Long, Float> createSickMap(List<String> assessmentName,
                                           Map<Long, Map<Integer, Float>> leaveDaysMap,
                                           List<TraineeScoreVO> scoreList) {
        return createLeaveOrScoreMap(assessmentName, leaveDaysMap, scoreList,
                TraineeLeaveType.SICK.getCode(), CompletionTemplateConstant.SICK_DEDUCTION);
    }

    /**
     * 创建五会假映射
     */
    private Map<Long, Float> createFiveCansMap(List<String> assessmentName,
                                               Map<Long, Map<Integer, Float>> leaveDaysMap,
                                               List<TraineeScoreVO> scoreList) {
        return createLeaveOrScoreMap(assessmentName, leaveDaysMap, scoreList,
                TraineeLeaveType.FIVE_CANS.getCode(), CompletionTemplateConstant.FIVE_CANS_LEAVE);
    }

    /**
     * 通用方法：创建请假类型或考核分数映射
     */
    private Map<Long, Float> createLeaveOrScoreMap(List<String> assessmentName,
                                                   Map<Long, Map<Integer, Float>> leaveDaysMap,
                                                   List<TraineeScoreVO> scoreList,
                                                   int leaveTypeCode,
                                                   String assessmentKey) {
        if (!assessmentName.contains(assessmentKey)) {
            //如果不包含则从请假管理查数据
            return leaveDaysMap.entrySet().stream()
                    .filter(e -> e.getValue().containsKey(leaveTypeCode))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            e -> e.getValue().get(leaveTypeCode)
                    ));
        }

        //否则则从结业考核中取数据
        return scoreList.stream()
                .filter(o -> Objects.equals(o.getAssessmentName(), assessmentKey))
                .collect(Collectors.toMap(
                        TraineeScoreVO::getTraineeId,
                        TraineeScoreVO::getScore
                ));
    }

    /**
     * 创建迟到早退映射
     */
    private Map<Long, Float> createLateEarlyMap(List<TraineeScoreVO> scoreList) {
        return createSimpleScoreMap(scoreList, CompletionTemplateConstant.LATE_EARLY_DEDUCTION);
    }

    /**
     * 创建旷课映射
     */
    private Map<Long, Float> createAbsentClassMap(List<TraineeScoreVO> scoreList) {
        return createSimpleScoreMap(scoreList, CompletionTemplateConstant.ABSENT_CLASS);
    }

    /**
     * 通用方法：创建简单分数映射
     */
    private Map<Long, Float> createSimpleScoreMap(List<TraineeScoreVO> scoreList, String assessmentName) {
        return scoreList.stream()
                .filter(o -> Objects.equals(o.getAssessmentName(), assessmentName))
                .collect(Collectors.toMap(
                        TraineeScoreVO::getTraineeId,
                        TraineeScoreVO::getScore
                ));
    }

    /**
     * 处理考勤数据
     */
    private Map<Long, AttendanceRateTraineeInfoVO> processAttendanceData(ClassManagementDO classManagementDO,
                                                                         List<String> assessmentName,
                                                                         List<TraineeScoreVO> scoreList) {
        AttendanceRateReqVO reqVO = new AttendanceRateReqVO();
        reqVO.setClassId(classManagementDO.getId());
        reqVO.setStartTime(classManagementDO.getClassOpenTime().toLocalDate());
        reqVO.setEndTime(classManagementDO.getCompletionTime().toLocalDate());

        //考勤管理中考勤数据
        List<AttendanceRateTraineeInfoVO> attendanceList =
                clockInInfoService.getListForAttendanceThreeRate(reqVO).getTraineeInfoList();

        attendanceList.forEach(item -> {
            if (item != null) {
                if (item.getAccommodationRate() != null) {
                    item.setAccommodationRate(item.getAccommodationRate().replace("%", ""));
                }
                if (item.getClassRate() != null) {
                    item.setClassRate(item.getClassRate().replace("%", ""));
                }
                if (item.getMealRate() != null) {
                    item.setMealRate(item.getMealRate().replace("%", ""));
                }
            }
        });

        //根据学员id将考勤数据转Map
        Map<Long, AttendanceRateTraineeInfoVO> attendanceMap = attendanceList.stream()
                .collect(Collectors.toMap(
                        AttendanceRateTraineeInfoVO::getTraineeId,
                        Function.identity(),
                        (a, b) -> a
                ));

        // 定义考核映射关系
        Map<String, String> assessmentMapping = new HashMap<>();

        assessmentMapping.put(CompletionTemplateConstant.ACCOMMODATION_RATE, "setAccommodationRate");

        assessmentMapping.put(CompletionTemplateConstant.MEAL_RATE, "setMealRate");
        assessmentMapping.put(CompletionTemplateConstant.CLASS_RATE, "setClassRate");

        System.out.println(scoreList);
        // 更新考勤数据
        assessmentMapping.forEach((key, method) -> {
            //如果结业考核中存在则从结业考核中取数据
            if (assessmentName.contains(key)) {
                scoreList.stream()
                        .filter(o -> Objects.equals(o.getAssessmentName(), key))
                        .forEach(item -> attendanceMap
                                .computeIfAbsent(item.getTraineeId(), k -> new AttendanceRateTraineeInfoVO())
                                .setRate(method, String.valueOf(item.getScore())));
            }
        });

        return attendanceMap;
    }


    // 获取 completionDOList 的方法
    private List<CompletionTemplateDO> getCompletionDOList(ClassManagementDO classManagementDO,String idCode) {
        List<CompletionTemplateDO> completionDOList = new ArrayList<>();

        if (StrUtil.isNotBlank(idCode)) {
            completionDOList = completionTemplateMapper.getDefaultTemplateByIdCode(idCode, DefaultCompletionEnum.NOT_DEFAULT.getCode());
        }

        // 如果 completionDOList 为空，查看校区默认模板
        if (CollUtil.isEmpty(completionDOList)) {
            completionDOList = completionTemplateMapper.getDefaultTemplateByIdCode(idCode, DefaultCompletionEnum.DEFAULT.getCode());
        }

        return completionDOList;
    }

    // 获取 idCode 的方法
    private String getIdCode(ClassManagementDO classManagementDO) {
        String idCode = classManagementDO.getIdCode();

        if (StrUtil.isBlank(idCode)) {
            idCode = completionTemplateMapper.getDefaultTemplateByCampus(classManagementDO.getCampus());
        }

        return idCode;
    }

    // 学员信息赋值
    private void traineeInformationAssignment( List<SchoolFeedbackFormRespVO.TraineeInfoVO> traineeINfoList,
                                               Map<Long, AttendanceRateTraineeInfoVO> attendanceMap,
                                               Map<Long, Float> businessMap,
                                               Map<Long, Float> sickMap,
                                               Map<Long, Float> fiveCansMap,
                                               Map<Long, Float> lateAndLeaveEarlyMap,
                                               Map<Long, Float> absentClassMap) {

        for (SchoolFeedbackFormRespVO.TraineeInfoVO vo : traineeINfoList) {

            //请假
            vo.setBusiness(businessMap.getOrDefault(vo.getId(),0.0f));
            vo.setSick(sickMap.getOrDefault(vo.getId(),0.0f));
            vo.setFiveCans(fiveCansMap.getOrDefault(vo.getId(),0.0f));
            vo.setLateAndLeaveEarly(lateAndLeaveEarlyMap.getOrDefault(vo.getId(),0f));
            vo.setAbsentClass(absentClassMap.getOrDefault(vo.getId(),0.0f));

            //考勤
            vo.setClassRate(attendanceMap.getOrDefault(vo.getId(),new AttendanceRateTraineeInfoVO()).getClassRate());
            vo.setMealRate(attendanceMap.getOrDefault(vo.getId(),new AttendanceRateTraineeInfoVO()).getMealRate());
            vo.setAccommodationRate(attendanceMap.getOrDefault(vo.getId(),new AttendanceRateTraineeInfoVO()).getAccommodationRate());
            vo.setSelfLearnRate("100.00");

            //班委
            vo.setSchoolPosition(attendanceMap.getOrDefault(vo.getId(),new AttendanceRateTraineeInfoVO()).getClassCommitteeName());

            //结业考核成绩
//            vo.setLearningExperience(scoreMap.getOrDefault("学习心得 (原分)",""));
//            vo.setTheoreticalGrades(scoreMap.getOrDefault("考试成绩 (原分)",""));
//            vo.setGraduationThesisScore(scoreMap.getOrDefault("结业论文成绩 (原分)",""));
//            vo.setComprehensiveEvaluationScore(scoreMap.getOrDefault("量化得分",""));

        }



    }

    /**
     * 班级信息赋值
     * @param classManagementDO 班级DO
     * @param respVO 返回VO类
     */
    private void classInformationAssignment(ClassManagementDO classManagementDO, SchoolFeedbackFormRespVO respVO) {
        LocalDateTime classOpenTime = classManagementDO.getClassOpenTime();
        LocalDateTime completionTime = classManagementDO.getCompletionTime();

        String time = classOpenTime.toLocalDate().toString() + "-" + completionTime.toLocalDate().toString();
        respVO.setClassName(classManagementDO.getClassName());
        respVO.setTrainingTime(time);
        respVO.setClassTypeDictId(classManagementDO.getClassTypeDictId());

        if (Objects.nonNull(classManagementDO.getClassTeacherLead())){
            Long classTeacherLead = classManagementDO.getClassTeacherLead();
            TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectById(classTeacherLead);
            respVO.setClassTeacherName(teacherInformationDO.getName());
        }
    }


    private void assignScores(Map<Long, List<SaveClassCompletionInfoReqVO>> groupedMap, List<ClassCompletionDO> targetList) {
        for (ClassCompletionDO item : targetList) {
            List<SaveClassCompletionInfoReqVO> reqList = groupedMap.get(item.getTraineeId());
            if (reqList != null && !reqList.isEmpty()) {
                Map<String, String> mapScore = reqList.get(0).getMapScore();
                if (mapScore != null && mapScore.containsKey(item.getSerialNumber())) {
                    item.setScore(mapScore.get(item.getSerialNumber()));
                }
            }
        }
    }

    // 新增时确保设置 serialNumber 和 score
    private void setSerialNumbersAndScores(List<ClassCompletionDO> insertList, List<SaveClassCompletionInfoReqVO> reqVOS) {
        for (ClassCompletionDO completionDO : insertList) {
            // 查找对应的 reqVO
            SaveClassCompletionInfoReqVO reqVO = reqVOS.stream()
                    .filter(vo -> vo.getTraineeId().equals(completionDO.getTraineeId()))
                    .findFirst()
                    .orElse(null);

            if (reqVO != null) {
                // 获取 serialNumber 和 score
                Map<String, String> mapScore = reqVO.getMapScore();
                if (mapScore != null && !mapScore.isEmpty()) {
                    for (String serialNumber : mapScore.keySet()) {
                        completionDO.setSerialNumber(serialNumber);  // 设置 serialNumber
                        completionDO.setScore(mapScore.get(serialNumber));  // 设置对应的 score
                        break;  // 如果一个 trainee 对应多个 serialNumber，根据实际业务调整
                    }
                }
            }
        }
    }


    /**
     * 唯一性校验
     * 模版名称唯一性校验
     *  @param id
     */
    public void campusNameRules(Integer id, String templateName){

        //在数据库中 匹配数据
        List<CompletionTemplateDO> completionTemplateDO = completionTemplateMapper.selectByTemplateName(templateName);

        if(completionTemplateDO.isEmpty()){
            return ;
        }

        if(id == null){
            throw exception(COMPLETION_TEMPLATE_NAME_EXISTS);
        }

        if(!completionTemplateDO.get(0).getId().equals(id)){
            throw exception(COMPLETION_TEMPLATE_NAME_EXISTS);
        }
    }

    /**
     * 唯一性校验
     * 规则名称
     * 同一个校区默认规则唯一
     *  @param id
     *  @param defaultRule
     *  @param campus
     */
    public void campusDefaultRules(Integer id, Integer defaultRule, Integer campus){

        if(defaultRule == 1){
            return;
        }

        //在数据库中 匹配数据
        List<CompletionTemplateDO> completionTemplateDO = completionTemplateMapper.selectByDefaultRule(defaultRule, campus);

        if(completionTemplateDO.isEmpty()){
            return ;
        }

        if(id == null){
            throw exception(COMPLETION_TEMPLATE_DEFAULT_RULE_EXISTS);
        }

        if(!completionTemplateDO.get(0).getId().equals(id)){
            throw exception(COMPLETION_TEMPLATE_DEFAULT_RULE_EXISTS);
        }
    }

    /**
     * 唯一性校验
     * 模版名称唯一性校验
     *  @param idCode
     *  @param templateName
     */
    public void editNameRules(String idCode, String templateName){

        //在数据库中 匹配数据
        List<CompletionTemplateDO> completionTemplateDO = completionTemplateMapper.selectByTemplateNameEdit(idCode, templateName);

        if(completionTemplateDO.isEmpty()){
            return;
        }else{
            throw exception(COMPLETION_TEMPLATE_NAME_EXISTS);
        }
    }

    /**
     * 唯一性校验
     * 规则名称
     * 同一个校区默认规则唯一
     *  @param defaultRule
     *  @param campus
     */
    public void editDefaultRules(Integer defaultRule, Integer campus, String idCode){

        if(defaultRule == 1){
            return;
        }


        //在数据库中 匹配数据
        List<CompletionTemplateDO> completionTemplateDO = completionTemplateMapper.selectByDefaultRuleEdit(defaultRule, campus, idCode);

        if(completionTemplateDO.isEmpty()){
            return;
        }else{
            throw exception(COMPLETION_TEMPLATE_DEFAULT_RULE_EXISTS);
        }

    }


    // 从数据库查询动态表头
    public  List<Map<String, Map<String, List<String>>>> getDynamicHeaders(Integer classId,List<ClassCompletionTemplateDO> list) {
        List<Map<String, Map<String, List<String>>>> headers;

//        List<CompletionTemplateDO> list = getCompletionTemplateList(classManagementMapper.selectById(classId));


        headers = convertToHeaders(list);

//        // 构建理论学习表头
//        Map<String, Map<String, List<String>>> theoryStudy = new HashMap<>();
//        theoryStudy.put("理论学习50分", new HashMap<String, List<String>>() {{
//            put("学习考勤20", Arrays.asList("考试成绩"));
//            put("学习评价", Arrays.asList("到课率", "学习笔记"));
//        }});
//
//        // 构建党性锻炼表头
//        Map<String, Map<String, List<String>>> partyStudy = new HashMap<>();
//        partyStudy.put("党性锻炼50分", new HashMap<String, List<String>>() {{
//            put("考勤纪律", Arrays.asList("考勤记录", "组织纪律"));
//            put("廉洁纪律", Arrays.asList("廉洁行为", "道德规范"));
//        }});
//
//        // 构建加分项表头
//        Map<String, Map<String, List<String>>> bonusItems = new HashMap<>();
//        bonusItems.put("加分项", new HashMap<String, List<String>>() {{
//            put("奖励记录", Arrays.asList("发表文章", "竞赛获奖"));
//        }});
//
//        // 构建另一个加分项表头
//        Map<String, Map<String, List<String>>> bonusItems1 = new HashMap<>();
//        bonusItems1.put("加分项1", new HashMap<String, List<String>>() {{
//            put("奖励记录1", Arrays.asList("发表文章1", "竞赛获奖1"));
//        }});
//
//        // 将所有动态表头加入列表
//        headers.add(theoryStudy);
//        headers.add(partyStudy);
//        headers.add(bonusItems);
//        headers.add(bonusItems1);

        return headers;
    }

    private List<ClassCompletionTemplateDO> getCompletionTemplateList(ClassManagementDO classDO) {
        List<ClassCompletionTemplateDO> list;

        if (StrUtil.isNotBlank(classDO.getIdCode())){
            list = classCompletionTemplateMapper.selectByTemplateCode(classDO.getId(), classDO.getIdCode());
        }else {
            //根据班级id查询对应的表头内容
            list = completionTemplateMapper.selectClassDefaultRule(0, classDO.getCampus());
        }
        return list;
    }


    public static List<Map<String, Map<String, List<String>>>> convertToHeaders(List<ClassCompletionTemplateDO> list) {
        list.removeIf(item -> item.getSerialNumber().contains("S"));

        // 使用 Stream API 进行分组和转换
        return list.stream()
                .collect(Collectors.groupingBy(
                        ClassCompletionTemplateDO::getModuleName,
                        LinkedHashMap::new, // 保证 moduleName 的顺序
                        Collectors.toList()
                ))
                .entrySet().stream()
                .map(entry -> {
                    String moduleName = entry.getKey();

                    // 使用 LinkedHashMap 保证 columnName 的顺序
                    Map<String, List<String>> columnMap = new LinkedHashMap<>();
                    entry.getValue().forEach(trainee -> {
                        columnMap.computeIfAbsent(trainee.getColumnName(), k -> new ArrayList<>())
                                .add(trainee.getAssessmentName());
                    });

                    // 封装结果
                    Map<String, Map<String, List<String>>> moduleMap = new LinkedHashMap<>();
                    moduleMap.put(moduleName, columnMap);
                    return moduleMap;
                })
                .collect(Collectors.toList());

    }


    // 从数据库查询学生信息
    public List<Map<String, Object>> getStudentData(HttpServletRequest request,Integer classId) {
        // 查出班级学员信息
        List<ClassCompletionDO> infoList = classCompletionMapper.getInfoByClassId(classId);

        // 一次性查询所有可能需要的数据，避免循环中重复查询
        Map<Long, List<TraineeLeaveRespVO>> leaveMapPersonal = Collections.emptyMap();
        Map<Long, List<TraineeLeaveRespVO>> leaveMapSick = Collections.emptyMap();
        Map<Long, List<TraineeLeaveRespVO>> leaveMapFiveCans = Collections.emptyMap();
        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap = Collections.emptyMap();
        Map<Long, List<ClockInInfoStudentStatusVO>> lateMap = Collections.emptyMap();
        Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap = Collections.emptyMap();

        // 检查是否需要查询请假数据
        boolean needLeavePersonal = false;
        boolean needLeaveSick = false;
        boolean needLeaveFiveCans = false;
        boolean needAttendance = false;
        boolean needLate = false;
        boolean needEvaluation = false;

        // 预检查需要的数据类型
        for (ClassCompletionDO info : infoList) {
            if (info.getAcquisitionMode() != null && info.getAcquisitionMode() == 1) {
                Integer dataSource = info.getDataSource();
                if (dataSource == DataSourceEnum.PERSONAL_LEAVE_NUM.getCode() ||
                        dataSource == DataSourceEnum.PERSONAL_LEAVE_DAYS.getCode()) {
                    needLeavePersonal = true;
                } else if (dataSource == DataSourceEnum.SICK_LEAVE_NUM.getCode() ||
                        dataSource == DataSourceEnum.SICK_LEAVE_DAYS.getCode()) {
                    needLeaveSick = true;
                } else if (dataSource == DataSourceEnum.FIVE_CANS_LEAVE_NUM.getCode() ||
                        dataSource == DataSourceEnum.FIVE_CANS_LEAVE_DAYS.getCode()) {
                    needLeaveFiveCans = true;
                } else if (dataSource >= DataSourceEnum.CLASS_ATTENDANCE_RATE.getCode() &&
                        dataSource <= DataSourceEnum.ACCOMMODATION_RATE.getCode()) {
                    needAttendance = true;
                } else if (dataSource == DataSourceEnum.LATE_COUNT.getCode()) {
                    needLate = true;
                } else if (dataSource == DataSourceEnum.COURSE_EVALUATION_RATE.getCode()) {
                    needEvaluation = true;
                }
            }
        }

        // 批量获取请假数据
        if (needLeavePersonal) {
            TraineeLeavePageReqVO reqVO = buildLeaveReqVO(classId, 10000);
            reqVO.setLeaveType(TraineeLeaveType.BUSINESS.getCode());
            leaveMapPersonal = leaveService.pageList(reqVO).getList().stream()
                    .collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId));
        }

        if (needLeaveSick) {
            TraineeLeavePageReqVO reqVO = buildLeaveReqVO(classId, 10000);
            reqVO.setLeaveType(TraineeLeaveType.SICK.getCode());
            leaveMapSick = leaveService.pageList(reqVO).getList().stream()
                    .collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId));
        }

        if (needLeaveFiveCans) {
            TraineeLeavePageReqVO reqVO = buildLeaveReqVO(classId, 10000);
            reqVO.setLeaveType(TraineeLeaveType.FIVE_CANS.getCode());
            leaveMapFiveCans = leaveService.pageList(reqVO).getList().stream()
                    .collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId));
        }

        // 批量获取考勤数据
        if (needAttendance) {
            AttendanceRateReqVO reqVO = buildAttendanceReqVO(classId);
            attendanceMap = clockInInfoService.getListForAttendanceThreeRate(reqVO).getTraineeInfoList().stream()
                    .collect(Collectors.groupingBy(AttendanceRateTraineeInfoVO::getTraineeId));
        }

        // 批量获取迟到数据
        if (needLate) {
            ClockInInfoPageReqVO reqVO = buildLateReqVO(classId);
            lateMap = clockInInfoService.getStudentClockingStatusPage(request, reqVO).getList().stream()
                    .collect(Collectors.groupingBy(ClockInInfoStudentStatusVO::getTraineeId));
        }

        // 批量获取评价数据
        if (needEvaluation) {
            TraineeEvaluationDetailPageReqVO reqVO = buildEvaluationReqVO(classId);
            evaluationMap = evaluationResponseService.getTraineeEvaluationDetailPage(reqVO).getList().stream()
                    .collect(Collectors.groupingBy(TraineeEvaluationDetailPageRespVO::getTraineeId));
        }

        // 现在处理每个学员的成绩信息
        for (ClassCompletionDO info : infoList) {
            if (info.getAcquisitionMode() != null && info.getAcquisitionMode() == 1) {
                Integer dataSource = info.getDataSource();
                Long traineeId = info.getTraineeId();

                if (dataSource == DataSourceEnum.PERSONAL_LEAVE_NUM.getCode()) {
                    List<TraineeLeaveRespVO> leaves = leaveMapPersonal.get(traineeId);
                    info.setScore(leaves != null ? String.valueOf(leaves.size()) : "0");
                } else if (dataSource == DataSourceEnum.SICK_LEAVE_NUM.getCode()) {
                    List<TraineeLeaveRespVO> leaves = leaveMapSick.get(traineeId);
                    info.setScore(leaves != null ? String.valueOf(leaves.size()) : "0");
                } else if (dataSource == DataSourceEnum.FIVE_CANS_LEAVE_NUM.getCode()) {
                    List<TraineeLeaveRespVO> leaves = leaveMapFiveCans.get(traineeId);
                    info.setScore(leaves != null ? String.valueOf(leaves.size()) : "0");
                } else if (dataSource == DataSourceEnum.PERSONAL_LEAVE_DAYS.getCode()) {
                    List<TraineeLeaveRespVO> leaves = leaveMapPersonal.get(traineeId);
                    double days = leaves != null ? leaves.stream().mapToDouble(TraineeLeaveRespVO::getDays).sum() : 0;
                    info.setScore(String.format("%.1f", days));
                } else if (dataSource == DataSourceEnum.SICK_LEAVE_DAYS.getCode()) {
                    List<TraineeLeaveRespVO> leaves = leaveMapSick.get(traineeId);
                    double days = leaves != null ? leaves.stream().mapToDouble(TraineeLeaveRespVO::getDays).sum() : 0;
                    info.setScore(String.format("%.1f", days));
                } else if (dataSource == DataSourceEnum.FIVE_CANS_LEAVE_DAYS.getCode()) {
                    List<TraineeLeaveRespVO> leaves = leaveMapFiveCans.get(traineeId);
                    double days = leaves != null ? leaves.stream().mapToDouble(TraineeLeaveRespVO::getDays).sum() : 0;
                    info.setScore(String.format("%.1f", days));
                } else if (dataSource >= DataSourceEnum.CLASS_ATTENDANCE_RATE.getCode() &&
                        dataSource <= DataSourceEnum.ACCOMMODATION_RATE.getCode()) {
                    List<AttendanceRateTraineeInfoVO> rates = attendanceMap.get(traineeId);
                    if (rates != null && !rates.isEmpty()) {
                        AttendanceRateTraineeInfoVO rateInfo = rates.get(0);
                        if (dataSource == DataSourceEnum.CLASS_ATTENDANCE_RATE.getCode()) {
                            info.setScore(rateInfo.getClassRate());
                        } else if (dataSource == DataSourceEnum.MEAL_ATTENDANCE_RATE.getCode()) {
                            info.setScore(rateInfo.getMealRate());
                        } else if (dataSource == DataSourceEnum.ACCOMMODATION_RATE.getCode()) {
                            info.setScore(rateInfo.getAccommodationRate());
                        }
                    } else {
                        info.setScore("0");
                    }
                } else if (dataSource == DataSourceEnum.COURSE_EVALUATION_RATE.getCode()) {
                    List<TraineeEvaluationDetailPageRespVO> evaluations = evaluationMap.get(traineeId);
                    if (evaluations != null && !evaluations.isEmpty()) {
                        info.setScore(String.format("%.2f", evaluations.get(0).getRatio() * 100));
                    } else {
                        info.setScore("0");
                    }
                } else if (dataSource == DataSourceEnum.LATE_COUNT.getCode()) {
                    List<ClockInInfoStudentStatusVO> lateInfo = lateMap.get(traineeId);
                    info.setScore(lateInfo != null ? String.valueOf(lateInfo.size()) : "0");
                }
            }

            // 确保所有项目都有得分
            if (info.getScore() == null) {
                info.setScore("0");
            }
        }

        // 使用LinkedHashMap保持顺序
        Map<Long, List<ClassCompletionDO>> groupedByTraineeId = infoList.stream()
                .collect(Collectors.groupingBy(
                        ClassCompletionDO::getTraineeId,
                        LinkedHashMap::new,
                        Collectors.toList()));

        // 预分配ArrayList大小以避免扩容开销
        List<Map<String, Object>> students = new ArrayList<>(groupedByTraineeId.size());

        // 遍历分组后的数据
        for (Map.Entry<Long, List<ClassCompletionDO>> entry : groupedByTraineeId.entrySet()) {
            List<ClassCompletionDO> records = entry.getValue();
            ClassCompletionDO firstRecord = records.get(0);

            Map<String, Object> student = new HashMap<>(5); // 预估容量

            // 设置基本信息，不再包括traineeId
            student.put("name", firstRecord.getTraineeName());
            student.put("unitPosition", firstRecord.getUnitPosition());
            student.put("classPosition", firstRecord.getClassPosition());
            student.put("groupName", firstRecord.getGroupName());

            // 提取该学员的所有成绩
            List<String> scores = records.stream()
                    .map(ClassCompletionDO::getScore)
                    .collect(Collectors.toList());

            student.put("scores", scores);
            students.add(student);
        }

        return students;
    }

    List<ClassCompletionDO> sortByModuleAndColumn(List<ClassCompletionDO> list) {
        if (list == null || list.size() <= 1) {
            return list;
        }

        // 1. 记录元素的初始索引，确保相对顺序不变
        Map<ClassCompletionDO, Integer> indexMap = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            indexMap.put(list.get(i), i);
        }

        // 2. 按 (moduleName, columnName) 分组，保持原始相对顺序
        Map<String, List<ClassCompletionDO>> groupedMap = list.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getModuleName() + "_" + item.getColumnName(),  // 用 moduleName 和 columnName 作为 key
                        LinkedHashMap::new, // 保持插入顺序
                        Collectors.toList()
                ));

        // 3. 按组重新排列，确保稳定性
        List<ClassCompletionDO> sortedList = new ArrayList<>();
        groupedMap.values().forEach(sortedList::addAll);

        return sortedList;
    }


    private Map<Integer, Map<Long, List<TraineeLeaveRespVO>>> queryLeaveData(Integer classId) {
        TraineeLeavePageReqVO leaveReqVO = new TraineeLeavePageReqVO();
        leaveReqVO.setClassId(Long.valueOf(classId));
        leaveReqVO.setPageNo(1);
        leaveReqVO.setPageSize(10000);

        Map<Integer, Map<Long, List<TraineeLeaveRespVO>>> leaveData = new HashMap<>();

        // 批量获取数据
        List<TraineeLeaveRespVO> allLeaveList = leaveService.pageList(buildLeaveReqVO(classId, 10000)).getList();

        leaveReqVO.setLeaveType(1);
        leaveData.put(0, allLeaveList.stream()
                .collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId)));
        leaveReqVO.setLeaveType(2);
        leaveData.put(1, allLeaveList.stream()
                .collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId)));
        leaveReqVO.setLeaveType(3);
        leaveData.put(2, allLeaveList.stream()
                .collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId)));

        return leaveData;
    }

    private Map<Long, List<AttendanceRateTraineeInfoVO>> queryAttendanceRateData(Integer classId) {
        AttendanceRateReqVO attendanceReqVO = new AttendanceRateReqVO();
        attendanceReqVO.setClassId(Long.valueOf(classId));

        List<AttendanceRateTraineeInfoVO> allAttendanceRates = clockInInfoService.getListForAttendanceThreeRate(attendanceReqVO).getTraineeInfoList();

        allAttendanceRates.forEach(item -> {
            if (item != null) {
                if (item.getAccommodationRate() != null) {
                    item.setAccommodationRate(item.getAccommodationRate().replace("%", ""));
                }
                if (item.getClassRate() != null) {
                    item.setClassRate(item.getClassRate().replace("%", ""));
                }
                if (item.getMealRate() != null) {
                    item.setMealRate(item.getMealRate().replace("%", ""));
                }
            }
        });
        return allAttendanceRates.stream().collect(Collectors.groupingBy(AttendanceRateTraineeInfoVO::getTraineeId));
    }

    private Map<Long, List<TraineeAttendanceInfoDTO>> queryLateInfoData(Integer classId) {
        AttendanceRateReqVO attendanceReqVO = new AttendanceRateReqVO();
        attendanceReqVO.setClassId(Long.valueOf(classId));
        return clockInInfoService.getLateInfo(attendanceReqVO)
                .stream().collect(Collectors.groupingBy(TraineeAttendanceInfoDTO::getTraineeId));
    }

    private void processStudentInfo(ClassCompletionDO info,
                                    Map<Integer, Map<Long, List<TraineeLeaveRespVO>>> leaveData,
                                    Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceRateMap,
                                    Map<Long, List<ClockInInfoStudentStatusVO>> lateInfoMap,
                                    Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap) {
        if (info.getScore() != null){
            return;
        }
        if (info.getAcquisitionMode() != null && info.getAcquisitionMode() == 1) {
            if (info.getDataSource() != null) {
                switch (info.getDataSource()) {
                    case 0:
                        Map<Long, List<TraineeLeaveRespVO>> leaveMap = leaveData.get(info.getDataSource());
                        List<TraineeLeaveRespVO> leaves = leaveMap.get(info.getTraineeId());

                        List<TraineeLeaveRespVO> tmpLeave = leaves.stream()
                                .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.BUSINESS.getCode()))
                                .collect(Collectors.toList());
                        info.setScore(String.valueOf(tmpLeave.size()));
                        break;
                    case 1:
                        Map<Long, List<TraineeLeaveRespVO>> leaveMap1 = leaveData.get(info.getDataSource());
                        List<TraineeLeaveRespVO> leaves1 = leaveMap1.get(info.getTraineeId());

                        List<TraineeLeaveRespVO> tmpLeave1 = leaves1.stream()
                                .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.BUSINESS.getCode()))
                                .collect(Collectors.toList());
                        info.setScore(String.valueOf(tmpLeave1.size()));
                        break;
                    case 2:
                        Map<Long, List<TraineeLeaveRespVO>> leaveMap2 = leaveData.get(info.getDataSource());
                        List<TraineeLeaveRespVO> leaves2 = leaveMap2.get(info.getTraineeId());

                        List<TraineeLeaveRespVO> tmpLeave2 = leaves2.stream()
                                .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.BUSINESS.getCode()))
                                .collect(Collectors.toList());
                        info.setScore(String.valueOf(tmpLeave2.size()));
                        break;
                    case 3:
                    case 4:
                    case 5:
                        processAttendanceRates(info, attendanceRateMap);
                        break;
                    case 6:
                        processEvaluationRates(info, evaluationMap);
                        break;
                    case 7:
                        info.setScore(String.valueOf(lateInfoMap.getOrDefault(info.getTraineeId(), Collections.emptyList()).size()));
                        break;
                    default:
                        info.setScore(String.valueOf(0));
                        break;
                }
            }
        } else {
            info.setScore("0");
        }
    }

    private void processEvaluationRates(ClassCompletionDO info, Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap) {
        List<TraineeEvaluationDetailPageRespVO> rates = evaluationMap.getOrDefault(info.getTraineeId(), Collections.emptyList());

        if (!rates.isEmpty()) {
            TraineeEvaluationDetailPageRespVO traineeInfo = rates.get(0);
//            return String.format("%.2f", traineeInfo.getRatio() * 100);
            info.setScore(String.format("%.2f", traineeInfo.getRatio() * 100));
        }else {
            info.setScore(String.valueOf(0));
        }
    }

    private void processAttendanceRates(ClassCompletionDO info,
                                        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceRateMap) {
        List<AttendanceRateTraineeInfoVO> rates = attendanceRateMap.getOrDefault(info.getTraineeId(), Collections.emptyList());
        if (!rates.isEmpty()) {
            AttendanceRateTraineeInfoVO traineeInfo = rates.get(0);
            switch (info.getDataSource()) {
                case 3:
                    info.setScore(traineeInfo.getClassRate());
                    break;
                case 4:
                    info.setScore(String.valueOf(traineeInfo.getMealRate()));
                    break;
                case 5:
                    info.setScore(traineeInfo.getAccommodationRate());
                    break;
                default:
                    info.setScore(String.valueOf(0));
                    break;
            }
        } else {
            info.setScore(String.valueOf(0));
        }
    }

    private Map<Long, List<TraineeEvaluationDetailPageRespVO>> queryEvaluationRate(Integer classId) {
        TraineeEvaluationDetailPageReqVO reqVO = new TraineeEvaluationDetailPageReqVO();
        reqVO.setClassId(Long.valueOf(classId));
        List<TraineeEvaluationDetailPageRespVO> list = evaluationResponseService.getTraineeEvaluationDetailPage(reqVO).getList();

        return list.stream().collect(Collectors.groupingBy(TraineeEvaluationDetailPageRespVO::getTraineeId));
    }

    private List<Map<String, Object>> groupAndAssembleResults(List<ClassCompletionDO> infoList) {
        Map<Long, List<ClassCompletionDO>> groupedByTraineeId = infoList.stream()
                .collect(Collectors.groupingBy(ClassCompletionDO::getTraineeId, LinkedHashMap::new, Collectors.toList()));

        List<Map<String, Object>> students = new ArrayList<>();
        groupedByTraineeId.forEach((traineeId, records) -> {
            ClassCompletionDO firstRecord = records.get(0);
            Map<String, Object> student = new HashMap<>();
            student.put("traineeId", firstRecord.getTraineeId().toString());
            student.put("name", firstRecord.getTraineeName());
            student.put("unitPosition", firstRecord.getUnitPosition());
            student.put("classPosition", firstRecord.getClassPosition());
            student.put("groupName", firstRecord.getGroupName());
            student.put("scores", records.stream().map(ClassCompletionDO::getScore).collect(Collectors.toList()));
            students.add(student);
        });

        return students;
    }


    // 构建表头
    // 构建表头逻辑
    private void buildHeader(Sheet sheet, List<String> fixedHeaders, List<Map<String, Map<String, List<String>>>> dynamicHeaders, List<String> lastHeaders) {
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        Font boldFont = sheet.getWorkbook().createFont();
        boldFont.setBold(true);
        headerStyle.setFont(boldFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 根据动态表头和固定表头计算总列数
        int totalSize = 0;
        for (Map<String, Map<String, List<String>>> outerMap : dynamicHeaders) {
            for (Map<String, List<String>> middleMap : outerMap.values()) {
                for (List<String> innerList : middleMap.values()) {
                    totalSize += innerList.size();
                }
            }
        }
        int totalCols = fixedHeaders.size() + totalSize + lastHeaders.size();

        // 创建三行表头
        Row row1 = sheet.createRow(0);
        Row row2 = sheet.createRow(1);
        Row row3 = sheet.createRow(2);

        // 设置前三列列宽
        for (int i = 0; i < fixedHeaders.size(); i++) {
            sheet.setColumnWidth(i, 15 * 256); // 15字符宽
        }

        // 处理固定表头
        int colIndex = 0;
        for (int i = 0; i < fixedHeaders.size(); i++) {
            Cell cell1 = row1.createCell(colIndex);
            Cell cell2 = row2.createCell(colIndex);
            Cell cell3 = row3.createCell(colIndex);

            cell1.setCellValue(fixedHeaders.get(i));
            cell2.setCellValue(fixedHeaders.get(i));
            cell3.setCellValue(fixedHeaders.get(i));

            cell1.setCellStyle(headerStyle);
            cell2.setCellStyle(headerStyle);
            cell3.setCellStyle(headerStyle);

            // 合并单元格 (开始行, 结束行, 开始列, 结束列)
            sheet.addMergedRegion(new CellRangeAddress(0, 2, colIndex, colIndex));
            colIndex++;
        }

        // 处理动态表头
        for (Map<String, Map<String, List<String>>> outerMap : dynamicHeaders) {
            for (Map.Entry<String, Map<String, List<String>>> outerEntry : outerMap.entrySet()) {
                String outerKey = outerEntry.getKey();
                Map<String, List<String>> middleMap = outerEntry.getValue();

                // 计算当前外层表头要合并的列数
                int outerSpan = 0;
                for (List<String> innerList : middleMap.values()) {
                    outerSpan += innerList.size();
                }

                // 设置外层表头
                if (outerSpan > 0) {
                    Cell outerCell = row1.createCell(colIndex);
                    outerCell.setCellValue(outerKey);
                    outerCell.setCellStyle(headerStyle);
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, colIndex, colIndex + outerSpan - 1));

                    // 处理中层表头
                    for (Map.Entry<String, List<String>> middleEntry : middleMap.entrySet()) {
                        String middleKey = middleEntry.getKey();
                        List<String> innerList = middleEntry.getValue();
                        int innerSpan = innerList.size();

                        // 设置中层表头
                        Cell middleCell = row2.createCell(colIndex);
                        middleCell.setCellValue(middleKey);
                        middleCell.setCellStyle(headerStyle);

                        if (innerSpan > 1) {
                            sheet.addMergedRegion(new CellRangeAddress(1, 1, colIndex, colIndex + innerSpan - 1));
                        }

                        // 设置内层表头
                        for (int i = 0; i < innerList.size(); i++) {
                            Cell innerCell = row3.createCell(colIndex);
                            innerCell.setCellValue(innerList.get(i));
                            innerCell.setCellStyle(headerStyle);

                            // 设置内层表头列宽
                            sheet.setColumnWidth(colIndex, 15 * 256);
                            colIndex++;
                        }
                    }
                }
            }
        }

        // 处理尾部表头
        for (int i = 0; i < lastHeaders.size(); i++) {
            Cell cell1 = row1.createCell(colIndex);
            Cell cell2 = row2.createCell(colIndex);
            Cell cell3 = row3.createCell(colIndex);

            cell1.setCellValue(lastHeaders.get(i));
            cell2.setCellValue(lastHeaders.get(i));
            cell3.setCellValue(lastHeaders.get(i));

            cell1.setCellStyle(headerStyle);
            cell2.setCellStyle(headerStyle);
            cell3.setCellStyle(headerStyle);

            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(0, 2, colIndex, colIndex));

            // 设置列宽
            sheet.setColumnWidth(colIndex, 15 * 256);
            colIndex++;
        }
    }

    private void fillStudentData(Workbook workbook, Sheet sheet, List<Map<String, Object>> students, Integer type, int size) {
        // 创建基本单元格样式
        CellStyle basicStyle = workbook.createCellStyle();
        basicStyle.setAlignment(HorizontalAlignment.CENTER);
        basicStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 预创建行，提高性能
        int rowIndex = 3; // 从第4行开始填充数据

        // 批量创建必要的行
        for (int i = 0; i < students.size(); i++) {
            sheet.createRow(rowIndex + i);
        }

        // 填充数据
        for (Map<String, Object> student : students) {
            Row dataRow = sheet.getRow(rowIndex);
            int cellIndex = 0;

            // 填充基本信息
            String name = (String) student.get("name");
            dataRow.createCell(cellIndex++).setCellValue(name != null ? name : "");

            String unitPosition = (String) student.get("unitPosition");
            dataRow.createCell(cellIndex++).setCellValue(unitPosition != null ? unitPosition : "");

            String classPosition = (String) student.get("classPosition");
            dataRow.createCell(cellIndex++).setCellValue(classPosition != null ? classPosition : "");

            // 填充成绩
            @SuppressWarnings("unchecked")
            List<String> scores = (List<String>) student.get("scores");
            if (scores != null) {
                for (String score : scores) {
                    Cell cell = dataRow.createCell(cellIndex++);
                    if (score != null) {
                        try {
                            // 尝试转换为数字
                            if (score.contains(".")) {
                                cell.setCellValue(Double.parseDouble(score));
                            } else {
                                cell.setCellValue(Integer.parseInt(score));
                            }
                        } catch (NumberFormatException e) {
                            // 转换失败时按字符串处理
                            cell.setCellValue(score);
                        }
                    } else {
                        cell.setCellValue("0");
                    }
                }
            }

            rowIndex++;
        }
    }

}
