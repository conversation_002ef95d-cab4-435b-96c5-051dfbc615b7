package com.unicom.swdx.module.edu.service.classcommen;


import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.dal.mysql.signupunit.SignUpUnitMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.dal.mysql.traineeleave.TraineeLeaveMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.enums.traineeleave.TraineeLeaveStatus;
import com.unicom.swdx.module.edu.enums.userrole.JwUserRoleTypeEnum;
import com.unicom.swdx.module.edu.service.rollcallsignin.RollcallSignInService;
import com.unicom.swdx.module.system.api.tenant.TenantApi;
import com.unicom.swdx.module.system.api.tenant.dto.TenantInfoRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Validated
@Slf4j
public class CommenServiceImpl implements CommenService {


    @Resource
    TraineeLeaveMapper traineeLeaveMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;

    @Resource
    private SignUpUnitMapper signUpUnitMapper;

    @Resource
    private ClockInInfoMapper clockInInfoMapper;

    @Resource
    private RollcallSignInService rollcallSignInService;

    @Resource
    private TenantApi tenantApi;

    @Value("${unicom.mid-base-uri}")
    private String MID_BASE_URI;

    /**
     * 教职工分配学员和小程序入口
     *
     * @param userIds 业中用户id列表
     * @return 小程序用户id列表
     */
    @Override
    @Async
    public void addTeacherRole(List<Long> userIds) {
        try {
            String addTeacherRoleUrl = MID_BASE_URI + "/prod-api/system/user/addTeacherRole";
            String parameter = JSONUtil.toJsonStr(userIds);
            HttpResponse response = HttpRequest.post(addTeacherRoleUrl)
                    .body(parameter)  // 设置请求体为原始数据
                    .contentType("application/json")  // 指定Content-Type为application/json
                    .execute();
            CommonResult<Boolean> result;
            Boolean success = false;
            try {
                result = JSON.parseObject(response.body(), new TypeReference<CommonResult<Boolean>>() {
                });
                success = result.getCheckedData();
            } catch (Exception e) {
                log.info("教职工{}分配学员和小程序入口失败：{}", userIds, e.getMessage());
            }
            log.info("教职工{}分配学员和小程序入口调用{}！", userIds, success);
        } catch (Exception e) {
            log.info("教职工{}分配学员和小程序入口异常：{}", userIds, e.getMessage());
        }
    }

    /**
     * 获取用户在教务的基本角色 (教师、学员、调训单位管理员)
     * （由于无user_role表，通过查相关表实现获取用户角色的公共实现）
     *
     * @param userId 用户id
     * @return 角色列表 {@link JwUserRoleTypeEnum}
     */
    @Override
    public List<Integer> getJwUserRoleByUserId(Long userId) {
        List<Integer> userRoleList = new ArrayList<>();
        // 1.查询学员
        TraineeDO traineeDO = traineeMapper.selectByUserId(userId);
        if (traineeDO != null) {
            userRoleList.add(JwUserRoleTypeEnum.TRAINEE.getRoleType());
        }
        // 2.查询教师
        TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectByUserId(userId);
        if (teacherInformationDO != null) {
            userRoleList.add(JwUserRoleTypeEnum.TEACHER.getRoleType());
        }
        // 3.查询调训单位管理员
        SignUpUnitDO signUpUnitDO = signUpUnitMapper.selectByUserIdFromTemplate(userId);
        if (signUpUnitDO != null) {
            userRoleList.add(JwUserRoleTypeEnum.UNIT_ADMIN.getRoleType());
        }
        // 4.查询租户管理员
        Set<Long> tenantAdminUserIdSet = tenantApi.getTenantByContactUserIds(Collections.singletonList(userId)).getCheckedData().stream()
                .map(TenantInfoRespDTO::getContactUserId)
                .collect(Collectors.toSet());
        if (tenantAdminUserIdSet.contains(userId)) {
            userRoleList.add(JwUserRoleTypeEnum.TENANT_ADMIN.getRoleType());
        }
        return userRoleList;
    }

    /**
     * 获取多个用户在教务的基本角色 (教师、学员、调训单位管理员、租户管理员)
     * （由于未使用user_role表，通过查相关表实现获取用户角色的公共实现）
     * （随着角色增加、查询的sql越多）
     *
     * @param userIds 用户id列表
     * @return Map<userId, 角色列表> {@link JwUserRoleTypeEnum}
     */
    @Override
    public Map<Long, List<Integer>> getJwUserRoleByUserIdList(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }

//        // 1.查询学员
//        List<Long> traineeUserIdList = traineeMapper.getTraineeByUserIdList(userIds)
//                .stream().map(TraineeDO::getUserId).collect(Collectors.toList());
//        // 2.查询教师
//        List<Long> teacherUserIdList = teacherInformationMapper.selectListByUserIdList(userIds)
//                .stream().map(TeacherInformationDO::getUserId).collect(Collectors.toList());
//        // 3.查询调训单位管理员
//        List<Long> signUpUnitUserIdList = signUpUnitMapper.selectListByUserIdListFromTemplate(userIds)
//                .stream().map(SignUpUnitDO::getUserId).collect(Collectors.toList());

        // 1.查询学员
        Set<Long> traineeUserIdSet = traineeMapper.getTraineeByUserIdList(userIds).stream()
                .map(TraineeDO::getUserId)
                .collect(Collectors.toSet());
        // 2.查询教师
        Set<Long> teacherUserIdSet = teacherInformationMapper.selectListByUserIdList(userIds).stream()
                .map(TeacherInformationDO::getUserId)
                .collect(Collectors.toSet());
        // 3.查询调训单位管理员
        Set<Long> signUpUnitUserIdSet = signUpUnitMapper.selectListByUserIdListFromTemplate(userIds).stream()
                .map(SignUpUnitDO::getUserId)
                .collect(Collectors.toSet());
        // 4.查询租户管理员
        Set<Long> tenantAdminUserIdSet = tenantApi.getTenantByContactUserIds(userIds).getCheckedData().stream()
                .map(TenantInfoRespDTO::getContactUserId)
                .collect(Collectors.toSet());

        Map<Long, List<Integer>> userRoleMap = new HashMap<>();
        for (Long userId : userIds) {
            List<Integer> userRoleList = new ArrayList<>();
            if (traineeUserIdSet.contains(userId)) {
                userRoleList.add(JwUserRoleTypeEnum.TRAINEE.getRoleType());
            }
            if (teacherUserIdSet.contains(userId)) {
                userRoleList.add(JwUserRoleTypeEnum.TEACHER.getRoleType());
            }
            if (signUpUnitUserIdSet.contains(userId)) {
                userRoleList.add(JwUserRoleTypeEnum.UNIT_ADMIN.getRoleType());
            }
            if (tenantAdminUserIdSet.contains(userId)) {
                userRoleList.add(JwUserRoleTypeEnum.TENANT_ADMIN.getRoleType());
            }
            userRoleMap.put(userId, userRoleList);
        }
        return userRoleMap;
    }

    @Override
    public List<TraineeDO> getUsersByClasscourseid(Long classcourseid, Long classid) {


        // 校验课表存在
        ClassCourseDO classCourseDO = rollcallSignInService.valiateClassCourseIdExist(classcourseid);


        // 查询班级所有(已报到)学员
        List<Integer> traineeStatusList = Collections.singletonList(TraineeStatusEnum.REPORTED.getStatus());
        List<TraineeDO> traineeDOList = traineeMapper.selectListByClassIdAndStatus(classid,
                traineeStatusList);

        List<Integer> leaveStatusList = Collections.singletonList(TraineeLeaveStatus.OK.getCode());
        if (traineeDOList.isEmpty()) {
            return new ArrayList<TraineeDO>();
        }
        List<Long> traineeIdList = traineeDOList.stream().map(TraineeDO::getId).collect(Collectors.toList());
        List<TraineeLeaveDO> traineeLeaveDOList = traineeLeaveMapper.selectListByTraineeIdListAndTime(traineeIdList,
                classCourseDO.getBeginTime(), classCourseDO.getEndTime(), leaveStatusList);


        // 获取不在 traineeLeaveDOList 的 TraineeDO
        List<TraineeDO> result = traineeDOList.stream()
                .filter(trainee -> traineeLeaveDOList.stream()
                        .noneMatch(leave -> leave.getTraineeId().equals(trainee.getId())))
                .collect(Collectors.toList());


        return result;
    }

    @Override
    public List<TraineeDO> getCheckedUserByClassCourseId(Long classcourseid, Long classid) {
        // 校验课表存在
        ClassCourseDO classCourseDO = rollcallSignInService.valiateClassCourseIdExist(classcourseid);


        // 查询班级所有(已报到)学员
        List<Integer> traineeStatusList = Collections.singletonList(TraineeStatusEnum.REPORTED.getStatus());
        List<TraineeDO> traineeDOList = traineeMapper.selectListByClassIdAndStatus(classid,
                traineeStatusList);

        List<Integer> leaveStatusList = Collections.singletonList(TraineeLeaveStatus.OK.getCode());
        if (traineeDOList.isEmpty()) {
            return new ArrayList<TraineeDO>();
        }
        List<Long> traineeIdList = traineeDOList.stream().map(TraineeDO::getId).collect(Collectors.toList());
        List<TraineeLeaveDO> traineeLeaveDOList = traineeLeaveMapper.selectListByTraineeIdListAndTime(traineeIdList,
                classCourseDO.getBeginTime(), classCourseDO.getEndTime(), leaveStatusList);

        // List<Long> uncheckedIdList = clockInInfoMapper.selectUnchecked(classcourseid);

        List<Long> checkedIdList = clockInInfoMapper.selectChecked(classcourseid);

        // 获取不在 traineeLeaveDOList 的 TraineeDO
        List<TraineeDO> result = traineeDOList.stream()
                .filter(trainee -> traineeLeaveDOList.stream()
                        .noneMatch(leave -> leave.getTraineeId().equals(trainee.getId())))
                .collect(Collectors.toList());
        result = result.stream().filter(traineeDO -> checkedIdList.contains(traineeDO.getId())).collect(Collectors.toList());


        return result;
    }

    @Override
    public List<TraineeDO> getAllUsersByClasscourseid(Long classcourseid, Long classid) {


        // 校验课表存在
        ClassCourseDO classCourseDO = rollcallSignInService.valiateClassCourseIdExist(classcourseid);


        // 查询班级所有(已报到)学员
        List<Integer> traineeStatusList = Collections.singletonList(TraineeStatusEnum.REPORTED.getStatus());
        List<TraineeDO> traineeDOList = traineeMapper.selectListByClassIdAndStatus(classid,
                traineeStatusList);

        return traineeDOList;
    }
}
