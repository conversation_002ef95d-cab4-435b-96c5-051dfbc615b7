package com.unicom.swdx.module.edu.controller.admin.plan.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.module.edu.controller.admin.planconfig.vo.PlanConfigBaseVO;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 教学计划 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 * <AUTHOR>
 */
@Data
public class PlanBaseVO {

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称", required = true)
    @NotNull(message = "计划名称不能为空")
    private String name;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期", required = true)
    @NotNull(message = "开始日期不能为空")
    private String beginDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期", required = true)
    @NotNull(message = "结束日期不能为空")
    private String endDate;

    /**
     * 常用教室id
     */
    @ApiModelProperty(value = "常用教室id", required = true)
    private Long classroomId;

    /**
     * 班级id
     */
    @ApiModelProperty(value = "班级id", required = true)
    @NotNull(message = "班级id不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 状态：0暂存，1应用
     */
    @ApiModelProperty(value = "状态：0暂存，1应用", required = true)
    private Boolean status;

    /**
     * 教学计划配置信息
     */
    @ApiModelProperty(value = "教学计划配置信息")
    @NotNull(message = "计划配置信息不能为空")
    private List<PlanConfigBaseVO> planConfigBaseVOList;

    /**
     * 常用教室生效日期
     */
    @ApiModelProperty(value = "常用教室生效日期")
    private String effectiveDate;

}
