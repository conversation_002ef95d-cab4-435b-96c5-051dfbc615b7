package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ApiModel("管理后台 - EduClassroomLibrary更新 Request VO")
@Data
@EqualsAndHashCode
public class ClassInfoRespVO {

    @ApiModelProperty(value = "班级id", example = "1")
    private Long id;

    @ApiModelProperty(value = "班级名称",  example = "一年级一班")
    private String name;

    @ApiModelProperty(value = "校区", example = "1")
    private Integer campus;

}
