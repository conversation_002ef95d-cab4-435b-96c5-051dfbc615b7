package com.unicom.swdx.module.edu.controller.admin.leavenotification.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("管理后台 - 离校报备详情 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeaveNotificationDetailRespVO extends LeaveNotificationRespVO {

    @ApiModelProperty(value = "未填写学员列表")
    private List<TraineeItem> notFilledTrainees;

    @ApiModelProperty(value = "离校学员列表")
    private List<TraineeItem> leavingTrainees;

    @ApiModelProperty(value = "不离校学员列表")
    private List<TraineeItem> stayingTrainees;

    @Data
    @ApiModel("学员信息项")
    public static class TraineeItem {

        @ApiModelProperty(value = "学员ID", required = true, example = "1024")
        private Long id;

        @ApiModelProperty(value = "学员姓名", required = true, example = "张三")
        private String name;

        @ApiModelProperty(value = "学员手机号", required = true, example = "138****1234")
        private String mobile;

        @ApiModelProperty(value = "是否为手机号脱敏状态", required = true, example = "true")
        private Boolean mobileMasked;

        @ApiModelProperty(value = "填报状态", required = true, example = "1", notes = "1-未填写 2-离校 3-不离校")
        private Integer status;

        @ApiModelProperty(value = "填报时间")
        private LocalDateTime fillTime;

        @ApiModelProperty(value = "离校原因", example = "回家")
        private String leaveReason;

        @ApiModelProperty(value = "离校地址", example = "北京市海淀区")
        private String leaveAddress;

        @ApiModelProperty(value = "交通方式", example = "高铁")
        private String transportation;

        @ApiModelProperty(value = "预计返校时间")
        private LocalDateTime expectedReturnTime;
    }
}