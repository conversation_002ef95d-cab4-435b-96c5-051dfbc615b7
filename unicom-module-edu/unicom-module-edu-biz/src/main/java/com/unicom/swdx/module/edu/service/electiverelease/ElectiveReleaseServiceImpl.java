package com.unicom.swdx.module.edu.service.electiverelease;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.common.util.desensitize.DesensitizeUtils;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.dto.ReleaseAndClassSelectedInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.dto.ReleaseAndClassSelectionInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.dto.ReleaseCourseSelectedInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.classInfo.ClassInfoVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.courseinfo.CourseInfoVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.*;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electivereleasecourses.*;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.dto.ElectiveTraineeSelectedCoursesAndReleaseDTO;
import com.unicom.swdx.module.edu.convert.electiverelease.ElectiveReleaseConvert;
import com.unicom.swdx.module.edu.convert.electivereleasecourses.ElectiveReleaseCoursesConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher.ClassCourseTeacherDO;
import com.unicom.swdx.module.edu.dal.dataobject.electiverelease.ElectiveReleaseDO;
import com.unicom.swdx.module.edu.dal.dataobject.electivereleaseclasses.ElectiveReleaseClassesDO;
import com.unicom.swdx.module.edu.dal.dataobject.electivereleasecourses.ElectiveReleaseCoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.classcourseteacher.ClassCourseTeacherMapper;
import com.unicom.swdx.module.edu.dal.mysql.electiverelease.ElectiveReleaseMapper;
import com.unicom.swdx.module.edu.dal.mysql.electivereleaseclasses.ElectiveReleaseClassesMapper;
import com.unicom.swdx.module.edu.dal.mysql.electivereleasecourses.ElectiveReleaseCoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.electivetraineeselection.ElectiveTraineeSelectionMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.courses.CoursesTypeEnum;
import com.unicom.swdx.module.edu.enums.electiverelease.ClassDayPeriodEnum;
import com.unicom.swdx.module.edu.enums.electiverelease.TraineeReleaseCoursesStatusEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.edu.utils.serialnumber.PageDataSerialNumberUtil.generateSerialNumberList;

/**
 * 选修课发布信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ElectiveReleaseServiceImpl implements ElectiveReleaseService {

    @Resource
    private ElectiveReleaseMapper electiveReleaseMapper;

    @Resource
    private ElectiveReleaseCoursesMapper electiveReleaseCoursesMapper;

    @Resource
    private ElectiveReleaseClassesMapper electiveReleaseClassesMapper;

    @Resource
    private ElectiveTraineeSelectionMapper electiveTraineeSelectionMapper;

    @Resource
    private ClassCourseTeacherMapper classCourseTeacherMapper;

    @Resource
    private ClassManagementService classManagementService;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private ClassCourseMapper classCourseMapper;

    private static final  String desensitizePublicKeyStr = "04d89d95d0129f8b22a27979fd2c5d1fad305e1ec9ebbd2f6ff26f5bfdf00e5493574e8b93a2a16d4f924efd5133b6e7474aa694ec6314e39c34ff89c22d7b3768";

    private static final String desensitizePrivateKeyStr = "5174bc2473f86d5cda2ddad6a2d9b705848dd65b6fa13ff0923b1d2b55f09d30";

    private static final SM2 sm2Desensitize = SmUtil.sm2(desensitizePrivateKeyStr,desensitizePublicKeyStr);

    private static String desensitizeEncrypt(String content) {
        return sm2Desensitize.encryptBase64(content, KeyType.PublicKey);
    }

    private static String desensitizeDecrypt(String content) {
        return sm2Desensitize.decryptStr(content, KeyType.PrivateKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createElectiveRelease(ElectiveReleaseCreateReqVO createReqVO) {
        // ==班级范围排课ID列表必填==
        if (Objects.isNull(createReqVO.getClassCourseIdList()) || createReqVO.getClassCourseIdList().isEmpty()) {
            throw exception(ELECTIVE_RELEASE_CLASS_IS_EMPTY);
        }
        // 根据排课表ID获取排课表DO
        List<ClassCourseDO> classCourseDOList = classCourseMapper.selectBatchIds(createReqVO.getClassCourseIdList());
        if (classCourseDOList.isEmpty()){
            throw exception(CLASS_COURSE_NOT_EXISTS);
        }
        ClassCourseDO classCourseDO = classCourseDOList.get(0);
        // 根据排课表id设置上课时间信息
        createReqVO.setClassDate(DateUtils.parseLocalDate(classCourseDO.getDate(),DateUtils.FORMAT_YEAR_MONTH_DAY));
        createReqVO.setDayPeriod(Integer.valueOf(classCourseDO.getPeriod()));
        createReqVO.setClassStartTime(classCourseDO.getBeginTime());
        createReqVO.setClassEndTime(classCourseDO.getEndTime());
        // 校验发布课程列表的班级、教师、教室等
        validateFields(createReqVO);
        // 发布信息插入DO
        ElectiveReleaseDO electiveRelease = ElectiveReleaseConvert.INSTANCE.convert(createReqVO);
        electiveReleaseMapper.insert(electiveRelease);

        // 构造班级范围DO集合插入
        List<ElectiveReleaseClassesDO> electiveReleaseClassesDOList = classCourseDOList
                .stream()
                .map(item -> ElectiveReleaseClassesDO.builder()
                        .releaseId(electiveRelease.getId())
                        .classId(item.getClassId())
                        .classCourseId(item.getId()).build())
                .collect(Collectors.toList());
        electiveReleaseClassesMapper.insertBatch(electiveReleaseClassesDOList);
        // 发布课程信息DO集合插入
        List<ElectiveReleaseCoursesDO> electiveReleaseCoursesDOList = convertToElectiveReleaseCoursesDOList(createReqVO.getCoursesList());

        electiveReleaseCoursesDOList.forEach(electiveReleaseCoursesDO -> electiveReleaseCoursesDO.setReleaseId(electiveRelease.getId()));
        if (!electiveReleaseCoursesDOList.isEmpty()) {
            electiveReleaseCoursesMapper.insertBatch(electiveReleaseCoursesDOList);
        }

        // 选修课发布信息对应的排课id 存入授课课程和授课教师信息至 edu_class_course_teacher
        List<ClassCourseTeacherDO> classCourseTeacherDOList = convertToClassCourseTeacherDOList(createReqVO.getCoursesList(),
                                                                classCourseDOList);

        if (!classCourseTeacherDOList.isEmpty()) {
            classCourseTeacherMapper.insertBatch(classCourseTeacherDOList);
        }
        // 返回
        return electiveRelease.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteElectiveRelease(Long id) {
        // 校验存在
        ElectiveReleaseDO electiveReleaseDO = this.validateElectiveReleaseExists(id);
        // 当前时间大于发布上课时间，不可删除
        if (LocalDateTime.now().isAfter(electiveReleaseDO.getClassStartTime())) {
            throw exception(ELECTIVE_RELEASE_DELETE_TIME_ERROR);
        }
        // 删除课程发布信息
        electiveReleaseMapper.deleteById(id);
        // 删除关联班级信息
        electiveReleaseClassesMapper.deleteByReleaseId(id);
        // 删除关联课程信息
        electiveReleaseCoursesMapper.deleteByReleaseId(id);
        // 删除学员选课信息
        electiveTraineeSelectionMapper.deleteByReleaseId(id);
    }

    /**
     * 批量删除选修课发布信息（支持勾选和条件）
     *
     * @param reqVO 批量删除选修课发布信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteElectiveRelease(ElectiveReleasePageReqVO reqVO) {
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        // 删除时查询无需返回选课人数信息
        reqVO.setReturnSelectionNumInfo(false);
        List<ElectiveReleasePageRespVO> respVOList = getElectiveReleasePage(reqVO).getList();
        if (respVOList.isEmpty()) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        // 当前时间大于发布上课时间，不可删除
        if (respVOList.stream().anyMatch(item -> now.isAfter(item.getClassStartTime()))){
            throw exception(ELECTIVE_RELEASE_DELETE_TIME_ERROR);
        }
        List<Long> releaseIdList = respVOList.stream().map(ElectiveReleasePageRespVO::getId).collect(Collectors.toList());
        // 删除选修课发布信息
        electiveReleaseMapper.deleteBatchIds(releaseIdList);
        // 删除关联班级信息
        electiveReleaseClassesMapper.deleteBatchByReleaseId(releaseIdList);
        // 删除关联课程信息
        electiveReleaseCoursesMapper.deleteBatchByReleaseId(releaseIdList);
        // 删除学员选课信息
        electiveTraineeSelectionMapper.deleteBatchByReleaseId(releaseIdList);
    }

    @Override
    public ElectiveReleaseGetRespVO getElectiveRelease(Long id) {
        ElectiveReleaseDO electiveReleaseDO = this.validateElectiveReleaseExists(id);
        ElectiveReleaseGetRespVO respVO = ElectiveReleaseConvert.INSTANCE.convert02(electiveReleaseDO);
        // 获取上课时间段的hh:mm格式字符串
        respVO.setClassStartTimeStr(DateUtils.format(electiveReleaseDO.getClassStartTime(), FORMAT_HOUR_MINUTE));
        respVO.setClassEndTimeStr(DateUtils.format(electiveReleaseDO.getClassEndTime(), FORMAT_HOUR_MINUTE));

        // 获取发布信息班级列表
        List<ClassInfoVO> classeInfoVOList = electiveReleaseClassesMapper.selectClassInfoListByReleaseId(id);
        respVO.setClassInfoList(classeInfoVOList);

        // 获取发布课程信息列表
        List<ElectiveReleaseCoursesSubRespVO> coursesSubBaseVOList = electiveReleaseCoursesMapper
                .selectReleaseCoursesListByReleaseIdList(Collections.singletonList(id));
        respVO.setCoursesList(coursesSubBaseVOList);
        return respVO;
    }

    @Override
    public PageResult<ElectiveReleasePageRespVO> getElectiveReleasePage(ElectiveReleasePageReqVO pageReqVO) {
        // 班主任设置班级查看范围
        if (Boolean.TRUE.equals(pageReqVO.getIsClassMaster())) {
            pageReqVO.setClassIdList(classManagementService.getLoginUserClassMasterLimitClassList());
        }
        IPage<ElectiveReleaseDO> page = MyBatisUtils.buildPage(pageReqVO);
        List<ElectiveReleaseDO> pageResultList = electiveReleaseMapper.selectPageByReqVO(page, pageReqVO);
        List<ElectiveReleasePageRespVO> respVOList = ElectiveReleaseConvert.INSTANCE
                .convertList04(packageElectiveReleasePageRespVOList(pageResultList));
        // 根据参数来判断是否需要人数信息
        if (Objects.nonNull(pageReqVO.getReturnSelectionNumInfo()) && Boolean.TRUE.equals(pageReqVO.getReturnSelectionNumInfo())) {
            List<Integer> statusList = Arrays.asList(TraineeStatusEnum.GRADUATED.getStatus(), TraineeStatusEnum.REPORTED.getStatus());
            // 获取发布信息班级范围内的的应选(已报到、已结业)人数、已选人数信息
            List<ReleaseAndClassSelectionInfoDTO> selectionNumInfoList = electiveReleaseMapper.getSelectionNumInfo(null, pageReqVO.getClassIdList(),
                    statusList);
            List<ReleaseAndClassSelectedInfoDTO> selectedNumInfoList = electiveReleaseMapper.getSelectedNumInfo(null, pageReqVO.getClassIdList());
            // 获取每个发布的每个班级的总应选人数、已选人数
            Map<Long, Integer> releaseIdTotalSelectionNumMap = selectionNumInfoList.stream().collect(Collectors.groupingBy(ReleaseAndClassSelectionInfoDTO::getReleaseId,
                    Collectors.summingInt(ReleaseAndClassSelectionInfoDTO::getSelectionNum)));
            Map<Long, Integer> releaseIdTotalSelectedNumMap = selectedNumInfoList.stream().collect(Collectors.groupingBy(ReleaseAndClassSelectedInfoDTO::getReleaseId,
                    Collectors.summingInt(ReleaseAndClassSelectedInfoDTO::getSelectedNum)));
            respVOList.forEach(releasePageRespVO -> {
                releasePageRespVO.setSelectionNum(releaseIdTotalSelectionNumMap.getOrDefault(releasePageRespVO.getId(), 0));
                releasePageRespVO.setSelectedNum(releaseIdTotalSelectedNumMap.getOrDefault(releasePageRespVO.getId(), 0));
            });
        }
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(pageReqVO.getIsSerialDesc(),
                page.getTotal(),
                pageReqVO,
                respVOList.size());
        for (int i = 0; i < respVOList.size(); i++) {
            respVOList.get(i).setSerialNumber(serialNumberList.get(i));
        }
        return new PageResult<>(respVOList, page.getTotal());
    }

    /**
     * 导出选修课发布信息 Excel
     *
     * @param reqVO 同分页查询参数
     * @return 选修课发布信息 Excel列表信息
     */
    @Override
    public List<ElectiveReleaseExcelVO> getExportElectiveReleaseExcel(ElectiveReleasePageReqVO reqVO) {
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        // 返回选课人数信息
        reqVO.setReturnSelectionNumInfo(true);
        List<ElectiveReleasePageRespVO> respVOList = getElectiveReleasePage(reqVO).getList();
        return ElectiveReleaseConvert.INSTANCE.convertList(respVOList);
    }

    /**
     * 选修课发布已选人数具体信息分页列表
     *
     * @param pageVO 分页查询参数
     * @return 选修课发布已选人数具体信息分页列表
     */
    @Override
    public PageResult<ElectiveReleaseSelectedInfoRespVO> getSelectedInfoPage(ElectiveReleaseSelectedInfoPageReqVO pageVO) {
        // 设置班主任班级查看范围
        if (Boolean.TRUE.equals(pageVO.getIsClassMaster())) {
            pageVO.setClassIdList(classManagementService.getLoginUserClassMasterLimitClassList());
        }
        IPage<ElectiveReleaseSelectedInfoRespVO> page = MyBatisUtils.buildPage(pageVO);
        List<ElectiveReleaseSelectedInfoRespVO> pageResultList = electiveReleaseMapper.getSelectedInfoPageByReqVO(page, pageVO);
        pageResultList.forEach(result -> {
            String rawPosition = result.getPosition();
            if (rawPosition != null) {
                String desensitizedPosition = DesensitizeUtils.sliderDesensitize(rawPosition, 1, 0);
                String encryptedPosition = desensitizeEncrypt(desensitizedPosition);
                result.setPosition(encryptedPosition);
            }
        });
        return new PageResult<>(pageResultList, page.getTotal());
    }

    /**
     * 选修课发布已选人数具体信息列表 excel信息
     *
     * @param reqVO 查询参数
     * @return 选修课发布已选人数具体信息列表 excel信息
     */
    @Override
    public List<ElectiveReleaseSelectedInfoExcelVO> getExportSelectedInfoList(ElectiveReleaseSelectedInfoPageReqVO reqVO) {
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        List<ElectiveReleaseSelectedInfoRespVO> list = getSelectedInfoPage(reqVO).getList();
        return ElectiveReleaseConvert.INSTANCE.convertList02(list);
    }

    /**
     * 查看选修课发布信息里面的选修课程信息分页
     *
     * @param reqVO 分页查询参数
     * @return 选修课发布信息里面的选修课程信息分页
     */
    @Override
    public PageResult<ElectiveReleaseCoursesRespVO> getReleaseCoursePage(ElectiveReleaseCoursesReqVO reqVO) {
        // 设置班主任班级查看范围
        if (Boolean.TRUE.equals(reqVO.getIsClassMaster())) {
            reqVO.setClassIdList(classManagementService.getLoginUserClassMasterLimitClassList());
        }
        IPage<ElectiveReleaseCoursesRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<ElectiveReleaseCoursesRespVO> pageResultList = electiveReleaseMapper.getReleaseCoursePage(page, reqVO);
        // 获取课程信息已选人数
        List<ReleaseCourseSelectedInfoDTO> courseSelectedInfoList = electiveReleaseCoursesMapper.getReleaseCourseSelectedNumInfo(reqVO.getReleaseId(),
                reqVO.getClassIdList());
        // 发布课程id -> 选课人数
        Map<Long, Long> selectedNumMap = courseSelectedInfoList.stream()
                .collect(Collectors.toMap(ReleaseCourseSelectedInfoDTO::getReleaseCourseId, ReleaseCourseSelectedInfoDTO::getSelectedNum));
        pageResultList.forEach(releaseCourseRespVO -> releaseCourseRespVO.setSelectedNum(
                selectedNumMap.getOrDefault(releaseCourseRespVO.getId(), 0L)));
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(reqVO.getIsSerialDesc(),
                page.getTotal(),
                reqVO,
                pageResultList.size());
        for (int i = 0; i < pageResultList.size(); i++) {
            pageResultList.get(i).setSerialNumber(serialNumberList.get(i));
        }
        return new PageResult<>(pageResultList, page.getTotal());
    }

    /**
     * 导出选修课发布信息里面的选修课程信息分页
     *
     * @param reqVO    分页查询参数
     * @param response 请求响应
     */
    @Override
    public void exportReleaseCourseExcel(ElectiveReleaseCoursesReqVO reqVO, HttpServletResponse response) throws IOException {
        // 根据发布信息id 查询发布信息
        ElectiveReleaseDO electiveReleaseDO = this.validateElectiveReleaseExists(reqVO.getReleaseId());
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        List<ElectiveReleaseCoursesRespVO> list = getReleaseCoursePage(reqVO).getList();
        List<ElectiveReleaseCoursesExcelVO> excelList = ElectiveReleaseCoursesConvert.INSTANCE.convertList02(list);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, electiveReleaseDO.getName() + ".xls",
                "数据", ElectiveReleaseCoursesExcelVO.class, excelList, reqVO.getIncludeColumnIndexes());
    }

    /**
     * 获得选修课发布信息列表（包含选课人数信息）
     *
     * @param classId 班级ID
     * @return 选修课发布信息列表（包含选课人数信息）
     */
    @Override
    public List<ElectiveReleasePageRespVO> getElectiveReleaseListByClassId(Long classId) {
        List<ElectiveReleaseDO> list = electiveReleaseMapper.getElectiveReleaseListByClassId(classId);
        List<ElectiveReleasePageRespVO> respVOList = ElectiveReleaseConvert.INSTANCE
                .convertList04(packageElectiveReleasePageRespVOList(list));
        List<Integer> statusList = Arrays.asList(TraineeStatusEnum.GRADUATED.getStatus(), TraineeStatusEnum.REPORTED.getStatus());
        // 获取发布信息班级范围内的的应选(已报到、已结业)人数、已选人数信息
        List<Long> classIdList = Collections.singletonList(classId);
        List<ReleaseAndClassSelectionInfoDTO> selectionNumInfoList = electiveReleaseMapper.getSelectionNumInfo(null,
                classIdList, statusList);
        List<ReleaseAndClassSelectedInfoDTO> selectedNumInfoList = electiveReleaseMapper.getSelectedNumInfo(null, classIdList);
        // 获取每个发布的每个班级的总应选人数、已选人数
        Map<Long, Integer> releaseIdTotalSelectionNumMap = selectionNumInfoList.stream().collect(Collectors.groupingBy(ReleaseAndClassSelectionInfoDTO::getReleaseId,
                Collectors.summingInt(ReleaseAndClassSelectionInfoDTO::getSelectionNum)));
        Map<Long, Integer> releaseIdTotalSelectedNumMap = selectedNumInfoList.stream().collect(Collectors.groupingBy(ReleaseAndClassSelectedInfoDTO::getReleaseId,
                Collectors.summingInt(ReleaseAndClassSelectedInfoDTO::getSelectedNum)));
        respVOList.forEach(releaseRespVO -> {
            releaseRespVO.setSelectionNum(releaseIdTotalSelectionNumMap.getOrDefault(releaseRespVO.getId(), 0));
            releaseRespVO.setSelectedNum(releaseIdTotalSelectedNumMap.getOrDefault(releaseRespVO.getId(), 0));
        });
        return respVOList;
    }

    /**
     * 根据学员ID获得选修课发布信息列表（每个选修课发布信息包含课程信息）
     *
     * @param status 选修课发布状态 0-待选的 1-已选的
     * @return 选修课发布信息列表
     */
    @Override
    public List<AppElectiveReleaseTraineeRespVO> getElectiveReleaseAndCoursesListByTraineeIdAndStatus(Integer status) {
        if (Objects.isNull(TraineeReleaseCoursesStatusEnum.getDescByStatus(status))) {
            throw exception(ELECTIVE_TRAINEE_RELEASE_COURSES_STATUS);
        }
        // 判断登录用户是否在学员表中存在
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 获取学员
        TraineeDO traineeDO = traineeMapper.selectByUserId(userId);
        if (Objects.isNull(traineeDO)) {
            throw exception(ELECTIVE_TRAINEE_USER_NOT_EXISTS);
        }

        List<AppElectiveReleaseTraineeRespVO> respVOList = new ArrayList<>();
        if (TraineeReleaseCoursesStatusEnum.UNSELECT.getType().equals(status)) {
            // 获得学员待选的选修课信息列表
            respVOList = getUnselectElectiveReleaseAndCoursesListByTraineeId(traineeDO.getId());
        } else if (TraineeReleaseCoursesStatusEnum.SELECTED.getType().equals(status)) {
            respVOList = getSelectElectiveReleaseAndCoursesListByTraineeId(traineeDO.getId());
        }
        return respVOList;
    }

    /**
     * 获得选修课发布的班级下拉信息
     *
     * @param releaseId 发布ID
     * @return 班级下拉信息
     */
    @Override
    public List<ClassInfoVO> getSimpleClassesInfoList(Long releaseId) {
        return electiveReleaseMapper.getSimpleClassesInfoList(releaseId);
    }

    /**
     * 获得选修课发布的课程下拉信息
     *
     * @param releaseId 发布ID
     * @return 课程下拉信息
     */
    @Override
    public List<CourseInfoVO> getSimpleCoursesInfoList(Long releaseId) {
        return electiveReleaseMapper.getSimpleCoursesInfoList(releaseId);
    }

    /**
     * 获得学员已选的选修课信息列表
     *
     * @param traineeId 学员ID
     * @return 选修课信息列表
     */
    private List<AppElectiveReleaseTraineeRespVO> getSelectElectiveReleaseAndCoursesListByTraineeId(Long traineeId) {
        List<ElectiveTraineeSelectedCoursesAndReleaseDTO> dtoList = electiveTraineeSelectionMapper.getSelectedCoursesListByTraineeId(traineeId);
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        return convertToAppElectiveReleaseTraineeRespVOList(dtoList);
    }

    private List<AppElectiveReleaseTraineeRespVO> convertToAppElectiveReleaseTraineeRespVOList(List<ElectiveTraineeSelectedCoursesAndReleaseDTO> dtoList) {
        return dtoList.stream().map(dto -> {
            AppElectiveReleaseTraineeRespVO respVO = new AppElectiveReleaseTraineeRespVO();
            respVO.setId(dto.getReleaseId());
            respVO.setName(dto.getReleaseName());
            String selectionDuration = DateUtils.format(dto.getSelectionStartTime()) +
                    " 至 " + DateUtils.format(dto.getSelectionEndTime());
            respVO.setSelectionDuration(selectionDuration);
            respVO.setClassDate(dto.getClassDate());
            respVO.setDayPeriod(dto.getDayPeriod());
            // 上课时段(上午 09:00 - 18:00)
            respVO.setClassDuration(
                    ClassDayPeriodEnum.getDescByPeriod(dto.getDayPeriod()) +
                            " " + DateUtils.format(dto.getClassStartTime(), FORMAT_HOUR_MINUTE) +
                            " - " + DateUtils.format(dto.getClassEndTime(), FORMAT_HOUR_MINUTE));
            respVO.setCreateTime(dto.getReleaseCreateTime());
            respVO.setCoursesList(Collections.singletonList(ElectiveReleaseConvert.INSTANCE.convert03(dto)));
            return respVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取学员待选的选修课信息列表
     *
     * @param traineeId 学员ID
     * @return 选修课信息列表
     */
    private List<AppElectiveReleaseTraineeRespVO> getUnselectElectiveReleaseAndCoursesListByTraineeId(Long traineeId) {
        List<ElectiveReleaseDO> releaseDOList = electiveReleaseMapper.getUnselectElectiveReleaseAndCoursesListByTraineeId(traineeId,
                LocalDateTime.now());
        List<AppElectiveReleaseTraineeRespVO> respVOList = ElectiveReleaseConvert.INSTANCE
                .convertList05(packageElectiveReleasePageRespVOList(releaseDOList));
        if (CollectionUtils.isEmpty(releaseDOList)) {
            return respVOList;
        }
        // 获取发布课程信息列表
        List<Long> releaseIdList = releaseDOList.stream().map(ElectiveReleaseDO::getId).collect(Collectors.toList());
        List<ElectiveReleaseCoursesSubRespVO> coursesSubRespVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(releaseIdList)) {
            coursesSubRespVOList = electiveReleaseCoursesMapper
                    .selectReleaseCoursesListByReleaseIdList(releaseIdList);
        }
        // 选修课发布id -> 发布课程列表
        Map<Long, List<ElectiveReleaseCoursesSubRespVO>> releaseIdCoursesMap = coursesSubRespVOList.stream()
                .collect(Collectors.groupingBy(ElectiveReleaseCoursesSubRespVO::getReleaseId));
        // 给每个发布信息设置课程列表
        respVOList.forEach(releaseRespVO -> releaseRespVO.setCoursesList(
                releaseIdCoursesMap.getOrDefault(releaseRespVO.getId(), Collections.emptyList())));
        return respVOList;
    }

    /**
     * 条件查询发布信息列表DO 转为 ElectiveReleasePageSimpleRespVO
     *
     * @param releaseDOList DO列表
     * @return ElectiveReleasePageSimpleRespVO 列表
     */
    private List<ElectiveReleasePageSimpleRespVO> packageElectiveReleasePageRespVOList(List<ElectiveReleaseDO> releaseDOList) {
        // 组装ElectiveReleasePageRespVO并设置选学时间段、上课时段
        return releaseDOList.stream()
                .map(this::packageElectiveReleasePageRespVO)
                .collect(Collectors.toList());
    }


    /**
     * 条件查询发布信息列表DO 转为 ElectiveReleasePageSimpleRespVO
     *
     * @param electiveReleaseDO DO
     * @return ElectiveReleasePageSimpleRespVO
     */
    private ElectiveReleasePageSimpleRespVO packageElectiveReleasePageRespVO(ElectiveReleaseDO electiveReleaseDO) {
        ElectiveReleasePageSimpleRespVO electiveReleasePageRespVO = new ElectiveReleasePageSimpleRespVO();
        electiveReleasePageRespVO.setId(electiveReleaseDO.getId());
        electiveReleasePageRespVO.setName(electiveReleaseDO.getName());
        String selectionDuration = DateUtils.format(electiveReleaseDO.getSelectionStartTime()) +
                " 至 " + DateUtils.format(electiveReleaseDO.getSelectionEndTime());
        electiveReleasePageRespVO.setSelectionDuration(selectionDuration);
        electiveReleasePageRespVO.setClassDate(electiveReleaseDO.getClassDate());
        electiveReleasePageRespVO.setDayPeriod(electiveReleaseDO.getDayPeriod());
        // 上课时段(上午 09:00 - 18:00)
        electiveReleasePageRespVO.setClassDuration(
                ClassDayPeriodEnum.getDescByPeriod(electiveReleaseDO.getDayPeriod()) +
                        " " + DateUtils.format(electiveReleaseDO.getClassStartTime(), FORMAT_HOUR_MINUTE) +
                        " - " + DateUtils.format(electiveReleaseDO.getClassEndTime(), FORMAT_HOUR_MINUTE));
        electiveReleasePageRespVO.setClassStartTime(electiveReleaseDO.getClassStartTime());
        electiveReleasePageRespVO.setClassEndTime(electiveReleaseDO.getClassEndTime());
        electiveReleasePageRespVO.setCreateTime(electiveReleaseDO.getCreateTime());
        return electiveReleasePageRespVO;
    }


    /**
     * 校验发布课程的教师、教师是否和以前的发布课程时间冲突
     *
     * @param reqVO 新增和修改时前端传参
     */
    private void validateFields(ElectiveReleaseCreateReqVO reqVO) {
        // 校验名称
        if (StringUtils.isBlank(reqVO.getName()) || reqVO.getName().trim().length() > 50) {
            throw exception(ELECTIVE_RELEASE_NAME_LENGTH_ERROR);
        }
        reqVO.setName(reqVO.getName().trim());

        // ==开始上课时间不能早于选学结束时间==
        if (reqVO.getClassStartTime().isBefore(reqVO.getSelectionEndTime())) {
            throw exception(ELECTIVE_RELEASE_SELECTION_END_TIME_ERROR);
        }
        // ==发布课程列表需必填==
        if (Objects.isNull(reqVO.getCoursesList()) || reqVO.getCoursesList().isEmpty()) {
            throw exception(ELECTIVE_RELEASE_COURSES_IS_EMPTY);
        }
        // ==该次发布课程中是否有重复的教室使用==
        List<Long> classroomIdList = reqVO.getCoursesList()
                .stream()
                .map(ElectiveReleaseCoursesSubBaseVO::getClassroomId)
                .collect(Collectors.toList());
        if (hasDuplicate(classroomIdList)) {
            throw exception(ELECTIVE_RELEASE_CLASSROOM_DUPLICATE);
        }
        // ==该次发布课程中是否有重复的教师授课==
        List<Long> teacherIdList = reqVO.getCoursesList()
                .stream()
                .map(ElectiveReleaseCoursesSubBaseVO::getTeacherId)
                .collect(Collectors.toList());
        if (hasDuplicate(teacherIdList)) {
            throw exception(ELECTIVE_RELEASE_TEACHER_DUPLICATE);
        }

        // ==查询当前上课时间段内，教师、教室是否被其他已发布的选修课程占用导致冲突==
        List<ElectiveReleaseCoursesDO> electiveReleaseCoursesDOList = electiveReleaseCoursesMapper.selectListByClassTimePeriod(reqVO.getClassStartTime(),
                reqVO.getClassEndTime(), null);
        // 获取当前上课时间段内已经被选修课程使用的教室, 教师列表
        Set<Long> usedTeacherIdSet = electiveReleaseCoursesDOList.stream()
                .map(ElectiveReleaseCoursesDO::getTeacherId)
                .collect(Collectors.toSet());
        Set<Long> teacherIdSet = new HashSet<>(teacherIdList);
        // set求交集
        if (!intersectionSet(teacherIdSet, usedTeacherIdSet).isEmpty()) {
            throw exception(ELECTIVE_RELEASE_TEACHER_CLASS_TIME_CONFLICT);
        }
        Set<Long> usedClassroomIdSet = electiveReleaseCoursesDOList.stream()
                .map(ElectiveReleaseCoursesDO::getClassroomId)
                .collect(Collectors.toSet());
        Set<Long> classroomIdSet = new HashSet<>(classroomIdList);
        if (!intersectionSet(classroomIdSet, usedClassroomIdSet).isEmpty()) {
            throw exception(ELECTIVE_RELEASE_CLASSROOM_CLASS_TIME_CONFLICT);
        }

        // 与日程安排里面里面已发布的专题课、教学活动课程的教师、教室校验冲突
        List<ClassCourseDO> classCourseDOList = classCourseMapper.getCoursesListByClassTimeAndCourseTypelist(
                Arrays.asList(CoursesTypeEnum.TOPIC_COURSE.getType(),
                        CoursesTypeEnum.TEACHING_ACTIVITY.getType()), reqVO.getClassStartTime(), reqVO.getClassEndTime());
        // 获取当前上课时间段内已经被专题课、教学活动课使用的教室, 教师列表
        usedTeacherIdSet = classCourseDOList.stream()
                .map(ClassCourseDO::getTeacherId)
                .collect(Collectors.toSet());
        if (!intersectionSet(teacherIdSet, usedTeacherIdSet).isEmpty()) {
            throw exception(ELECTIVE_RELEASE_TEACHER_CLASS_TIME_CONFLICT);
        }

        usedClassroomIdSet = classCourseDOList.stream()
                .map(ClassCourseDO::getClassroomId)
                .collect(Collectors.toSet());
        if (!intersectionSet(classroomIdSet, usedClassroomIdSet).isEmpty()) {
            throw exception(ELECTIVE_RELEASE_CLASSROOM_CLASS_TIME_CONFLICT);
        }
    }

    /**
     * 取交集（取两个集合中都存在的元素）
     *
     * @return Set<T> 交集集合
     */
    public <T> Set<T> intersectionSet(Set<T> setA, Set<T> setB) {
        Set<T> resSet = new HashSet<>(setA);
        resSet.retainAll(setB);
        return resSet;
    }


    /**
     * 判断集合中是否有重复元素
     *
     * @param list 待判断的集合
     * @return boolean true:有重复元素，false：没有重复元素
     */
    private boolean hasDuplicate(List<?> list) {
        return list.stream().distinct().count() != list.size();
    }

    /**
     * 解析 classStartTime 和 classEndTime 传的 "HH:mm" 和 classDate
     * 生成  LocalDateTime "yyyy-MM-dd HH:mm:ss" 格式的上课开始时间和上课结束时间
     *
     * @param reqVO 课程发布信息
     */
    private void setClassDateTime(ElectiveReleaseCreateReqVO reqVO) {
        LocalDateTime classStartTime = LocalDateTime.of(reqVO.getClassDate(), LocalTime.parse(reqVO.getClassStartTimeStr()));
        LocalDateTime classEndTime = LocalDateTime.of(reqVO.getClassDate(), LocalTime.parse(reqVO.getClassEndTimeStr()));
        reqVO.setClassStartTime(classStartTime);
        reqVO.setClassEndTime(classEndTime);
    }

    private ElectiveReleaseDO validateElectiveReleaseExists(Long id) {
        ElectiveReleaseDO electiveReleaseDO = electiveReleaseMapper.selectById(id);
        if (Objects.isNull(electiveReleaseDO)) {
            throw exception(ELECTIVE_RELEASE_NOT_EXISTS);
        }
        return electiveReleaseDO;
    }

    private List<ClassCourseTeacherDO> convertToClassCourseTeacherDOList(List<ElectiveReleaseCoursesSubBaseVO> coursesList, List<ClassCourseDO> classCourseDOList) {
        if (coursesList == null || classCourseDOList == null) {
            return Collections.emptyList();
        }
        List<ClassCourseTeacherDO> classCourseTeacherDOList = new ArrayList<>();

        for (ClassCourseDO classCourseDO : classCourseDOList) {
            for (ElectiveReleaseCoursesSubBaseVO electiveReleaseCoursesSubBaseVO : coursesList) {
                ClassCourseTeacherDO classCourseTeacherDO = new ClassCourseTeacherDO();
                classCourseTeacherDO.setClassCourseId(classCourseDO.getId());
                classCourseTeacherDO.setTeacherId(electiveReleaseCoursesSubBaseVO.getTeacherId());
                classCourseTeacherDO.setCourseId(electiveReleaseCoursesSubBaseVO.getCourseId());
                classCourseTeacherDO.setSort(0L);
                classCourseTeacherDOList.add(classCourseTeacherDO);
            }
        }
        return classCourseTeacherDOList;
    }


    public List<ElectiveReleaseCoursesDO> convertToElectiveReleaseCoursesDOList(List<ElectiveReleaseCoursesSubBaseVO> coursesList) {
        if (coursesList == null) {
            return Collections.emptyList();
        }

        List<ElectiveReleaseCoursesDO> list = new ArrayList<>(coursesList.size());
        for (ElectiveReleaseCoursesSubBaseVO electiveReleaseCoursesSubBaseVO : coursesList) {
            list.add(electiveReleaseCoursesSubBaseVOToElectiveReleaseCoursesDO(electiveReleaseCoursesSubBaseVO));
        }

        return list;
    }

    private ElectiveReleaseCoursesDO electiveReleaseCoursesSubBaseVOToElectiveReleaseCoursesDO(ElectiveReleaseCoursesSubBaseVO electiveReleaseCoursesSubBaseVO) {
        if (electiveReleaseCoursesSubBaseVO == null) {
            return null;
        }

        ElectiveReleaseCoursesDO electiveReleaseCoursesDO = new ElectiveReleaseCoursesDO();

        electiveReleaseCoursesDO.setCourseId(electiveReleaseCoursesSubBaseVO.getCourseId());
        electiveReleaseCoursesDO.setTeacherId(electiveReleaseCoursesSubBaseVO.getTeacherId());
        electiveReleaseCoursesDO.setClassroomId(electiveReleaseCoursesSubBaseVO.getClassroomId());
        electiveReleaseCoursesDO.setCreateTime(electiveReleaseCoursesSubBaseVO.getCreateTime());

        return electiveReleaseCoursesDO;
    }
}
