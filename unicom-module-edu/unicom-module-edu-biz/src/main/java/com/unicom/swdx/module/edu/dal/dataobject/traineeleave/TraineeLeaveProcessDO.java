package com.unicom.swdx.module.edu.dal.dataobject.traineeleave;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;
import com.unicom.swdx.framework.mybatis.core.type.StringListTypeHandler;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@TableName(value = "edu_trainee_leave_process",autoResultMap = true)
@KeySequence("edu_trainee_leave_process_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraineeLeaveProcessDO extends TenantBaseDO {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    // 请假id
    @JsonSerialize(using = ToStringSerializer.class)
    private Long leaveId;

    // 节点用户id
    private Long userId;

    // 节点用户姓名
    private String userName;

    // 审批节点名称
    private String taskName;

    // 审批节点状态，包括：0发起-1已撤回-2未审批-4已通过-5已拒绝
    private Integer taskStatus;

    // 审批意见，班主任同意时不填意见，拒绝必须填意见，转办人同意/不同意都可填可不填意见
    private String taskComment;

    // 节点用户类型，包括：学员-班主任-分管领导
    private String userType;

    // 节点操作时间
    private LocalDateTime operateTime;

    // 修改原因
    private String modifyReason;

}
