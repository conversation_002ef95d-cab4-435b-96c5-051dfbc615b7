package com.unicom.swdx.module.edu.dal.dataobject.teacherinformation;

import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.common.enums.GenderEnum;
import com.unicom.swdx.framework.common.enums.NationEnum;
import com.unicom.swdx.framework.common.enums.PoliticalOutlookEnum;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import com.unicom.swdx.module.system.api.user.dto.UserInputDTO;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 师资信息 DO
 *
 * <AUTHOR>
 */
@TableName("edu_teacher_information")
@KeySequence("edu_teacher_information_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeacherInformationDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 师资来源，0校内，1校外
     */
    private Integer source;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idNumber;


    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 性别
     * 枚举 {@link GenderEnum}
     */
    private Integer gender;

    /**
     * 民族
     * 枚举 {@link NationEnum}
     */
    private Integer nation;

    /**
     * 政治面貌
     * 枚举 {@link PoliticalOutlookEnum}
     */
    private Integer politicalStatus;

    /**
     * 来校年月
     */
    private LocalDate arrivalTime;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 联系方式
     */
    private String contactInformation;

    /**
     * 通讯地址
     */
    private String mailingAddress;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 所在单位
     */
    private String workUnit;

    /**
     * 学历 字典 person_education
     */
    private Integer education;

    /**
     * 学位 person_academic_degree  字典ID
     */
    private Integer degree;

    /**
     * 职称
     */
    private String professionalTitle;

    /**
     * 职级 person_rank
     */
    private String rank;

    /**
     * 行政级别 person_administrative_position_rank
     */
    private String administrativeLevel;

    /**
     * 所属部门id
     */
    private String deptIds;

    /**
     * 所属部门名称
     */
    private String deptNames;

    /**
     * 备注
     */
    private String remark;

    private String otherSystemId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 证件号码(des)加密
     */
    private String idNumberDes;


    /**
     * 业中人事id
     */
    private Long hrId;


    private Long systemId;




}
