package com.unicom.swdx.module.edu.controller.admin.courses;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.*;
import com.unicom.swdx.module.edu.convert.courses.CoursesConvert;
import com.unicom.swdx.module.edu.enums.courses.CoursesTypeEnum;
import com.unicom.swdx.module.edu.service.courses.CoursesService;
import com.unicom.swdx.module.edu.utils.excel.ExcelImportResultRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Api(tags = "管理后台 - 课程库")
@RestController
@RequestMapping("/edu/courses")
@Validated
public class CoursesController {

    @Resource
    private CoursesService coursesService;

    @PostMapping("/create")
    @ApiOperation("创建课程库")
    @PreAuthorize("@ss.hasPermission('edu:courses:create')")
    public CommonResult<Long> createCourses(@Valid @RequestBody CoursesCreateReqVO createReqVO) {
        return success(coursesService.createCourses(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新课程库")
    @PreAuthorize("@ss.hasPermission('edu:courses:update')")
    public CommonResult<Boolean> updateCourses(@Valid @RequestBody CoursesUpdateReqVO updateReqVO) {
        coursesService.updateCourses(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除课程库")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:courses:delete')")
    public CommonResult<Boolean> deleteCourses(@RequestParam("id") Long id) {
        coursesService.deleteCourses(id);
        return success(true);
    }

    @PostMapping("/batchDelete")
    @ApiOperation("批量删除课程库（多选和条件）")
    @PreAuthorize("@ss.hasPermission('edu:courses:delete')")
    public CommonResult<Boolean> batchDeleteCourses(@Valid @RequestBody CoursesPageReqVO pageReqVO) {
        coursesService.batchDeleteCourses(pageReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得课程库")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:courses:query')")
    public CommonResult<CoursesRespVO> getCourses(@RequestParam("id") Long id) {
        CoursesRespVO courses = coursesService.getCourses(id);
        return success(courses);
    }

    @PostMapping("/list")
    @ApiOperation("获得课程库列表")
    @PreAuthorize("@ss.hasPermission('edu:courses:query')")
    public CommonResult<List<CoursesRespVO>> getCoursesList(HttpServletRequest request, @Valid @RequestBody CoursesPageReqVO reqVO) {
        List<CoursesRespVO> list = coursesService.getCoursesListByReqVO(request, reqVO);
        return success(list);
    }

    @PostMapping("/page")
    @ApiOperation("获得课程库分页")
    @PreAuthorize("@ss.hasPermission('edu:courses:query')")
    public CommonResult<PageResult<CoursesRespVO>> getCoursesPage(HttpServletRequest request, @Valid @RequestBody CoursesPageReqVO pageVO) {
        PageResult<CoursesRespVO> pageResult = coursesService.getCoursesPage(pageVO, request);
        return success(pageResult);
    }

    @PostMapping("/pageForTopicTeachingRecord")
    @ApiOperation("获得一个专题课的授课记录")
    @PreAuthorize("@ss.hasPermission('edu:courses:query')")
    public CommonResult<PageResult<CoursesTeachingRecordRespVO>> getPageForTeachingRecord(@Valid @RequestBody CoursesTeachingRecordReqVO pageVO) {
        PageResult<CoursesTeachingRecordRespVO> pageResult = coursesService.getPageForTeachingRecord(pageVO);
        return success(pageResult);
    }

    @PostMapping("/exportTopicTeachingRecordExcel")
    @ApiOperation("导出一个专题课的授课记录")
    @PreAuthorize("@ss.hasPermission('edu:courses:export')")
    public void exportTopicTeachingRecordExcel(@Valid @RequestBody CoursesTeachingRecordReqVO pageVO, HttpServletResponse response) throws IOException {
        List<CoursesTeachingRecordTopicExcelVO> list = coursesService.getExportTopicTeachingRecordExcel(pageVO);
        // 因为excel第一列是课程名称，单个导出时需要排除 所有includeColumnIndexes全部加一
        Set<Integer> includeColumnIndexes = pageVO.getIncludeColumnIndexes().stream()
                .map(i -> i + 1).collect(Collectors.toSet());
        ExcelUtils.writeByIncludeColumnIndexes(response, "授课记录.xls",
                "数据", CoursesTeachingRecordTopicExcelVO.class, list, includeColumnIndexes);
    }

    @PostMapping("/exportBatchTopicTeachingRecordExcel")
    @ApiOperation("导出勾选下的多个专题课的授课记录")
    @PreAuthorize("@ss.hasPermission('edu:courses:export')")
    public void exportBatchTopicTeachingRecordExcel(@Valid @RequestBody CoursesPageReqVO pageVO, HttpServletResponse response) throws IOException {
        List<CoursesTeachingRecordTopicExcelVO> list = coursesService.getExportBatchTopicTeachingRecordExcel(pageVO);
        ExcelUtils.write(response, "授课记录.xls", "数据", CoursesTeachingRecordTopicExcelVO.class, list);
    }

    @PostMapping("/pageForElectiveTeachingRecord")
    @ApiOperation("获得一个选修课的授课记录")
    @PreAuthorize("@ss.hasPermission('edu:courses:query')")
    public CommonResult<PageResult<CoursesTeachingRecordRespVO>> getPageForElectiveTeachingRecord(@Valid @RequestBody CoursesTeachingRecordReqVO pageVO) {
        PageResult<CoursesTeachingRecordRespVO> pageResult = coursesService.getPageForElectiveTeachingRecord(pageVO);
        return success(pageResult);
    }

    @PostMapping("/exportElectiveTeachingRecordExcel")
    @ApiOperation("导出一个选修课的授课记录")
    @PreAuthorize("@ss.hasPermission('edu:courses:export')")
    public void exportElectiveTeachingRecordExcel(@Valid @RequestBody CoursesTeachingRecordReqVO pageVO, HttpServletResponse response) throws IOException {
        List<CoursesTeachingRecordElectiveExcelVO> list = coursesService.getExportElectiveTeachingRecordExcel(pageVO);
        // 因为excel第一列是课程名称，单个导出时需要排除 所有includeColumnIndexes全部加一
        Set<Integer> includeColumnIndexes = pageVO.getIncludeColumnIndexes().stream()
                .map(i -> i + 1).collect(Collectors.toSet());
        ExcelUtils.writeByIncludeColumnIndexes(response, "授课记录.xls",
                "数据", CoursesTeachingRecordElectiveExcelVO.class, list, includeColumnIndexes);
    }

    @PostMapping("/exportBatchElectiveTeachingRecordExcel")
    @ApiOperation("导出勾选下的多个选修课的授课记录")
    @PreAuthorize("@ss.hasPermission('edu:courses:export')")
    public void exportBatchElectiveTeachingRecordExcel(@Valid @RequestBody CoursesPageReqVO pageVO, HttpServletResponse response) throws IOException {
        List<CoursesTeachingRecordElectiveExcelVO> list = coursesService.getExportBatchElectiveTeachingRecordExcel(pageVO);
        ExcelUtils.write(response, "授课记录.xls", "数据", CoursesTeachingRecordElectiveExcelVO.class, list);
    }

    @PostMapping("/export-excel")
    @ApiOperation("导出专题课、选修课 Excel")
    @PreAuthorize("@ss.hasPermission('edu:courses:export')")
    @OperateLog(type = EXPORT)
    public void exportCoursesExcel(HttpServletRequest request, HttpServletResponse response,
                                   @Valid @RequestBody CoursesPageReqVO pageVO) throws IOException {
        List<CoursesExcelVO> exportCoursesExcel = coursesService.getExportCoursesExcel(pageVO, request);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, CoursesTypeEnum.getDescByType(pageVO.getCoursesType()) + "库.xls",
                "数据", CoursesExcelVO.class, getHeaderList(pageVO.getCoursesType()), exportCoursesExcel, pageVO.getIncludeColumnIndexes());
    }

    @PostMapping("/export-activity-excel")
    @ApiOperation("导出教学活动 Excel")
    @PreAuthorize("@ss.hasPermission('edu:courses:export')")
    @OperateLog(type = EXPORT)
    public void exportActivityExcel(HttpServletRequest request, HttpServletResponse response,
                                    @Valid @RequestBody CoursesPageReqVO pageVO) throws IOException {
        List<CoursesActivityExcelVO> exportCoursesExcel = coursesService.getExportActivityCoursesExcel(pageVO, request);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "教学活动库.xls",
                "数据", CoursesActivityExcelVO.class, exportCoursesExcel, pageVO.getIncludeColumnIndexes());
    }

    @GetMapping("/export-topic-template")
    @ApiOperation("专题库导入模板导出")
    @PreAuthorize("@ss.hasPermission('edu:courses:export')")
    @OperateLog(type = EXPORT)
    public void exportTopicTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        coursesService.exportTopicTemplate(request, response);
    }

    @GetMapping("/export-optional-template")
    @ApiOperation("选修课库导入模板导出")
    @PreAuthorize("@ss.hasPermission('edu:courses:export')")
    @OperateLog(type = EXPORT)
    public void exportOptionalTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        coursesService.exportOptionalTemplate(request, response);
    }

    @GetMapping("/export-activity-template")
    @ApiOperation("教学活动导入模板导出")
    @PreAuthorize("@ss.hasPermission('edu:courses:export')")
    @OperateLog(type = EXPORT)
    public void exportActivityTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        coursesService.exportActivityTemplate(request, response);
    }


    @PostMapping("/import-optional-courses")
    @ApiOperation("导入选修课")
    @PreAuthorize("@ss.hasPermission('edu:courses:import')")
    public CommonResult<ExcelImportResultRespVO> imporOptionalCourses(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws IOException, InterruptedException {
        List<CoursesOptionalImportExcelVO> list = ExcelUtils.readFirstSheetAndCheckHead(file, CoursesOptionalImportExcelVO.class);
        // 导入课程库Excel
        ExcelImportResultRespVO coursesImportResultRespVO = coursesService.importOptionalCourses(request, list);
        return success(coursesImportResultRespVO);
    }

    @PostMapping("/import-topic-courses")
    @ApiOperation("导入专题")
    @PreAuthorize("@ss.hasPermission('edu:courses:import')")
    public CommonResult<ExcelImportResultRespVO> importTopicCourses(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws IOException, InterruptedException {
        List<CoursesTopicImportExcelVO> list = ExcelUtils.readFirstSheetAndCheckHead(file, CoursesTopicImportExcelVO.class);
        // 导入专题库Excel
        ExcelImportResultRespVO coursesImportResultRespVO = coursesService.importTopicCourses(request, list);
        return success(coursesImportResultRespVO);
    }

    @PostMapping("/import-activity-courses")
    @ApiOperation("导入教学活动库")
    @PreAuthorize("@ss.hasPermission('edu:courses:import')")
    public CommonResult<ExcelImportResultRespVO> importActivityCourses(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws IOException, InterruptedException {
        List<CoursesActivityImportExcelVO> list = ExcelUtils.readFirstSheetAndCheckHead(file, CoursesActivityImportExcelVO.class);
        // 导入专题库Excel
        ExcelImportResultRespVO coursesImportResultRespVO = coursesService.importActivityCourses(request, CoursesConvert.INSTANCE.convertList10(list));
        return success(coursesImportResultRespVO);
    }

    private List<List<String>> getHeaderList(Integer coursesType) {
        List<List<String>> headerList = new ArrayList<>();
        List<String> list;
        if (CoursesTypeEnum.TOPIC_COURSE.getType().equals(coursesType)) {
            list = Arrays.asList("专题名称", "授课教师", "专题分类", "教学形式", "教学方式", "管理部门", "专题状态");
        } else {
            list = Arrays.asList("选修课名称", "授课教师", "课程分类", "教学形式", "教学方式", "管理部门", "课程状态");
        }
        list.forEach(data -> headerList.add(Collections.singletonList(data)));
        return headerList;
    }
}
