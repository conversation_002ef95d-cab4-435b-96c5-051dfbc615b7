package com.unicom.swdx.module.edu.service.plan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.planconfig.vo.PlanConfigBaseVO;
import com.unicom.swdx.module.edu.convert.planconfig.PlanConfigConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classroomlibrary.ClassroomLibraryDO;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;
import com.unicom.swdx.module.edu.dal.dataobject.planconfig.PlanConfigDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineeleave.TraineeLeaveDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.classroomlibrary.ClassroomLibraryMapper;
import com.unicom.swdx.module.edu.dal.mysql.clockininfo.ClockInInfoMapper;
import com.unicom.swdx.module.edu.dal.mysql.planconfig.PlanConfigMapper;
import com.unicom.swdx.module.edu.enums.classcourse.PeriodEnum;
import com.unicom.swdx.module.edu.service.classcourse.ClassCourseService;
import com.unicom.swdx.module.edu.service.classroomlibrary.ClassroomLibraryService;
import com.unicom.swdx.module.edu.service.evaluationdetail.EvaluationDetailService;
import com.unicom.swdx.module.edu.service.evaluationresponse.EvaluationResponseService;
import com.unicom.swdx.module.edu.utils.classcourse.ClassCourseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.OverridingMethodsMustInvokeSuper;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import com.unicom.swdx.module.edu.controller.admin.plan.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.plan.PlanDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.convert.plan.PlanConvert;
import com.unicom.swdx.module.edu.dal.mysql.plan.PlanMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;


/**
 * <AUTHOR>
 */
@Service
@Validated
public class PlanServiceImpl extends ServiceImpl<PlanMapper, PlanDO> implements PlanService {

    private static final String DATE_FORMAT = "yyyy-MM-dd";

    @Resource
    private PlanMapper planMapper;

    @Resource
    private ClassCourseMapper classCourseMapper;

    @Resource
    private ClassroomLibraryMapper classroomLibraryMapper;

    @Resource
    private PlanConfigMapper planConfigMapper;

    @Resource
    public ClassCourseUtil classCourseUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPlan(PlanCreateReqVO createReqVO) {
        //插入前判定是否该班级存在冲突教学计划
        checkPlanTimeConflict(createReqVO.getClassId(),createReqVO.getBeginDate(),createReqVO.getEndDate(),null);

//        //检查新增日期范围内该班级是否有游离于教学计划外的课表格子，有则删除
//        deleteIsoLateClassCourse(createReqVO.getBeginDate(),createReqVO.getEndDate(),createReqVO.getClassId());

        //删除日期范围内的课表格子
        LambdaQueryWrapper<ClassCourseDO> classCourseDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        classCourseDOLambdaQueryWrapper.eq(ClassCourseDO::getClassId, createReqVO.getClassId());
        //检查日期在开始和结束时间之内的课表格子
        classCourseDOLambdaQueryWrapper.ge(ClassCourseDO::getDate, createReqVO.getBeginDate());
        classCourseDOLambdaQueryWrapper.le(ClassCourseDO::getDate, createReqVO.getEndDate());
        //找到目标格子
        List<ClassCourseDO> classCourseDOListToDelete = classCourseMapper.selectList(classCourseDOLambdaQueryWrapper);
        if(!classCourseDOListToDelete.isEmpty()){
            classCourseMapper.deleteBatchIds(classCourseDOListToDelete);
            //删除课表的关联信息
            classCourseUtil.deleteClassCourseLinkInfo(classCourseDOListToDelete.stream().map(ClassCourseDO::getId).collect(Collectors.toList()));
        }
        // 插入
        PlanDO plan = PlanConvert.INSTANCE.convert(createReqVO);
        planMapper.insert(plan);

        //创建课程计划后同时批量创建课表格子
        List<LocalDate> localDates = generateDateRange(plan.getBeginDate(), plan.getEndDate());
        List<ClassCourseDO> classCourseDOList = new ArrayList<>();

        //开始填充每一个需要创建的课表格子
        // 获取计划配置
        List<PlanConfigBaseVO> planConfigList = createReqVO.getPlanConfigBaseVOList();

        //保存教学计划配置匹配信息
        List<PlanConfigDO> planConfigDOList = PlanConfigConvert.INSTANCE.convertToDoList(planConfigList);
        planConfigDOList.forEach(planConfigDO -> planConfigDO.setPlanId(plan.getId()));
        planConfigMapper.insertBatch(planConfigDOList);

        // 遍历所有的日期
        for (LocalDate localDate : localDates) {
            // 获取当前日期对应的星期几（1代表周一，7代表周日）
            int dayOfWeek = localDate.getDayOfWeek().getValue();

            // 遍历教学计划的配置
            for (PlanConfigBaseVO config : planConfigList) {
                // 判断当前日期是否与计划配置中的日期匹配
                if (String.valueOf(dayOfWeek).equals(config.getDayOfWeek())) {
                    // 根据时间段来处理课表格子
                    ClassCourseDO classCourseDO = getClassCourseDO(localDate, config, plan);

                    // 将生成的课表格子添加到列表
                    classCourseDOList.add(classCourseDO);
                }
            }
        }

        //校验课程的结束时间需要大于开始时间
        classCourseDOList.forEach(classCourseDO -> {
            if(classCourseDO.getBeginTime()!=null && classCourseDO.getEndTime()!=null && classCourseDO.getBeginTime().isAfter(classCourseDO.getEndTime())){
                throw exception(CLASS_COURSE_BEGIN_TIME_LATER_THAN_END_TIME);
            }
        });
        classCourseMapper.insertBatch(classCourseDOList);

        // 返回
        return plan.getId();
    }

    /**
     * 删除指定日期范围内不符合计划的课程
     * 该方法用于清理给定班级在指定日期范围内没有关联计划的课程
     * 这通常是用于维护数据库整洁，移除由于计划变更或取消而不再需要的课程记录
     *
     * @param beginDate 开始日期，用于界定需要审查和清理的课程范围
     * @param endDate 结束日期，与开始日期一起界定课程审查和清理的范围
     * @param classId 班级ID，指定需要清理课程的班级
     */
    private void deleteIsoLateClassCourse(String beginDate , String endDate , Long classId) {
        List<LocalDate> newLocalDates = generateDateRange(beginDate, endDate);
        List<String> localDatesString = newLocalDates.stream().map(LocalDate::toString).collect(Collectors.toList());
        List<ClassCourseDO> classCourseDOS = classCourseMapper.selectList(new LambdaQueryWrapper<ClassCourseDO>()
                .eq(ClassCourseDO::getClassId, classId)
                //游离格子的计划id为空
                .isNull(ClassCourseDO::getPlanId)
                .in(ClassCourseDO::getDate, localDatesString));
        List<Long> deleteIds = classCourseDOS.stream().map(ClassCourseDO::getId).collect(Collectors.toList());
        if(!deleteIds.isEmpty()){
            classCourseMapper.deleteBatchIds(deleteIds);
        }
    }

    /**
     * 检查新计划是否有时间冲突
     * 该方法通过查询数据库中已有的计划，并与新计划的日期范围进行比较，以确定是否存在时间冲突
     *
     * @param classId、beginDate、endDate 包含新计划信息的请求对象，包括班级ID、开始日期和结束日期
     */
    private void checkPlanTimeConflict(Long classId, String beginDate, String endDate , Long id) {
        LambdaQueryWrapper<PlanDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlanDO::getClassId, classId);
        if(id != null){
            queryWrapper.ne(PlanDO::getId, id);
        }
        List<PlanDO> planDOList = planMapper.selectList(queryWrapper);
        if (!planDOList.isEmpty()) {
            // 解析新计划的日期范围
            LocalDate newStart = LocalDate.parse(beginDate);
            LocalDate newEnd = LocalDate.parse(endDate);
            //如果存在冲突则进行冲突判断
            planDOList.forEach(planDO -> {
                // 解析已有计划的日期范围
                LocalDate existingStart = LocalDate.parse(planDO.getBeginDate());
                LocalDate existingEnd = LocalDate.parse(planDO.getEndDate());
                //如果存在冲突则进行冲突判断
                if (!newEnd.isBefore(existingStart) && !newStart.isAfter(existingEnd)) {
                    throw exception(PLAN_CONFLICT);
                }
            });
        }
    }

    /**
     * 创建教学计划时-批量创建教学计划格子的方法
     * @param localDate 日期
     * @param config 教学计划配置
     * @param plan 教学计划
     * @return 单个课表格子
     */
    private static ClassCourseDO getClassCourseDO(LocalDate localDate, PlanConfigBaseVO config, PlanDO plan) {
        ClassCourseDO classCourseDO = new ClassCourseDO();
        classCourseDO.setPlanId(plan.getId());
        classCourseDO.setClassId(plan.getClassId());
        classCourseDO.setDate(localDate.toString());
        //标记由配置创建的初始格子
        classCourseDO.setOriginal(true);

        // 组合 LocalDate 和 LocalTime
        LocalDateTime beginDateTime = localDate.atTime(config.getBeginTime().getHour(), config.getBeginTime().getMinute(), config.getBeginTime().getSecond());
        LocalDateTime endDateTime = localDate.atTime(config.getEndTime().getHour(), config.getEndTime().getMinute(), config.getEndTime().getSecond());

        // 设置时间段和开始、结束时间
        classCourseDO.setPeriod(config.getPeriod());
        classCourseDO.setBeginTime(beginDateTime);
        classCourseDO.setEndTime(endDateTime);

        // 设置是否考勤（假设所有时间段都需要考勤，您可以根据需要修改）
        // 上午和下午需要考勤，晚上不需要
        classCourseDO.setIsCheck(config.getPeriod().equals(PeriodEnum.MORNING.getCode().toString()) || config.getPeriod().equals(PeriodEnum.AFTERNOON.getCode().toString()));
        return classCourseDO;
    }

    /**
     * 生成范围内日期
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @return 范围日期列表
     */
    public List<LocalDate> generateDateRange(String beginDate,String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT);
        LocalDate start = LocalDate.parse(beginDate, formatter);
        LocalDate end = LocalDate.parse(endDate, formatter);
        List<LocalDate> dateList = new ArrayList<>();
        for (LocalDate date = start; date.isBefore(end) || date.isEqual(end); date = date.plusDays(1)) {
            dateList.add(date);
        }
        return dateList;
    }

    @Override
    public void updatePlan(PlanUpdateReqVO updateReqVO) {
        // 校验存在
        validatePlanExists(updateReqVO.getId());

        //插入前判定是否该班级存在冲突教学计划
        checkPlanTimeConflict(updateReqVO.getClassId(),updateReqVO.getBeginDate(),updateReqVO.getEndDate(),updateReqVO.getId());

        // 获取更新前的教学计划
        PlanDO existingPlan = planMapper.selectById(updateReqVO.getId());

        // 更新（教学计划本身）
        PlanDO updateObj = PlanConvert.INSTANCE.convert(updateReqVO);

        planMapper.updateById(updateObj);

        // 获取当前请求体的教学计划配置
        List<PlanConfigBaseVO> updatedConfigList = updateReqVO.getPlanConfigBaseVOList();

        //校验：若教学计划配置有修改，则删除原来计划配置在创建计划方法产生的课表格子，并重新生成
        if (hasPlanConfigChanged(existingPlan, updatedConfigList)) {

            //删除原教学计划配置，保存新教学计划配置
            LambdaQueryWrapper<PlanConfigDO> configWrapper = new LambdaQueryWrapper<>();
            configWrapper.eq(PlanConfigDO::getPlanId, existingPlan.getId());
            planConfigMapper.delete(configWrapper);

            //保存教学计划配置匹配信息
            List<PlanConfigDO> planConfigDOList = PlanConfigConvert.INSTANCE.convertToDoList(updatedConfigList);
            planConfigDOList.forEach(planConfigDO -> planConfigDO.setPlanId(updateReqVO.getId()));
            planConfigMapper.insertBatch(planConfigDOList);

            // 删除原来计划配置产生的课表格子
            LambdaQueryWrapper<ClassCourseDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ClassCourseDO::getPlanId, existingPlan.getId());
            List<ClassCourseDO> classCourseDOS = classCourseMapper.selectList(queryWrapper);
            classCourseMapper.delete(queryWrapper);
            List<Long> classCourseIdList = classCourseDOS.stream().map(ClassCourseDO::getId).collect(Collectors.toList());

            //删除关联的数据
            classCourseUtil.deleteClassCourseLinkInfo(classCourseIdList);

            // 重新生成课表格子
            List<LocalDate> localDates = generateDateRange(updateObj.getBeginDate(), updateObj.getEndDate());
            List<ClassCourseDO> classCourseDOList = new ArrayList<>();

            // 填充课表格子
            updatedConfigList.forEach(config -> {
                for (LocalDate localDate : localDates) {
                    int dayOfWeek = localDate.getDayOfWeek().getValue();
                    if (String.valueOf(dayOfWeek).equals(config.getDayOfWeek())) {
                        ClassCourseDO classCourseDO = getClassCourseDO(localDate, config, updateObj);
                        classCourseDOList.add(classCourseDO);
                    }
                }
            });

            // 校验课程的开始时间需要大于结束时间
            classCourseDOList.forEach(classCourseDO -> {
                if (classCourseDO.getBeginTime() != null && classCourseDO.getEndTime() != null &&
                        classCourseDO.getBeginTime().isAfter(classCourseDO.getEndTime())) {
                    throw exception(CLASS_COURSE_BEGIN_TIME_LATER_THAN_END_TIME);
                }
            });

            // 插入新的课表格子
            classCourseMapper.insertBatch(classCourseDOList);
        } else {
            //校验：若教学计划配置本身没有修改，但是教学计划的开始、结束日期有变动的话，则删除原来日期不在更新日期的日期的课程表格子，补充更新日期相比原来日期新增日期的教学计划格子
            // 如果教学计划的配置没有改变，检查日期是否有变化
            if (!existingPlan.getBeginDate().equals(updateReqVO.getBeginDate()) ||
                    !existingPlan.getEndDate().equals(updateReqVO.getEndDate())) {

                // 获取原日期范围
                List<LocalDate> oldLocalDates = generateDateRange(existingPlan.getBeginDate(), existingPlan.getEndDate());
                List<LocalDate> newLocalDates = generateDateRange(updateReqVO.getBeginDate(), updateReqVO.getEndDate());

                // 删除原日期范围内不在新日期范围的课表格子
                deleteByPlanIdAndDateRange(existingPlan.getId(), oldLocalDates, newLocalDates);

                //检查新增日期范围内是否有游离于教学计划外的课表格子，有则删除
                deleteIsoLateClassCourse(updateReqVO.getBeginDate(), updateReqVO.getEndDate(), existingPlan.getClassId());

                // 生成新的课表格子（针对新增日期）
                List<ClassCourseDO> newClassCourseDOList = new ArrayList<>();
                updatedConfigList.forEach(config -> {
                    for (LocalDate localDate : newLocalDates) {
                        //判断当天是否不处于原配置时间段里
                        if(!oldLocalDates.contains(localDate)){
                            int dayOfWeek = localDate.getDayOfWeek().getValue();
                            if (String.valueOf(dayOfWeek).equals(config.getDayOfWeek())) {
                                ClassCourseDO classCourseDO = getClassCourseDO(localDate, config, updateObj);
                                newClassCourseDOList.add(classCourseDO);
                            }
                        }
                    }
                });

                // 校验新的课表格子中的开始时间是否大于结束时间
                newClassCourseDOList.forEach(classCourseDO -> {
                    if (classCourseDO.getBeginTime() != null && classCourseDO.getEndTime() != null &&
                            classCourseDO.getBeginTime().isAfter(classCourseDO.getEndTime())) {
                        throw exception(CLASS_COURSE_BEGIN_TIME_LATER_THAN_END_TIME);
                    }
                });

                // 插入新的课表格子
                classCourseMapper.insertBatch(newClassCourseDOList);
            }
        }

        // 判定教学计划的常用教室是否有变更
        updateEffectiveTimeClassCourseClassroom(updateReqVO, existingPlan);

    }

    /**
     * 更新教学计划时，处理生效时间变化对课程安排的影响
     * 当教学计划的生效时间发生变化时，本方法会调整受影响的课程安排，以确保课程安排与新的生效时间一致
     * 具体来说，如果生效时间晚于原计划的生效时间，那么所有在新生效时间之后的课程安排，如果原本安排在原计划的常用教室中，
     * 则会自动更新为新计划的常用教室
     *
     * @param updateReqVO 包含更新后教学计划信息的请求对象，包括新的生效时间和新的常用教室ID
     * @param existingPlan 原有的教学计划对象，包含原生效时间和原常用教室ID
     */
    private void updateEffectiveTimeClassCourseClassroom(PlanUpdateReqVO updateReqVO, PlanDO existingPlan) {
        if (!existingPlan.getClassroomId().equals(updateReqVO.getClassroomId())) {
            // 若常用教室变更，则匹配对应的课表格子，判断课表格子本身的已排课的教室是否与原来教学计划的常用教室一致，若一致则统一变更为新的常用教室
            LambdaQueryWrapper<ClassCourseDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ClassCourseDO::getPlanId, updateReqVO.getId());
            // 对应的date字段要晚于生效时间
            queryWrapper.ge(ClassCourseDO::getDate, updateReqVO.getEffectiveDate());
            List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(queryWrapper);
            // 筛选出那些已安排教室且等于原教学计划常用教室的课表格子
            List<ClassCourseDO> classCourseDOFilteredList = classCourseDOList.stream()
            .filter(classCourseDO -> classCourseDO.getClassroomId() != null && classCourseDO.getClassroomId().equals(existingPlan.getClassroomId())).collect(Collectors.toList());
            classCourseDOFilteredList.forEach(classCourseDO -> {
                classCourseDO.setClassroomId(updateReqVO.getClassroomId());
            });
            classCourseMapper.updateBatch(classCourseDOFilteredList);
        }
    }


    /**
     * 根据计划ID和日期范围删除课程安排
     * 此方法旨在比较旧的日期范围和新的日期范围，然后删除那些在旧日期范围内但不在新日期范围内的课程安排
     *
     * @param planId 计划ID，用于标识特定的课程计划
     * @param oldLocalDates 旧的日期范围列表，表示原始安排的日期
     * @param newLocalDates 新的日期范围列表，表示需要保留的日期
     */
    public void deleteByPlanIdAndDateRange(Long planId, List<LocalDate> oldLocalDates, List<LocalDate> newLocalDates) {
        // 获取原来日期范围的所有课表格子
        LambdaQueryWrapper<ClassCourseDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ClassCourseDO::getPlanId, planId);
        List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(queryWrapper);

        // 筛选出那些不在新日期范围内的课表格子，且在原始日期范围内
        List<ClassCourseDO> toDeleteClassCourses = new ArrayList<>();
        for (ClassCourseDO classCourseDO : classCourseDOList) {
            // 将课表格子的日期字符串转换为 LocalDate
            LocalDate courseDate = LocalDate.parse(classCourseDO.getDate());
            if (oldLocalDates.contains(courseDate) && !newLocalDates.contains(courseDate)) {
                toDeleteClassCourses.add(classCourseDO);
            }
        }

        // 删除不在新日期范围内的课表格子
        if (!toDeleteClassCourses.isEmpty()) {
            List<Long> idsToDelete = toDeleteClassCourses.stream()
                    .map(ClassCourseDO::getId)
                    .collect(Collectors.toList());
            classCourseMapper.deleteBatchIds(idsToDelete);

            //删除关联的数据
            classCourseUtil.deleteClassCourseLinkInfo(idsToDelete);
        }
    }


    private boolean hasPlanConfigChanged(PlanDO existingPlan, List<PlanConfigBaseVO> updatedConfigList) {
        // 比较旧的计划配置和新的计划配置，判断是否发生变化
        LambdaQueryWrapper<PlanConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlanConfigDO::getPlanId, existingPlan.getId());
        List<PlanConfigDO> existingConfigList = planConfigMapper.selectList(queryWrapper);
        existingConfigList.forEach(config -> {
           config.setId(null);
            config.setCreateTime(null);
            config.setUpdateTime(null);
            config.setCreator(null);
            config.setUpdater(null);
            config.setDeleted(null);
        });
//        List<PlanConfigBaseVO> compareList = PlanConfigConvert.INSTANCE.convertToBaseVoList(existingConfigList);
//        System.out.println(compareList);
        List<PlanConfigDO> compareList = PlanConfigConvert.INSTANCE.convertToDoList(updatedConfigList);
        return !existingConfigList.equals(compareList);
    }

    @Override
    public void updateStatus(Long id){
//        // 校验存在
//        this.validatePlanExists(id);
//        // 更新
//        PlanDO updateObj = new PlanDO();
//        updateObj.setId(id);
//        updateObj.setStatus(true);
//        planMapper.updateById(updateObj);
//        //点击生成课表按钮 - 生成课表同时置同一班级其他教学计划的状态为0，同一时间只能有一个教学计划生效
//        PlanDO planDO = this.getById(id);
//        List<PlanDO> updateStatusList = this.lambdaQuery().eq(PlanDO::getClassId, planDO.getClassId()).list();
//        updateStatusList.removeIf(vo -> id.equals(vo.getId()));
//        updateStatusList.forEach(updateVO -> updateVO.setStatus(false));
//        this.updateBatchById(updateStatusList);
//
//        //删除该班级下除该教学计划外所有课表的考勤信息
//        List<Long> classCourseIds = classCourseMapper.selectList(new LambdaQueryWrapper<ClassCourseDO>()
//                .eq(ClassCourseDO::getClassId, planDO.getClassId()).ne(ClassCourseDO::getPlanId, id))
//                .stream().map(ClassCourseDO::getId)
//                .collect(Collectors.toList());
//        classCourseUtil.deleteClassCourseLinkInfo(classCourseIds);
    }

    @Override
    public void deletePlan(Long id) {
        // 校验存在
        this.validatePlanExists(id);
        // 删除
        planMapper.deleteById(id);
        //删除关联课表
        LambdaQueryWrapper<ClassCourseDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ClassCourseDO::getPlanId,id);
        List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(queryWrapper);
        classCourseMapper.delete(queryWrapper);
        //删除课表的关联信息
        classCourseUtil.deleteClassCourseLinkInfo(classCourseDOList.stream().map(ClassCourseDO::getId).collect(Collectors.toList()));
    }

    private void validatePlanExists(Long id) {
        if (planMapper.selectById(id) == null) {
            throw exception(PLAN_NOT_EXISTS);
        }
    }

    @Override
    public PlanDO getPlan(Long id) {
        PlanDO planDO = planMapper.selectById(id);
        if(planDO == null){
            throw exception(PLAN_NOT_EXISTS);
        }
        LambdaQueryWrapper<PlanConfigDO> planWrapper = new LambdaQueryWrapper<>();
        planWrapper.eq(PlanConfigDO::getPlanId,id);
        List<PlanConfigDO> planConfigDOList = planConfigMapper.selectList(planWrapper);
        List<PlanConfigBaseVO> planConfigBaseVOS = PlanConfigConvert.INSTANCE.convertToBaseVoList(planConfigDOList);
        planDO.setPlanConfigBaseVOList(planConfigBaseVOS);
        return planDO;
    }

    @Override
    public List<PlanDO> getPlanList(Collection<Long> ids) {
        return planMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<PlanDO> getPlanPage(PlanPageReqVO pageReqVO) {
        PageResult<PlanDO> planDOPageResult = planMapper.selectPage(pageReqVO);
        List<PlanDO> list = planDOPageResult.getList();
        List<Integer> classroomIds = list.stream()
                .map(planDO -> planDO.getClassroomId() == null ? null : planDO.getClassroomId().intValue())
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<ClassroomLibraryDO> classroomLibraryDOList = !classroomIds.isEmpty() ? classroomLibraryMapper.selectBatchIds(classroomIds) : new ArrayList<>();
        Map<Integer, ClassroomLibraryDO> classroomLibraryMap = classroomLibraryDOList.stream()
                .collect(Collectors.toMap(ClassroomLibraryDO::getId, doItem -> doItem));
        list.forEach(vo -> {
            try{
                if(vo.getClassroomId()!=null){
                    vo.setClassroomName(classroomLibraryMap.get(vo.getClassroomId().intValue()).getClassName());
                }
            }catch (Exception e){
                log.error(e.getMessage());
            }
        });
        planDOPageResult.setList(list);
        return planDOPageResult;
    }

    @Override
    public List<PlanDO> getPlanList(PlanExportReqVO exportReqVO) {
        return planMapper.selectList(exportReqVO);
    }

    @Override
    public void deleteByIds(List<Long> ids){
        // 批量删除
        planMapper.deleteBatchIds(ids);
        //删除关联课表
        LambdaQueryWrapper<ClassCourseDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ClassCourseDO::getPlanId,ids);
        List<ClassCourseDO> classCourseDOList = classCourseMapper.selectList(queryWrapper);
        classCourseMapper.delete(queryWrapper);
        //删除课表的关联信息
        classCourseUtil.deleteClassCourseLinkInfo(classCourseDOList.stream().map(ClassCourseDO::getId).collect(Collectors.toList()));
    }

//    @Override
//    public PlanDO getPlanByClassId(Long classId) {
//        return planMapper.selectOne(new LambdaQueryWrapperX<PlanDO>()
//                .eq(PlanDO::getClassId, classId)
//                .eq(PlanDO::getStatus, true)
//                .orderByDesc(PlanDO::getCreateTime)
//                .last("limit 1"));
//    }

}
