package com.unicom.swdx.module.edu.dal.mysql.classcourse;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.classcourse.dto.ClassCourseClassNameRespDTO;
import com.unicom.swdx.module.edu.controller.admin.classcourse.dto.ClassMergeCourseGroupRespDTO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.dto.TeacherInformationClassCourseDTO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherLectureTimeRespVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeachingRecordReqVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeachingRecordRespVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.businesscenter.TeachingHoursForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.classcourse.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.CollectionUtils;


@Mapper
public interface ClassCourseMapper extends BaseMapperX<ClassCourseDO> {

    default PageResult<ClassCourseDO> selectPage(ClassCoursePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ClassCourseDO>()
                .eqIfPresent(ClassCourseDO::getClassId, reqVO.getClassId())
                .eqIfPresent(ClassCourseDO::getCourseId, reqVO.getCourseId())
                .betweenIfPresent(ClassCourseDO::getBeginTime, reqVO.getBetweenTime())
                .betweenIfPresent(ClassCourseDO::getEndTime, reqVO.getBetweenTime())
                .eqIfPresent(ClassCourseDO::getTeacherId, reqVO.getTeacherId())
                .eqIfPresent(ClassCourseDO::getClassroomId, reqVO.getClassroomId())
                .eqIfPresent(ClassCourseDO::getIsTemporary, reqVO.getIsTemporary())
                .eqIfPresent(ClassCourseDO::getIsMerge, reqVO.getIsMerge())
                .eqIfPresent(ClassCourseDO::getIsChange, reqVO.getIsChange())
                .eqIfPresent(ClassCourseDO::getPlanId, reqVO.getPlanId())
                .likeIfPresent(ClassCourseDO::getDate,reqVO.getDate())
                .betweenIfPresent(ClassCourseDO::getDate,reqVO.getTimeRange())
                .orderByAsc(ClassCourseDO::getBeginTime)
                .orderByDesc(ClassCourseDO::getId));
    }

    default List<ClassCourseDO> selectList(ClassCourseExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ClassCourseDO>()
                .eqIfPresent(ClassCourseDO::getClassId, reqVO.getClassId())
                .eqIfPresent(ClassCourseDO::getCourseId, reqVO.getCourseId())
                .betweenIfPresent(ClassCourseDO::getBeginTime, reqVO.getBeginTime())
                .betweenIfPresent(ClassCourseDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(ClassCourseDO::getTeacherId, reqVO.getTeacherId())
                .eqIfPresent(ClassCourseDO::getClassroomId, reqVO.getClassroomId())
                .eqIfPresent(ClassCourseDO::getIsTemporary, reqVO.getIsTemporary())
                .eqIfPresent(ClassCourseDO::getIsMerge, reqVO.getIsMerge())
                .eqIfPresent(ClassCourseDO::getIsChange, reqVO.getIsChange())
                .orderByDesc(ClassCourseDO::getId));
    }

    /**
     * 获得必修课
     * @param reqVO
     * @return
     */
    List<MyClassScheduleVO> getMyClassSchedule(@Param("reqVO") MyClassScheduleParamsVO reqVO);

    /**
     * 获得选修课
     * @param reqVO
     * @return
     */
    List<MyClassScheduleVO> getOptionalCourse(@Param("reqVO") MyClassScheduleParamsVO reqVO);
    /**
     * 获得学员id
     * @param userId
     * @return
     */
    Long getEduTraineeId(@Param("userId") Long userId);
    /**
     * 获得学员所在班级id
     * @param userId
     * @return
     */
    Long getEduTraineeClassId(@Param("userId") Long userId);



    List<ClassTimeTableInfoVO> getTimeTable(@Param("reqVO") ClassTimeTableReqVO reqVO);

    default Set<Long> getClassIdByTeacherId(Integer teacherId) {

        LambdaQueryWrapper<ClassCourseDO> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(ClassCourseDO::getTeacherId, teacherId);

        wrapper.select(ClassCourseDO::getClassId);

        return selectList(wrapper).stream().map(ClassCourseDO::getClassId).collect(Collectors.toSet());
    }

    /**
     * 根据班级id和课程类型查询 已发布的课程列表
     * @param classId 指定班级
     * @param courseTypeList 指定需要的课程类型列表
     * @return 课程列表
     */
    List<ClassCourseDO> getCoursesListByClassIdAndCourseTypelist(@Param("classId") Long classId,
                                                                 @Param("courseTypeList") List<Integer> courseTypeList);

    /**
     * 根据课程类型获取上课时间与某一段时间有交叉的 已发布的课程信息
     * @param courseTypeList 指定需要的课程类型列表
     * @param startDateTime 指定开始时间
     * @param endDateTime 指定结束时间
     * @return 课程列表
     */
    List<ClassCourseDO> getCoursesListByClassTimeAndCourseTypelist(@Param("courseTypeList") List<Integer> courseTypeList,
                                                                 @Param("startDateTime") LocalDateTime startDateTime,
                                                                 @Param("endDateTime") LocalDateTime endDateTime);

    /**
     * 根据课程id集合查询课程信息
     * @param courseIdList 指定课程id集合
     * @return 课程日程安排列表
     */
    default List<ClassCourseDO> selectListByCourseIdList(List<Long> courseIdList){
        if (CollectionUtils.isEmpty(courseIdList)){
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapperX<ClassCourseDO>()
                .in(ClassCourseDO::getCourseId, courseIdList));
    }

    List<TeacherLectureTimeRespVO> getLectureInfoByTeacherId(@Param("teacherId") Long id);


    void updateClassCourseByClockingIn(@Param("clockingInVO") ClassCourseClockingInVO clockingInVO);


    /**
     * 根据教师id查询课程信息
     * @param id 教师id
     * @return 课程信息列表
     */
    default List<ClassCourseDO> getClassCourseByTeacherId(Long id) {
        LambdaQueryWrapper<ClassCourseDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ClassCourseDO::getTeacherId, id);

        return selectList(wrapper);
    }

    /**
     * 获取发布课表下拉列表
     *
     * @param reqVO 请求参数
     * @return 下拉列表
     */
    List<ClassCourseSimpleRespVO> getSimpleList(@Param("reqVO") ClassCourseSimpleReqVO reqVO);

    /**
     * 根据排课表id查询课表详细信息
     * @param id 排课表id查
     * @return 课表详细信息
     */
    ClassCourseRespVO selectRespVOById(Long id);

    /**
     * 根据午别、上课日期获取选修课上课时间段
     *
     * @param dayPeriod 午别
     * @param classDate 上课日期 上课日期 YY-MM-HH
     * @return 上课时段
     */
    List<ClassCourseDO> getClassTimeByPeriodAndDate(@Param("dayPeriod") Integer dayPeriod,
                                                                      @Param("classDate") String classDate);

    List<ClassCourseDO> getEndedCourse(@Param("endTime") LocalDateTime time, @Param("now")LocalDateTime now);

    List<ClassCourseDO> getDepartmentCourse(@Param("endTime") LocalDateTime time, @Param("now")LocalDateTime now);

    List<ClassCourseDO> getCoursesListByClassIdAndCourseDate(@Param("classId") Long classId, @Param("reportingTime") String reportingTime);

    /**
     * 获取专题现在已发布的课程 - 包含多授课教师（部门授课 教师id为部门id）信息
     * @param reqVO 请求参数
     * @return 排课-教师列表信息
     */
    List<TeacherInformationClassCourseDTO> selectTopicListJoinTeacher(@Param("reqVO") TeachingHoursForBusinessCenterReqVO reqVO);

    /**
     * 获取选修课现在已发布的课程信息
     * @param reqVO 请求参数
     * @return 排课-教师列表信息
     */
    List<TeacherInformationClassCourseDTO> selectOptionalListJoinTeacher(@Param("reqVO") TeachingHoursForBusinessCenterReqVO reqVO);

    /**
     * 根据排课id查询合并的课程信息 (截断上课时间至分钟)
     * @param classCourseId 排课id
     * @return 合并的课程信息列表
     */
    List<ClassCourseClassNameRespDTO> selectMergedClassCourse(@Param("classCourseId") Long classCourseId);


        /**
         * 根据目标时间段和班级ID列表查询课程信息 (截断上课时间至分钟)
         *
         * @param classIdList 班级ID列表
         * @param beginTime 开始时间
         * @param endTime 结束时间
         * @return 课程信息列表
         */
    List<ClassCourseDO> selectClassCourseByClassIdsAndTime(@Param("classIdList") List<Long> classIdList,
                                                              @Param("beginTime") LocalDateTime beginTime,
                                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 根据合班信息查询合并的班级 (截断上课时间至分钟)
     * @param reqVO 合班授课信息
     * @return 合并的班级信息列表
     */
    List<ClassCourseClassNameRespDTO> getMergedClassListByReqVO(@Param("reqVO") ClassMergeReqVO reqVO);
    List<ClassCourseDO> getDistributedCourse(@Param("endTime") LocalDateTime time, @Param("now")LocalDateTime now);


    List<ClassCourseDO> getDistributedDepartmentCourse(@Param("endTime") LocalDateTime time, @Param("now")LocalDateTime now);

    List<ClassCourseClassNameRespDTO>  getMergedClassListByCourseTimeBatch(@Param("reqList") List<ClassMergeReqVO> reqVOList);
    /**
     * 获取合班授课分组信息列表
     *
     * @param classCourseIds 筛选出包含这些排课id的合班授课分组信息列表
     * @return 合班授课分组信息列表
     */
    List<ClassMergeCourseGroupRespDTO> getClassMergeCourseGroup(@Param("classCourseIds") List<Long> classCourseIds);
    List<ClassCourseDO> selectDepartmentLeaderCourse(@Param("now") LocalDateTime now);

    /**
     * 获取教师授课记录
     *
     * @param reqVO 请求参数
     * @return 教师授课记录
     */
    List<TeachingRecordRespVO> getTeachingRecordPage(@Param("reqVO") TeachingRecordReqVO reqVO);
}
