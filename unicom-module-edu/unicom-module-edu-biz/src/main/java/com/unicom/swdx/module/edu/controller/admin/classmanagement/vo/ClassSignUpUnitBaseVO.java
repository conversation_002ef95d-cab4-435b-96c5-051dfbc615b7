package com.unicom.swdx.module.edu.controller.admin.classmanagement.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* EduSignUpUnit Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ClassSignUpUnitBaseVO {

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "单位分类")
    private Integer unitClassification;

    @ApiModelProperty(value = "单位责任人")
    private String unitChargePeople;

    @ApiModelProperty(value = "名额人数")
    private Integer capacity;

    @ApiModelProperty(value = "班级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "是否限制人数，0-限制，1-不限制")
    private Integer isRestrict;

    @ApiModelProperty(value = "排序")
    private Integer sort;

}
