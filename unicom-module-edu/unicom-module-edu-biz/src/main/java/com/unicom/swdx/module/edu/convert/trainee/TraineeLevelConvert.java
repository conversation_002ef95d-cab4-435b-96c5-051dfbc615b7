package com.unicom.swdx.module.edu.convert.trainee;

import com.unicom.swdx.module.edu.controller.admin.homepage.vo.TraineeEthnicAnalysisRespVO;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.TraineeLevelAnalysisRespVO;
import com.unicom.swdx.module.edu.controller.admin.homepage.vo.TraineeUnitAnalysisRespVO;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * EduClassroomLibrary Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TraineeLevelConvert {

    TraineeLevelConvert INSTANCE = Mappers.getMapper(TraineeLevelConvert.class);

    default List<TraineeLevelAnalysisRespVO.DataVO> covertList(List<DictDataRespDTO> levelList) {
        if ( levelList == null ) {
            return null;
        }

        List<TraineeLevelAnalysisRespVO.DataVO> list = new ArrayList<TraineeLevelAnalysisRespVO.DataVO>( levelList.size() );
        for ( DictDataRespDTO dictDataRespDTO : levelList ) {
            if ( dictDataRespDTO == null ) {
                return null;
            }

            TraineeLevelAnalysisRespVO.DataVO dataVO = new TraineeLevelAnalysisRespVO.DataVO();

            if ( dictDataRespDTO.getId() != null ) {
                dataVO.setId( dictDataRespDTO.getId().intValue() );
                dataVO.setItemName(dictDataRespDTO.getLabel());

            }

            list.add( dataVO );
        }

        return list;
    }

    List<TraineeEthnicAnalysisRespVO.EthnicType> covertEthnicList(List<DictDataRespDTO> levelList);

    default List<TraineeUnitAnalysisRespVO> covertUnitList(List<DictDataRespDTO> unitTypeList) {
        if ( unitTypeList == null ) {
            return null;
        }

        List<TraineeUnitAnalysisRespVO> list = new ArrayList<>( unitTypeList.size() );
        for ( DictDataRespDTO dictDataRespDTO : unitTypeList ) {
            if ( dictDataRespDTO == null ) {
                return null;
            }

            TraineeUnitAnalysisRespVO dataVO = new TraineeUnitAnalysisRespVO();

            if ( dictDataRespDTO.getId() != null ) {
                dataVO.setId(dictDataRespDTO.getId());
                dataVO.setItemName(dictDataRespDTO.getLabel());
                dataVO.setItemNum(0);
            }

            list.add( dataVO );
        }

        return list;
    }
}
