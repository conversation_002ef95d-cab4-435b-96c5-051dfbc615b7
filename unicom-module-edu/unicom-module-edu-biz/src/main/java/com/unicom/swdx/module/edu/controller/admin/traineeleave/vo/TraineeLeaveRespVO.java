package com.unicom.swdx.module.edu.controller.admin.traineeleave.vo;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @ClassName: TraineeLeaveCreateReqVO
 * @Author: youxiaoyan
 * @Date: 2024/11/5
 */
@Data
@ApiModel(value = "学员请假 response VO")
public class TraineeLeaveRespVO extends TraineeLeaveBaseVO {

    @ApiModelProperty(value = "请假id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "请假标题")
    private String title;

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty(value = "学员姓名")
    private String traineeName;

    @ApiModelProperty(value = "班主任姓名")
    private String classTeacherLeadName;

    @ApiModelProperty(value = "流程状态")
    private Integer status;

    @ApiModelProperty(value = "提交时间")
    private String applyTimeStr;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

    private String tenantName;

    @ApiModelProperty(value = "请假流程")
    private List<TraineeLeaveProcessVO> processList;

    public void setApplyTimeStr(String applyTimeStr) {
        if(Objects.nonNull(applyTimeStr) && StringUtils.isNotBlank(applyTimeStr) && applyTimeStr.length() > 19){
            this.applyTimeStr = StringUtils.substring(applyTimeStr, 0, 19);
        }else {
            this.applyTimeStr = applyTimeStr;
        }
    }
}
