package com.unicom.swdx.module.edu.controller.admin.trainee.excelimporthandler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;

public class CustomHeaderWriteHandler implements SheetWriteHandler {

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();

        // 添加注释到指定单元格 (例如，B2单元格)
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        CreationHelper factory = workbook.getCreationHelper();
        Drawing<?> drawing = sheet.createDrawingPatriarch();

        Row row = sheet.getRow(0); // 第二行，索引为1
        if (row == null) {
            row = sheet.createRow(0);
        }

        // 创建注释
        addComment1(factory, row.createCell(0), row, drawing, workbook);
        addComment1(factory, row.createCell(1), row, drawing, workbook);

    }

    private static void addComment1(CreationHelper factory, Cell cell, Row row, Drawing<?> drawing, Workbook workbook) {
        ClientAnchor anchor = factory.createClientAnchor();
        anchor.setCol1(cell.getColumnIndex());
        anchor.setCol2(cell.getColumnIndex() + 3);
        anchor.setRow1(row.getRowNum());
        anchor.setRow2(row.getRowNum() + 3);

        Comment comment = drawing.createCellComment(anchor);
        comment.setString(factory.createRichTextString("填写说明:\n此项为必填项，不填将导致导入失败"));
        cell.setCellComment(comment);

        // 设置单元格样式 (可选)
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
//        cellStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cell.setCellStyle(cellStyle);
    }

}
