package com.unicom.swdx.module.edu.controller.admin.signupunit.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免信息导入有问题
public class SignUnitImportTemplateExcelVO {
    @ExcelProperty(value = "单位管理员用户名")
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String username;

    @ExcelProperty(value = "单位名称")
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String unitName;

    @ExcelProperty(value = "单位分类")
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String unitClassificationName;

    @ExcelProperty(value = "单位负责人")
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String unitChargePeople;

    @ExcelProperty(value = "负责人电话")
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String phone;

    @ExcelProperty(value = "*是否启用 1启2禁")
    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String status;

    @ExcelProperty(value = "排序")
//    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String sort;

}

