package com.unicom.swdx.module.edu.controller.admin.traineeleave.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TraineeLeaveTransferDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long leaveId;

    private String userId;

    private String title;

    private String name;

    private String time;

}
