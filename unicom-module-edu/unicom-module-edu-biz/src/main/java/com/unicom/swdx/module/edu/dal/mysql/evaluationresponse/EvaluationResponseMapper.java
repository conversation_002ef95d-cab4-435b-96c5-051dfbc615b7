package com.unicom.swdx.module.edu.dal.mysql.evaluationresponse;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.dto.EvaluationRankScoreDetailDTO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.deptevaluation.DeptEvaluationPageRespVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.myevaluation.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.ClassEvaluationForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.businesscenter.RankDetailVO;
import com.unicom.swdx.module.edu.dal.dataobject.evaluationresponse.EvaluationResponseDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程评价记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EvaluationResponseMapper extends BaseMapperX<EvaluationResponseDO> {

    default PageResult<EvaluationResponseDO> selectPage(EvaluationResponsePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EvaluationResponseDO>()
                .eqIfPresent(EvaluationResponseDO::getQuestionnaireId, reqVO.getQuestionnaireId())
                .eqIfPresent(EvaluationResponseDO::getStudentId, reqVO.getStudentId())
                .eqIfPresent(EvaluationResponseDO::getIssuer, reqVO.getIssuer())
                .eqIfPresent(EvaluationResponseDO::getDeptId, reqVO.getDeptId())
                .betweenIfPresent(EvaluationResponseDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(EvaluationResponseDO::getTeacherId, reqVO.getTeacherId())
                .eqIfPresent(EvaluationResponseDO::getClassCourseId, reqVO.getClassCourseId())
                .eqIfPresent(EvaluationResponseDO::getScore, reqVO.getScore())
                .eqIfPresent(EvaluationResponseDO::getGrade, reqVO.getGrade())
                .eqIfPresent(EvaluationResponseDO::getHandle, reqVO.getHandle())
                .eqIfPresent(EvaluationResponseDO::getRemarktype, reqVO.getRemarktype())
                .orderByDesc(EvaluationResponseDO::getId));
    }

    /**
     * 我的评估-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    List<MyEvaluationPageRespVO> getMyEvaluationPage(@Param("reqVO") MyEvaluationPageReqVO reqVO);

        /**
         * 我的评估-评分详情
         *
         * @param reqVO 查询参数
         * @return 分页结果
         */
    List<MyEvaluationDetailPageRespVO> getMyEvaluationDetail(Page<MyEvaluationDetailPageRespVO> page,
                                                             @Param("reqVO") MyEvaluationDetailPageReqVO reqVO);

    /**
     * 教师评估-分页查询
     *
     * @param page  分页参数
     * @param reqVO 查询参数
     * @return 分页结果
     */
    List<TeacherEvaluationPageRespVO> getTeacherEvaluationPage(Page<TeacherEvaluationPageRespVO> page,
                                                               @Param("reqVO") MyEvaluationPageReqVO reqVO);

    /**
     * 部门评估-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    List<DeptEvaluationPageRespVO> getDeptEvaluationPage(Page<DeptEvaluationPageRespVO> page,
                                                         @Param("reqVO") DeptEvaluationPageReqVO reqVO);

    /**
     * 班次评估统计-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    List<ClassEvaluationStatsPageRespVO> getClassEvaluationStatsPage(Page<ClassEvaluationStatsPageRespVO> page,
                                                                     @Param("reqVO") ClassEvaluationStatsPageReqVO reqVO);

    /**
     * 班次评估统计-学员参评情况-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    List<TraineeEvaluationDetailPageRespVO> getTraineeEvaluationDetailPage(Page<TraineeEvaluationDetailPageRespVO> page,
                                                                           @Param("reqVO") TraineeEvaluationDetailPageReqVO reqVO);

    default List<EvaluationResponseDO> selectList(EvaluationResponsePageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<EvaluationResponseDO>()
                .eqIfPresent(EvaluationResponseDO::getQuestionnaireId, reqVO.getQuestionnaireId())
                .eqIfPresent(EvaluationResponseDO::getStudentId, reqVO.getStudentId())
                .eqIfPresent(EvaluationResponseDO::getIssuer, reqVO.getIssuer())
                .eqIfPresent(EvaluationResponseDO::getDeptId, reqVO.getDeptId())
                .betweenIfPresent(EvaluationResponseDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(EvaluationResponseDO::getTeacherId, reqVO.getTeacherId())
                .eqIfPresent(EvaluationResponseDO::getClassCourseId, reqVO.getClassCourseId())
                .eqIfPresent(EvaluationResponseDO::getScore, reqVO.getScore())
                .eqIfPresent(EvaluationResponseDO::getGrade, reqVO.getGrade())
                .eqIfPresent(EvaluationResponseDO::getHandle, reqVO.getHandle())
                .eqIfPresent(EvaluationResponseDO::getRemarktype, reqVO.getRemarktype())
                .orderByDesc(EvaluationResponseDO::getId));
    }


    List<EvaluationRespVO> selectStudentEvaluation(@Param("userId") Long loginUserId, @Param("handle") Boolean handle, @Param("courseName") String courseName);

    void updateEvaluation(@Param("submitVO") EvaluationSubmitVO submitVO, @Param("userId") Long loginUserId);

    /**
     * 班次评估统计-学员参评情况-学员已评、未评详情-分页
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    List<TraineeEvaluatedAndUnEvaluatedDetailPageRespVO> getTraineeEvaluatedAndUnEvaluatedDetailPage(Page<TraineeEvaluatedAndUnEvaluatedDetailPageRespVO> page,
                                                                                                     @Param("reqVO") TraineeEvaluatedAndUnEvaluatedDetailPageReqVO reqVO);

    /**
     * 班次评估统计-课评详情-学员详情-分页
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    List<CourseTraineeEvaluationDetailPageRespVO> getCourseTraineeEvaluationDetailPage(Page<CourseTraineeEvaluationDetailPageRespVO> page,
                                                                                       @Param("reqVO") MyEvaluationDetailPageReqVO reqVO);

    /**
     * 部门评估统计-分页查询
     *
     * @param reqVO 查询参数
     * @return 分页结果
     */
    List<DeptEvaluationPageRespVO> getDeptEvaluationStatsPage(Page<DeptEvaluationPageRespVO> page,
                                                              @Param("reqVO") DeptEvaluationPageReqVO reqVO);

    Long countUnhandled(@Param("userId") Long id, @Param("now") LocalDateTime now);

    List<AllEvaluationPageRespVO> selectAllPage(IPage<AllEvaluationPageRespVO> page,
                                                      @Param("reqVO") AllEvaluationPageReqVO reqVO);

    void modifyEvaluation(@Param("reqVO") EvaluationModifyVO updateReqVO);

    void revoke(@Param("id") Long id);

    /**
     * 撤回已评问卷（将已评问卷状态修改为未评状态）
     * 
     * @param id 评估响应ID
     */
    void revokeEvaluated(@Param("id") Long id);

    void distribute(@Param("id") Long id, @Param("expireTime") LocalDateTime expireTime);

    EvaluationResponseDO selectExistEvaluation(@Param("studentId") Long studentId, @Param("classCourseId") Long classCourseId);

    /**
     * 批量根据学员id和对应排课id 键值对删除
     * @param traineeIdAndClassCourseIdPairs 学员id和对应排课id 键值对列表
     */
    void batchDeleteByPairOfTraineeIdAndClassCourseId(@Param("pairs") List<Pair<Long, Long>> traineeIdAndClassCourseIdPairs);

    List<Long> getExistEvaluationTrainee(@Param("classCourseId") Long classCourseId);

    List<EvaluationSummaryPageRespVO> getEvaluationSummaryPage(Page<EvaluationSummaryPageRespVO> page, @Param("reqVO")EvaluationSummaryPageReqVO pageReqVO);

    /**
     * 获取部门排行详情
     * @param reqVO 查询参数
     * @return 排行详情列表
     */
    List<RankDetailVO> getDeptRankDetailVOList(@Param("reqVO") ClassEvaluationForBusinessCenterReqVO reqVO);

    /**
     * 获取部门评课率、评课平均分
     * @param reqVO 查询参数
     * @return  评课率、评课平均分
     */
    ClassEvaluationForBusinessCenterRespVO getCourseEvaluationRateAndAvgScore(@Param("reqVO") ClassEvaluationForBusinessCenterReqVO reqVO);

    /**
     * 部门教师课程评课详情
     * @param reqVO 查询参数
     * @return 排行详情列表
     */
    List<RankDetailVO> getTeacherRankDetailVOList(@Param("reqVO") ClassEvaluationForBusinessCenterReqVO reqVO);

    /**
     * 获取专题课程评课详情
     * @param classId 班次id
     * @param isDone 是否已完成评课
     * @return 评课详情列表
     */
    List<CourseEvaluationResponseVO> getTopicCourseEvaluation(@Param("classId") Long classId,
                                                              @Param("isDone") Boolean isDone);

    /**
     * 获取选修课程评课详情
     * @param classId 班次id
     * @return 评课详情列表
     */
    List<OptionalCourseEvaluationDetailVO> getElectiveCourseEvaluation(@Param("classId") Long classId);

    /**
     * 获取选修课课程学员评课详情
     * @param classCourseId 班次课程id
     * @param courseId 课程id
     * @param isDone 是否已完成评课
     * @return 评课详情列表
     */
    List<CourseEvaluationTraineeDetailVO> getElectiveCourseEvaluationTraineeDetailForApp(@Param("classCourseId") Long classCourseId,
                                                                                         @Param("courseId") Long courseId,
                                                                                         @Param("isDone") Boolean isDone);

    /**
     * 获取专题课程学员评课详情
     * @param classCourseId 班次课程id
     * @param isDone 是否已完成评课
     * @return 评课详情列表
     */
    List<CourseEvaluationTraineeDetailVO> getTopicCourseEvaluationTraineeDetailForApp(@Param("classCourseId") Long classCourseId,
                                                                                      @Param("isDone") Boolean isDone);
}
