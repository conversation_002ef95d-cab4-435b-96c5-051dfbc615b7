package com.unicom.swdx.module.edu.service.traineegroup;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.util.desensitize.DesensitizeUtils;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.AppTraineeGroupRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.GroupBaseReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineegroup.vo.TraineeGroupRespVO;
import com.unicom.swdx.module.edu.convert.trainee.TraineeConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcommittee.ClassCommitteeDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.traineegroup.TraineeGroupDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.traineegroup.TraineeGroupMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.trainee.SexEnum;
import com.unicom.swdx.module.edu.enums.traingroup.GroupSortEnum;
import com.unicom.swdx.module.edu.service.classcommittee.ClassCommitteeServiceImpl;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementServiceImpl;
import com.unicom.swdx.module.edu.service.teacherinformation.TeacherInformationServiceImpl;
import com.unicom.swdx.module.edu.service.training.TraineeServiceImpl;
import com.unicom.swdx.module.edu.utils.numberconverter.NumberConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import com.unicom.swdx.module.system.api.user.dto.AdminUserRespDTO;
import com.unicom.swdx.framework.common.pojo.CommonResult;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * @ClassName: TraineeGroupServiceImpl
 * @Author: lty
 * @Date: 2024/10/17 11:29
 */
@Service
@Validated
@Slf4j
public class TraineeGroupServiceImpl extends ServiceImpl<TraineeGroupMapper, TraineeGroupDO> implements TraineeGroupService  {

    @Resource
    ClassManagementServiceImpl classManagementServiceImpl;

    @Resource
    TeacherInformationServiceImpl teacherInformationServiceImpl;

    @Resource
    TraineeServiceImpl traineeServiceImpl;

    @Resource
    ClassCommitteeServiceImpl classCommitteeServiceImpl;

    @Resource
    private AdminUserApi adminUserApi;

    private static final String GROUP_FIRST_APP_NAME = "班主任";

    private static final String UN_GROUPED_NAME = "未分组";

    private static final Long UN_GROUPED_ID = 0L;

    private static final  String desensitizePublicKeyStr = "04d89d95d0129f8b22a27979fd2c5d1fad305e1ec9ebbd2f6ff26f5bfdf00e5493574e8b93a2a16d4f924efd5133b6e7474aa694ec6314e39c34ff89c22d7b3768";

    private static final String desensitizePrivateKeyStr = "5174bc2473f86d5cda2ddad6a2d9b705848dd65b6fa13ff0923b1d2b55f09d30";

    private static final SM2 sm2Desensitize = SmUtil.sm2(desensitizePrivateKeyStr,desensitizePublicKeyStr);

    private static String desensitizeEncrypt(String content) {
        return sm2Desensitize.encryptBase64(content, KeyType.PublicKey);
    }

    private static String desensitizeDecrypt(String content) {
        return sm2Desensitize.decryptStr(content, KeyType.PrivateKey);
    }

    @Autowired
    private TraineeMapper traineeMapper;

    @Override
    public Long addGroup(GroupBaseReqVO reqVO) {

        // 校验小组名称是否重复
        checkGroupName(reqVO);

        // 获取班级里面的最大序号
        Integer maxSort = baseMapper.getMaxSort(reqVO.getClassId());

        TraineeGroupDO group = new TraineeGroupDO();
        group.setGroupName(reqVO.getGroupName());
        group.setSort(++maxSort);
        group.setClassId(reqVO.getClassId());
        group.setTenantId(SecurityFrameworkUtils.getTenantId());
        baseMapper.insert(group);

        try {
            traineeServiceImpl.setGroupOrder(group.getId());
        } catch (Exception e) {
            log.info("排序失败：{}",e.getMessage());
        }
        return group.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long editGroup(GroupBaseReqVO reqVO) {

        //校验存在
        TraineeGroupDO group = this.getById(reqVO.getId());
        if (group == null) {
            throw exception(TRAINEE_GROUP_NOT_EXISTS);
        }

        //校验名称是否重复
        checkGroupName(reqVO);
        group.setClassId(reqVO.getClassId());
        group.setGroupName(reqVO.getGroupName());

        this.updateById(group);
        return group.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteGroup(List<Long> ids) {

        //根据小组id查询对应学员
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TraineeDO::getGroupId,ids);
        List<TraineeDO> traineeList = traineeServiceImpl.list(wrapper);

        traineeList.forEach(item->{
            item.setGroupId(0L);
            item.setGroupSort(0);
        });
        traineeServiceImpl.updateBatchById(traineeList);

        //校验存在
        List<TraineeGroupDO> groupList = this.listByIds(ids);
        if (groupList.isEmpty()) {
            throw exception(TRAINEE_GROUP_NOT_EXISTS);
        }

        this.removeBatchByIds(ids);

        return true;
    }

    @Override
    public Page<TraineeGroupRespVO> getPage(TraineeGroupReqVO reqVO) {
        Page<TraineeGroupRespVO> page = this.baseMapper.selectGroupPage(MyBatisUtils.buildPage(reqVO),reqVO);
        List<TraineeGroupRespVO> records = page.getRecords();
        for (int i = 0; i < records.size(); i++) {


            TraineeGroupRespVO vo = records.get(i);

            if (i!=0){
                vo.setPreId(records.get(i-1).getId());
            }
            if (i!=records.size()-1){
                vo.setNextId(records.get(i+1).getId());
            }
            vo.setIndex((page.getCurrent() - 1) * page.getSize() + i + 1);
        }
        page.setRecords(records);
        return page;
    }

    @Override
    public List<TraineeGroupRespVO> exportGroup(TraineeGroupReqVO reqVO) {
        return this.baseMapper.selectGroupList(reqVO);
    }

    private void checkGroupName(GroupBaseReqVO reqVO) {

        LambdaQueryWrapper<TraineeGroupDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeGroupDO::getGroupName,reqVO.getGroupName());
        wrapper.eq(TraineeGroupDO::getClassId,reqVO.getClassId());
        wrapper.ne(Objects.nonNull(reqVO.getId()),TraineeGroupDO::getId,reqVO.getId());
        if (this.count(wrapper) > 0) {
            throw exception(TRAINEE_GROUP_NAME_EXISTS);
        }

    }

    @Override
    public List<AppTraineeGroupRespVO> traineeListByGroup(Long classId){
        List<AppTraineeGroupRespVO> appTraineeGroupRespVOList = new ArrayList<>();
        //班主任放第一位
        ClassManagementDO classManagementDO = classManagementServiceImpl.getClassManagement(classId);
        if(classManagementDO == null){
            throw exception(CLASS_MANAGEMENT_NOT_EXISTS);
        }
        TeacherInformationDO teacherInformationDO = teacherInformationServiceImpl.getById(classManagementDO.getClassTeacherLead());
        //班级教师不存在的特殊情况
        if(teacherInformationDO!=null){
            AppTraineeGroupRespVO appTraineeGroupRespVO = new AppTraineeGroupRespVO();
            appTraineeGroupRespVO.setGroupName(GROUP_FIRST_APP_NAME);
            appTraineeGroupRespVO.setPosition(GROUP_FIRST_APP_NAME);
            appTraineeGroupRespVO.setClassCommitteeName(GROUP_FIRST_APP_NAME);
            appTraineeGroupRespVO.setName(teacherInformationDO.getName());
            appTraineeGroupRespVO.setId(teacherInformationDO.getId());
            String encryptedPhone = desensitizeEncrypt(teacherInformationDO.getContactInformation());
            appTraineeGroupRespVO.setPhone(encryptedPhone);
            appTraineeGroupRespVO.setSex(SexEnum.getDescByStatus(teacherInformationDO.getGender()));
            // 查询头像
            if (teacherInformationDO.getUserId() != null) {
                CommonResult<AdminUserRespDTO> userResult = adminUserApi.getUser(teacherInformationDO.getUserId());
                if (userResult != null && userResult.getData() != null) {
                    appTraineeGroupRespVO.setAvatar(userResult.getData().getAvatar());
                }
            }
            appTraineeGroupRespVOList.add(appTraineeGroupRespVO);
        }

        //组内成员根据字段group_sort（组内排序去展示）,组与组之间的排序用group表的sort字段
        List<TraineeDO> traineeList = traineeServiceImpl
                .lambdaQuery().eq(TraineeDO::getClassId, classId)
                .list();
        //学员->分组
        List<Long> groupIdList = traineeList.stream()
                .map(TraineeDO::getGroupId)
                .collect(Collectors.toList());
        List<TraineeGroupDO> traineeGroupDOS = !groupIdList.isEmpty() ? this.listByIds(groupIdList) : new ArrayList<>();
        //学员->班委
        List<Long> classCommitteeIdList = traineeList.stream()
                .map(TraineeDO::getClassCommitteeId)
                .collect(Collectors.toList());
        List<ClassCommitteeDO> classCommitteeDOS = !classCommitteeIdList.isEmpty() ? classCommitteeServiceImpl.listByIds(classCommitteeIdList) : new ArrayList<>();
        Map<Long, ClassCommitteeDO> classCommitteeMap = classCommitteeDOS.stream()
                .collect(Collectors.toMap(ClassCommitteeDO::getId, committee -> committee));
        // 根据 sort 属性进行排序
        traineeGroupDOS.sort(Comparator.comparingInt(TraineeGroupDO::getSort).thenComparing(TraineeGroupDO::getGroupName));
        traineeGroupDOS.forEach(traineeGroupDO -> {
            List<TraineeDO> traineeDOList = traineeList.stream()
                    .filter(traineeDO -> traineeGroupDO.getId().equals(traineeDO.getGroupId()))
                    .collect(Collectors.toList());
            traineeDOList.sort(Comparator.comparingInt(TraineeDO::getGroupSort));
            List<AppTraineeGroupRespVO> appTraineeGroupRespVOS = TraineeConvert.INSTANCE.convertToAppTraineeGroupRespVOList(traineeDOList);
            appTraineeGroupRespVOS.forEach(respVO -> {
                respVO.setGroupName(traineeGroupDO.getGroupName());
                ClassCommitteeDO classCommitteeDO = classCommitteeMap.get(respVO.getClassCommitteeId());
                if(classCommitteeDO != null){
                    respVO.setClassCommitteeName(classCommitteeDO.getClassCommitteeName());
                }
                String rawPhone = respVO.getPhone();
                if (rawPhone != null) {
                    String encryptedPhone = desensitizeEncrypt(rawPhone);
                    respVO.setPhone(encryptedPhone);
                }
                // 查询头像
                if (respVO.getId() != null) {
                    // 需要TraineeDO的userId，需找到对应TraineeDO
                    TraineeDO traineeDO = traineeDOList.stream().filter(t -> t.getId().equals(respVO.getId())).findFirst().orElse(null);
                    if (traineeDO != null && traineeDO.getPhoto() != null) {
                        respVO.setAvatar(traineeDO.getPhoto());
                    }
                }
            });
            appTraineeGroupRespVOList.addAll(appTraineeGroupRespVOS);
        });
        //未分组学员
        List<TraineeDO> ungroupedList = traineeList.stream().filter(traineeDO -> UN_GROUPED_ID.equals(traineeDO.getGroupId())).collect(Collectors.toList());
        List<AppTraineeGroupRespVO> lastVOS = TraineeConvert.INSTANCE.convertToAppTraineeGroupRespVOList(ungroupedList);
        lastVOS.forEach(respVO -> {
            respVO.setGroupName(UN_GROUPED_NAME);
            ClassCommitteeDO classCommitteeDO = classCommitteeMap.get(respVO.getClassCommitteeId());
            if(classCommitteeDO != null){
                respVO.setClassCommitteeName(classCommitteeDO.getClassCommitteeName());
            }
            String rawPhone = respVO.getPhone();
            if (rawPhone != null) {
                String encryptedPhone = desensitizeEncrypt(rawPhone);
                respVO.setPhone(encryptedPhone);
            }
            // 查询头像
            if (respVO.getId() != null) {
                TraineeDO traineeDO = ungroupedList.stream().filter(t -> t.getId().equals(respVO.getId())).findFirst().orElse(null);
                if (traineeDO != null && traineeDO.getPhoto() != null) {
                    respVO.setAvatar(traineeDO.getPhoto());
                }
            }
        });
        appTraineeGroupRespVOList.addAll(lastVOS);
        return appTraineeGroupRespVOList;
    }

    @Override
    public List<Map<String, String>> getGroupList(Long classId) {

        //根据班级id获取小组
        List<TraineeGroupDO> traineeGroupDOList = this.lambdaQuery()
                .eq(TraineeGroupDO::getClassId, classId)
                .orderByAsc(TraineeGroupDO::getSort)
                .orderByAsc(TraineeGroupDO::getGroupName)
                .list();
        List<Map<String, String>> list = traineeGroupDOList.stream().map(traineeGroupDO -> {
            Map<String, String> map = new HashMap<>();
            map.put("value", traineeGroupDO.getId().toString());
            map.put("label", traineeGroupDO.getGroupName());
            return map;
        }).collect(Collectors.toList());
        Map<String,String> map = new HashMap<>();
        map.put("value","0");
        map.put("label","未分组");
        list.add(map);

        return list;

    }

    @Override
    public TraineeGroupDO getGroupById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public Boolean autoCreateGroup(Long classId, Long number) {
        List<TraineeGroupDO> existingGroups = this.lambdaQuery().eq(TraineeGroupDO::getClassId, classId).list();
        Set<String> existingGroupNames = existingGroups.stream()
                .map(TraineeGroupDO::getGroupName)
                .collect(Collectors.toSet());

        int sort = 1;
        boolean flag = false;
        //遍历existingGroupNames，拿到最大的小组序号
        for (String existingGroupName : existingGroupNames) {

            try {
                if (existingGroupName.startsWith("第") && existingGroupName.endsWith("小组")) {
                    String groupNum = existingGroupName.replace("第", "").replace("小组", "");
                    int groupSort = NumberConverter.chineseToArabic(groupNum);
                    if (groupSort >= sort) {
                        sort = groupSort;
                        flag = true;
                    }
                }
            }catch (Exception e){

            }
        }


        Map<Integer, String> chineseNumbers = new HashMap<>();
        chineseNumbers.put(0, "零");
        chineseNumbers.put(1, "一");
        chineseNumbers.put(2, "二");
        chineseNumbers.put(3, "三");
        chineseNumbers.put(4, "四");
        chineseNumbers.put(5, "五");
        chineseNumbers.put(6, "六");
        chineseNumbers.put(7, "七");
        chineseNumbers.put(8, "八");
        chineseNumbers.put(9, "九");

        //2 2  3 4

        long l = sort + number;
        if (sort!= 1 || flag){
            sort++;
        }else {
            l--;
        }
        Integer maxSort = baseMapper.getMaxSort(classId);
        for (int i = sort; i <= l; i++) {
//            String chineseI = convertToChineseNumber(i, chineseNumbers);
            String baseGroupName = "第" + NumberConverter.arabicToChinese(i) + "小组";
            String uniqueGroupName = baseGroupName;
            int suffix = 1;

            // 检查是否已经存在该小组名，如果存在则添加后缀
            while (existingGroupNames.contains(uniqueGroupName)) {
                uniqueGroupName = baseGroupName + "(" + convertToChineseNumber(suffix, chineseNumbers) + ")";
                suffix++;
            }

            TraineeGroupDO traineeGroupDO = new TraineeGroupDO();
            traineeGroupDO.setGroupName(uniqueGroupName);
            traineeGroupDO.setClassId(classId);
            traineeGroupDO.setSort(++maxSort);
            traineeGroupDO.setTenantId(SecurityFrameworkUtils.getTenantId());
            this.save(traineeGroupDO);

            // 更新已存在的小组名集合
            existingGroupNames.add(uniqueGroupName);
        }
        // 假设所有小组都成功创建
        return true;
    }

    @Override
    public Boolean getTrainee(Long groupId) {
        LambdaQueryWrapper<TraineeDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TraineeDO::getGroupId,groupId);
        List<TraineeDO> traineeDOList = traineeServiceImpl.list(wrapper);
        if (!traineeDOList.isEmpty()){
            return true;
        }
        return false;
    }

    /**
     * 检查指定班级ID是否存在对应的学员组。
     *
     * @param classId 班级ID，用于查询对应的学员组
     * @return 如果存在与指定班级ID关联的学员组，则返回true；否则返回false。如果classId为null，则直接返回false。
     */
    @Override
    public Boolean hasGroup(Long classId) {
        if (classId == null) {
            return false;
        }
//        LambdaQueryWrapper<TraineeGroupDO> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(TraineeGroupDO::getClassId, classId);

        LambdaQueryWrapper<TraineeDO> wrapper1 = new LambdaQueryWrapper<>();

        wrapper1.eq(TraineeDO::getClassId,classId);
        wrapper1.eq(TraineeDO::getGroupId,0);

        Long count = traineeMapper.selectCount(wrapper1);

        return count == 0;
    }


    private String convertToChineseNumber(int number, Map<Integer, String> chineseNumbers) {
        if (number == 10) {
            return "十"; // 特殊处理10，因为十位和个位需要分开处理
        }
        StringBuilder chineseNumber = new StringBuilder();
        boolean needZero = false; // 标记是否需要补零（例如，数字3应该转换为“三”，而不是“0三”）

        // 处理数字的各个位
        List<Integer> digits = new ArrayList<>();
        while (number > 0) {
            digits.add(0, number % 10); // 将数字的每一位添加到列表的开头
            number /= 10;
        }

        for (int digit : digits) {
            if (digit == 0) {
                if (needZero || (digits.size() > 1 && !chineseNumber.toString().isEmpty())) {
                    chineseNumber.insert(0, chineseNumbers.get(digit)); // 在需要时插入“零”
                }
            } else {
                chineseNumber.insert(0, chineseNumbers.get(digit)); // 插入中文数字
                needZero = true; // 标记已经插入过非零数字，后续不需要再补零
            }
        }

        // 特殊处理：如果数字是1-9之间的单个数字，则直接返回对应的中文数字，不需要“零”或其他前缀
        if (chineseNumber.length() == 1 && chineseNumber.charAt(0) != '零') {
            return chineseNumber.toString();
        }

        // 去除开头的“零”（如果有的话）
        while (chineseNumber.length() > 1 && chineseNumber.charAt(0) == '零') {
            chineseNumber.deleteCharAt(0);
        }

        // 如果转换后的字符串为空（例如，输入数字为0），则返回“零”
        return chineseNumber.length() == 0 ? "零" : chineseNumber.toString();
    }

}
