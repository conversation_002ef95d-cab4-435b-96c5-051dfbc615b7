package com.unicom.swdx.module.edu.controller.admin.leavereport.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@Schema(description = "管理后台 - 离校申请查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeaveReportQueryReqVO extends PageParam {

    @Schema(description = "离校报备名称，模糊匹配", example = "第一次离校申请")
    private String name;

    @Schema(description = "放假时间(单日)，查询落在放假时间区间内的记录", example = "2023-05-15")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate holidayDate;

    @Schema(description = "班级名称，模糊匹配", example = "软件开发1班")
    private String className;

    @Schema(description = "班主任姓名，模糊匹配", example = "张老师")
    private String teacherName;

    @Schema(description = "状态，参见 枚举类 0-过期 1-进行中", example = "1")
    private Integer status;

    @Schema(description = "校区", example = "1")
    private Integer campus;

    @Schema(description = "多选id（导出时使用）")
    private List<Long> ids;

    @Schema(description = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;

    @Schema(description = "班级状态筛选，2-开班中，3-已结业，4-未开始，为空则查询所有", example = "2")
    private Integer classStatus;
}
