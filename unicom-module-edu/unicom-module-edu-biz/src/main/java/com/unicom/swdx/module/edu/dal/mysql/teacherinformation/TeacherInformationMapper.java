package com.unicom.swdx.module.edu.dal.mysql.teacherinformation;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Mapper
public interface TeacherInformationMapper extends BaseMapperX<TeacherInformationDO> {
    List<TeacherInformationRespVO> selectPageByPageVO(IPage<TeacherInformationRespVO> page, @Param("reqVO") TeacherInformationPageReqVO reqVO);

    List<TeacherInformationExcelVO> selectExportList(@Param("reqVO") TeacherInformationExportReqVO exportVO);

    List<TeacherInformationRespVO> selectListByIds(@Param("ids") List<Long> ids);

    List<TeacherInformationDO> selectTeachersDeleted(@Param("tenantId") Long tenantId);

    default List<TeacherInformationDO> selectListByTeanatId(Long tenantId) {
        return selectList(new LambdaQueryWrapperX<TeacherInformationDO>()
                .eq(TeacherInformationDO::getSource, 0)
                .eqIfPresent(TeacherInformationDO::getTenantId, tenantId)
                .isNotNull(TeacherInformationDO::getHrId)
        );
    }


    /**
     * 根据选修课发布上课时间段获取空闲下拉教师数据
     *
     * @param classStartDateTime 上课开始时间
     * @param classEndDateTime   上课结束时间
     * @param excludeReleaseId   排除的选修课发布id
     * @param courseId           选修课id
     * @return 教师信息
     */
    List<TeacherInformationSimpleRespVO> listForElectiveRelease(@Param("classStartDateTime") LocalDateTime classStartDateTime,
                                                                @Param("classEndDateTime") LocalDateTime classEndDateTime,
                                                                @Param("excludeReleaseId") Long excludeReleaseId,
                                                                @Param("excludeClassCourseId") Long excludeClassCourseId,
                                                                @Param("courseId") Long courseId);

    default TeacherInformationDO selectByUserId(Long userId){
        return selectOne(new LambdaQueryWrapper<TeacherInformationDO>()
                .eq(TeacherInformationDO::getUserId, userId)
                .last("limit 1"));
    }

    /**
     * 获取教师授课记录
     *
     * @param page          分页对象
     * @param teacherId     教师ID
     * @param className     班次名称
     * @param courseName    课程名称
     * @param beginTime     开始时间
     * @param endTime       结束时间
     * @param educateFormId 教学方式ID
     * @return 授课记录列表
     */
    List<TeachingRecordRespVO> getTeachingRecords(
            @Param("page") IPage<?> page,
            @Param("teacherId") Long teacherId,
            @Param("className") String className,
            @Param("courseName") String courseName,
            @Param("beginTime") LocalDateTime beginTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("educateFormId") Long educateFormId);

    default List<TeacherInformationDO> selectListByUserIdList(List<Long> userIds){
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapper<TeacherInformationDO>()
                .in(TeacherInformationDO::getUserId, userIds));
    }

    default List<TeacherInformationDO> selectDOListByIds(List<Long> ids){
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapper<TeacherInformationDO>()
                .in(TeacherInformationDO::getId, ids)
                .orderByDesc(TeacherInformationDO::getCreateTime));
    }


    /**
     * 获取教师姓名列表
     *
     * @return 返回包含教师姓名的列表
     */
    default List<String> selectNameList(){
        return selectList(new LambdaQueryWrapperX<TeacherInformationDO>()
                .select(TeacherInformationDO::getName)
                .orderByAsc(TeacherInformationDO::getName))
                .stream().map(TeacherInformationDO::getName).collect(Collectors.toList());
    }

    /**
     * 根据教师姓名列表查询对应的教师信息列表
     *
     * @param teacherNameList 教师姓名列表
     * @return 教师信息列表
     */
    default List<TeacherInformationDO> selectListByNames(List<String> teacherNameList){
        if (CollectionUtils.isEmpty(teacherNameList)) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapper<TeacherInformationDO>()
                .in(TeacherInformationDO::getName, teacherNameList));
    }

    default TeacherInformationDO selectBySystemId(Long teacherSystemId){
        return selectOne(new LambdaQueryWrapperX<TeacherInformationDO>()
                .eq(TeacherInformationDO::getSystemId, teacherSystemId)
                .orderByDesc(TeacherInformationDO::getUpdateTime)
                .last("limit 1"));
    }

    default List<TeacherInformationDO> selectBySystemIdList(List<Long> teacherSystemIdList){
        if (CollectionUtils.isEmpty(teacherSystemIdList)) {
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapperX<TeacherInformationDO>()
                .in(TeacherInformationDO::getSystemId, teacherSystemIdList));
    }
}
