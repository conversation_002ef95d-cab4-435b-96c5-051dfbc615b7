package com.unicom.swdx.module.edu.service.classclockcalendar;

import java.util.*;
import javax.validation.*;
import com.unicom.swdx.module.edu.controller.admin.classclockcalendar.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.classclockcalendar.ClassClockCalendarDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

/**
 * 班级考勤日历 Service 接口
 *
 * <AUTHOR>
 */
public interface ClassClockCalendarService {

    /**
     * 创建班级考勤日历
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createClassClockCalendar(@Valid ClassClockCalendarCreateReqVO createReqVO);

    /**
     * 更新班级考勤日历
     *
     * @param updateReqVO 更新信息
     */
    void updateClassClockCalendar(@Valid ClassClockCalendarUpdateReqVO updateReqVO);

    /**
     * 批量更新班级考勤日历
     *
     * @param updateReqVO 更新信息
     */
    void updateClassClockCalendarBatch(@Valid ClassClockCalendarParamsVO updateReqVO);

    /**
     * 删除对应班级考勤日历
     *
     * @param classId 编号
     */
    void deleteClassClockCalendar(Long classId);

    /**
     * 获得班级考勤日历
     *
     * @param id 编号
     * @return 班级考勤日历
     */
    ClassClockCalendarDO getClassClockCalendar(Integer id);

    /**
     * 获得班级考勤日历列表
     *
     * @param ids 编号
     * @return 班级考勤日历列表
     */
//    List<ClassClockCalendarDO> getClassClockCalendarList(Collection<Integer> ids);

    /**
     * 获得班级考勤日历分页
     *
     * @param pageReqVO 分页查询
     * @return 班级考勤日历分页
     */
    List<ClassClockCalendarDO> getClassClockCalendarPage(ClassClockCalendarListReqVO pageReqVO);


}
