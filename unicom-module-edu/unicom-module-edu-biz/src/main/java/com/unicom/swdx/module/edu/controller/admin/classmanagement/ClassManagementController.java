package com.unicom.swdx.module.edu.controller.admin.classmanagement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.validation.ExcelValidator;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.excelimporthandler.*;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.*;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.controller.admin.trainee.excelimporthandler.CustomHeaderWriteHandler;
import com.unicom.swdx.module.edu.convert.classmanagement.ClassManagementConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.Normalizer;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.unicom.swdx.framework.common.enums.ErrorCodeConstants.EXPORT_FAILED;
import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.IMPORT;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.CLASS_MANAGEMENT_HEAD_NAME_ERROR;

@Api(tags = "管理后台 - 班次管理")
@RestController
@RequestMapping("/edu/class-management")
@Validated
public class ClassManagementController {

    @Resource
    private ClassManagementService classManagementService;
    @Resource
    private ClassManagementMapper classManagementMapper;

    private static final String EXPECTED_TEMPLATE_NAME = "班级导入模版";

    @PostMapping("/create")
    @ApiOperation("新增")
    @PreAuthorize("@ss.hasPermission('edu:class-management:create')")
    public CommonResult<Long> createClassManagement(@Valid @RequestBody ClassManagementCreateReqVO createReqVO) {
        return success(classManagementService.createClassManagement(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("编辑")
    @PreAuthorize("@ss.hasPermission('edu:class-management:update')")
    public CommonResult<Boolean> updateClassManagement(@Valid @RequestBody ClassManagementUpdateReqVO updateReqVO) {
        classManagementService.updateClassManagement(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:class-management:delete')")
    public CommonResult<Boolean> deleteClassManagement(@RequestParam("id") Long id) {
        classManagementService.deleteClassManagement(id);
        return success(true);
    }

    @PostMapping("/delete-batch")
    @ApiOperation("批量删除")
    @PreAuthorize("@ss.hasPermission('edu:class-management:delete')")
    public CommonResult<Boolean> deleteClassManagerDeleteBatch(@Valid @RequestBody ClassManagerDeleteVO classManagerDeleteVO) {
        classManagementService.deleteClassManagerDeleteBatch(classManagerDeleteVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得单个数据列表")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PermitAll
    public CommonResult<ClassManagementRespVO> getClassManagement(@RequestParam("id") Long id) {
        ClassManagementDO classManagement = classManagementService.getClassManagement(id);
        return success(ClassManagementConvert.INSTANCE.convert(classManagement));
    }

    @GetMapping("/list")
    @ApiOperation("获得多个数据列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<ClassManagementRespVO>> getClassManagementList(@RequestParam("ids") Collection<Integer> ids) {
        List<ClassManagementDO> list = classManagementService.getClassManagementList(ids);
        return success(ClassManagementConvert.INSTANCE.convertList(list));
    }


    //给业中单独调的接口
    @GetMapping("/listSingle")
    @ApiOperation("获得多个数据列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PermitAll
    @TenantIgnore
    public CommonResult<List<ClassManagementRespVO>> getClassManagementListSingle(@RequestParam("ids") Collection<Integer> ids) {
        List<ClassManagementDO> list = classManagementService.getClassManagementList(ids);
        return success(ClassManagementConvert.INSTANCE.convertList(list));
    }



    @GetMapping("/page")
    @ApiOperation("分页列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<PageResult<ClassManagementRespVO>> getClassManagementPage(HttpServletRequest request, @Valid ClassManagementPageReqVO pageVO) {
        PageResult<ClassManagementRespVO> pageResult = classManagementService.getClassManagementPage(request,pageVO);
//        PageResult<ClassManagementRespVO> classManagementRespVOPageResult = ClassManagementConvert.INSTANCE.convertPage(pageResult);
        return success(pageResult);
    }

    @PostMapping("/simpleListForBusinessCenter")
    @ApiOperation("业中首页-仪表盘-考勤三率班级下钻")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    @PermitAll
    @TenantIgnore
    public CommonResult<List<ClassManagementSimpleForBusinessCenterRespVO>> simpleListForBusinessCenter(@Valid @RequestBody ClassManagementSimpleForBusinessCenterReqVO reqVO) {
        List<ClassManagementSimpleForBusinessCenterRespVO> resList = classManagementService.simpleListForBusinessCenter(reqVO);
        return success(resList);
    }

    @PostMapping("/pageForElectiveReleaseCreate")
    @ApiOperation("创建选修课发布-根据上课时间段选择班级范围分页列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<PageResult<ClassManagementElectiveReleaseRespVO>> getPageForElectiveReleaseCreate(@Valid @RequestBody ClassManagementElectiveReleasePageReqVO reqVO) {
        PageResult<ClassManagementElectiveReleaseRespVO> pageResult = classManagementService.getPageForElectiveReleaseCreate(reqVO);
        return success(pageResult);
    }

    @PostMapping("/update-sort")
    @ApiOperation("更新排序")
    @PreAuthorize("@ss.hasPermission('edu:class-management:update')")
    public CommonResult<Boolean> updateClassManagementSort(@RequestParam("id") Long id, @RequestParam("sort") Integer sort) {
        classManagementService.updateClassManagementSort(id, sort);
        return success(true);
    }

    @PostMapping("/publish-batch")
    @ApiOperation("批量发布")
    @PreAuthorize("@ss.hasPermission('edu:class-management:update')")
    public CommonResult<Boolean> updateClassManagementPublish(@Valid @RequestBody ClassManagerDeleteVO classManagerDeleteVO) {
        classManagementService.updateClassManagementPublish(classManagerDeleteVO);
        return success(true);
    }

    @GetMapping("/export-excel")
    @ApiOperation(value = "班级管理导出-增加办班类型")
    @PreAuthorize("@ss.hasPermission('edu:class-management:export')")
    public void exportClassManagementExcel(ClassManagementExportParamsVO reqVO, HttpServletResponse response) throws IOException {
        List<ClassManagementExcelVO> list = classManagementService.getClassManagementInfoList(reqVO);
        List<ClassManagementExportExcelVO> list1 = ClassManagementConvert.INSTANCE.convertList1(list);

        ExcelUtils.writeByIncludeColumnIndexes(response, "班级列表.xls",
                "数据", ClassManagementExportExcelVO.class, list1, reqVO.getIncludeColumnIndexes());
    }

    @GetMapping("/class-export-excel")
    @ApiOperation(value = "班级管理导出")
    @PreAuthorize("@ss.hasPermission('edu:class-management:export')")
    public void exportClassManagementExcels(ClassManagementExportParamsVO reqVO, HttpServletResponse response) throws IOException {
        List<ClassManagementExcelVO> list = classManagementService.getClassManagementInfoList(reqVO);

        ExcelUtils.writeByIncludeColumnIndexes(response, "班级列表.xls",
                "数据", ClassManagementExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    @GetMapping("/get-import-templates")
    @ApiOperation("获得导入班级信息模板")
    @TenantIgnore
    public void importTemplate(HttpServletResponse response) throws IOException {

        // 输出
        String filename = "班次导入模板.xls";
        String sheetName = "班级信息列表";
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");

        //省委和市县的下载模板不一样，所以需要根据租户id分别返回下载模板
        Long tenantId = SecurityFrameworkUtils.getTenantId();

        if (Objects.equals(25L, tenantId) || Objects.equals(230L, tenantId)) {
            List<ClassInfoSourceImportExcelVO> classInfoImportExcelVOS = buildClassInfoSourceImportData();
            try {
                EasyExcel.write(response.getOutputStream(), ClassInfoSourceImportExcelVO.class)
                        .autoCloseStream(false)
                        .registerWriteHandler(new GetCampusSheetWriteHandler(classManagementMapper, 11))
                        .registerWriteHandler(new GetClassSourceSheetWriteHandler(classManagementMapper))
                        .registerWriteHandler(new GetClassAttributeSheetWriteHandler(classManagementMapper, 4))
                        .registerWriteHandler(new GetClassTypeSheetWriteHandler(classManagementMapper, 3))
                        .registerWriteHandler(new GetEvaluateSheetWriteHandler(18))
                        .registerWriteHandler(new GetPaymentSheetWriteHandler(17))
                        .registerWriteHandler(new GetTurnSheetWriteHandler(10))
                        .registerWriteHandler(new GetLearningSystemUnitSheetWriteHandler(8))
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet(sheetName)
                        .doWrite(classInfoImportExcelVOS);
            } catch (IOException e) {
                response.setContentType("application/json;charset=UTF-8");
                throw exception(EXPORT_FAILED);
            }
        } else {
            List<ClassInfoImportExcelVO> classInfoImportExcelVOS = buildClassInfoImportData();
            try {
                EasyExcel.write(response.getOutputStream(), ClassInfoImportExcelVO.class)
                        .autoCloseStream(false)
                        .registerWriteHandler(new GetCampusSheetWriteHandler(classManagementMapper, 10))
                        .registerWriteHandler(new GetClassAttributeSheetWriteHandler(classManagementMapper, 3))
                        .registerWriteHandler(new GetClassTypeSheetWriteHandler(classManagementMapper, 2))
                        .registerWriteHandler(new GetEvaluateSheetWriteHandler(17))
                        .registerWriteHandler(new GetPaymentSheetWriteHandler(16))
                        .registerWriteHandler(new GetTurnSheetWriteHandler(9))
                        .registerWriteHandler(new GetLearningSystemUnitSheetWriteHandler(7))
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet(sheetName)
                        .doWrite(classInfoImportExcelVOS);
            } catch (IOException e) {
                response.setContentType("application/json;charset=UTF-8");
                throw exception(EXPORT_FAILED);
            }
        }

    }


    @PostMapping("/import")
    @ApiOperation("导入班级信息")
    @OperateLog(type = IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)
    })
    @PreAuthorize("@ss.hasPermission('edu:class-management:import')")
    public CommonResult<ClassManagementImportRespVO> importExcel(@RequestParam("file") MultipartFile file) throws Exception {

        // 获取文件名并去除路径和扩展名
        String originalFileName = file.getOriginalFilename();

        if (originalFileName == null || originalFileName.isEmpty()) {
            throw new IllegalArgumentException("文件名为空");
        }

        // 获取文件名（不带路径和扩展名）
        String fileNameWithoutExtension = new File(originalFileName).getName().substring(0, originalFileName.lastIndexOf('.')).trim();

        // 标准化处理
        fileNameWithoutExtension = Normalizer.normalize(fileNameWithoutExtension, Normalizer.Form.NFC);


        List<ClassInfoImportStrExcelVO> list = null;
        // 校验表头信息
        try{
            if (Objects.equals(25L, SecurityFrameworkUtils.getTenantId()) || Objects.equals(230L, SecurityFrameworkUtils.getTenantId())){
                list = ExcelUtils.readFirstSheetAndCheckHead(file, ClassInfoImportStrExcelVO.class);
            }else {
                List<ClassInfoImportExcelVO> list1 = ExcelUtils.readFirstSheetAndCheckHead(file, ClassInfoImportExcelVO.class);
                list = ClassManagementConvert.INSTANCE.convertList3(list1);
            }

        }catch (Exception e){
            throw exception(CLASS_MANAGEMENT_HEAD_NAME_ERROR);
        }

        // 验证 Excel 数据
        ExcelValidator.valid(list,1);

        // 调用服务层，导入班次信息
        return success(classManagementService.importClassInfo(list));
    }

    @GetMapping("/page-applet")
    @ApiOperation("小程序班级切换")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<ClassManagementDO>> getClassManagementPageApplet(@Valid ClassManagementPageReqVO pageVO) {

        List<ClassManagementDO> result = classManagementService.getClassManagementPageApplet(pageVO);
        return success(result);
    }


    @GetMapping("/getClass")
    @ApiOperation("根据开班状态和教师id获取班级列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<ClassInfoRespVO>> getClassByStatus(@RequestParam("status") Integer status,
                                                                @RequestParam(value = "teacherId", required = false) Long teacherId,
                                                                @RequestParam(value = "clockIn", required = false) Integer clockIn) {
        List<ClassInfoRespVO> result = classManagementService.getClassPageByStatus(status,teacherId, clockIn);
        return success(result);
    }

    @GetMapping("/get-default-class")
    @ApiOperation("根据教师id获取默认班级")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<String>> getDefaultClassByTeacherId(@RequestParam(value = "teacherId" ,required = false) Long teacherId,
                                                                 @RequestParam(value = "clockIn", required = false) Integer clockIn) {
        List<String> result = classManagementService.getDefaultClassByTeacherId(teacherId, clockIn);
        if (Objects.isNull(result)){
            return success(Collections.emptyList());
        }
        return success(result);
    }


    @PostMapping("/clocking-in-rule")
    @ApiOperation("班级考勤规则设置")
    @PreAuthorize("@ss.hasPermission('edu:class-management:update')")
    public CommonResult<Boolean> updateClassClockingInRule(@Valid @RequestBody ClassClockingInUpdateReqVO clockingInUpdateReqVO) {
        classManagementService.updateClassClockingInRule(clockingInUpdateReqVO);
        return success(true);
    }

    @PostMapping("/class-sign-up")
    @ApiOperation("名额分配")
    @PreAuthorize("@ss.hasPermission('edu:class-management:create')")
    public CommonResult<Integer> createClassSignUp(@Valid @RequestBody List<ClassSignUpUnitBaseVO> classSignUpUnitBaseVO) {
        return success(classManagementService.createClassSignUp(classSignUpUnitBaseVO));
    }

    @GetMapping("/getSimple")
    @ApiOperation("获得班级信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<ClassSimpleInfo> getSimpleClassInfo(@RequestParam("id") Long id) {
        ClassSimpleInfo classInfo = classManagementService.getSimpleClassInfo(id);
        return success(classInfo);
    }

    @GetMapping("/getSimpleList")
    @ApiOperation("获得班级信息列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<ClassSimpleInfo>> getSimpleClassInfo(@RequestParam(value = "teacherId", required = false) Long teacherId,
                                                            @RequestParam(value = "className", required = false) String className) {
        List<ClassSimpleInfo> list = classManagementService.getSimpleClassInfoList(teacherId, className);
        return success(list);
    }

    @GetMapping("/classHasCourseList")
    @ApiOperation("首页-班次课表-列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<PageResult<ClassHaveCourseVO>> getClassHasCourseList(@RequestParam(value = "className", required = false) String className , PageParam pageVO) {
        List<ClassHaveCourseVO> list = classManagementService.getClassHasCourseList(className);
//        list = list == null ? new ArrayList<>() : list;
        // 手动计算分页
        int total = list.size();
        int pageNo = pageVO.getPageNo();
        int pageSize = pageVO.getPageSize();
        // 计算分页起始索引和结束索引
        int fromIndex = Math.min((pageNo - 1) * pageSize, total);
        int toIndex = Math.min(fromIndex + pageSize, total);
        // 获取分页后的子列表
        List<ClassHaveCourseVO> pagedList = list.subList(fromIndex, toIndex);
        return success(new PageResult<>(pagedList, (long)total));
    }

    @GetMapping("/stat")
    @ApiOperation("首页-调训统计")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<StatRespVO> getStat() {
        StatRespVO result = classManagementService.getStat();
        return success(result);
    }

    @PostMapping("/completion-template")
    @ApiOperation("选择结业模版")
    @PreAuthorize("@ss.hasPermission('edu:class-management:update')")
    public CommonResult<Boolean> updateCompletionTemplate(@Valid @RequestBody ClassCompletionUpdateReqVO classCompletionUpdateReqVO) {
        classManagementService.updateCompletionTemplate(classCompletionUpdateReqVO);
        return success(true);
    }

    @GetMapping("/getClassClockAll")
    @ApiOperation("根据开班状态和教师id获取班级列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<ClassManagementDO>> getClassClockAll(@RequestParam(value = "className", required = false) String className) {
        List<ClassInfoRespVO> list1 = classManagementService.getClassPageByStatus(2,null, 1);
        List<ClassInfoRespVO> list2 = classManagementService.getClassPageByStatus(4,null, 1);
        List<ClassInfoRespVO> mergedList = Stream.concat(list1.stream(), list2.stream())
                .collect(Collectors.toList());
        if(className!=null){
            mergedList=mergedList.stream().filter(s -> s.getName().contains(className)).collect(Collectors.toList());
        }
        List<Long> ids = mergedList.stream().map(ClassInfoRespVO::getId).collect(Collectors.toList());
        List<Integer> integers = ids.stream().map(Long::intValue).collect(Collectors.toList());
        List<ClassManagementDO> list=new ArrayList<>();
        if(integers.size()>0){
            list = classManagementService.getClassManagementList(integers);
            list=list.stream().sorted(Comparator.comparing(ClassManagementDO::getCreateTime).reversed()).collect(Collectors.toList());
        }

        return success(list);
    }

    @GetMapping("/getClassByUnitId")
    @ApiOperation("根据单位id获取班级列表")
    public CommonResult<List<ClassManagementDO>> updateCompletionTemplate(@RequestParam(value = "unitId") Integer unitId) {
        return success(classManagementService.getClassByUnitId(unitId));
    }


    @GetMapping("/get-unit-import-templates")
    @ApiOperation("获得名额分配导入模板")
    @TenantIgnore
    public void unitImportTemplate(HttpServletResponse response) throws IOException {
        // 输出
        String filename = "班次导入模板.xls";
        String sheetName = "班级信息列表";
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        List<UnitImportExcelVO> unitImportExcelVOS = new ArrayList<>();
        UnitImportExcelVO classInfoImportExcelVO = new UnitImportExcelVO();
        classInfoImportExcelVO.setUnitName("示例：测试单位名称（勿删）");
        classInfoImportExcelVO.setNumber("99");

        unitImportExcelVOS.add(classInfoImportExcelVO);
        // 输出 Excel
        try {
            EasyExcel.write(response.getOutputStream(), UnitImportExcelVO.class)
                    .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                    .registerWriteHandler(new CustomHeaderWriteHandler()) // 基于 column 长度，自动适配。最大 255 宽度
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 基于 column 长度，自动适配。最大 255 宽度
                    .sheet(sheetName).doWrite(unitImportExcelVOS);
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }


    @PostMapping("/check-unit-import")
    @ApiOperation("导入班级名额分配信息")
    @OperateLog(type = IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)
    })
    @PreAuthorize("@ss.hasPermission('edu:class-management:import')")
    public CommonResult<List<String>> checkUnitImportExcel(@RequestParam("file") MultipartFile file,@RequestParam("classId")Long classId){

        // 获取文件名并去除路径和扩展名
        String originalFileName = file.getOriginalFilename();

        if (originalFileName == null || originalFileName.isEmpty()) {
            throw new IllegalArgumentException("文件名为空");
        }

        // 获取文件名（不带路径和扩展名）
        String fileNameWithoutExtension = new File(originalFileName).getName().substring(0, originalFileName.lastIndexOf('.')).trim();

        // 标准化处理
        fileNameWithoutExtension = Normalizer.normalize(fileNameWithoutExtension, Normalizer.Form.NFC);


        List<UnitImportExcelVO> list = null;
        // 校验表头信息
        try{
            list = ExcelUtils.readFirstSheetAndCheckHead(file, UnitImportExcelVO.class);
        }catch (Exception e){
            throw exception(CLASS_MANAGEMENT_HEAD_NAME_ERROR);
        }

        // 验证 Excel 数据
        ExcelValidator.valid(list,1);


        // 调用服务层，导入班次信息
        return success(classManagementService.checkUnitImportClassInfo(list,classId));
    }

    @PostMapping("/unit-import")
    @ApiOperation("导入班级名额分配信息")
    @OperateLog(type = IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)
    })
    @PreAuthorize("@ss.hasPermission('edu:class-management:import')")
    public CommonResult<ClassManagementImportRespVO> unitImportExcel(@RequestParam("file") MultipartFile file,@RequestParam("classId")Long classId){

        // 获取文件名并去除路径和扩展名
        String originalFileName = file.getOriginalFilename();

        if (originalFileName == null || originalFileName.isEmpty()) {
            throw new IllegalArgumentException("文件名为空");
        }

        // 获取文件名（不带路径和扩展名）
        String fileNameWithoutExtension = new File(originalFileName).getName().substring(0, originalFileName.lastIndexOf('.')).trim();

        // 标准化处理
        fileNameWithoutExtension = Normalizer.normalize(fileNameWithoutExtension, Normalizer.Form.NFC);


        List<UnitImportExcelVO> list = null;
        // 校验表头信息
        try{
            list = ExcelUtils.readFirstSheetAndCheckHead(file, UnitImportExcelVO.class);
        }catch (Exception e){
            throw exception(CLASS_MANAGEMENT_HEAD_NAME_ERROR);
        }

        // 验证 Excel 数据
        ExcelValidator.valid(list,1);

        // 调用服务层，导入班次信息
        return success(classManagementService.unitImportClassInfo(list,classId));
    }


    private List<ClassInfoSourceImportExcelVO> buildClassInfoSourceImportData() {
        ClassInfoSourceImportExcelVO vo = new ClassInfoSourceImportExcelVO();
        vo.setISort("示例：1");
        vo.setClassName("测试班级");
        vo.setClassSource("省委组织部");
        vo.setClassTypeDictId("中青年干部培训班");
        vo.setClassAttribute("主体班");
        vo.setYear("2024");
        vo.setSemester("下学期");
        vo.setLearningSystem("4");
        vo.setLearningSystemUnit("天");
        vo.setTrainingObject("中青年干部");
        vo.setPeopleNumber("30");
        vo.setTurn("第二轮");
        vo.setCampus("校本部");
        vo.setReportingTime("2024/12/17");
        vo.setClassOpenTime("2024/12/28");
        vo.setCompletionTime("2024/12/29");
        vo.setRegistrationStartTime("2024/12/30");
        vo.setRegistrationEndTime("2024/12/31");
        vo.setPaymentReport("否");
        vo.setEvaluate("是");
        vo.setSort("1");
        vo.setRemark("备注说明");
        return Collections.singletonList(vo);
    }

    private List<ClassInfoImportExcelVO> buildClassInfoImportData() {
        ClassInfoImportExcelVO vo = new ClassInfoImportExcelVO();
        vo.setISort("示例：1");
        vo.setClassName("测试班级");
        vo.setClassTypeDictId("中青年干部培训班");
        vo.setClassAttribute("主体班");
        vo.setYear("2024");
        vo.setSemester("下学期");
        vo.setLearningSystem("4");
        vo.setLearningSystemUnit("天");
        vo.setTrainingObject("中青年干部");
        vo.setPeopleNumber("30");
        vo.setTurn("第二轮");
        vo.setCampus("校本部");
        vo.setReportingTime("2024/12/17");
        vo.setClassOpenTime("2024/12/28");
        vo.setCompletionTime("2024/12/29");
        vo.setRegistrationStartTime("2024/12/30");
        vo.setRegistrationEndTime("2024/12/31");
        vo.setPaymentReport("否");
        vo.setEvaluate("是");
        vo.setSort("1");
        vo.setRemark("备注说明");
        return Collections.singletonList(vo);
    }

}
