package com.unicom.swdx.module.edu.dal.mysql.plan;

import java.util.*;

import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.dal.dataobject.plan.PlanDO;
import org.apache.ibatis.annotations.Mapper;
import com.unicom.swdx.module.edu.controller.admin.plan.vo.*;


@Mapper
public interface PlanMapper extends BaseMapperX<PlanDO> {

    default PageResult<PlanDO> selectPage(PlanPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PlanDO>()
                .likeIfPresent(PlanDO::getName, reqVO.getName())
                .betweenIfPresent(PlanDO::getBeginDate, reqVO.getBeginDate())
                .betweenIfPresent(PlanDO::getEndDate, reqVO.getEndDate())
//                .eqIfPresent(PlanDO::getPeriod, reqVO.getPeriod())
//                .betweenIfPresent(PlanDO::getBeginTime, reqVO.getBeginTime())
//                .betweenIfPresent(PlanDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(PlanDO::getClassroomId, reqVO.getClassroomId())
                .eqIfPresent(PlanDO::getClassId, reqVO.getClassId())
                .inIfPresent(PlanDO::getClassId, reqVO.getClassIdList())
//                .eqIfPresent(PlanDO::getStatus,reqVO.getStatus())
                //按照创建时间正序排序
                .orderByAsc(PlanDO::getId));
    }

    default List<PlanDO> selectList(PlanExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PlanDO>()
                .likeIfPresent(PlanDO::getName, reqVO.getName())
                .betweenIfPresent(PlanDO::getBeginDate, reqVO.getBeginDate())
                .betweenIfPresent(PlanDO::getEndDate, reqVO.getEndDate())
//                .eqIfPresent(PlanDO::getPeriod, reqVO.getPeriod())
//                .betweenIfPresent(PlanDO::getBeginTime, reqVO.getBeginTime())
//                .betweenIfPresent(PlanDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(PlanDO::getClassroomId, reqVO.getClassroomId())
                .eqIfPresent(PlanDO::getClassId, reqVO.getClassId())
//                .eqIfPresent(PlanDO::getStatus,reqVO.getStatus())
                .orderByDesc(PlanDO::getId));
    }

}
