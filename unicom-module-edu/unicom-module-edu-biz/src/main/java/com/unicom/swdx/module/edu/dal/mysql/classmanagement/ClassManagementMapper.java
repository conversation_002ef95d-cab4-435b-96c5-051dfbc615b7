package com.unicom.swdx.module.edu.dal.mysql.classmanagement;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.mybatis.core.mapper.BaseMapperX;
import com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.TraineeReportExcelVO;
import com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.TraineeReportPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.TraineeReportPageRespVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.*;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassClockInDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.enums.clockininfo.TypeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.kafka.common.protocol.types.Field;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * EduClassManagement Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ClassManagementMapper extends BaseMapperX<ClassManagementDO> {

    Integer selectClassCount(@Param("year") Integer year);

    /**
     * 班级列表
     * @param buildPage
     * @param reqVO
     * @return
     */
    List<ClassManagementDO> selectPageList(IPage<ClassManagementDO> buildPage, @Param("reqVO") ClassManagementPageReqVO reqVO);
    /**
     * 班级列表
     * @param reqVO
     * @return
     */
    List<ClassManagementDO> selectClassManagementList( @Param("reqVO") ClassManagementPageReqVO reqVO);
    /**
     * 更新 排序
     * @param id sort
     * @return
     */
    Integer updateClassManagementSortById(@Param("id") Long id, @Param("sort") Integer sort);

    /**
     * 批量发布
     * @param id
     * @return
     */
    Integer updateClassManagementPublishById(@Param("id") Long id);
    /**
     * 批量撤销发布
     * @param id
     * @return
     */
    Integer updateClassManagementCancelPublishById(@Param("id") Long id);
    /**
     * 导出
     * @param reqVO
     * @return
     */
    List<ClassManagementDO> getClassManagementInfo(@Param("reqVO") ClassManagementExportParamsVO reqVO);
    /**
     * 导出
     * @param id
     * @return
     */
    String getDictLabelById(@Param("id") Integer id);
    /**
     *
     * @param label
     * @return
     */
    Integer getIdByDictLabel(@Param("label") String label, @Param("type") Integer type);

    Integer getIdByDictLabelcampus(@Param("label") String label, @Param("type") Integer type  , @Param("tenantId")  Long tenantId);
    /**
     *
     * @param id
     * @return
     */
    Integer getClassPeopleCount(@Param("id") Long id);
    /**
     *
     * @param id
     * @return
     */
    String getClassTeacherLeadInfo(@Param("id") Long id);
    /**
     *
     * @param
     * @return
     */
    List<ClassInfoImportDictLableExcelVO> getDictTypeByDictLabel(@Param("type") Integer type , @Param("tenantId")  Long tenantId);
    /**
     * 班级列表 小程序
     * @param reqVO
     * @return
     */
    List<ClassManagementDO> selectPageListApplet(@Param("reqVO") ClassManagementPageReqVO reqVO);

    /**
     *
     * @param id
     * @return
     */
    Long getTeacherId(@Param("id") Long id);

    /**
     * 获取当天报名的班级
     * @return 班级信息
     */
    List<ClassManagementDO> selectListByRegistrationStartTimeToday();

    default List<Long> getAllClass() {
        LambdaQueryWrapper<ClassManagementDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ClassManagementDO::getPublish, 1);
        wrapper.le(ClassManagementDO::getCompletionTime, LocalDateTime.now());
        List<ClassManagementDO> classList = selectList(wrapper);
        return classList.stream().map(ClassManagementDO::getId).collect(Collectors.toList());
    }

    default List<ClassManagementDO> selectPageListByStatus(Integer status, Set<Long> classIds) {

        if (CollUtil.isEmpty(classIds)){
            return new ArrayList<>();
        }

        LambdaQueryWrapper<ClassManagementDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ClassManagementDO::getPublish,1);

        if (status == 4) {
            //未开班
            wrapper.gt(ClassManagementDO::getClassOpenTime, LocalDateTime.now());
        } else if (status == 2) {
            //开班中
            wrapper.and(i -> i.le(ClassManagementDO::getClassOpenTime, LocalDateTime.now())
                    .gt(ClassManagementDO::getCompletionTime, LocalDateTime.now()));
        }else {
            //已结业
            wrapper.le(ClassManagementDO::getCompletionTime, LocalDateTime.now());
        }
        if (!classIds.isEmpty()) {
            wrapper.in(ClassManagementDO::getId,classIds);
        }
        wrapper.orderByDesc(ClassManagementDO::getClassNameCode);
        return selectList(wrapper);
    }
    List<Long> getClassIdListByTeacherId(@Param("teacherId") Long teacherId);

    /**
     * 插入班级的默认规则
     * @param classClockInDO
     * @return
     */
    void insertClassClockIn(@Param("reqVO") ClassClockInDO classClockInDO);
    /**
     * 删除班级表中对应的规则
     * @param
     * @return
     */
    void deleteClassClockById(@Param("id") Long id);
    /**
     * 更新班级考勤规则
     * @param reqVO
     * @return
     */
    void updateClassClockingInRule(@Param("reqVO") ClassClockingInUpdateReqVO reqVO);
    /**
     * 更新班级考勤中间表规则id
     * @param reqVO
     * @return
     */
    void updateClassClockingInRuleId(@Param("reqVO") ClassClockingInUpdateReqVO reqVO);
    /**
     * 获取到课考勤 ID
     * @param classId
     * @return
     */
    ClassRuleClockingInVO getClassClockInByAttendanceCheck(@Param("classId") Long classId);
    /**
     * 获取就餐考勤 ID
     * @param classId
     * @return
     */
    ClassRuleClockingInVO getClassClockInByMealAttendance(@Param("classId") Long classId);
    /**
     * 获取住宿考勤 ID
     * @param classId
     * @return
     */
    ClassRuleClockingInVO getClassClockInByCheckIn(@Param("classId") Long classId);

    /**
     * 获取班级启用规则集
     * @return
     */
    List<ClassRuleClockingInVO> getClassClockIn();

    ClassSimpleInfo selectSimpleClassInfo(@Param("id") Long id);

    List<ClassSimpleInfo> selectSimpleClassInfoList(@Param("teacherId") Long teacherId, @Param("className") String className);

    List<Long> getClassIdListByTeacherId1(@Param("teacherId") Long teacherId);

    List<ClassManagementElectiveReleaseRespVO> getPageForElectiveReleaseCreate(Page<ClassManagementElectiveReleaseRespVO> buildPage,
                                                                               @Param("reqVO") ClassManagementElectiveReleasePageReqVO reqVO);

    List<ClassManagementDO> getClassList();

    List<ClassManagementDO> getComplateClassList();

    /**
     * 更新班级结业模版
     * @param reqVO
     * @return
     */
    void updateCompletionTemplate(@Param("reqVO") ClassCompletionUpdateReqVO reqVO);

    default List<Long> getClassByCampus(Integer campus) {
        LambdaQueryWrapper<ClassManagementDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ClassManagementDO::getCampus,campus);
        wrapper.isNull(ClassManagementDO::getIdCode);

        return selectList(wrapper).stream().map(ClassManagementDO::getId).collect(Collectors.toList());
    }

    List<TraineeReportPageRespVO> selectTraineeReportPage(@Param("buildPage") IPage<ClassManagementDO> buildPage, @Param("reqVO") TraineeReportPageReqVO reqVO);

    List<TraineeReportExcelVO> selectTraineeReportList(@Param("reqVO") TraineeReportPageReqVO reqVO);

    List<ClassManagementDO> selectClassList();


    public static Cache<String, ClassRuleClockingInVO > resultCacheclass=
            CacheBuilder.newBuilder()
                    .initialCapacity(128) // 初始容量
                    .maximumSize(1024)   // 设定最大容量
                    .expireAfterWrite(5L, TimeUnit.MINUTES) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();

    default ClassRuleClockingInVO getClassRuleClockingInVObyTypeClass(TypeEnum key, Long classId){


        if(classId==null){
            return null;
        }

        String classIdstr =  classId.toString();

        try {

            if(classIdstr.contains(".")){
                classIdstr = classIdstr.replace("." , "");
            }


        }catch (Exception e){
            e.printStackTrace();
        }


        ClassRuleClockingInVO classClockInByAttendanceCheck = null;

        switch (key) {
            case COURSE:
                try {
                    classClockInByAttendanceCheck = resultCacheclass.get("daoke" + classIdstr, () -> {
                        return getClassClockInByAttendanceCheck(classId);
                    });
                } catch (ExecutionException e) {
                    classClockInByAttendanceCheck =getClassClockInByAttendanceCheck(classId);
                }
                break;
            case DINE:
                try {
                    classClockInByAttendanceCheck = resultCacheclass.get("jiucan" + classIdstr, () -> {
                        return getClassClockInByMealAttendance(classId);
                    });
                } catch (ExecutionException e) {
                    classClockInByAttendanceCheck =getClassClockInByMealAttendance(classId);
                }
                break;
            case LIVE:
                try {
                    classClockInByAttendanceCheck = resultCacheclass.get("resultCacheclass" + classIdstr, () -> {
                        return getClassClockInByCheckIn(classId);
                    });
                } catch (ExecutionException e) {
                    classClockInByAttendanceCheck =getClassClockInByCheckIn(classId);;
                }
                break;
            default:
                System.out.println("未知的班级考勤类型");
        }

        return classClockInByAttendanceCheck;
    };

    List<ClassManagementSimpleForBusinessCenterRespVO> simpleListForBusinessCenter(@Param("reqVO") ClassManagementSimpleForBusinessCenterReqVO reqVO);

    /**
     * 获取已排课表班级ID集合
     * @return 班级ID集合
     */
    List<Long> getClassIdList();
}
