package com.unicom.swdx.module.edu.service.cadreInformation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.crypt.sm4.SM4Util;
import com.unicom.swdx.framework.common.util.object.PageUtils;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.*;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.AddTraineeInfoReqVO;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.EditTraineeInfoReqVO;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.TraineeBaseVO;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.TraineeImportRespVO;
import com.unicom.swdx.module.edu.convert.cadreInformation.CadreInformationConvert;
import com.unicom.swdx.module.edu.dal.dataobject.cadreInformation.CadreInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.signupunit.SignUpUnitDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.cadreInformation.CadreInformationMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.signupunit.SignUpUnitMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.classmanagement.ClassManageDictTypeEnum;
import com.unicom.swdx.module.edu.enums.classmanagement.SemesterEnum;
import com.unicom.swdx.module.edu.enums.trainee.CoverEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeDictTypeEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.mq.producer.TraineeProducer;
import com.unicom.swdx.module.edu.service.training.TraineeDictConvertService;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import com.unicom.swdx.module.edu.utils.IdCard.CheckIdCard;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import com.unicom.swdx.module.system.api.user.AdminUserApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.unicom.swdx.module.system.enums.kafka.member.TraineeEventType.editMemberEventType;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.edu.service.signupunit.SignUpUnitServiceImpl.isValidPhoneNumber;

/**
 * @ClassName: TraineeGroupServiceImpl
 * @Author: lty
 * @Date: 2024/10/17 11:29
 */
@Service
@Validated
@Slf4j
public class CadreInformationServiceImpl extends ServiceImpl<CadreInformationMapper, CadreInformationDO> implements CadreInformationService {


    @Resource
    private CadreInformationMapper cadreInformationMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private SignUpUnitMapper signUpUnitMapper;

    @Resource
    private TraineeDictConvertService traineeDictConvertService;

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private DictDataApi dictDataApi;


    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    @Lazy
    private TraineeService traineeService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TraineeProducer traineeProducer;

    private static final String TRAINEE_OPERATION_LOCK_PREFIX = "TRAINEE_OPERATION_LOCK:";

    private static final  String desensitizePublicKeyStr = "04d89d95d0129f8b22a27979fd2c5d1fad305e1ec9ebbd2f6ff26f5bfdf00e5493574e8b93a2a16d4f924efd5133b6e7474aa694ec6314e39c34ff89c22d7b3768";

    private static final String desensitizePrivateKeyStr = "5174bc2473f86d5cda2ddad6a2d9b705848dd65b6fa13ff0923b1d2b55f09d30";

    private static final SM2 sm2Desensitize = SmUtil.sm2(desensitizePrivateKeyStr,desensitizePublicKeyStr);

    @Override
    public Long addCadreInformation(AddTraineeInfoReqVO reqVO) {

        String position = reqVO.getPosition();

        String phone = reqVO.getPhone();

        String cardNo = reqVO.getCardNo();

        Boolean flag = reqVO.getFlag();
        if (flag == null) {
            flag = false;
        }
        if (position != null && !position.isEmpty() && !flag) {
            String decryptedPosition = desensitizeDecrypt(position);
            reqVO.setPosition(decryptedPosition);
        }

        if (phone != null && !phone.isEmpty() && !flag) {
            String decryptedPhone = desensitizeDecrypt(phone);
            reqVO.setPhone(decryptedPhone);
        }

        if (cardNo != null && !cardNo.isEmpty() && !flag) {
            String decryptedCardNo = desensitizeDecrypt(cardNo);
            reqVO.setCardNo(decryptedCardNo);
        }

        checkCadreUnique(reqVO);

        CadreInformationDO cadreInformationDO = CadreInformationConvert.INSTANCE.covert(reqVO);

        if (!Objects.isNull(reqVO.getTenantId())){
            cadreInformationDO.setTenantId(Long.valueOf(reqVO.getTenantId()));
        }
        this.save(cadreInformationDO);

        return cadreInformationDO.getId();
    }

    @Override
    public Long editCadreInformation(EditTraineeInfoReqVO reqVO) {

        String position = reqVO.getPosition();

        String phone = reqVO.getPhone();

        String cardNo = reqVO.getCardNo();

        if (position != null && !position.isEmpty()) {
            String decryptedPosition = desensitizeDecrypt(position);
            reqVO.setPosition(decryptedPosition);
        }

        if (phone != null && !phone.isEmpty()) {
            String decryptedPhone = desensitizeDecrypt(phone);
            reqVO.setPhone(decryptedPhone);
        }

        if (cardNo != null && !cardNo.isEmpty()) {
            String decryptedCardNo = desensitizeDecrypt(cardNo);
            reqVO.setCardNo(decryptedCardNo);
        }
        //校验存在
        checkExist(reqVO);

        //校验重复
        checkCadreUnique(reqVO);


        CadreInformationDO cadreInformationDO = CadreInformationConvert.INSTANCE.covert(reqVO);

        this.updateById(cadreInformationDO);

        // 查找未结业班次中的该学员信息，实现全局同步
        syncTraineeInfoToOtherSystems(reqVO);

        return cadreInformationDO.getId();
    }

    /**
     * 同步学员信息到其他系统（教务管理系统和学员管理系统）
     * 查找所有未结业班次中的该学员信息，并进行同步更新
     *
     * @param reqVO 学员信息请求对象
     */
    private void syncTraineeInfoToOtherSystems(EditTraineeInfoReqVO reqVO) {
        // 获取请求中的同步标记，避免无限递归
        Boolean syncFlag = reqVO.getSyncFlag();

        // 如果是同步请求或已经同步过，则不再进行同步
        if (syncFlag != null && syncFlag) {
            log.info("跳过同步操作，因为当前是同步请求，身份证号: {}", reqVO.getCardNo());
            return;
        }

        // 使用Redisson锁防止并发同步导致的数据不一致
        String lockKey = TRAINEE_OPERATION_LOCK_PREFIX + "SYNC:" + reqVO.getCardNo();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取锁，最多等待5秒，锁过期时间30秒
            if (!lock.tryLock(5, 30, TimeUnit.SECONDS)) {
                log.warn("获取同步锁失败，可能有其他线程正在同步该学员信息，身份证号: {}", reqVO.getCardNo());
                return;
            }

            log.info("开始同步学员信息到其他系统，身份证号: {}", reqVO.getCardNo());

            // 根据身份证号查找所有学员信息
            List<TraineeDO> trainees = traineeMapper.selectByCardNo(reqVO.getCardNo());
            if (CollUtil.isEmpty(trainees)) {
                log.info("未找到需要同步的学员信息，身份证号: {}", reqVO.getCardNo());
                return;
            }

            // 获取当前日期时间
            LocalDateTime now = LocalDateTime.now();

            // 批量获取班级信息以提高性能
            Set<Long> classIds = trainees.stream()
                    .map(TraineeDO::getClassId)
                    .collect(Collectors.toSet());

            Map<Long, ClassManagementDO> classMap = classManagementMapper.selectBatchIds(classIds)
                    .stream()
                    .collect(Collectors.toMap(ClassManagementDO::getId, Function.identity()));

            // 过滤出未结业的班次中的学员
            List<TraineeDO> uncompletedTrainees = trainees.stream()
                    .filter(trainee -> {
                        ClassManagementDO classDO = classMap.get(trainee.getClassId());
                        // 如果班级不存在或已结业（结业时间已过），则不需要同步
                        return classDO != null &&
                                classDO.getCompletionTime() != null &&
                                classDO.getCompletionTime().isAfter(now) &&
                        // 确保是已发布的班级
                                classDO.getPublish() == 1;
                    })
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(uncompletedTrainees)) {
                log.info("未找到未结业班次中的学员信息，身份证号: {}", reqVO.getCardNo());
                return;
            }

            log.info("找到{}个未结业班次中的学员需要同步，身份证号: {}", uncompletedTrainees.size(), reqVO.getCardNo());

            // 对每个未结业班次中的学员信息进行更新
            for (TraineeDO trainee : uncompletedTrainees) {
                // 如果是同一个学员，跳过（避免重复更新）
                if (Objects.equals(trainee.getId(), reqVO.getId())) {
                    continue;
                }

                // 创建更新请求
                EditTraineeInfoReqVO updateReqVO = new EditTraineeInfoReqVO();
                // 复制基本信息
                updateReqVO.setId(trainee.getId());
                updateReqVO.setName(reqVO.getName());
                updateReqVO.setSex(reqVO.getSex());
                updateReqVO.setCardNo(reqVO.getCardNo());
                updateReqVO.setPhone(reqVO.getPhone());
                updateReqVO.setUnitName(reqVO.getUnitName());
                updateReqVO.setClassId(trainee.getClassId()); // 保持原班级ID
                updateReqVO.setEducationalLevel(reqVO.getEducationalLevel());
                updateReqVO.setBirthday(reqVO.getBirthday());
                updateReqVO.setEthnic(reqVO.getEthnic());
                updateReqVO.setPosition(reqVO.getPosition());
                updateReqVO.setJobLevel(reqVO.getJobLevel());
                updateReqVO.setGraduationSchool(reqVO.getGraduationSchool());
                updateReqVO.setPoliticalIdentity(reqVO.getPoliticalIdentity());
                updateReqVO.setRemark(reqVO.getRemark());
                updateReqVO.setPhoto(reqVO.getPhoto());
                updateReqVO.setUnitId(trainee.getUnitId());
                updateReqVO.setUnitClassification(reqVO.getUnitClassification());

                // 保持原有状态和分组信息
                updateReqVO.setStatus(trainee.getStatus());
                updateReqVO.setGroupId(trainee.getGroupId());
                updateReqVO.setGroupSort(trainee.getGroupSort());

                // 保留原有租户ID
                updateReqVO.setTenantId(trainee.getTenantId());

                // 设置同步标记，避免循环同步
                updateReqVO.setSyncFlag(true);

                // 使用学员服务进行更新
                try {
                    traineeService.editTrainee(updateReqVO);
                    log.info("同步更新学员信息成功，学员ID: {}, 班级ID: {}, 班级名称: {}",
                            trainee.getId(), trainee.getClassId(),
                            classMap.get(trainee.getClassId()) != null
                                    ? classMap.get(trainee.getClassId()).getClassName()
                                    : "未知");

                    // 发送消息到Kafka，通知其他系统学员信息已更新
//                    traineeProducer.sendTraineeToKafka(editMemberEventType, trainee);
                } catch (Exception e) {
                    log.error("同步更新学员信息失败，学员ID: {}, 班级ID: {}, 错误: {}",
                            trainee.getId(), trainee.getClassId(), e.getMessage(), e);
                }
            }

            log.info("完成同步学员信息到其他系统，身份证号: {}", reqVO.getCardNo());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("同步学员信息被中断: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("同步学员信息到其他系统失败: {}", e.getMessage(), e);
        } finally {
            // 确保锁被释放
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public Boolean batchDelete(CadreBatchDeleteReqVO reqVO) {

        if (CollUtil.isEmpty(reqVO.getIds())){
            return false;
        }

        //校验存在
        List<CadreInformationDO> list = cadreInformationMapper.selectBatchIds(reqVO.getIds());

        if (CollUtil.isEmpty(list)){
            throw exception(CADRE_INFORMATION_NOT_EXISTS);
        }

        if (list.size() != reqVO.getIds().size()){
            throw exception(CADRE_INFORMATION_NOT_EXISTS);
        }

        cadreInformationMapper.deleteBatchIds(reqVO.getIds());

        return true;

    }

    @Override
    public Boolean delete(Long id) {
        CadreBatchDeleteReqVO reqVO = new CadreBatchDeleteReqVO();
        reqVO.setIds(Collections.singletonList(id));
        return this.batchDelete(reqVO);
    }

    @Override
    public CadreInformationDO queryByCadreIdCard(String cadreIdCard,Long unitId, Integer tenantId) {

        return cadreInformationMapper.selectByCard(SM4Util.encrypt(cadreIdCard),unitId,Long.valueOf(tenantId));

    }

    @Override
    public Page<PageCadreInformationRespVO> pageCadreInformation(PageCadreInformationReqVO reqVO) {

        String rawPhone = reqVO.getPhone();

        if (rawPhone != null) {
            String encryptedPhone = desensitizeDecrypt(rawPhone);
            reqVO.setPhone(encryptedPhone);
        }
        Page<PageCadreInformationRespVO> page = cadreInformationMapper.getPage(MyBatisUtils.buildPage(reqVO), reqVO);

        int start = PageUtils.getStart(reqVO);
        for (PageCadreInformationRespVO record : page.getRecords()) {
            record.setIndex(++start);
            record.setPhone(desensitizeEncrypt(record.getPhone()));
        }

        return page;
    }

    @Override
    public CadreImportRespVO importInfo(List<CadreInfoImportExcelVO> list, Long unitId, Integer cover) {

        int num;
        if ("示例：1（示例请勿删除）".equals(list.get(0).getIndex())) {
            list.remove(0);
            num = 3;
        } else {
            num = 2;
        }

        List<CadreInformationDO> importList = CadreInformationConvert.INSTANCE.convertImportList(list);

        if (CollUtil.isEmpty(importList)){
            return CadreImportRespVO.builder()
                    .count(0)
                    .tag(1)
                    .build();
        }

        // 用于存储错误信息
        List<String> errorMessages = new ArrayList<>();

        // 用于存储校验通过和校验失败的数据
        List<CadreInformationDO> rightData = new ArrayList<>();
        List<String> deleteCardList = new ArrayList<>();

        // 用于存储表格中重复身份证信息
        List<String> idCardList = cadreInformationMapper.selectListByUnitId(unitId).stream().map(CadreInformationDO::getCardNo).collect(Collectors.toList());

        //获取民族
        Map<String, Map<String, Long>> nationMap = traineeMapper.getNation();
        SignUpUnitDO signUpUnitDO = signUpUnitMapper.selectById(unitId);
        Map<String, Map<String, Long>> educationalLevel = traineeDictConvertService.getDictDateMap(TraineeDictTypeEnum.EDUCATIONAL_LEVEL.getType());
        Map<String, Map<String, Long>> personRank = traineeDictConvertService.getDictDateMap(TraineeDictTypeEnum.JOB_LEVEL.getType());
        Map<String, Map<String, Long>> politicalIdentity = traineeDictConvertService.getDictDateMap(TraineeDictTypeEnum.POLITICAL_IDENTITY.getType());


        for (int i = 0; i < importList.size(); i++) {
            CadreInfoImportExcelVO cadreInfoVO = list.get(i);

            List<String> currentErrors = new ArrayList<>(); // 当前数据的错误信息列表

            // 学员名字
            if (StringUtils.isNotBlank(cadreInfoVO.getName())) {
                //姓名只包含中英文
//                boolean containsOnlyChineseAndEnglish = cadreInfoVO.getName().matches("^[\\u4e00-\\u9fa5a-zA-Z]+$");
//                if (!containsOnlyChineseAndEnglish) {
//                    currentErrors.add("第 " + (i + num) + " 行: 干部名字只能包含中文和英文");
//                }
//
//                if (cadreInfoVO.getName().length() > 50) {
//                    currentErrors.add("第 " + (i + num) + " 行: 姓名长度超出限制");
//                }

            } else {
                currentErrors.add("第 " + (i + num) + " 行: 干部名字不能为空");
            }

            if (StringUtils.isNotBlank(cadreInfoVO.getEducationalLevelName())) {
                if (educationalLevel.get(cadreInfoVO.getEducationalLevelName()) == null){
                    currentErrors.add("第 " + (i + num) + " 行: 文化程度数据不正确");
                }
            }else {
                currentErrors.add("第 " + (i + num) + " 行: 文化程度不能为空");
            }

            if (StringUtils.isNotBlank(cadreInfoVO.getPoliticalIdentityName())) {
                if (politicalIdentity.get(cadreInfoVO.getPoliticalIdentityName()) == null){
                    currentErrors.add("第 " + (i + num) + " 行: 政治面貌数据不正确");
                }
            }else {
                currentErrors.add("第 " + (i + num) + " 行: 政治面貌不能为空");
            }

            if (StringUtils.isNotBlank(cadreInfoVO.getEthnicName())) {
                if (nationMap.get(cadreInfoVO.getEthnicName()) == null){
                    currentErrors.add("第 " + (i + num) + " 行: 干部民族数据不正确");
                }
            }

            if (StringUtils.isNotBlank(cadreInfoVO.getPosition())) {

                if (cadreInfoVO.getPosition().length() > 200) {
                    currentErrors.add("第 " + (i + num) + " 行: 职务长度超出限制");
                }

            }else {
                currentErrors.add("第 " + (i + num) + " 行: 职务不能为空");
            }

            if (StringUtils.isNotBlank(cadreInfoVO.getJobLevelName())) {
                if (personRank.get(cadreInfoVO.getJobLevelName()) == null){
                    currentErrors.add("第 " + (i + num) + " 行: 职级数据不正确");
                }
            }

            if (StringUtils.isBlank(cadreInfoVO.getUnitName())) {
                //给个默认值
                cadreInfoVO.setUnitName(signUpUnitDO.getUnitName());
            }else {
                if (!cadreInfoVO.getUnitName().equals(signUpUnitDO.getUnitName())){
                    currentErrors.add("第 " + (i + num) + " 行: 干部单位数据不正确");
                }
            }

            //身份证校验
            if (StringUtils.isNotBlank(cadreInfoVO.getCardNo())) {
                TraineeBaseVO req = new TraineeBaseVO();
                req.setCardNo(cadreInfoVO.getCardNo());

                if(!CheckIdCard.isValidBirthDate(cadreInfoVO.getCardNo())){
                    currentErrors.add("第 " + (i + num) + " 行: 干部身份证格式不对");
                }else {
                    String birthDate = CheckIdCard.getFormattedBirthDate(cadreInfoVO.getCardNo());
                    cadreInfoVO.setBirth(birthDate);

                    String sex = CheckIdCard.getGenderByIdCard(cadreInfoVO.getCardNo());
                    cadreInfoVO.setSex(sex);

                    //判重
                    String s = checkImportUnique(req, idCardList);
                    if (Objects.equals(cover, CoverEnum.YES.getCode())) {

                        if (StrUtil.isNotBlank(s)){
                            //删除
                            rightData.removeIf(item -> Objects.equals(item.getCardNo(), req.getCardNo()));
                            deleteCardList.add(req.getCardNo());
                        }
                    }else {
                        if (StringUtils.isNotBlank(s)) {
                            currentErrors.add("第 " + (i + num) + " 行: 干部" + s);
                        }
                    }

                }
            } else {
                currentErrors.add("第 " + (i + num) + " 行: 干部身份证不能为空");
            }

            //手机号
            if (StringUtils.isNotBlank(cadreInfoVO.getPhone())) {
                boolean validPhoneNumber = isValidPhoneNumber(cadreInfoVO.getPhone());
                if (!validPhoneNumber) {
                    currentErrors.add("第 " + (i + num) + " 行: 干部手机号格式不对");
                }
            } else {
                currentErrors.add("第 " + (i + num) + " 行: 干部手机号不能为空");
            }

            if (!currentErrors.isEmpty()) {
                // 如果当前行有错误，添加到错误信息列表
                errorMessages.addAll(currentErrors);
            } else {
                CadreInformationDO item = importList.get(i);

                if (StrUtil.isNotBlank(cadreInfoVO.getEducationalLevelName())){
                    item.setEducationalLevel(educationalLevel.get(cadreInfoVO.getEducationalLevelName()).get("id").intValue());
                }

                if (StrUtil.isNotBlank(cadreInfoVO.getJobLevelName())){
                    item.setJobLevel(personRank.get(cadreInfoVO.getJobLevelName()).get("id").intValue());
                }


                if (StrUtil.isNotBlank(cadreInfoVO.getPoliticalIdentityName())){
                    item.setPoliticalIdentity(politicalIdentity.get(cadreInfoVO.getPoliticalIdentityName()).get("id").intValue());
                }

                if (StrUtil.isNotBlank(cadreInfoVO.getEthnicName())){
                    Long ethnic = nationMap.get(cadreInfoVO.getEthnicName()).get("id");
                    item.setEthnic(ethnic.intValue());
                }

                if (StringUtils.isNotBlank(cadreInfoVO.getSex())){
                    item.setSex(cadreInfoVO.getSex());
                }

                item.setCardNo(cadreInfoVO.getCardNo());
                // 时间字符串转LocalDate yyyy/MM/hh
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/M/d");
                item.setBirthday(LocalDate.parse(cadreInfoVO.getBirth(), formatter));

                item.setUnitId(unitId);

                rightData.add(item);
            }

        }

        // 处理校验通过的数据：保存到数据库
        if (CollUtil.isNotEmpty(rightData)) {

            //如果deleteCardList不为空，删除
            if (CollUtil.isNotEmpty(deleteCardList)){
                List<CadreInformationDO> traineeByCardNoList = cadreInformationMapper.getTraineeByCardNoList(unitId, deleteCardList);
                if (CollUtil.isNotEmpty(traineeByCardNoList)){
                    List<Long> ids = traineeByCardNoList.stream().map(CadreInformationDO::getId).collect(Collectors.toList());
                    cadreInformationMapper.deleteBatchIds(ids);
                }
            }

            this.saveBatch(rightData);

            if(rightData.size() == importList.size() || CollUtil.isEmpty(errorMessages)){
                return CadreImportRespVO.builder()
                        .count(rightData.size())
                        .tag(1)
                        .build();
            }else {
                return CadreImportRespVO.builder()
                        .errorMessages(errorMessages)
                        .count(rightData.size())
                        .tag(2)
                        .build();

            }
        }else {
            return CadreImportRespVO.builder()
                    .errorMessages(errorMessages)
                    .count(rightData.size())
                    .tag(3)
                    .build();
        }

    }

    @Override
    public List<ExportCadreInfoExcelVO> getCadreInfoList(ExportCadreInformationReqVO reqVO) {

        List<ExportCadreInfoExcelVO> list = cadreInformationMapper.getList(reqVO);

        //处理字典值
        handleDictValue(list);

        return list;
    }

    @Override
    public Page<CadreInfoDetailRespVO> getCadreInfoDetailPage(CadreInfoDetailReqVO reqVO) {

        return cadreInformationMapper.selectInfoDetailPage(MyBatisUtils.buildPage(reqVO),reqVO);
    }

    @Override
    public List<ExportCadreInfoDetailExcelVO> getCadreInfoDetailList(CadreInfoDetailReqVO reqVO) {
        List<ExportCadreInfoDetailExcelVO> list = cadreInformationMapper.selectInfoDetailList(reqVO);

        handleCadreInfoDetailDictValue(list);

        return list;
    }

    @Override
    public PageResult<TraineeReportPageRespVO> traineeReportPage(TraineeReportPageReqVO reqVO) {

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(reqVO.getClassName())){
            reqVO.setClassName(reqVO.getClassName().replaceAll("([%_])", "\\\\$1"));
        }

        Page buildPage = MyBatisUtils.buildPage(reqVO);

        List<TraineeReportPageRespVO> classManagementDOList = classManagementMapper.selectTraineeReportPage(buildPage, reqVO);

        //返回班级状态
        for(TraineeReportPageRespVO list : classManagementDOList){


            if(list.getPublish() == 2){

                list.setClassStatus("待发布");

            }else if(list.getPublish() == 1){

                list.setClassStatus("已发布");

                LocalDateTime currentTime = LocalDateTime.now();

                if (currentTime.isAfter(list.getCompletionTime())) {
                    list.setClassStatus("已结束");
                } else if (currentTime.isAfter(list.getClassOpenTime())) {
                    list.setClassStatus("开班中");
                } else if (currentTime.isAfter(list.getRegistrationEndTime())) {
                    list.setClassStatus("报名结束");
                } else if (currentTime.isAfter(list.getRegistrationStartTime())) {
                    list.setClassStatus("报名中");
                }


            }

        }

        return new PageResult<>(classManagementDOList, buildPage.getTotal());

    }

    @Override
    public List<TraineeReportExcelVO> exportTraineeReport(TraineeReportPageReqVO reqVO) {

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(reqVO.getClassName())){
            reqVO.setClassName(reqVO.getClassName().replaceAll("([%_])", "\\\\$1"));
        }


        List<TraineeReportExcelVO> list = classManagementMapper.selectTraineeReportList(reqVO);

        handleCampusDictValue(list);

        return list;
    }

    @Override
    public Page<TraineeInfoPageRespVO> traineeInfoByUnitId(TraineeInfoPageReqVO reqVO) {

        //获取reqVO中的class id
        Long classId = reqVO.getClassId();

        List<Long> classIds = new ArrayList<>();
        classIds.add(reqVO.getClassId());

        //获取和这个班级开班时间不相交 为结业 的班级
        ClassManagementDO currentClass = classManagementMapper.selectById(classId);

        //获取所有未结业的班级
        List<ClassManagementDO> existingClasses = classManagementMapper.selectClassList();

        LocalDateTime currentStart = currentClass.getReportingTime();
        LocalDateTime currentEnd = currentClass.getCompletionTime();

        for (ClassManagementDO existingClass : existingClasses) {
            LocalDateTime existingStart = existingClass.getReportingTime();
            LocalDateTime existingEnd = existingClass.getCompletionTime();

            // 严格判断时间段是否相交（包括端点相等）
            boolean isOverlap =
                    (currentStart.isBefore(existingEnd) || currentStart.isEqual(existingEnd)) &&
                            (existingStart.isBefore(currentEnd) || existingStart.isEqual(currentEnd));

            if (isOverlap) {
                classIds.add(existingClass.getId());
            }
        }


//        List<ClassManagementDO> classManagementDOList = classManagementMapper.selectClassByClassId(classId);


        Page<TraineeInfoPageRespVO> page = cadreInformationMapper.traineeInfoByUnitId(MyBatisUtils.buildPage(reqVO),reqVO,classIds);

        AtomicInteger start = new AtomicInteger(PageUtils.getStart(reqVO));
        page.getRecords().forEach(item->{
            item.setIndex(start.incrementAndGet());
            item.setPhone(DesensitizedUtil.mobilePhone(item.getPhone()));
        });

        return page;
    }

    @Override
    public CadreImportRespVO confirm(TraineeConfirmReqVO reqVO) {

        // 获取班级信息
        ClassManagementDO classManagementDO = classManagementMapper.selectById(reqVO.getClassId());
        if (Objects.isNull(classManagementDO)){
            throw exception(CLASS_MANAGEMENT_NOT_EXISTS);
        }

        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(classManagementDO.getRegistrationEndTime())){
            throw exception(CLASS_MANAGEMENT_REGISTRATION_END_TIME_EXCEED);
        }

        List<Long> ids = reqVO.getIds();

        List<CadreInformationDO> cadreInfoList = cadreInformationMapper.selectBatchIds(ids);


        List<TraineeDO> traineeList = CadreInformationConvert.INSTANCE.covertList(cadreInfoList);

        //插入system_users表获取userId;

        SignUpUnitDO unitDO = signUpUnitMapper.selectById(reqVO.getUnitId());

        //获取班级下的所有单位
        List<SignUpUnitDO>unitList = signUpUnitMapper.getAllUnitByClassId(reqVO.getClassId());

//        Map<String, Integer> map = unitList.stream().collect(Collectors.toMap(SignUpUnitDO::getUnitName, SignUpUnitDO::getId, (k1, k2) -> k1));
        Map<Long, Long> map = unitList.stream()
                .collect(Collectors.toMap(
                        SignUpUnitDO::getParentId,        // Key: Long (parentId)
                        unit -> unit.getId().longValue(), // Value: Long (id)
                        (k1, k2) -> k1                   // Merge function: 如果 key 冲突，保留第一个值
                ));

        traineeList.forEach(trainee -> {
            trainee.setId(null);
            trainee.setClassId(reqVO.getClassId());
            trainee.setStatus(TraineeStatusEnum.REGISTERED.getStatus());
//            trainee.setUnitId(Long.valueOf(map.get(unitDO.getUnitName())));
            trainee.setUnitId(map.get(Long.valueOf(unitDO.getId())));
            trainee.setUnitName(unitDO.getUnitName());
            trainee.setUnitClassification(Long.valueOf(unitDO.getUnitClassification()));
        });

        //校验学员是否已经报名
//        checkUnique(traineeList);
//
//        checkCapacity(reqVO, unitDO);
//
//        traineeMapper.insertBatch(traineeList);

        String s = processTrainee(reqVO, unitDO, traineeList);

        if (StrUtil.isNotBlank(s)){
            return CadreImportRespVO.builder()
                    .errorMessages(Collections.singletonList(s))
                    .count(1)
                    .tag(2)
                    .build();
        }

        // Map 来存储 phone 和对应的第一次出现的 TraineeDO
        Map<String, TraineeDO> uniquePhoneMap = new LinkedHashMap<>();
        List<TraineeDO> duplicateList = new ArrayList<>();

        for (TraineeDO trainee : traineeList) {
            if (uniquePhoneMap.containsKey(trainee.getPhone())) {
                // 如果 phone 已经存在，将当前元素加入重复列表
                duplicateList.add(trainee);
            } else {
                // 如果 phone 不存在，记录第一次出现的 TraineeDO
                uniquePhoneMap.put(trainee.getPhone(), trainee);
            }
        }

        // 提取只保留第一个的列表
        List<TraineeDO> firstOccurrenceList = new ArrayList<>(uniquePhoneMap.values());

        String jsonStr = JSONUtil.toJsonStr(firstOccurrenceList);
        List<Long> userIds = adminUserApi.createUsers(jsonStr).getCheckedData();

        firstOccurrenceList.forEach(trainee -> {
            trainee.setUserId(userIds.get(firstOccurrenceList.indexOf(trainee)));
        });

        traineeMapper.insertBatch(firstOccurrenceList); // 批量插入学员数据

        String returnIds = traineeService.sendTraineeBatchToMid(firstOccurrenceList);

        log.info("批量导入学员业中返回："+returnIds);
        adminUserApi.updateSystemIdBatchById(returnIds);

        if (duplicateList.size() == 0){
            return CadreImportRespVO.builder()
                    .count(firstOccurrenceList.size())
                    .tag(1)
                    .build();
        }else {
            StringBuffer sb = new StringBuffer();
            for (int i = 0; i < duplicateList.size(); i++) {
                sb.append(duplicateList.get(i).getName());
                if (i < duplicateList.size() - 1) {
                    sb.append(",");
                }
            }
            sb.append(" 手机号重复");
            return CadreImportRespVO.builder()
                    .errorMessages(Collections.singletonList(sb.toString()))
                    .count(duplicateList.size())
                    .tag(2)
                    .build();
        }
    }

    @Override
    public List<CadreInformationDO> getCadreInfoByTrainees(List<TraineeDO> traineeList) {

        List<TraineeDO> list = new ArrayList<>();
        Set<Long> set = traineeList.stream().map(TraineeDO::getUnitId).collect(Collectors.toSet());
        List<SignUpUnitDO> unitList = signUpUnitMapper.selectBatchIds(set);
        Map<Integer, Long> map = unitList.stream()
                .collect(HashMap::new,
                        (m, unit) -> m.put(unit.getId(), unit.getParentId()),
                        HashMap::putAll);

        for (TraineeDO traineeDO : traineeList) {
            TraineeDO trainee = new TraineeDO();
            trainee.setCardNo(SM4Util.encrypt(traineeDO.getCardNo()));
            Long parentId = map.get(traineeDO.getUnitId().intValue());
            if (parentId != null) {
                trainee.setUnitId(parentId);
                list.add(trainee);
            }
        }

        return cadreInformationMapper.getCadreInfoByTrainees(list);
    }


    private void checkCapacity(TraineeConfirmReqVO reqVO, SignUpUnitDO unitDO) {
        //判断班级和单位是否还有名额
//        ClassManagementDO classDO = classManagementMapper.selectById(reqVO.getClassId());
        // 判断该班级是否还有名额
//        Long count = traineeMapper.getActualPeopleNumber(reqVO.getClassId(), 3);
//        if (count + reqVO.getIds().size() > classDO.getPeopleNumber()) {
//            throw exception(TRAINEE_CLASS_NO_QUOTA);
//        }

        // 判断该单位是否还有名额
//        SignUpUnitDO signUpUnitDO = signUpUnitMapper.selectByClassIdAndUnitName(reqVO.getClassId(), unitDO.getUnitName());
        SignUpUnitDO signUpUnitDO = signUpUnitMapper.selectByClassIdAndParentId(reqVO.getClassId(), unitDO.getId());
        Long unitCount = traineeMapper.getActualPeopleNumber(Long.valueOf(signUpUnitDO.getId()),2);
        if (signUpUnitDO.getIsRestrict() == 0 && (signUpUnitDO.getCapacity() == null || unitCount + reqVO.getIds().size() > signUpUnitDO.getCapacity())){
            throw exception(TRAINEE_UNIT_NO_QUOTA);
        }
    }

    public String processTrainee(TraineeConfirmReqVO reqVO, SignUpUnitDO unitDO, List<TraineeDO> traineeList) {
        // 使用 Redisson 获取分布式锁
        RLock lock = redissonClient.getLock(TRAINEE_OPERATION_LOCK_PREFIX + reqVO.getUnitId());

        try {
            // 尝试加锁，最多等待10秒，锁持有时间为30秒
            boolean isLocked = lock.tryLock(10, 30, java.util.concurrent.TimeUnit.SECONDS);
            if (!isLocked) {
                throw exception(TRAINEE_LOCK_ACQUIRE_FAILED);
            }

            String name = checkUnique(traineeList,reqVO); // 检查唯一性

            checkCapacity(reqVO, unitDO); // 检查名额
//            traineeMapper.insertBatch(traineeList); // 批量插入学员数据

            return name;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw exception(TRAINEE_LOCK_ACQUIRE_FAILED);
        } finally {
            // 释放锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private String checkUnique(List<TraineeDO> list,TraineeConfirmReqVO reqVO) {
        // 检查人员是否已注册
        //获取相交班级id
        //获取reqVO中的class id
        Long classId = reqVO.getClassId();

        List<Long> classIds = new ArrayList<>();
        classIds.add(reqVO.getClassId());

        //获取和这个班级开班时间不相交 为结业 的班级
        ClassManagementDO currentClass = classManagementMapper.selectById(classId);

        //获取所有未结业的班级
        List<ClassManagementDO> existingClasses = classManagementMapper.selectClassList();

        LocalDateTime currentStart = currentClass.getReportingTime();
        LocalDateTime currentEnd = currentClass.getCompletionTime();

        for (ClassManagementDO existingClass : existingClasses) {
            LocalDateTime existingStart = existingClass.getReportingTime();
            LocalDateTime existingEnd = existingClass.getCompletionTime();

            // 严格判断时间段是否相交（包括端点相等）
            boolean isOverlap =
                    (currentStart.isBefore(existingEnd) || currentStart.isEqual(existingEnd)) &&
                            (existingStart.isBefore(currentEnd) || existingStart.isEqual(currentEnd));

            if (isOverlap) {
                classIds.add(existingClass.getId());
            }
        }

        List<TraineeDO> traineeList = traineeMapper.getTraineeByTraineeList(list,classIds);
        if (!traineeList.isEmpty()) {
//            return CADRE_ALREADY_REGISTERED;
            throw exception(CADRE_ALREADY_REGISTERED);
        }

        // 检查是否与数据库中的手机号重复
        List<TraineeDO> duplicatePhoneTrainees = traineeMapper.getTraineeByTraineePhone(list,classIds);
        if (!duplicatePhoneTrainees.isEmpty()) {
            StringBuffer sb = new StringBuffer();
            boolean first = true;
            for (TraineeDO trainee : list) {
                if (duplicatePhoneTrainees.stream().anyMatch(t -> t.getPhone().equals(trainee.getPhone()))) {
                    if (!first) {
                        sb.append(",");
                    }
                    sb.append(trainee.getName());
                    first = false;
                }
            }
            return sb.append("存在手机号重复，请更正后再报名").toString();
//            throw exception(CADRE_PHONE_NOT_UNIQUE, sb.toString());
        }
        return "";
    }

    private void handleCampusDictValue(List<TraineeReportExcelVO> list) {
        //查出字典中所有职级
        List<String> dict = new ArrayList<>();
        dict.add(ClassManageDictTypeEnum.CAMPUS.getType());
        List<DictDataRespDTO> campusList = dictDataApi.getByDictTypes(dict).getCheckedData();
        Map<Long, String> map = campusList.stream().collect(Collectors.toMap(DictDataRespDTO::getId, DictDataRespDTO::getLabel, (k1, k2) -> k1));

        list.forEach(item -> item.setCampus(map.getOrDefault(Long.valueOf(item.getCampus()), StrUtil.EMPTY)));
    }

    private void handleCadreInfoDetailDictValue(List<ExportCadreInfoDetailExcelVO> list) {
        Map<Long, Map<String, String>> attributeMap = traineeDictConvertService.getDictDateMapById(ClassManageDictTypeEnum.CLASS_ATTRIBUTE.getType());
        Map<Long, Map<String, String>> semesterMap = traineeDictConvertService.getDictDateMapById(ClassManageDictTypeEnum.SEMESTER.getType());

        for (ExportCadreInfoDetailExcelVO excelVO : list) {
            if (excelVO.getClassAttribute() != null) {
                excelVO.setClassAttribute(attributeMap.get(Long.valueOf(excelVO.getClassAttribute())).get("label"));
            }
            if (excelVO.getSemester() != null) {
                excelVO.setSemester(SemesterEnum.getDescByCode(Integer.valueOf(excelVO.getSemester())));
            }
        }
    }


    private void handleDictValue(List<ExportCadreInfoExcelVO> list) {

        Map<Long, Map<String, String>> educationalLevel = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.EDUCATIONAL_LEVEL.getType());
        Map<Long, Map<String, String>> politicalIdentityMap = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.POLITICAL_IDENTITY.getType());
        Map<Long, Map<String, String>> personRank = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.JOB_LEVEL.getType());
        Map<Long, Map<String, String>> nationMap = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.ETHNIC.getType());

        for (ExportCadreInfoExcelVO excelVO : list) {
            // 处理 educationalLevel
            Optional.ofNullable(excelVO.getEducationalLevel())
                    .map(Long::valueOf)
                    .map(educationalLevel::get)
                    .map(map -> map.get("label"))
                    .ifPresent(excelVO::setEducationalLevel);

            // 处理 jobLevel
            Optional.ofNullable(excelVO.getJobLevel())
                    .map(Long::valueOf)
                    .map(personRank::get)
                    .map(map -> map.get("label"))
                    .ifPresent(excelVO::setJobLevel);

            // 处理 politicalIdentity
            Optional.ofNullable(excelVO.getPoliticalIdentity())
                    .map(Long::valueOf)
                    .map(politicalIdentityMap::get)
                    .map(map -> map.get("label"))
                    .ifPresent(excelVO::setPoliticalIdentity);

            // 处理 ethnic
            Optional.ofNullable(excelVO.getEthnic())
                    .map(Long::valueOf)
                    .map(nationMap::get)
                    .map(map -> map.get("label"))
                    .ifPresent(excelVO::setEthnic);

            if (StrUtil.isBlank(excelVO.getJobLevel())){
                if (StrUtil.isNotBlank(excelVO.getOldjobLevel())){
                    excelVO.setJobLevel(excelVO.getOldjobLevel());
                }
            }

        }
    }

    private String checkImportUnique(TraineeBaseVO req, List<String> idCardList) {

        // 对 cardNo 进行加密
        if (idCardList.contains(req.getCardNo())){
            return "信息已存在";
        }

        idCardList.add(req.getCardNo());

        return null;
    }

    private void checkExist(EditTraineeInfoReqVO reqVO) {
        CadreInformationDO cadreInformationDO = cadreInformationMapper.selectById(reqVO.getId());
        if (cadreInformationDO == null) {
            throw exception(CADRE_INFORMATION_NOT_EXISTS);
        }
    }


    private void checkCadreUnique(TraineeBaseVO reqVO) {
        List<CadreInformationDO> list = cadreInformationMapper.getTraineeByCardNo(reqVO);
        if (CollUtil.isNotEmpty(list)) {
            throw exception(CADRE_INFORMATION_EXISTS);
        }
    }


    private static String desensitizeDecrypt(String content) {
        try {
            return sm2Desensitize.decryptStr(content, KeyType.PrivateKey);
        }catch (Exception e){
            log.info(content + "解密失败");
            return content;
        }
    }

    private static String desensitizeEncrypt(String content) {
        return sm2Desensitize.encryptBase64(content, KeyType.PublicKey);
    }
}
