package com.unicom.swdx.module.edu.dal.dataobject.classcourseteacher;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import com.unicom.swdx.framework.mybatis.core.dataobject.BaseDO;

/**
 * 课程表-教师-授课关系 DO
 *
 * <AUTHOR>
 */
@TableName("edu_class_course_teacher")
@KeySequence("edu_class_course_teacher_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassCourseTeacherDO extends BaseDO {

    /**
     * 唯一标识符，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 课程表id
     */
    private Long classCourseId;
    /**
     * 授课教师id
     */
    private Long teacherId;
    /**
     * 排序
     */
    private Long sort;
    /**
     *  部门名称
     */
    private String deptName;
    /**
     * 课程id
     */
    private Long courseId;

}
