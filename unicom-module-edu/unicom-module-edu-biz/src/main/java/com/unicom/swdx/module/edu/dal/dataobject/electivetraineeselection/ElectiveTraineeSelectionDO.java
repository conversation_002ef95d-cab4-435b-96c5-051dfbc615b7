package com.unicom.swdx.module.edu.dal.dataobject.electivetraineeselection;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * 选修课学员选课 DO
 *
 * <AUTHOR>
 */
@TableName("edu_elective_trainee_selection")
@KeySequence("edu_elective_trainee_selection_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ElectiveTraineeSelectionDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 选修课发布ID
     */
    private Long releaseId;
    /**
     * 发布课程ID
     */
    private Long releaseCourseId;
    /**
     * 学员ID
     */
    private Long traineeId;
    /**
     * 系统内部门
     */
    private Long deptId;

    /**
     * 是否已下发问卷
     */
    private Boolean isDistributed;

}
