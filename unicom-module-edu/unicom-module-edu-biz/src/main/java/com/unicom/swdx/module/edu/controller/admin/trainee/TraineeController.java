package com.unicom.swdx.module.edu.controller.admin.trainee;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.validation.ExcelValidator;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.module.edu.controller.admin.trainee.excelimporthandler.*;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.*;
import com.unicom.swdx.module.edu.convert.trainee.TraineeConvert;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.trainee.TraineeDictTypeEnum;
import com.unicom.swdx.module.edu.job.TraineeStatusJob;
import com.unicom.swdx.module.edu.service.training.TraineeDictConvertService;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import com.unicom.swdx.module.edu.utils.fuzzyquery.EscapeSelectUtil;
import com.unicom.swdx.module.edu.wx.xcx.WxXcxApiService;
import dm.jdbc.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.Normalizer;
import java.util.*;

import static com.unicom.swdx.framework.common.enums.ErrorCodeConstants.EXPORT_FAILED;
import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.IMPORT;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

@Api(tags = "管理后台 - 学员信息")
@RestController
@RequestMapping("/edu/trainee")
@Validated
@Slf4j
public class TraineeController {

    @Resource
    private TraineeService traineeService;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private TraineeDictConvertService traineeDictConvertService;

    @Resource
    private WxXcxApiService wxXcxApiService;

    @Resource
    private TraineeStatusJob traineeStatusJob;

    private static final String EXPECTED_TEMPLATE_NAME = "报名模板";
    /**
     * 报名详情分页
     * @param reqVO
     * @return
     */
    @GetMapping("/pageRegistration")
    @ApiOperation(value = "报名详情分页")
    @PreAuthorize("@ss.hasPermission('edu:trainee:page')")
    public CommonResult<Page<RegistrationPageRespVO>> pageRegistration(RegistrationInfoReqVO reqVO) {
        return success(traineeService.getPage(reqVO));
    }

    /**
     * 报名详情导出
     * @param reqVO
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportRegistrationInfo")
    @ApiOperation(value = "报名详情导出")
    @PreAuthorize("@ss.hasPermission('edu:trainee:export')")
    public void exportRegistrationInfo(@RequestBody RegistrationInfoReqVO reqVO, HttpServletResponse response) throws IOException {
        List<RegistrationDetailExcelVO> list = traineeService.getRegistrationInfoList(reqVO);


        ExcelUtils.writeByIncludeColumnIndexes(response, "报名详情.xls",
                "数据", RegistrationDetailExcelVO.class,list, reqVO.getIncludeColumnIndexes());

    }

    /**
     * 根据班级id查看单位报名详情 分页
     * @param reqVO 入参
     * @return
     */
    @GetMapping("/pageRegistrationInfoByClassId")
    @ApiOperation(value = "根据班级id查看单位报名详情")
    @PreAuthorize("@ss.hasPermission('edu:trainee:page')")
    public CommonResult<Page<RegistrationInfoRespVO>> pageRegistrationByClassId(RegistrationInfoReqVO reqVO) {
        return success(traineeService.getPageByClassId(reqVO));
    }

    @GetMapping("/getTrainById")
    @ApiOperation(value = "根据id获取学员信息")
    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
    public CommonResult<TraineeBaseVO> getTrainById(Long id) {
        return success(traineeService.getTraineeDetailById(id));
    }

//    @GetMapping("/getTraineeByGroup")
//    @ApiOperation(value = "根据id获取学员信息")
//    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
//    public CommonResult<PageResult<TraineeGroupingRespVO>> getTrainByGroup(TraineeGroupReqVO reqVO) {
//        return success(traineeService.getTrainByGroup(reqVO));
//    }

    @GetMapping("/getTrainByCardNo")
    @ApiOperation(value = "根据身份证号码获取学员信息")
    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
    @PermitAll
    public CommonResult<TraineeByCardNoVO> getTrainByCardNo(@RequestParam("cardNo") String cardNo,@RequestParam(value = "classId",required = false, defaultValue = "202") Long classId) {
        return success(traineeService.getTrainByCardNo(cardNo,classId));
    }

    /**
     * 根据班级id查看单位报名详情 分页
     * @param reqVO 入参
     * @return
     */
    @PostMapping("/exportRegistrationInfoByClassId")
    @ApiOperation(value = "导出单位报名详情")
    @PreAuthorize("@ss.hasPermission('edu:trainee:export')")
    public void pageRegistrationByClassId(@RequestBody RegistrationInfoReqVO reqVO,HttpServletResponse response) throws IOException {
        List<RegistrationInfoExcelVO> list = traineeService.getUnitRegistrationList(reqVO);
        ExcelUtils.writeByIncludeColumnIndexes(response, "报名详情.xls",
                "数据", RegistrationInfoExcelVO.class,list, reqVO.getIncludeColumnIndexes());
    }

    /**
     * 根据班级id查看学员名单 分页
     * @param reqVO 入参
     * @return
     */
    @GetMapping("/pageTraineeInfo")
    @ApiOperation(value = "查看学员名单信息")
    @PreAuthorize("@ss.hasPermission('edu:trainee:page')")
    public CommonResult<Page<UnitRegistrationPageRespVO>> pageTraineeInfo(HttpServletRequest request, TraineeInfoReqVO reqVO) {
        //判断该账户是否是学员
        Long userIds = SecurityFrameworkUtils.getLoginUserId();

        if(!traineeService.selectTraineeByUserId(request,userIds)){
            throw exception(NO_PERMISSION_ERROR);
        }
        return success(traineeService.pageTraineeInfo(reqVO));
    }


    /**
     * 根据班级id查看学员名单 分页
     * @param reqVO 入参
     * @return
     */
    @PostMapping("/exportTraineeInfo")
    @ApiOperation(value = "导出学员名单信息")
    @PreAuthorize("@ss.hasPermission('edu:trainee:export')")
    public void pageTraineeInfo(@RequestBody TraineeInfoReqVO reqVO,HttpServletResponse response) throws IOException {
//        List<UnitRegistrationPageRespVO> tmpList = traineeService.getTraineeInfoList(reqVO);
        Set<Integer> includeColumnIndexes = reqVO.getIncludeColumnIndexes();
        //序号一定要显示，前端传参从1开始
        reqVO.setIncludeColumnIndexes(includeColumnIndexes);
        List<RegistrationExcelVO> list = traineeService.getTraineeInfoList(reqVO);

        if (Objects.equals(reqVO.getType(), "registerView")){  //单位学员导出
            ExcelUtils.writeByIncludeColumnIndexes(response, "学员名单.xls",
                    "数据", RegistrationUnitExcelVO.class,TraineeConvert.INSTANCE.convertUnitList(list), reqVO.getIncludeColumnIndexes());
        } else if (Objects.equals(reqVO.getType(), "train") || Objects.equals(reqVO.getType(), "class")) {  //培训管理
            ExcelUtils.writeByIncludeColumnIndexes(response, "学员名单.xls",
                    "数据", RegistrationTrainExcelVO.class,TraineeConvert.INSTANCE.convertTrainList(list), reqVO.getIncludeColumnIndexes());
        }else if (Objects.equals(reqVO.getType(), "reportList")) {  //报到详情
            ExcelUtils.writeByIncludeColumnIndexes(response, "学员名单.xls",
                    "数据", RegistrationReportExcelVO.class,TraineeConvert.INSTANCE.convertReportList(list), reqVO.getIncludeColumnIndexes());
        }else if(Objects.equals(reqVO.getType(), "studentStatusInquiry")){  //学员状态管理
            ExcelUtils.writeByIncludeColumnIndexes(response, "学员名单.xls",
                    "数据", StudentStatusQueryExcelVO.class,TraineeConvert.INSTANCE.convertQueryList(list), reqVO.getIncludeColumnIndexes());
        }else{  //学员名单
            ExcelUtils.writeByIncludeColumnIndexes(response, "学员名单.xls",
                    "数据", RegistrationExcelVO.class,list, reqVO.getIncludeColumnIndexes());
        }

    }

    /**
     * 现场报名
     * @param reqVO 入参
     * @return
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增学员报名")
    @PreAuthorize("@ss.hasPermission('edu:trainee:add')")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    public CommonResult<Long> addTrainee(@Valid @RequestBody AddTraineeInfoReqVO reqVO) {
        return success(traineeService.addTrainee(reqVO));
    }

    /**
     * 现场报名 编辑
     * @param reqVO 入参
     * @return
     */
    @PostMapping("/edit")
    @ApiOperation(value = "修改学员报名")
    @PreAuthorize("@ss.hasPermission('edu:trainee:edit')")
    public CommonResult<Long> editTrainee(@RequestBody EditTraineeInfoReqVO reqVO) {
        return success(traineeService.editTrainee(reqVO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除学员")
    @PreAuthorize("@ss.hasPermission('edu:trainee:delete')")
    public CommonResult<Boolean> deleteTrainee(@RequestParam Long id) {
        return success(traineeService.deleteTrainee(id));
    }

    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除学员")
    @PreAuthorize("@ss.hasPermission('edu:trainee:delete')")
    public CommonResult<Boolean> batchDeleteTrainee(@RequestBody TraineeDeleteReqVO reqVO) {
        return success(traineeService.batchDeleteTrainee(reqVO));
    }

    /**
     * 报到详情分页
     * @param reqVO 入参
     * @return
     */
    @GetMapping("/reportPage")
    @ApiOperation(value = "报到详情分页")
    @PreAuthorize("@ss.hasPermission('edu:trainee:page')")
    public CommonResult<Page<ReportPageRespVO>> reportPage(ReportPageReqVO reqVO) {
        reqVO.setReportStatus(reqVO.getStatus());
        return success(traineeService.reportPage(reqVO));
    }

    /**
     * 发送全量的学员数据到业中
     * @param url 业中地址
     * @return
     */
    @GetMapping("/sendAllTraineeInfo")
    @ApiOperation("发送全量的学员数据到业中")
    public CommonResult<Boolean> sendAllTraineeInfo(@RequestParam("url") String url) {
        traineeService.sendAllTraineeInfo(url);
        return success(true);
    }

    @PostMapping("/exportReportInfo")
    @ApiOperation("根据classId获得EduSignUpUnit")
    public void exportReportInfo(@RequestBody ReportPageReqVO reqVO,HttpServletResponse response) throws IOException {
        reqVO.setReportStatus(reqVO.getStatus());
        Set<Integer> includeColumnIndexes = reqVO.getIncludeColumnIndexes();
        //序号一定要显示，前端传参从1开始
        reqVO.setIncludeColumnIndexes(includeColumnIndexes);
        List<ReportPageRespVO> list = traineeService.getreportList(reqVO);
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setIndex((long)(i + 1));
        }
        if (Objects.isNull(reqVO.getReportStatus())){
            List<ReportInfoExcelVO> list1 = TraineeConvert.INSTANCE.convertList(list);
            ExcelUtils.writeByIncludeColumnIndexes(response, "报到情况.xls",
                    "数据", ReportInfoExcelVO.class,list1, reqVO.getIncludeColumnIndexes());
        }else {
            List<ReportInfoDetailExcelVO> list1 = TraineeConvert.INSTANCE.convertList1(list);
            ExcelUtils.writeByIncludeColumnIndexes(response, "报到情况.xls",
                    "数据", ReportInfoDetailExcelVO.class,list1, reqVO.getIncludeColumnIndexes());
        }

    }

    /**
     * 教务教学查看学员名单信息
     * @param reqVO 入参
     * @return
     */
    @GetMapping("/getAllTraineeInfo")
    @ApiOperation(value = "教务教学查看学员名单信息")
    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
    public CommonResult<List<UnitRegistrationPageRespVO>> getAllTraineeInfo(TraineeInfoReqVO reqVO) {
        return success(traineeService.getAllTraineeInfo(reqVO));
    }

    /**
     * 教务教学查看学员名单信息
     * @param reqVO 入参
     * @return
     */
    @PostMapping("/exportAllTraineeInfo")
    @ApiOperation(value = "教务教学导出学员名单信息")
    @PreAuthorize("@ss.hasPermission('edu:trainee:export')")
    public void exportAllTraineeInfo(@RequestBody TraineeInfoReqVO reqVO,HttpServletResponse response) throws IOException {

        Set<Integer> includeColumnIndexes = reqVO.getIncludeColumnIndexes();
        //序号一定要显示，前端传参从1开始
        reqVO.setIncludeColumnIndexes(includeColumnIndexes);

        List<AllTraineeExcelVO> list = TraineeConvert.INSTANCE.covertTrainList(traineeService.getAllTraineeInfo(reqVO));

        Map<Long, Map<String, String>> educationalLevel = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.EDUCATIONAL_LEVEL.getType());
        Map<Long, Map<String, String>> politicalIdentityMap = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.POLITICAL_IDENTITY.getType());
        for (int i = 0; i < list.size(); i++) {
            AllTraineeExcelVO excelVO = list.get(i);

            if (StrUtil.isNotBlank(excelVO.getEducationalLevel())){
                if (educationalLevel != null && educationalLevel.containsKey(Long.valueOf(excelVO.getEducationalLevel())) && educationalLevel.get(Long.valueOf(excelVO.getEducationalLevel())) != null) {
                    excelVO.setEducationalLevel(educationalLevel.get(Long.valueOf(excelVO.getEducationalLevel())).get("label"));
                } else {
                    excelVO.setEducationalLevel(null); // 或者设置一个默认值
                }
            }
            if (StrUtil.isNotBlank(excelVO.getPoliticalIdentity())){
                if (politicalIdentityMap != null && politicalIdentityMap.containsKey(Long.valueOf(excelVO.getPoliticalIdentity())) && politicalIdentityMap.get(Long.valueOf(excelVO.getPoliticalIdentity())) != null) {
                    excelVO.setPoliticalIdentity(politicalIdentityMap.get(Long.valueOf(excelVO.getPoliticalIdentity())).get("label"));
                } else {
                    excelVO.setPoliticalIdentity(null); // 或者设置一个默认值
                }
            }
        }
        ExcelUtils.writeByIncludeColumnIndexes(response, "学员名单.xls",
                "数据", AllTraineeExcelVO.class,list, reqVO.getIncludeColumnIndexes());

    }

    /**
     * 现场报到（批量）
     * @param  ids 入参
     * @return boolean
     */
    @PostMapping("/checkInByIds")
    @ApiOperation(value = "现场报到")
    public CommonResult<Boolean> checkInByIds(@RequestBody List<Long> ids){
        return success(traineeService.checkInByIds(ids));
    }

    /**
     * 学员访问小程序前判断状态
     * @param  phone 学员手机号
     * @return 学员状态
     */
    @GetMapping("/getTraineeType")
    @ApiOperation(value = "学员访问小程序前判断状态")
    public CommonResult<Map<String, Object>> getTraineeType(@RequestParam String phone){
        return success(traineeService.getTraineeType(phone));
    }

    /**
     * 学员访问小程序前判断状态
     * @param  traineeId 学员id
     * @return 学员状态
     */
    @GetMapping("/getTraineeTypebyTraineeId")
    @ApiOperation(value = "学员访问小程序获取班级信息")
    public CommonResult<Map<String, Object>> getTraineeTypebyTraineeId(@RequestParam String traineeId){
        return success(traineeService.getTraineeTypebyTraineeId(traineeId));
    }




    /**
     * 学员分组 和 设置班委
     */
    @PostMapping("/setGroupOrCommittee")
    @ApiOperation(value = "学员分组 和 设置班委")
    public CommonResult<Boolean> setGroupOrCommittee(@RequestBody SetGroupCommitteeReqVO reqVO){
        return success(traineeService.setGroupOrCommittee(reqVO));
    }

    /**
     * 学员批量分组
     */
    @PostMapping("/batchSetGroup")
    @ApiOperation(value = "学员批量分组")
    public CommonResult<Boolean> batchSetGroup(@RequestBody BatchSetGroupReqVO reqVO){
        return success(traineeService.batchSetGroup(reqVO));
    }

    /**
     * 修改学员组内顺序
     */
    @PostMapping("/traineeOrder")
    @ApiOperation(value = "修改学员组内顺序")
    public CommonResult<Boolean> traineeOrder(@RequestBody TraineeOrderReqVO reqVO){
        return success(traineeService.traineeOrder(reqVO));
    }

    @GetMapping("/get-import-templates")
    @ApiOperation("获得导报名信息模板")
    public void importTemplate(Long classId,HttpServletResponse response) throws IOException {
        // 输出
        String filename = "报名信息.xls";
        String sheetName = "报名信息列表";
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 输出 Excel
        try {
            List<TraineeInfoImportStrExcelVO> list = getTraineeInfoImportStrExcelVOS();

            EasyExcel.write(response.getOutputStream(), TraineeInfoImportStrExcelVO.class)
                    .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
//                    .relativeHeadRowIndex(1) //第一行为空行，表头往后移动一行
//                    .registerWriteHandler(new CustomHeaderWriteHandler())
                    .registerWriteHandler(new GetEducationSheetWriteHandler(traineeMapper))
                    .registerWriteHandler(new GetLevelSheetWriteHandler(traineeMapper))
                    .registerWriteHandler(new GetLevelUnitWriteHandler(classId,traineeMapper))
                    .registerWriteHandler(new GetNationSheetWriteHandler(traineeMapper))
//                    .registerWriteHandler(new GetSexSheetWriteHandler(traineeMapper))
                    .registerWriteHandler(new GetzzmmSheetWriteHandler(traineeMapper))
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 基于 column 长度，自动适配。最大 255 宽度
                    .sheet(sheetName).doWrite(list);
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }

    @PostMapping("/import")
    @ApiOperation("导入学员信息")
    @OperateLog(type = IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class),
            @ApiImplicitParam(name = "classId", value = "班级ID", required = true, dataTypeClass = Long.class)
    })
    @PreAuthorize("@ss.hasPermission('edu:train:import')")
    public CommonResult<TraineeImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                         @RequestParam Long classId) throws Exception {

        // 获取文件名并去除路径和扩展名
        String originalFileName = file.getOriginalFilename();

        if (originalFileName == null || originalFileName.isEmpty()) {
            throw new IllegalArgumentException("文件名为空");
        }

        // 获取文件名（不带路径和扩展名）
        String fileNameWithoutExtension = new File(originalFileName).getName().substring(0, originalFileName.lastIndexOf('.')).trim();

        // 标准化处理
        fileNameWithoutExtension = Normalizer.normalize(fileNameWithoutExtension, Normalizer.Form.NFC);


        List<TraineeInfoImportStrExcelVO> list = null;
        // 校验表头信息
        try {
            list = ExcelUtils.readFirstSheetAndCheckHead(file, TraineeInfoImportStrExcelVO.class);
        } catch (Exception e) {
            throw exception(CLASS_MANAGEMENT_HEAD_NAME_ERROR);
        }

        // 验证 Excel 数据
        ExcelValidator.valid(list,1);

        // 调用服务层，导入学生信息
        return success(traineeService.importInfo(list,classId));
    }

    /**
     * 现场报名
     * @param reqVO 入参
     * @return
     */
    @PostMapping("/xcx-add")
    @ApiOperation(value = "新增学员报名")
    @PermitAll
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    public CommonResult<Long> xcxAddTrainee(@Valid @RequestBody AddTraineeInfoReqVO reqVO) {
        return success(traineeService.addTrainee(reqVO));
    }

    /**
     * 现场报名 编辑
     * @param reqVO 入参
     * @return
     */
    @PostMapping("/xcx-edit")
    @ApiOperation(value = "修改学员报名")
    @PreAuthorize("@ss.hasPermission('edu:trainee:edit')")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    @PermitAll
    public CommonResult<Long> xcxEditTrainee(@RequestBody EditTraineeInfoReqVO reqVO) {
        return success(traineeService.editTrainee(reqVO));
    }


    /**
     * 获取小程序二维码
     * @return 小程序二维码
     */
    @GetMapping("/getWxXcxQRCode")
//    @PermitAll
    @ApiOperation("获取微信小程序二维码")
    public CommonResult<WxXcxQRCodeRespVO> getWxXcxQRCode(Long classId) {
        return success(wxXcxApiService.getQRCodeBase64(classId));
    }

    /**
     * 参训统计
     * @param reqVO 入参
     * @return
     */
    @GetMapping("/traineeStat")
    @ApiOperation(value = "参训统计")
    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
    public CommonResult<Page<TraineeStatPageRespVO>> traineeStat(@Valid TraineeInfoStatReqVO reqVO) {
        //处理模糊查询的逃逸字符
        if(StringUtil.isNotEmpty(reqVO.getClassName())){
            reqVO.setClassName(EscapeSelectUtil.escapeChar(reqVO.getClassName()));
        }
        if(StringUtil.isNotEmpty(reqVO.getNameOrPhone())){
            reqVO.setNameOrPhone(EscapeSelectUtil.escapeChar(reqVO.getNameOrPhone()));
        }
        return success(traineeService.getTraineeStat(reqVO));
    }

    /**
     * 参训统计-导出
     * @param response
     * @param reqVO
     */
    @GetMapping("/traineeStatExport")
    @ApiOperation(value = "参训统计-导出")
    @PreAuthorize("@ss.hasPermission('edu:trainee:export')")
    public void traineeStatExport(HttpServletResponse response, @Valid TraineeInfoStatReqVO reqVO) {
        //处理模糊查询的逃逸字符
        if(StringUtil.isNotEmpty(reqVO.getClassName())){
            reqVO.setClassName(EscapeSelectUtil.escapeChar(reqVO.getClassName()));
        }
        if(StringUtil.isNotEmpty(reqVO.getNameOrPhone())){
            reqVO.setNameOrPhone(EscapeSelectUtil.escapeChar(reqVO.getNameOrPhone()));
        }
        traineeService.traineeStatExport(response,reqVO);
    }


    /**
     * 单位报名统计
     * @param reqVO 入参
     * @return
     */
    @GetMapping("/traineeStatByUnit")
    @ApiOperation(value = "单位报名统计")
    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
    public CommonResult<Page<TraineeStatByUnitPageRespVO>> traineeStatByUnit(@Valid TraineeInfoStatByUnitReqVO reqVO) {
        //处理模糊查询的逃逸字符
        if(StringUtil.isNotEmpty(reqVO.getClassName())){
            reqVO.setClassName(EscapeSelectUtil.escapeChar(reqVO.getClassName()));
        }
        if(StringUtil.isNotEmpty(reqVO.getNameOrPhone())){
            reqVO.setNameOrPhone(EscapeSelectUtil.escapeChar(reqVO.getNameOrPhone()));
        }
        return success(traineeService.getTraineeStatByUnit(reqVO));
    }

    /**
     * 单位报名统计-导出
     * @param response
     * @param reqVO
     */
    @GetMapping("/traineeStatByUnitExport")
    @ApiOperation(value = "单位报名统计-导出")
    @PreAuthorize("@ss.hasPermission('edu:trainee:export')")
    public void traineeStatByUnitExport(HttpServletResponse response, @Valid TraineeInfoStatByUnitReqVO reqVO) {
        //处理模糊查询的逃逸字符
        if(StringUtil.isNotEmpty(reqVO.getClassName())){
            reqVO.setClassName(EscapeSelectUtil.escapeChar(reqVO.getClassName()));
        }
        if(StringUtil.isNotEmpty(reqVO.getNameOrPhone())){
            reqVO.setNameOrPhone(EscapeSelectUtil.escapeChar(reqVO.getNameOrPhone()));
        }
        traineeService.traineeStatByUnitExport(response,reqVO);
    }



    /**
     * 参训报名-报名详情
     * @param reqVO 入参
     * @return
     */
    @GetMapping("/traineeInfo")
    @ApiOperation(value = "参训报名-报名详情")
    @PreAuthorize("@ss.hasPermission('edu:trainee:get')")
    public CommonResult<Page<TraineeInfoPageRespVO>> traineeInfoPage(@Valid TraineeInfoPageReqVO reqVO) {
        return success(traineeService.getTraineeInfoPage(reqVO));
    }

    /**
     * 报名详情导出
     * @param reqVO
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportTraineeReportInfo")
    @ApiOperation(value = "参训报名-报名详情导出")
    @PreAuthorize("@ss.hasPermission('edu:trainee:export')")
    public void exportTraineeInfo(@RequestBody TraineeInfoPageReqVO reqVO, HttpServletResponse response) throws IOException {
        List<ExportTraineeInfoExcelVO> list = traineeService.exportTraineeInfo(reqVO);

        ExcelUtils.writeByIncludeColumnIndexes(response, "参训报名详情.xls",
                "数据", ExportTraineeInfoExcelVO.class,list, reqVO.getIncludeColumnIndexes());

    }
    private List<TraineeInfoImportStrExcelVO> getTraineeInfoImportStrExcelVOS() {
        List<TraineeInfoImportStrExcelVO> list = new ArrayList<>();
        TraineeInfoImportStrExcelVO vo = new TraineeInfoImportStrExcelVO();
        vo.setIndex("示例：1（示例请勿删除）");        // 序号（示例中的特殊格式）
        vo.setName("张三");                            // 学员姓名
        vo.setPhone("18612345678");                   // 学员手机号（截取前11位，假设示例数据有误）
        vo.setCardNo("******************");           // 学员身份证
        vo.setUnitName("测试单位");                     // 学员所在单位
        vo.setEducationalLevelName("硕士研究生");        // 文化程度
        vo.setEthnicName("汉族");                      // 学员民族
        vo.setPosition("科员");                        // 学员职务
        vo.setJobLevelName("一级科员");                 // 职级
        vo.setPoliticalIdentityName("中共党员");        // 政治面貌
        vo.setGraduationSchool("湖南大学");             // 毕业院校
        vo.setLastTrainingTime("2023-10-15"); // 最近参训日期
        vo.setLastTrainingClass("2023年中青班第二期"); // 最近培训班次
        vo.setLatestQualExamDate("2023-12-20"); // 最近参加任职资格考试日期
        vo.setRemark("备注说明");                       // 学员备注
        list.add(vo);
        return list;
    }
}
