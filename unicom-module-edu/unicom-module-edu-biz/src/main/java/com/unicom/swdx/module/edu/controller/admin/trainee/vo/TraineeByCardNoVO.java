package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName: TraineeByCardNoVO
 * @Author: ZHK
 * @Date: 2024/10/20 11:45
 * @Description: TODO
 */
@Data
@ApiModel(value = "根据身份证获取报名详情返回VO")
public class TraineeByCardNoVO extends TraineeBaseVO{
    /**
     * 班级名称
     */
    @ApiModelProperty(value = "班级名称")
    private String className;
}
