package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;
import java.util.Set;

@ApiModel("调课记录导出筛选 VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CourseChangeExportReqVO {

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty(value = "调课类型字典值--edu_course_change_type")
    private Integer changeType;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;
}
