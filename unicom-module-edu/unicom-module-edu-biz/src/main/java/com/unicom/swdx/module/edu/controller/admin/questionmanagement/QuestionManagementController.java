package com.unicom.swdx.module.edu.controller.admin.questionmanagement;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;



import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionmanagement.QuestionManagementDO;
import com.unicom.swdx.module.edu.service.questionmanagement.QuestionManagementService;

@Tag(name = "管理后台 - 题目管理")
@RestController
@RequestMapping("/edu/question-management")
@Validated
public class QuestionManagementController {

    @Resource
    private QuestionManagementService questionManagementService;

    @PostMapping("/create")
    @Operation(summary = "创建题目管理")
    @PreAuthorize("@ss.hasPermission('edu:question-management:create')")
    public CommonResult<Long> createQuestionManagement(@Valid @RequestBody QuestionManagementSaveReqVO createReqVO) {
        return success(questionManagementService.createQuestionManagement(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新题目管理")
    @PreAuthorize("@ss.hasPermission('edu:question-management:update')")
    public CommonResult<Boolean> updateQuestionManagement(@Valid @RequestBody QuestionManagementSaveReqVO updateReqVO) {
        questionManagementService.updateQuestionManagement(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除题目管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:question-management:delete')")
    public CommonResult<Boolean> deleteQuestionManagement(@RequestParam("id") Long id) {
        questionManagementService.deleteQuestionManagement(id);
        return success(true);
    }

    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除题目管理(多选和条件)")
    @PreAuthorize("@ss.hasPermission('edu:question-management:delete')")
    public CommonResult<Boolean> batchDeleteQuestionManagement(@RequestParam("ids") List<Long> ids) {
        questionManagementService.batchDeleteQuestionManagement(ids);
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得题目管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-management:query')")
    public CommonResult<QuestionManagementRespVO> getQuestionManagement(@RequestParam("id") Long id) {
        QuestionManagementDO questionManagement = questionManagementService.getQuestionManagement(id);
        return success(BeanUtils.toBean(questionManagement, QuestionManagementRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得题目管理分页")
    @PreAuthorize("@ss.hasPermission('edu:question-management:query')")
    public CommonResult<PageResult<QuestionManagementRespVO>> getQuestionManagementPage(@Valid QuestionManagementPageReqVO pageReqVO) {
        PageResult<QuestionManagementRespVO> pageResult = questionManagementService.getQuestionManagementPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出题目管理 Excel")
    @PreAuthorize("@ss.hasPermission('edu:question-management:export')")

    public void exportQuestionManagementExcel(@Valid QuestionManagementPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<QuestionManagementRespVO> list = questionManagementService.getQuestionManagementPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "题目管理.xls", "数据", QuestionManagementRespVO.class,
                        BeanUtils.toBean(list, QuestionManagementRespVO.class));
    }

}
