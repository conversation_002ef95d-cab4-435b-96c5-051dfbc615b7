package com.unicom.swdx.module.edu.dal.dataobject.todoitems;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.unicom.swdx.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * 班主任待办事项 DO
 *
 * <AUTHOR>
 */
@TableName("edu_todo_items")
@KeySequence("edu_todo_items_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TodoItemsDO extends TenantBaseDO {

    /**
     * 待办事项ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 待办事项的具体内容
     */
    private String content;
    /**
     * 待办状态，0-未办 1-已办
     */
    private Integer status;
    /**
     * 待办事项类型编码(0请假申请、1报名确认)
     */
    private Integer type;
    /**
     * 班级ID
     */
    private Long classId;
    /**
     * 相关人员ID
     */
    private Long personId;
    /**
     * 部门 ID
     */
    private Long deptId;
    /**
     * 学员请假 ID
     */
    private Long leaveId;

}
