spring:
  application:
    name: gateway-server
  cloud:
    nacos:
      server-addr: 116.162.221.164:8848
#      server-addr: 116.162.216.112:8848
      username: nacos
      password: 8$SxLPqTv^sR

      discovery:
        #本地开发配置
#        namespace: b724a614-df09-44a1-9865-8aa5040c4982
        namespace: 637d8292-4c31-46d6-a1c5-13cd2dac5bae
#        namespace: 420797d7-6b65-4f97-9483-6786ac8ee9be
        #正式环境数据库配置
#        namespace: c6ae999d-8e47-461b-ad68-f380bacf6f0f
        auto-register: true
        # 本地开发时，更改自定义的group_id，避免访问到其他人的服务，现默认使用系统用户名
        group: ${user.name}
      config:
        name: gateway
        type: yaml
#        namespace: b724a614-df09-44a1-9865-8aa5040c4982
        namespace: 637d8292-4c31-46d6-a1c5-13cd2dac5bae
#        namespace: 420797d7-6b65-4f97-9483-6786ac8ee9be
#        namespace: c6ae999d-84e47-461b-ad68-f380bacf6f0f
        group: DEFAULT_GROUP
        auto-refresh: true
        enable-remote-sync-config: true
        file-extension: yaml
# 日志文件配置。注意，如果 logging.file.name 不放在 bootstrap.yaml 配置文件，而是放在 application.yaml 中，会导致出现 LOG_FILE_IS_UNDEFINED 文件
logging:
  file:
    path: ./logs
